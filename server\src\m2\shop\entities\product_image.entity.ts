import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Product } from './products.entity';
import { Status } from './status.entity';

@Entity('product_image')
export class ProductImage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: true })
  product_id?: string;
  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column({ type: 'varchar', length: 255 })
  image_url: string;

  @Column({ type: 'varchar', length: 150 })
  image_id: string;

  @Column({ type: 'uuid', nullable: true })
  status_id?: string;
  @ManyToOne(() => Status, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'status_id' })
  status?: Status;

  @Column({ type: 'boolean', default: false })
  is_thumbnail?: boolean;

  @Column({ type: 'int', nullable: true })
  display_order?: number;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;
}
