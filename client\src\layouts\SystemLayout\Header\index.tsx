import classNames from 'classnames/bind'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { GiHamburgerMenu } from 'react-icons/gi'
import { RiMenuFoldFill } from 'react-icons/ri'
import { useEffect, useState } from 'react'
import { Dropdown, type MenuProps } from 'antd' // Fix: thêm 'type' keyword
import { UserOutlined, LogoutOutlined } from '@ant-design/icons'
import { useDispatch } from 'react-redux'

import styles from './Header.module.scss'
import { ROUTES } from '../../../constants/routes'
import { useGetUserProfileHook } from '@/hooks/user'
import { useLogout } from '@/hooks/auth/useLogin'
import { getAvatarProps } from '@/utils/avatar'

const cx = classNames.bind(styles)

type HeaderSystemProps = {
  isToggleNavbar: boolean
  setToogleNavbar?: (value: boolean) => void
  drawerRightState?: boolean
  setDrawerRightState?: (value: boolean) => void
}

const HeaderSystem = ({ isToggleNavbar, setToogleNavbar }: HeaderSystemProps) => {
  const { data } = useGetUserProfileHook()
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768)
  const navigate = useNavigate()
  const { Logoutfunc } = useLogout()

  const [user, setUser] = useState<{ name: string; avatar?: string | null }>({
    name: data?.fullName || 'Người dùng',
    avatar: data?.avatarUrl,
  })

  useEffect(() => {
    if (data) {
      setUser({
        name:
          data?.fullName.split(' ').length >= 2
            ? data?.fullName.split(' ')[data?.fullName.split(' ').length - 1]
            : data?.fullName,
        avatar: data?.avatarUrl,
      })
    }
  }, [data])

  useEffect(() => {
    if (data?.fullName) {
      const fullName = String(data.fullName).trim()
      const parts = fullName.split(' ').filter(Boolean)
      const name = parts.length >= 2 ? parts[parts.length - 1] : fullName

      setUser({
        name,
        avatar: data?.avatarUrl,
      })
    }
  }, [data])

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Handle logout - use the centralized logout function
  const handleLogout = async () => {
    try {
      await Logoutfunc()
    } catch (error) {
      console.error('Logout failed:', error)
      // Fallback - navigate to login anyway
      navigate(ROUTES.LOGIN)
    }
  }

  // Dropdown menu items
  const menuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Hồ sơ cá nhân',
      onClick: () => navigate(ROUTES.PROFILE),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Đăng xuất',
      onClick: handleLogout,
      danger: true,
    },
  ]

  return (
    <div className={cx('header-wrapper')}>
      <div className={cx('header-content')}>
        {/* Phần avatar và tên người dùng với dropdown */}
        <Dropdown
          menu={{ items: menuItems }}
          trigger={['click']}
          placement="bottomRight"
          className={cx('user-dropdown')}
          overlayStyle={{ zIndex: 1050 }}
        >
          <div className={cx('user-info')}>
            <span className={cx('user-greeting')}>Xin chào!</span>
            <span className={cx('user-name')}>{user.name}</span>
            <img {...getAvatarProps(user.avatar, user.name)} className={cx('avatar')} />
          </div>
        </Dropdown>
      </div>
    </div>
  )
}

export default HeaderSystem
