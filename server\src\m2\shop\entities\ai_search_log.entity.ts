import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../../m1/user/entities/user.entity';

export enum AiSearchQueryType {
  TEXT = 'text',
  IMAGE = 'image',
}

@Entity('ai_search_logs')
export class AiSearchLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid', { nullable: true })
  user_id?: string;
  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @Column({ type: 'enum', enum: AiSearchQueryType })
  query_type: AiSearchQueryType;

  @Column({ type: 'text' })
  query_data: string;

  @Column({ type: 'int' })
  result_count: number;

  @Column({ type: 'int', nullable: true })
  latency_ms?: number;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;
}
