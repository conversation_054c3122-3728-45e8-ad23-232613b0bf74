import {
  Injectable,
  NotFoundException,
  Inject,
  forwardRef,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductCategory } from '../entities/product_categories.entity';
import { CreateProductCategoryDto } from '../dto/create-product-category.dto';
import { UpdateProductCategoryDto } from '../dto/update-product-category.dto';
import { ProductCategoryResponseDto } from '../dto/product-category-response.dto';
import { ClientProxy } from '@nestjs/microservices';
import { LogActionType } from 'src/constants';
import { CloudinaryService } from 'src/cloudinary/cloudinary.service';

@Injectable()
export class ProductCategoriesService {
  constructor(
    @InjectRepository(ProductCategory)
    private readonly categoryRepo: Repository<ProductCategory>,
    @Inject('LOG_SERVICE')
    private readonly logClient: ClientProxy,
    private readonly cloudinaryService: CloudinaryService,
  ) {}

  /**
   * L<PERSON>y danh sách categories theo type
   */
  async getCategories(type?: string): Promise<ProductCategoryResponseDto[]> {
    if (type && type.toLowerCase() === 'tree') {
      return this.findAllTree();
    }
    return this.findAllFlat();
  }

  /**
   * Tạo category mới
   */
  async createCategory(dto: CreateProductCategoryDto) {
    return this.create(dto);
  }

  /**
   * Lấy category theo ID
   */
  async getCategoryById(id: string): Promise<ProductCategoryResponseDto> {
    const category = await this.findOne(id);
    if (!category) {
      throw new NotFoundException('Category not found');
    }
    return category;
  }

  /**
   * Cập nhật category
   */
  async updateCategory(id: string, dto: UpdateProductCategoryDto) {
    // Lấy entity gốc để update
    const cat = await this.categoryRepo.findOne({ where: { id } });
    if (!cat) throw new NotFoundException('Category not found');
    // Nếu chỉ muốn bỏ parent
    if (dto.parentId !== undefined && dto.parentId === null) {
      await this.categoryRepo.update(id, {
        name: dto.name !== undefined ? dto.name : cat.name,
        slug: dto.slug !== undefined ? dto.slug : cat.slug,
        icon_url: dto.icon_url !== undefined ? dto.icon_url : cat.icon_url,
        parent_id: null,
        parent: undefined,
      });
      const updated = await this.categoryRepo.findOne({ where: { id } });
      if (!updated) throw new NotFoundException('Category not found after update');
      this.logClient.emit('log.update', {
        module: 'ProductCategory',
        action: LogActionType.UPDATE,
        performedBy: null,
        targetId: updated.id,
        description: `Cập nhật category: ${updated.name}`,
        timestamp: new Date().toISOString(),
        data: updated,
      });
      return {
        id: updated.id,
        name: updated.name,
        slug: updated.slug,
        parentId: updated.parent_id || undefined,
        level: updated.level,
        order_index: updated.order_index,
        icon_url: updated.icon_url,
        is_active: updated.is_active,
        created_at: updated.created_at,
        children: [],
      };
    }
    // Cập nhật các trường khác như cũ
    if (dto.name !== undefined) cat.name = dto.name;
    if (dto.slug !== undefined) cat.slug = dto.slug;
    if (dto.icon_url !== undefined) cat.icon_url = dto.icon_url;
    if (dto.parentId !== undefined) {
      if (!dto.parentId) {
        cat.parent = undefined;
        cat.parent_id = undefined;
      } else {
        const parent = await this.categoryRepo.findOne({ where: { id: dto.parentId } });
        if (!parent) throw new NotFoundException('Parent category not found');
        cat.parent = parent;
        cat.parent_id = parent.id;
      }
    }
    const saved = await this.categoryRepo.save(cat);
    this.logClient.emit('log.update', {
      module: 'ProductCategory',
      action: LogActionType.UPDATE,
      performedBy: null, // Có thể truyền userId nếu có
      targetId: saved.id,
      description: `Cập nhật category: ${saved.name}`,
      timestamp: new Date().toISOString(),
      data: saved,
    });
    return {
      id: saved.id,
      name: saved.name,
      slug: saved.slug,
      parentId: saved.parent_id || undefined,
      level: saved.level,
      order_index: saved.order_index,
      icon_url: saved.icon_url,
      is_active: saved.is_active,
      created_at: saved.created_at,
      children: [],
    };
  }

  /**
   * Xóa category
   */
  async deleteCategory(id: string) {
    return this.remove(id);
  }

  /**
   * Upload icon cho category
   */
  async uploadIcon(file: Express.Multer.File) {
    try {
      const uploaded = await this.cloudinaryService.uploadFile(file);

      return {
        icon_url: uploaded.secure_url,
        public_id: uploaded.public_id,
        message: 'Upload icon thành công',
      };
    } catch (error) {
      throw new BadRequestException('Upload icon thất bại: ' + (error?.message || error));
    }
  }

  /**
   * Lấy tất cả category dạng list phẳng có level
   */
  async findAllFlat(): Promise<ProductCategoryResponseDto[]> {
    const all = await this.categoryRepo.find();
    // Trả về mảng phẳng, mỗi item KHÔNG có children
    return all.map((cat) => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      parentId: cat.parent_id || undefined,
      level: cat.level,
      order_index: cat.order_index,
      icon_url: cat.icon_url,
      is_active: cat.is_active,
      created_at: cat.created_at,
      children: undefined,
    }));
  }

  /**
   * Lấy tất cả category dạng cây lồng nhau
   */
  async findAllTree(): Promise<ProductCategoryResponseDto[]> {
    const all = await this.categoryRepo.find();
    const map = new Map<string, ProductCategoryResponseDto>();
    all.forEach((cat) => {
      map.set(cat.id, {
        id: cat.id,
        name: cat.name,
        slug: cat.slug,
        parentId: cat.parent_id || undefined,
        level: cat.level,
        order_index: cat.order_index,
        icon_url: cat.icon_url,
        is_active: cat.is_active,
        created_at: cat.created_at,
        children: [],
      });
    });
    // Gán children cho từng node
    map.forEach((cat) => {
      if (cat.parentId && map.has(cat.parentId)) {
        map.get(cat.parentId)!.children!.push(cat);
      }
    });
    // Chỉ lấy các node không có parent (gốc)
    const roots: ProductCategoryResponseDto[] = [];
    map.forEach((cat) => {
      if (!cat.parentId) roots.push(cat);
    });
    // Nếu muốn loại bỏ trường children khi rỗng:
    function cleanChildren(node: ProductCategoryResponseDto) {
      if (node.children && node.children.length === 0) {
        delete node.children;
      } else if (node.children) {
        node.children.forEach(cleanChildren);
      }
    }
    roots.forEach(cleanChildren);
    return roots;
  }

  /**
   * Lấy 1 category theo id
   */
  async findOne(id: string): Promise<ProductCategoryResponseDto | undefined> {
    const cat = await this.categoryRepo.findOne({ where: { id } });
    if (!cat) return undefined;
    return {
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      parentId: cat.parent_id || undefined,
      level: cat.level,
      order_index: cat.order_index,
      icon_url: cat.icon_url,
      is_active: cat.is_active,
      created_at: cat.created_at,
      children: undefined,
    };
  }

  async create(dto: CreateProductCategoryDto) {
    try {
      const entity = this.categoryRepo.create({
        name: dto.name,
        slug: dto.slug,
        icon_url: dto.icon_url,
      });
      if (dto.parentId) {
        const parent = await this.categoryRepo.findOne({ where: { id: dto.parentId } });
        if (!parent) throw new NotFoundException('Parent category not found');
        entity.parent = parent;
      }
      const saved = await this.categoryRepo.save(entity);
      this.logClient.emit('log.update', {
        module: 'ProductCategory',
        action: LogActionType.CREATE,
        performedBy: null, // Có thể truyền userId nếu có
        targetId: saved.id,
        description: `Tạo category mới: ${saved.name}`,
        timestamp: new Date().toISOString(),
        data: saved,
      });
      return saved;
    } catch (error) {
      throw new BadRequestException('Tạo category thất bại: ' + (error?.message || error));
    }
  }

  async update(id: string, dto: UpdateProductCategoryDto) {
    // Lấy entity gốc để update
    const cat = await this.categoryRepo.findOne({ where: { id } });
    if (!cat) throw new NotFoundException('Category not found');
    if (dto.name !== undefined) cat.name = dto.name;
    if (dto.slug !== undefined) cat.slug = dto.slug;
    if (dto.icon_url !== undefined) cat.icon_url = dto.icon_url;
    if (dto.parentId !== undefined) {
      if (dto.parentId === null) {
        cat.parent = undefined;
        cat.parent_id = undefined;
      } else if (!dto.parentId) {
        cat.parent = undefined;
        cat.parent_id = undefined;
      } else {
        const parent = await this.categoryRepo.findOne({ where: { id: dto.parentId } });
        if (!parent) throw new NotFoundException('Parent category not found');
        cat.parent = parent;
        cat.parent_id = parent.id;
      }
    }
    const saved = await this.categoryRepo.save(cat);
    this.logClient.emit('log.update', {
      module: 'ProductCategory',
      action: LogActionType.UPDATE,
      performedBy: null, // Có thể truyền userId nếu có
      targetId: saved.id,
      description: `Cập nhật category: ${saved.name}`,
      timestamp: new Date().toISOString(),
      data: saved,
    });
    // Trả về DTO response
    return {
      id: saved.id,
      name: saved.name,
      slug: saved.slug,
      parentId: saved.parent_id || undefined,
      level: saved.level,
      order_index: saved.order_index,
      icon_url: saved.icon_url,
      is_active: saved.is_active,
      created_at: saved.created_at,
      children: [],
    };
  }

  async remove(id: string) {
    // Lấy entity gốc để xóa
    const cat = await this.categoryRepo.findOne({ where: { id } });
    if (!cat) throw new NotFoundException('Category not found');
    await this.categoryRepo.remove(cat);
    this.logClient.emit('log.update', {
      module: 'ProductCategory',
      action: LogActionType.DELETE,
      performedBy: null, // Có thể truyền userId nếu có
      targetId: cat.id,
      description: `Xóa category: ${cat.name}`,
      timestamp: new Date().toISOString(),
      data: cat,
    });
    return { deleted: true };
  }

  /**
   * Lấy tất cả id con (đệ quy) của một category (bao gồm cả chính nó)
   */
  async getAllDescendantCategoryIds(categoryId: string): Promise<string[]> {
    const all = await this.categoryRepo.find();
    const result: string[] = [];
    function collect(id: string) {
      result.push(id);
      all.filter((c) => c.parent_id === id).forEach((c) => collect(c.id));
    }
    collect(categoryId);
    return result;
  }
}
