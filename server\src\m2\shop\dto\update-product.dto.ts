import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  IsNumber,
  IsString,
  IsArray,
  ValidateNested,
  IsInt,
  IsObject,
} from 'class-validator';
import { ProductAttributeDto, ProductImageDto } from './create-product.dto';
import { UpdateProductVersionDto } from './product-version.dto';

export class UpdateProductDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ required: false, description: 'ID trạng thái sản phẩm (status_id)' })
  @IsOptional()
  @IsString()
  status_id?: string;

  @ApiProperty({ description: 'Mô tả sản phẩm do người dùng nhập hoặc AI sinh' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  price: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  unit?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  category_id?: string;

  @ApiProperty({ type: [ProductImageDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageDto)
  product_images?: ProductImageDto[];

  @ApiProperty({ type: [ProductAttributeDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductAttributeDto)
  product_attribute?: ProductAttributeDto[];

  @ApiProperty({ type: [UpdateProductVersionDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductAttributeDto)
  product_versions?: UpdateProductVersionDto[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  stock?: number;

  // @ApiProperty({ required: false })
  // @IsOptional()
  // @IsString()
  // status?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  seo_keywords?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  thumbnail_url?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  is_public?: boolean;
}
