import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../../m1/user/entities/user.entity';
import { Product } from './products.entity';

export enum GeoDiscountType {
  PERCENT = 'percent',
  FIXED = 'fixed',
}

@Entity('geo_discounts')
export class GeoDiscount {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  product_id: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  province_code?: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  district_code?: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  commune_code?: string;

  @Column({ type: 'enum', enum: GeoDiscountType })
  discount_type: GeoDiscountType;

  @Column({ type: 'decimal', precision: 12, scale: 2 })
  discount_value: string;

  @Column({ type: 'timestamp' })
  start_date: Date;

  @Column({ type: 'timestamp' })
  end_date: Date;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @Column('uuid', { nullable: true })
  created_by?: string;
  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'created_by' })
  createdBy?: User;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;

  @ManyToOne(() => Product, (product) => product.geo_discounts, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'product_id' })
  product: Product;
}
