{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@fortawesome/react-fontawesome": "github:fortawesome/react-fontawesome", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/forms": "^0.5.10", "antd": "^5.26.1", "antd-img-crop": "^4.25.0", "axios": "^1.10.0", "bootstrap": "^5.3.7", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "framer-motion": "^12.23.6", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.518.0", "react": "^18.3.1", "react-chatbot-kit": "^2.2.2", "react-dom": "^18.2.0", "react-hook-form": "^7.58.1", "react-icon": "^1.0.0", "react-icons": "^5.5.0", "react-otp-input": "^3.1.1", "react-redux": "^9.2.0", "react-responsive": "^10.0.1", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "reactstrap": "^9.2.3", "recharts": "^3.1.0", "redux-persist": "^6.0.0", "sass": "^1.89.2", "sharp": "^0.34.2", "svgo": "^4.0.0", "tailwind-merge": "^3.3.1", "vietnam-provinces-js": "^2.0.0", "vite-plugin-image-optimizer": "^1.1.9", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/vite": "^4.1.10", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/reactstrap": "^8.7.2", "@types/recharts": "^1.8.29", "@types/redux-persist": "^4.3.1", "@vitejs/plugin-basic-ssl": "^2.0.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "prettier": "^3.5.3", "shadcn-ui": "^0.9.5", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-html": "^3.2.2", "vite-tsconfig-paths": "^5.1.4"}}