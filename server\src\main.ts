import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { DataSource } from 'typeorm';
import { AllExceptionsFilter } from './common/filters/all-exceptions.filter';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { seedRBAC } from './database/seeds/m1/role.seed';
import { seedExtendedRBAC } from './database/seeds/m1/extended-role.seed';
import { seedProductCategories } from './database/seeds/m2/product-categories.seed';
import { seedStatus } from './database/seeds/m2/status.seed';

async function bootstrap() {
  console.log('--- ENV CHECK ---');
  console.log('DB_HOST:', process.env.DB_HOST);
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('-----------------');

  const app = await NestFactory.create(AppModule);

  // 1) Queue log
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [process.env.RABBITMQ_URL || 'amqp://localhost:5672'],
      queue: process.env.RABBITMQ_QUEUE || 'log_queue',
      queueOptions: { durable: true },
    },
  });

  // 2) Queue image-vectorize_
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [process.env.RABBITMQ_URL || 'amqp://localhost:5672'],
      queue: process.env.IMAGE_VECTOR_QUEUE || 'image_vectorize_queue',
      queueOptions: { durable: true },
    },
  });

  // Start tất cả microservices trước khi app.listen
  app.enableCors({
    origin: 'http://localhost:3000', // Chỉ cho phép từ nguồn này (nếu cần)
    // origin: true, // Cho phép mọi nguồn (dev/test). Không nên dùng cho production!
    credentials: true,
  });

  app.useGlobalFilters(new AllExceptionsFilter());
  app.useGlobalInterceptors(new TransformInterceptor());

  const config = new DocumentBuilder()
    .setTitle('TAP API Gateway')
    .setDescription('API docs cho dự án TAP')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: 'Nhập token theo định dạng: Bearer <token>',
        in: 'header',
      },
      'access-token', // Đây là tên định danh để sử dụng với @ApiBearerAuth()
    )
    .build();

  seedRBAC(app.get(DataSource));
  seedExtendedRBAC(app.get(DataSource));
  seedProductCategories(app.get(DataSource));
  seedStatus(app.get(DataSource));

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('swagger', app, document);

  await app.listen(8080);

  await app.startAllMicroservices();
}
bootstrap();
