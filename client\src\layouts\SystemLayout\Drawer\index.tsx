import classNames from 'classnames/bind'
import styles from '../Header/Header.module.scss'
import { Drawer, Select, Space } from 'antd'
import { BiCalendar, BiSolidSchool } from 'react-icons/bi'
import { useState } from 'react'

const cx = classNames.bind(styles)

interface DrawerRightProps {
  onClose: () => void
  visible: boolean
}

const DrawerRight = ({ onClose, visible }: DrawerRightProps) => {
  return (
    <Drawer title={<h1>Cài đặt</h1>} placement="right" onClose={onClose} open={visible}>
      <Space direction="vertical" size={'large'}>
        <div>
          <BiSolidSchool className={cx('school-icon', 'pe-2')} />{' '}
        </div>
      </Space>
    </Drawer>
  )
}

export default DrawerRight
