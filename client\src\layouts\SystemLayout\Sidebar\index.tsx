// File: components/Sidebar.tsx (đã sửa dùng Redux authState + có toggle + logout)

import React, { useEffect, useState } from 'react'
import classNames from 'classnames/bind'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { MdLogout } from 'react-icons/md'
import { RiMenuFold2Fill, RiMenuFoldFill } from 'react-icons/ri'
import { Image } from 'antd'

import styles from './Sidebar.module.scss'
import ConfirmModal from '@/components/ConfirmModal'
import { ROUTES } from '@/constants/routes'
import { useLogout } from '@/hooks/auth/useLogin'
import { getAvatarProps } from '@/utils/avatar'
import { useGetUserProfileHook } from '@/hooks/user'
import {
  commonSidebarItems,
  // commonSidebarItems,
  permissionSidebarItems,
  type SiderItem,
} from '@/constants/sidebarItems'
import type { RootState } from '@/libs/state/store'
import { checkPermission } from '@/helpers/permission'

const cx = classNames.bind(styles)

type SidebarSystemProps = {
  isToggleNavbar: boolean
  setIsToggleNavbar: (val: boolean) => void
}

const SidebarSystem = ({ isToggleNavbar, setIsToggleNavbar }: SidebarSystemProps) => {
  const navigate = useNavigate()
  const pathName = useLocation().pathname

  const authUser = useSelector((state: RootState) => state.auth)
  const userPermissions = authUser?.permissions || []

  // Lấy thông tin profile để có avatar mới nhất
  const { data: userProfile } = useGetUserProfileHook()

  const sidebarItemsToShow: SiderItem[] = [
    ...commonSidebarItems,
    ...permissionSidebarItems.filter(
      (item) =>
        !item.requiredPermissions || checkPermission(userPermissions, item.requiredPermissions, authUser?.role?.role_code)
    ),
  ]

  const [isMobile, setIsMobile] = useState<boolean>(window.innerWidth < 768)
  const [confirmOpen, setConfirmOpen] = useState<boolean>(false)
  const { Logoutfunc } = useLogout()

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <>
      <nav className={cx('navbar', `${isToggleNavbar ? 'toggle' : ''}`)}>
        <div>
          {/* Logo */}
          <div className={cx('navbar-logo')}>
            <div className={cx('navbar-item-inner-logo')}>
              <img className={cx('navbar-logo-img')} src={'/logo.png'} alt="TAP Logo" />
            </div>

            <div
              className={cx('icon-menu-wrapper')}
              onClick={() => setIsToggleNavbar(!isToggleNavbar)}
            >
              {isToggleNavbar ? (
                <RiMenuFoldFill size={26} className={cx('icon-menu')} />
              ) : (
                <RiMenuFold2Fill size={26} className={cx('icon-menu')} />
              )}
            </div>
          </div>

          {/* Sidebar items */}
          <ul className={cx('navbar-items')}>
            {sidebarItemsToShow.map((item: SiderItem, index: number) => (
              <li key={index} className={cx('navbar-item', item.link === pathName ? 'active' : '')}>
                <Link className={cx('navbar-item-inner')} to={item.link}>
                  <div className={cx('navbar-item-inner-icon-wrapper')}>{item.icon}</div>
                  <div className={cx('link-text-wrapper')}>
                    <span className={cx('link-text')}>{item.name}</span>
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Profile + Logout */}
        <div className={cx('profile-wrapper')}>
          <div className={cx('profile-item')}>
            <Link className={cx('profile-item-inner')} to={ROUTES.PROFILE}>
              <div className={cx('profile-item-inner-icon-wrapper')}>
                <div className={cx('avatar-wrapper')}>
                  <img
                    {...getAvatarProps(userProfile?.avatarUrl, userProfile?.fullName)}
                    className={cx('user-avatar')}
                  />
                </div>
              </div>
              <div className={cx('link-text-wrapper')}>
                <span className={cx('link-text')}>Hồ sơ cá nhân</span>
              </div>
            </Link>
          </div>

          <div className={cx('profile-item', 'log-out-wrapper')}>
            <div className={cx('profile-item-inner')} onClick={() => setConfirmOpen(true)}>
              <div className={cx('profile-item-inner-icon-wrapper')}>
                <MdLogout className={cx('profile-item-inner-icon')} />
              </div>
              <div className={cx('link-text-wrapper')}>
                <span className={cx('link-text')}>Đăng xuất</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <ConfirmModal
        isOpen={confirmOpen}
        onClose={() => setConfirmOpen(false)}
        onConfirm={Logoutfunc}
        title="Xác nhận đăng xuất"
        message="Bạn có chắc chắn muốn đăng xuất khỏi hệ thống?"
        type="warning"
      />
    </>
  )
}

export default SidebarSystem
