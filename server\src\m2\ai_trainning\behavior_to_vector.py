# Đ<PERSON>c user_behavior_logs.json, sinh vector embedding và lưu ra file/vector store
# Yêu cầu: pip install pandas sentence-transformers

import pandas as pd
from sentence_transformers import SentenceTransformer
import numpy as np
import json

# 1. <PERSON><PERSON><PERSON> dữ liệu log
logs = pd.read_json('user_behavior_logs.json')

# 2. Tiền xử lý: tạo text mô tả cho từng hành vi
# (Bạn có thể tùy chỉnh template này cho phù hợp)
def log_to_text(row):
    return f"User {row['user_id']} {row['event_type']} product {row['product_id']} at {row['created_at']}"

logs['text'] = logs.apply(log_to_text, axis=1)

# 3. Load model embedding (có thể thay bằng model khác nếu muốn)
model = SentenceTransformer('all-MiniLM-L6-v2')

# 4. Sinh vector embedding cho từng log
embeddings = model.encode(logs['text'].tolist(), show_progress_bar=True)

# 5. L<PERSON>u vector + metadata ra file (hoặc lưu vào DB/FAISS ở bước sau)
output = []
for i, row in logs.iterrows():
    # Ensure product_id is not NaN (replace with None for JSON null)
    product_id = row['product_id']
    if pd.isna(product_id):
        product_id = None
    output.append({
        'user_id': row['user_id'],
        'event_type': row['event_type'],
        'product_id': product_id,
        'created_at': str(row['created_at']),
        'embedding': embeddings[i].tolist(),
    })

with open('user_behavior_vectors.json', 'w') as f:
    json.dump(output, f, indent=2)

print('Đã sinh vector và lưu ra user_behavior_vectors.json')
