import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { RoleGroup } from './role_groups.entity';
import { Permission } from './permissions.entity';

@Entity('role_group_permissions')
export class RoleGroupPermission {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => RoleGroup)
  @JoinColumn({ name: 'role_group_id' })
  roleGroup: RoleGroup;

  @ManyToOne(() => Permission)
  @JoinColumn({ name: 'permission_id' })
  permission: Permission;
}
