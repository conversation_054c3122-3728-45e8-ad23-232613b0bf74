@import '../../styles/variables.scss';

.container {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 8px;
  
  &.loaded {
    background-color: transparent;
  }
  
  &.error {
    background-color: #fafafa;
    border: 1px dashed #d9d9d9;
  }
}

// Aspect ratio variants
.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-16-9 {
  aspect-ratio: 16 / 9;
}

.aspect-4-3 {
  aspect-ratio: 4 / 3;
}

.aspect-3-2 {
  aspect-ratio: 3 / 2;
}

.aspect-auto {
  aspect-ratio: auto;
  min-height: 120px;
  
  @media (max-width: 768px) {
    min-height: 100px;
  }
  
  @media (max-width: 480px) {
    min-height: 80px;
  }
}

// Image styles
.image {
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
  
  &.fit-cover {
    object-fit: cover;
  }
  
  &.fit-contain {
    object-fit: contain;
  }
  
  &.fit-fill {
    object-fit: fill;
  }
  
  &.fit-scale-down {
    object-fit: scale-down;
  }
}

// Loading placeholder
.placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.skeleton {
  width: 60%;
  height: 60%;
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Error state
.errorState {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  color: #999;
}

.errorIcon {
  font-size: 24px;
  margin-bottom: $spacing-xs;
  opacity: 0.5;
  
  @media (max-width: 768px) {
    font-size: 20px;
  }
  
  @media (max-width: 480px) {
    font-size: 16px;
  }
}

.errorText {
  @include caption;
  text-align: center;
  opacity: 0.7;
  
  @media (max-width: 480px) {
    @include small-text;
  }
}

// Responsive optimizations
@media (max-width: 768px) {
  .container {
    border-radius: 6px;
  }
  
  .image {
    transition: none; // Disable transitions on mobile for better performance
  }
}

@media (max-width: 480px) {
  .container {
    border-radius: 4px;
  }
}

// Hover effects (desktop only)
@media (min-width: 769px) {
  .container:hover .image {
    transform: scale(1.02);
  }
  
  .container.loaded:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

// Performance optimizations
.image {
  // Enable hardware acceleration
  transform: translateZ(0);
  backface-visibility: hidden;
  
  // Optimize rendering
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

// High DPI displays
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .image {
    image-rendering: auto;
  }
}
