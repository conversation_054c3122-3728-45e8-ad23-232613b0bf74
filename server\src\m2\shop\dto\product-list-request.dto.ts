import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsInt, IsString } from 'class-validator';

export class ProductListRequestDto {
  @ApiProperty({ required: false, description: 'ID trạng thái sản phẩm (status_id)' })
  @IsOptional()
  @IsString()
  status_id?: string;
  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @IsInt()
  page?: number = 1;

  @ApiProperty({ required: false, default: 20 })
  @IsOptional()
  @IsInt()
  size?: number = 20;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  category_id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  user_id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  price_min?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  price_max?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  created_from?: string; // ISO date string

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  created_to?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  has_stock?: boolean;

  @ApiProperty({ required: false, default: 'created_at' })
  @IsOptional()
  @IsString()
  order_by?: string = 'created_at';

  @ApiProperty({ required: false, default: 'desc' })
  @IsOptional()
  @IsString()
  order_dir?: 'asc' | 'desc' = 'desc';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sort_by?: string; // Thêm trường sort_by để hỗ trợ sắp xếp theo nhiều trường khác nhau

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sort_order?: 'asc' | 'desc'; // Thêm trường sort_order để xác định thứ tự sắp xếp
}
