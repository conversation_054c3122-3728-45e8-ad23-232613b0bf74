import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductReview } from '../entities/product_review.entity';
import { Product } from '../entities/products.entity';
import { User } from '../../../m1/user/entities/user.entity';
import { CreateProductReviewDto, UpdateProductReviewDto } from '../dto/product-review.dto';

export class ProductToReviewDto {
  order_id: string;
  bought_at: Date;
  status_code: string;
  status_name: string;
  order_item_id: string;
  product_id: string;
  product_name: string;
  product_image: string;
  variant_id?: string;
  version_name?: string;
  quantity: number;
  unit_price: string;
  total_price: string;
}
import { LogService } from 'src/m_log/log/log.service';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { LogActionType } from 'src/constants';

@Injectable()
export class ProductReviewService {
  constructor(
    @InjectRepository(ProductReview)
    private readonly reviewRepo: Repository<ProductReview>,
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    private readonly logService: LogService,
    @Inject('LOG_SERVICE')
    private readonly logClient: ClientProxy,
  ) {}

  // Lấy danh sách sản phẩm đã hoàn thành cần đánh giá
  async getProductsToReview(user_id: string) {
    try {
      // Lấy status COMPLETED
      const completedStatus = await this.reviewRepo.manager
        .getRepository('statuses')
        .findOne({ where: { code: 'COMPLETED' } });
      if (!completedStatus) return [];
      // Lấy các order đã hoàn thành của user
      const orders = await this.reviewRepo.manager.getRepository('orders').find({
        where: { user_id, status_id: completedStatus.id },
        relations: ['orderItems', 'orderItems.product', 'orderItems.version', 'status'],
        order: { created_at: 'DESC' },
      });
      if (!orders.length) return [];
      // Lấy các order_item chưa review
      const result: ProductToReviewDto[] = [];
      for (const order of orders) {
        for (const item of order.orderItems) {
          // Kiểm tra đã review chưa

          const existed = await this.reviewRepo.findOne({
            where: {
              user_id,
              order_id: order.id,
              product_id: item.product_id,
            },
          });
          if (!existed) {
            result.push({
              order_id: order.id,
              bought_at: order.created_at,
              status_code: order.status.code,
              status_name: order.status.label,
              order_item_id: item.id,
              product_id: item.product_id,
              product_name: item.product?.name,
              product_image: item.product?.thumbnail_url,
              variant_id: item.variant_id,
              version_name: item.version?.name,
              quantity: item.quantity,
              unit_price: item.unit_price,
              total_price: item.total_price,
            });
          }
        }
      }
      return result;
    } catch (error) {
      console.error('Error fetching products to review:', error);
      throw new BadRequestException(
        'Lỗi lấy danh sách sản phẩm cần đánh giá: ' + (error?.message || error),
      );
    }
  }

  // Lấy danh sách đánh giá của user để chỉnh sửa
  async getMyReviews(user_id: string) {
    try {
      const reviews = await this.reviewRepo.find({
        where: { user_id },
        order: { created_at: 'DESC' },
        relations: ['product'],
        select: [
          'id',
          'product_id',
          'order_id',
          'rating',
          'content',
          'images',
          'created_at',
          'parent_id',
        ],
      });

      // Thêm thông tin sản phẩm vào response
      const reviewsWithProductInfo = reviews.map((review) => ({
        ...review,
        product_name: review.product?.name,
        product_image: review.product?.thumbnail_url,
      }));

      return reviewsWithProductInfo;
    } catch (error) {
      throw new BadRequestException('Lỗi lấy danh sách đánh giá: ' + (error?.message || error));
    }
  }

  // Tạo đánh giá mới hoặc rep lại đánh giá
  async createReview(user_id: string, dto: CreateProductReviewDto) {
    try {
      // Bắt buộc phải có order_id và product_id hợp lệ để xác định duy nhất sản phẩm trong đơn hàng
      let reviewData: any = { ...dto, user_id };
      if (
        !reviewData.order_id ||
        reviewData.order_id === 'string' ||
        !/^[0-9a-fA-F-]{36}$/.test(reviewData.order_id)
      ) {
        throw new BadRequestException('order_id không hợp lệ');
      }
      if (
        !reviewData.product_id ||
        reviewData.product_id === 'string' ||
        !/^[0-9a-fA-F-]{36}$/.test(reviewData.product_id)
      ) {
        throw new BadRequestException('product_id không hợp lệ');
      }
      // Kiểm tra đã review chưa (theo user_id, order_id, product_id)
      const existed = await this.reviewRepo.findOne({
        where: {
          user_id,
          order_id: reviewData.order_id,
          product_id: reviewData.product_id,
        },
      });
      if (existed) {
        throw new ConflictException('Bạn đã đánh giá sản phẩm này trong đơn hàng này rồi');
      }
      const review = this.reviewRepo.create(reviewData);
      const saved = await this.reviewRepo.save(review);
      const reviewObj = Array.isArray(saved) ? saved[0] : saved;
      this.logClient.emit('log.create', {
        module: 'product_review',
        action: LogActionType.CREATE,
        performedBy: user_id,
        targetId: reviewObj.id,
        description: `Tạo đánh giá sản phẩm`,
        timestamp: new Date().toISOString(),
        data: { review_id: reviewObj.id, ...reviewData },
      });
      return reviewObj;
    } catch (error) {
      console.error('Error creating product review:', error);
      throw new BadRequestException('Lỗi tạo đánh giá: ' + (error?.message || error));
    }
  }

  // Sửa đánh giá
  async updateReview(user_id: string, review_id: string, dto: UpdateProductReviewDto) {
    try {
      const review = await this.reviewRepo.findOne({ where: { id: review_id, user_id } });
      if (!review) throw new NotFoundException('Không tìm thấy đánh giá');
      Object.assign(review, dto);
      const saved = await this.reviewRepo.save(review);
      this.logClient.emit('log.update', {
        module: 'product_review',
        action: LogActionType.UPDATE,
        performedBy: user_id,
        targetId: review_id,
        description: `Chỉnh sửa đánh giá sản phẩm`,
        timestamp: new Date().toISOString(),
        data: { review_id, ...dto },
      });
      return saved;
    } catch (error) {
      throw new BadRequestException('Lỗi sửa đánh giá: ' + (error?.message || error));
    }
  }

  // Xóa đánh giá
  async deleteReview(user_id: string, review_id: string) {
    try {
      const review = await this.reviewRepo.findOne({ where: { id: review_id, user_id } });
      if (!review) throw new NotFoundException('Không tìm thấy đánh giá');
      await this.reviewRepo.delete(review_id);
      this.logClient.emit('log.delete', {
        module: 'product_review',
        action: LogActionType.DELETE,
        performedBy: user_id,
        targetId: review_id,
        description: `Xóa đánh giá sản phẩm`,
        timestamp: new Date().toISOString(),
        data: { review_id },
      });
      return { success: true };
    } catch (error) {
      throw new BadRequestException('Lỗi xóa đánh giá: ' + (error?.message || error));
    }
  }
}
