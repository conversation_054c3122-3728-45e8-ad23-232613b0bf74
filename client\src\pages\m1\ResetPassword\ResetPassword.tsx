import { useState, useEffect } from 'react'
import { Button, Form, FormGroup, Label, Input, FormFeedback } from 'reactstrap'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { MailPlus } from 'lucide-react'
import { useSendOtpResetPasswordHook } from '@/hooks/auth/useResetPassword'
import loginIllustration from '../../../assets/img/logo.png'
import { ROUTES } from '@/constants/routes'

interface Style {
  [key: string]: React.CSSProperties
}

const ResetPassword = () => {
  const [forgotError, setForgotError] = useState('')
  const [forgotEmail, setForgotEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [forgotSuccess, setForgotSuccess] = useState('')
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const sendOtp = useSendOtpResetPasswordHook()
  const navigate = useNavigate()

  useEffect(() => {
    window.scrollTo(0, 0)
    return () => {
      window.scrollTo(0, 0)
    }
  }, [])

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setForgotError('')
    setIsLoading(true)

    if (!validateEmail(forgotEmail)) {
      setForgotError('Email không hợp lệ')
      setIsLoading(false)
      return
    }

    try {
      const success = await sendOtp({ target: forgotEmail })
      if (success) {
        setForgotSuccess('Mã OTP đã được gửi đến email của bạn')
        setForgotEmail('')
        navigate(ROUTES.OPT_RESET_PASSWORD, {
          state: { email: forgotEmail },
        })
      }
    } catch (err: any) {
      setForgotError('Gửi OTP thất bại. Vui lòng thử lại.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <motion.div
      style={styles.container}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        style={styles.header}
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <img src={loginIllustration} alt="Logo TAP" style={styles.logo} />
      </motion.div>

      <div style={styles.formContainer}>
        <motion.h3
          style={styles.subTitle}
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4 }}
        >
          Đặt lại mật khẩu
        </motion.h3>

        <Form onSubmit={handleForgotPassword}>
          {forgotSuccess && <div style={styles.successMessage}>{forgotSuccess}</div>}
          <FormGroup>
            <Label style={styles.label}>Địa chỉ email</Label>
            <div style={styles.inputWrapper}>
              <MailPlus style={styles.inputIcon} />
              <Input
                id="forgotEmail"
                type="email"
                value={forgotEmail}
                onChange={(e) => setForgotEmail(e.target.value)}
                placeholder="<EMAIL>"
                invalid={!!forgotError}
                required
                style={
                  focusedField === 'forgotEmail'
                    ? { ...styles.input, ...styles.inputFocus }
                    : styles.input
                }
                onFocus={() => setFocusedField('forgotEmail')}
                onBlur={() => setFocusedField(null)}
              />
            </div>
            {forgotError && <FormFeedback>{forgotError}</FormFeedback>}
          </FormGroup>
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              color="primary"
              block
              type="submit"
              disabled={isLoading}
              style={
                isLoading
                  ? { ...styles.submitButton, ...styles.submitButtonDisabled }
                  : styles.submitButton
              }
              onMouseEnter={(e) =>
                !isLoading && Object.assign(e.currentTarget.style, styles.submitButtonHover)
              }
              onMouseLeave={(e) =>
                !isLoading && Object.assign(e.currentTarget.style, styles.submitButton)
              }
            >
              <MailPlus
                style={{
                  width: '1.25rem',
                  height: '1.25rem',
                  marginRight: '0.5rem',
                  display: 'inline-block',
                }}
              />
              {isLoading ? 'Đang gửi...' : 'Gửi OTP'}
            </Button>
          </motion.div>
          <div style={styles.registerContainer}>
            <div
              style={styles.link}
              onClick={() => navigate(ROUTES.LOGIN)}
              onMouseEnter={(e) => Object.assign(e.currentTarget.style, styles.linkHover)}
              onMouseLeave={(e) => Object.assign(e.currentTarget.style, styles.link)}
            >
              Quay lại đăng nhập
            </div>
          </div>
        </Form>
      </div>
    </motion.div>
  )
}

export default ResetPassword

const styles: Style = {
  container: {
    minHeight: '100vh',
    width: '100%',
    background: '#FFFFFF',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 0,
    margin: 0,
    overflow: 'hidden',
  },
  header: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: '0.625rem 0',
  },
  logo: {
    width: '9.375rem',
    height: '9.375rem',
  },
  formContainer: {
    backgroundColor: '#FFFFFF',
    padding: '2.5rem',
    borderRadius: '15px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
    width: '100%',
    maxWidth: '500px',
    margin: 0,
  },
  subTitle: {
    fontSize: '1.4rem',
    color: '#7A7A7A',
    textAlign: 'center',
    marginBottom: '1.5rem',
    userSelect: 'none',
  },
  label: {
    color: '#2E2E2E',
    fontSize: '1.1rem',
    marginBottom: '0.5rem',
  },
  inputWrapper: {
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    marginBottom: '1rem',
  },
  input: {
    border: '0.0625rem solid #E0E0E0',
    borderRadius: '0.5rem',
    color: '#2E2E2E',
    width: '100%',
    padding: '0.75rem 2.5rem 0.75rem 2.5rem',
    fontSize: '1.1rem',
    transition: 'all 0.2s ease',
  },
  inputFocus: {
    borderColor: '#34A853',
    boxShadow: '0 0 0 0.2rem rgba(52, 168, 83, 0.4)',
  },
  inputIcon: {
    position: 'absolute',
    left: '0.75rem',
    color: '#7A7A7A',
  },
  link: {
    color: '#2D9CDB',
    textDecoration: 'none',
    fontSize: '1.1rem',
  },
  linkHover: {
    color: '#34A853',
    textDecoration: 'underline',
  },
  submitButton: {
    backgroundColor: '#2D9CDB',
    borderColor: '#2D9CDB',
    color: '#FFFFFF',
    width: '100%',
    padding: '0.75rem',
    borderRadius: '0.5rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all 0.3s ease',
    fontSize: '1.1rem',
  },
  submitButtonHover: {
    backgroundColor: '#34A853',
    borderColor: '#34A853',
    transform: 'scale(1.05)',
  },
  submitButtonDisabled: {
    backgroundColor: '#B2DFDB',
    borderColor: '#B2DFDB',
    cursor: 'not-allowed',
  },
  registerContainer: {
    textAlign: 'center',
    marginTop: '1.5rem',
    fontSize: '1.1rem',
  },
  successMessage: {
    color: '#34A853',
    textAlign: 'center',
    marginBottom: '1.5rem',
    fontSize: '1.1rem',
  },
}
