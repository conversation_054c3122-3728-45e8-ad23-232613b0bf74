import { Controller, Post, UploadedFile, UseInterceptors, Req, Body } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiTags,
  ApiConsumes,
  ApiBody,
  ApiResponse,
} from '@nestjs/swagger';
import { ImageSearchService } from '../services/image-search.service';
import { ImageSearchRequestDto, ImageSearchResponseDto } from '../dto/image-search.dto';

@ApiTags('Image Search')
@ApiBearerAuth('access-token')
@Controller('image-search')
export class ImageSearchController {
  constructor(private readonly imageSearchService: ImageSearchService) {}

  @Post('search')
  @ApiOperation({ summary: 'Tìm kiếm sản phẩm bằng ảnh (image-to-product)' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: ImageSearchRequestDto })
  @ApiResponse({ status: 200, type: ImageSearchResponseDto })
  @UseInterceptors(FileInterceptor('file'))
  async searchByImage(
    @UploadedFile() file: Express.Multer.File,
    @Req() req: any,
  ): Promise<ImageSearchResponseDto> {
    const userId = req.user?.userId;
    const product = await this.imageSearchService.searchProductByImage(file, userId);
    return {
      id: product.id,
      name: product.name,
      price: product.price,
      thumbnail_url: product.thumbnail_url,
    };
  }
}
