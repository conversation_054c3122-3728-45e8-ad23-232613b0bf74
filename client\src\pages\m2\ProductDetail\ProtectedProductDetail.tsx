import React from 'react'
import { useSelector } from 'react-redux'
import { Navigate } from 'react-router-dom'
import ProductDetail from './index'
import type { RootState } from '@/libs/state/store'
import { ROUTES } from '@/constants/routes'

const ProtectedProductDetail: React.FC = () => {
  const authUser = useSelector((state: RootState) => state.auth)
  const isAuthenticated = !!authUser?.id

  if (!isAuthenticated) {
    return <Navigate to={ROUTES.LOGIN} replace />
  }

  return <ProductDetail />
}

export default ProtectedProductDetail 