import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Product } from './products.entity';
import { OrderItem } from './order_item.entity';

@Entity('product_versions')
export class ProductVersion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  product_id: string;

  @ManyToOne(() => Product, (product) => product.product_versions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column({ type: 'varchar', length: 150 })
  name: string; // Hiển thị: Size M – Màu Đỏ

  @Column({ type: 'decimal', precision: 12, scale: 2 })
  price: number;

  @Column({ type: 'int', default: 0 })
  stock: number;

  @Column({ type: 'jsonb', nullable: true })
  options?: Record<string, string>; // Phân loại: { size: 'M', color: 'Đỏ' }

  @Column({ type: 'varchar', length: 100, nullable: true })
  sku_code?: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relation to OrderItem
  @OneToMany(() => OrderItem, (orderItem) => orderItem.version)
  orderItems: OrderItem[];
}
