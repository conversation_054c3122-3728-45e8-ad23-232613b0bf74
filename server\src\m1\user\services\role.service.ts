import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Role } from '../entities/role.entity';
import { Repository, Like } from 'typeorm';
import { CreateRoleDto } from '../../auth/dto/create-role.dto';
import { Permission } from '../entities/permissions.entity';
import { RoleGroup } from '../entities/role_groups.entity';
import { RoleGroupPermission } from '../entities/role_group_permissions.entity';
import { RoleScope } from '../entities/role_scopes.entity';

@Injectable()
export class RoleService {
  constructor(
    @InjectRepository(Role) private readonly roleRepo: Repository<Role>,
    @InjectRepository(Permission) private readonly permRepo: Repository<Permission>,
    @InjectRepository(RoleGroup) private readonly roleGroupRepo: Repository<RoleGroup>,
    @InjectRepository(RoleGroupPermission)
    private readonly rgpRepo: Repository<RoleGroupPermission>,
    @InjectRepository(RoleScope) private readonly roleScopeRepo: Repository<RoleScope>,
    @InjectRepository(RoleGroupPermission)
    private readonly rolePermRepo: Repository<RoleGroupPermission>,
  ) {}

  /**
   * Bổ nhiệm nhóm vai trò cho user, kiểm tra hợp lệ
   */
  async assignRoleGroupToUser(userId: string, roleGroupCode: string) {
    // Tách tiền tố
    const [prefix] = roleGroupCode.split('.');
    // Kiểm tra role tồn tại
    const role = await this.roleRepo.findOneBy({ role_code: prefix });
    if (!role) throw new Error('Tiền tố nhóm vai trò không hợp lệ');
    // Kiểm tra roleGroup tồn tại và thuộc về role
    const roleGroup = await this.roleGroupRepo.findOneBy({ code: roleGroupCode });
    if (!roleGroup || !roleGroup.code.startsWith(prefix + '.')) {
      throw new Error('Nhóm vai trò không hợp lệ hoặc không thuộc vai trò này');
    }
    // Cập nhật user
    const userRepo = this.roleRepo.manager.getRepository('User');
    const user = await userRepo.findOneBy({ id: userId });
    if (!user) throw new Error('Không tìm thấy user');
    user.roleGroupCode = roleGroupCode;
    await userRepo.save(user);
    return { success: true };
  }

  /**
   * Lấy danh sách role group, lọc theo roleId nếu có
   */
  async getAllRoleGroups(roleId?: string) {
    if (roleId) {
      // Lọc trực tiếp bằng roleId (foreign key)
      return await this.roleGroupRepo.find({
        where: { roleId },
        order: { code: 'ASC' },
      });
    }
    return await this.roleGroupRepo.find({ order: { code: 'ASC' } });
  }

  async createRole(dto: CreateRoleDto) {
    const exists = await this.roleRepo.findOneBy({ role_code: dto.role_code });
    if (exists) throw new Error('Role đã tồn tại');
    return this.roleRepo.save(dto);
  }

  /**
   * Lấy tất cả role trừ R05, R08 (dùng cho endpoint cũ)
   */
  async getAllRolesExcept() {
    return this.roleRepo.find({
      where: [
        { role_code: 'R01' },
        { role_code: 'R02' },
        { role_code: 'R03' },
        { role_code: 'R04' },
        { role_code: 'R06' },
        { role_code: 'R07' },
        // Thêm các role khác nếu cần
      ],
      order: { role_code: 'ASC' },
    });
  }

  /**
   * Lấy tất cả role và quyền (dành cho admin quản lý)
   */
  async getAllRolesForAdmin() {
    const roles = await this.roleRepo.find({ order: { role_code: 'ASC' } });
    return roles.map((role) => role);
  }

  async getRoleById(id: string) {
    const role = await this.roleRepo.findOneBy({ id });
    if (!role) throw new NotFoundException('Không tìm thấy vai trò');
    return role;
  }

  async getAllPermissions() {
    return this.permRepo.find({ order: { code: 'ASC' } });
  }

  async getRolePermissions(roleGroupCode: string) {
    // Tìm roleGroup theo code
    const roleGroup = await this.roleGroupRepo.findOneBy({ code: roleGroupCode });
    if (!roleGroup) throw new NotFoundException('Không tìm thấy nhóm vai trò');
    // Lấy các permission của roleGroup
    const rgps = await this.rgpRepo.find({
      where: { roleGroup: { id: roleGroup.id } },
      relations: ['permission'],
    });
    return rgps.map((rgp) => rgp.permission);
  }

  async createDefaultPermissions() {
    // Tạo các quyền mặc định nếu chưa có
    const defaultPerms = [
      { code: 'PERM_USER_VIEW_SELF', name: 'Xem hồ sơ cá nhân' },
      { code: 'PERM_USER_UPDATE_SELF', name: 'Cập nhật hồ sơ cá nhân' },
      { code: 'PERM_SESSION_LIST', name: 'Xem phiên đăng nhập' },
    ];
    for (const perm of defaultPerms) {
      const exists = await this.permRepo.findOneBy({ code: perm.code });
      if (!exists) await this.permRepo.save(this.permRepo.create(perm));
    }
    return await this.permRepo.find({
      where: [
        { code: 'PERM_USER_VIEW_SELF' },
        { code: 'PERM_USER_UPDATE_SELF' },
        { code: 'PERM_SESSION_LIST' },
      ],
    });
  }

  async getRoleByIdOrCode(idOrCode: string) {
    let role = await this.roleRepo.findOneBy({ role_code: idOrCode });
    if (!role) role = await this.roleRepo.findOneBy({ role_code: idOrCode });
    return role;
  }

  async getRoleGroupByCode(code: string) {
    console.log('Tìm kiếm RoleGroup với code:', code);
    return this.roleGroupRepo.findOneBy({ code });
  }

  /**
   * Lấy danh sách phạm vi vai trò (roleScope)
   */
  async getAllRoleScopes() {
    return await this.roleScopeRepo.find();
  }

  /**
   * Lấy danh sách quyền của vai trò (rolePermission)
   */
  async getAllRolePermissions() {
    return await this.rolePermRepo.find();
  }

  /**
   * Bổ nhiệm vai trò cho user
   */
  async assignRoleToUser(userId: string, roleId: string) {
    // Tìm user và role, cập nhật role cho user
    const userRepo = this.roleRepo.manager.getRepository('User');
    const user = await userRepo.findOneBy({ id: userId });
    if (!user) throw new Error('Không tìm thấy user');
    const role = await this.roleRepo.findOneBy({ id: roleId });
    if (!role) throw new Error('Không tìm thấy role');
    user.role = role;
    await userRepo.save(user);
    return { success: true };
  }
}
