import { MigrationInterface, QueryRunner } from "typeorm";

export class Init1753331231817 implements MigrationInterface {
    name = 'Init1753331231817'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "system_logs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "userId" character varying, "action" character varying NOT NULL, "module" character varying, "data" json, "performedBy" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_56861c4b9d16aa90259f4ce0a2c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_affiliates" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "userId" uuid NOT NULL, "referrerId" uuid, "level_1" character varying, "level_2" character varying, "level_3" character varying, "level_4" character varying, "level_5" character varying, CONSTRAINT "PK_d6a410ca4076a54f18450678c54" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_locations" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "province_code" character varying, "district_code" character varying, "commune_code" character varying, "user_id" uuid, CONSTRAINT "PK_4afd5dae13173e88183db3cd210" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "role_groups" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "code" character varying NOT NULL, "name" character varying NOT NULL, "description" character varying, "roleId" uuid NOT NULL, "moduleScope" text, CONSTRAINT "UQ_45dcb645c53ad407244e60d639f" UNIQUE ("code"), CONSTRAINT "PK_d4acd163227ca8dfb8de61ee1af" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "roles" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "role_code" character varying(10) NOT NULL, "name" character varying(255) NOT NULL, "description" text, CONSTRAINT "UQ_3feee96f9a604421f24496988db" UNIQUE ("role_code"), CONSTRAINT "PK_c1433d71a4838793a49dcad46ab" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_profiles" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "userId" uuid NOT NULL, "fullName" character varying, "phoneNumber" character varying, "citizenId" character varying, "gender" character varying, "dateOfBirth" date, "avatarUrl" text, "avatarId" text, "address" text, "provinceCode" character varying(10), "communeCode" character varying(10), "bio" text, "facebookUrl" text, "zaloNumber" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_8481388d6325e752cd4d7e26c6d" UNIQUE ("userId"), CONSTRAINT "REL_8481388d6325e752cd4d7e26c6" UNIQUE ("userId"), CONSTRAINT "PK_1ec6662219f4605723f1e41b6cb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_qr" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "userId" uuid NOT NULL, "qrCode" text NOT NULL, "qrType" character varying NOT NULL DEFAULT 'profile', "description" character varying, "linkUrl" character varying, "isActive" boolean NOT NULL DEFAULT true, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_9a6b870296ec826e3548740f55e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_providers" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "provider" character varying NOT NULL, "providerId" character varying NOT NULL, "email" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "user_id" uuid, CONSTRAINT "PK_7c253db00c7cac2a44f1f5a5c58" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_2fa_settings" ("user_id" uuid NOT NULL, "is_otp_enabled" boolean NOT NULL DEFAULT false, "is_totp_enabled" boolean NOT NULL DEFAULT false, "totp_secret_key" text, "backup_codes" text array, "verified_at" TIMESTAMP, CONSTRAINT "PK_1cd42214eda91b0a75b1baef647" PRIMARY KEY ("user_id"))`);
        await queryRunner.query(`CREATE TABLE "auth_sessions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "ip_address" character varying, "user_agent" text, "device_fingerprint" character varying, "location" character varying, "is_recognized" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_641507381f32580e8479efc36cd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "product_categories" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying(100) NOT NULL, "slug" character varying(150) NOT NULL, "parent_id" uuid, "level" integer, "order_index" integer, "icon_url" character varying(255), "is_active" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_f314a8b42f88d87b2dcb7fc491a" UNIQUE ("slug"), CONSTRAINT "PK_7069dac60d88408eca56fdc9e0c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_a75bfadcd8291a0538ab7abfdc" ON "product_categories" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_f314a8b42f88d87b2dcb7fc491" ON "product_categories" ("slug") `);
        await queryRunner.query(`CREATE INDEX "IDX_5f151d414daab0290f65b517ed" ON "product_categories" ("parent_id") `);
        await queryRunner.query(`CREATE TABLE "ai_image_vectors" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "product_id" uuid NOT NULL, "image_id" character varying(100) NOT NULL, "vector" text, "model" character varying(50) NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_ef8bcce685e56b06a9924309cd5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "tags" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying(100) NOT NULL, "slug" character varying(100) NOT NULL, CONSTRAINT "UQ_b3aa10c29ea4e61a830362bd25a" UNIQUE ("slug"), CONSTRAINT "PK_e7dc17249a1148a1970748eda99" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_d90243459a697eadb8ad56e909" ON "tags" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_b3aa10c29ea4e61a830362bd25" ON "tags" ("slug") `);
        await queryRunner.query(`CREATE TABLE "product_tags" ("product_id" uuid NOT NULL, "tag_id" uuid NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_8ca809b37ff76596b63fe60ac41" PRIMARY KEY ("product_id", "tag_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_5b0c6fc53c574299ecc7f9ee22" ON "product_tags" ("product_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_f2cd3faf2e129a4c69c05a291e" ON "product_tags" ("tag_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_8ca809b37ff76596b63fe60ac4" ON "product_tags" ("product_id", "tag_id") `);
        await queryRunner.query(`CREATE TABLE "product_versions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "product_id" uuid NOT NULL, "name" character varying(150) NOT NULL, "price" numeric(12,2) NOT NULL, "stock" integer NOT NULL DEFAULT '0', "options" jsonb, "sku_code" character varying(100), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_dbd6ab6ae9343c6c6f2df5e76db" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "order_items" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "order_id" uuid NOT NULL, "product_id" uuid NOT NULL, "product_name" character varying(150) NOT NULL, "variant_id" uuid, "selected_options" jsonb, "quantity" integer NOT NULL, "unit_price" numeric(12,2) NOT NULL, "total_price" numeric(12,2) NOT NULL, "note" text, "is_active" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_005269d8574e6fac0493715c308" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "inventory" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "product_id" uuid NOT NULL, "version_id" uuid, "location_code" character varying(20), "stock_quantity" integer NOT NULL, "reserved_quantity" integer NOT NULL, "threshold_min" integer, "last_updated_by" uuid, "note" text, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_82aa5da437c5bbfb80703b08309" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "combo_items" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "combo_id" uuid NOT NULL, "product_id" uuid NOT NULL, "quantity" integer NOT NULL, "note" text, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_f633e0564e3422d489c5cebe2e6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."geo_discounts_discount_type_enum" AS ENUM('percent', 'fixed')`);
        await queryRunner.query(`CREATE TABLE "geo_discounts" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "product_id" uuid NOT NULL, "province_code" character varying(10), "district_code" character varying(10), "commune_code" character varying(10), "discount_type" "public"."geo_discounts_discount_type_enum" NOT NULL, "discount_value" numeric(12,2) NOT NULL, "start_date" TIMESTAMP NOT NULL, "end_date" TIMESTAMP NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "created_by" uuid, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_c93fa39770f707330406faac0c3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "product_reviews" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "product_id" uuid NOT NULL, "user_id" uuid NOT NULL, "order_id" uuid, "rating" integer NOT NULL, "content" text, "images" jsonb, "parent_id" uuid, "is_public" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_67c1501aea1b0633ec441b00bd5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "product_comments" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "product_id" uuid NOT NULL, "user_id" uuid NOT NULL, "content" text NOT NULL, "parent_id" uuid, "is_visible" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_89c843deed630c2b3d226d04a1b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "product_attributes" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "product_id" uuid NOT NULL, "name" character varying(100) NOT NULL, "value" character varying(255) NOT NULL, "unit" character varying(20), "is_searchable" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_4fa18fc5c893cb9894fc40ca921" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."products_source_enum" AS ENUM('manual', 'ai_generated')`);
        await queryRunner.query(`CREATE TABLE "products" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "name" character varying(100) NOT NULL, "slug" character varying(150) NOT NULL, "description" text, "category_id" uuid NOT NULL, "price" numeric(12,2) NOT NULL, "unit" character varying(20) NOT NULL, "stock" integer NOT NULL, "status_id" uuid NOT NULL, "is_public" boolean NOT NULL DEFAULT true, "seo_keywords" character varying(255), "thumbnail_url" character varying(255), "source" "public"."products_source_enum", "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_16f0ab8bcb20b14e406c9072bb9" UNIQUE ("user_id", "slug"), CONSTRAINT "PK_0806c755e0aca124e67c0cf6d7d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_176b502c5ebd6e72cafbd9d6f7" ON "products" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_4c9fb58de893725258746385e1" ON "products" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_464f927ae360106b783ed0b410" ON "products" ("slug") `);
        await queryRunner.query(`CREATE INDEX "IDX_9a5f6868c96e0069e699f33e12" ON "products" ("category_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_fadfdf0a24e9dc5b8d5b85f4ee" ON "products" ("status_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_16f0ab8bcb20b14e406c9072bb" ON "products" ("user_id", "slug") `);
        await queryRunner.query(`CREATE TABLE "product_image" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "product_id" uuid, "image_url" character varying(255) NOT NULL, "image_id" character varying(150) NOT NULL, "status_id" uuid, "is_thumbnail" boolean NOT NULL DEFAULT false, "display_order" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_99d98a80f57857d51b5f63c8240" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "statuses" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" character varying(50) NOT NULL, "label" character varying(100) NOT NULL, "code" character varying(50) NOT NULL, "description" text, "color" character varying(20), "icon" character varying(50), "is_final" boolean NOT NULL DEFAULT false, "order_index" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_2fd3770acdb67736f1a3e3d5399" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_eb8b346692d17faf642f86812b" ON "statuses" ("type", "label") `);
        await queryRunner.query(`CREATE TYPE "public"."payments_method_enum" AS ENUM('COD', 'BANK', 'MOMO', 'VNPAY')`);
        await queryRunner.query(`CREATE TYPE "public"."payments_status_enum" AS ENUM('PENDING', 'PAID', 'FAILED', 'CANCELLED')`);
        await queryRunner.query(`CREATE TABLE "payments" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "method" "public"."payments_method_enum" NOT NULL, "status" "public"."payments_status_enum" NOT NULL DEFAULT 'PENDING', "amount" numeric(12,2) NOT NULL, "transaction_id" character varying(255), "meta" jsonb, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_197ab7af18c93fbb0c9b28b4a59" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."orders_source_enum" AS ENUM('web', 'app', 'zalo', 'guest')`);
        await queryRunner.query(`CREATE TABLE "orders" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid, "guest_phone" character varying(20), "guest_email" character varying(150), "status_id" uuid NOT NULL, "source" "public"."orders_source_enum" NOT NULL, "total_price" numeric(12,2) NOT NULL, "shipping_name" character varying(100) NOT NULL, "shipping_phone" character varying(20) NOT NULL, "shipping_address" text NOT NULL, "shipping_latlng" text, "note" text, "payment_id" uuid, "ref_code" character varying(50), "is_guest" boolean NOT NULL, "suspected" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_710e2d4957aa5878dfe94e4ac2f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "users" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "uid_code" character varying NOT NULL, "phone" character varying, "email" character varying, "password" character varying, "refreshToken" character varying, "referral_by" character varying, "status" character varying NOT NULL DEFAULT 'active', "role_id" uuid NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "roleGroupCode" character varying, CONSTRAINT "UQ_6ff0296b6af23d7a937c7822384" UNIQUE ("uid_code"), CONSTRAINT "UQ_a000cca60bcf04454e727699490" UNIQUE ("phone"), CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."vouchers_type_enum" AS ENUM('percent', 'fixed', 'referral')`);
        await queryRunner.query(`CREATE TABLE "vouchers" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "code" character varying(50) NOT NULL, "type" "public"."vouchers_type_enum" NOT NULL, "value" numeric(10,2) NOT NULL, "min_order" numeric(12,2), "max_discount" numeric(12,2), "quantity" integer NOT NULL, "used_count" integer NOT NULL DEFAULT '0', "start_date" TIMESTAMP NOT NULL, "end_date" TIMESTAMP NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "created_by" uuid, "note" text, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_efc30b2b9169e05e0e1e19d6dd6" UNIQUE ("code"), CONSTRAINT "PK_ed1b7dd909a696560763acdbc04" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_efc30b2b9169e05e0e1e19d6dd" ON "vouchers" ("code") `);
        await queryRunner.query(`CREATE INDEX "IDX_f7485b501fc28a1894c46c44cc" ON "vouchers" ("type") `);
        await queryRunner.query(`CREATE INDEX "IDX_9f297aed56938e360af20100b4" ON "vouchers" ("start_date") `);
        await queryRunner.query(`CREATE INDEX "IDX_703399826badfbf3e7e0a79fc2" ON "vouchers" ("end_date") `);
        await queryRunner.query(`CREATE INDEX "IDX_a359255ef8c252592620199552" ON "vouchers" ("created_by") `);
        await queryRunner.query(`CREATE TABLE "user_vector_embedding" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "vector" double precision array NOT NULL, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_af7b27198e410ec5a4f233d5da6" UNIQUE ("user_id"), CONSTRAINT "PK_30f2f7cb7a1dd14753cd6d01a0e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."user_feedbacks_type_enum" AS ENUM('bug', 'suggestion', 'complaint', 'other')`);
        await queryRunner.query(`CREATE TABLE "user_feedbacks" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "title" character varying(200) NOT NULL, "content" text NOT NULL, "images" jsonb, "type" "public"."user_feedbacks_type_enum" NOT NULL, "status_id" character varying(50) NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_44c05e443443c7b8fd770f7b9b1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_687b71029451e0bb0b7f62dfb1" ON "user_feedbacks" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_dab14175bc34902d14510a4f88" ON "user_feedbacks" ("type") `);
        await queryRunner.query(`CREATE INDEX "IDX_786933de9ff616704f55c2e506" ON "user_feedbacks" ("status_id") `);
        await queryRunner.query(`CREATE TYPE "public"."user_behaviors_event_type_enum" AS ENUM('search', 'click', 'view', 'add_to_cart', 'update_cart_quantity', 'remove_cart_item', 'clear_cart', 'check_cart_inventory', 'create', 'update', 'request_cancel', 'confirm_received', 'ai_onboarding')`);
        await queryRunner.query(`CREATE TABLE "user_behaviors" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "event_type" "public"."user_behaviors_event_type_enum" NOT NULL, "product_id" uuid, "event_data" jsonb, "device_info" jsonb, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_c345f97744cae055a1777e02c4c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."report_flags_type_enum" AS ENUM('violation', 'feedback', 'ai')`);
        await queryRunner.query(`CREATE TABLE "report_flags" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "code" character varying(50) NOT NULL, "type" "public"."report_flags_type_enum" NOT NULL, "title" character varying(100) NOT NULL, "description" text, "is_active" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_87883c38ec673f433d6a93de6e2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "violation_reports" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "reporter_id" uuid NOT NULL, "target_type" character varying(50) NOT NULL, "target_id" uuid NOT NULL, "reason" text NOT NULL, "evidence" jsonb, "status_id" character varying(50) NOT NULL, "resolved_by" uuid, "resolution_note" text, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_d90dd15f5811f5ac24597b12101" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "product_vector_embedding" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "product_id" uuid NOT NULL, "vector" double precision array NOT NULL, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_059491e225f48e06b282023c2b7" UNIQUE ("product_id"), CONSTRAINT "PK_6bf47bc03721693709381a68104" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."product_qr_codes_type_enum" AS ENUM('default', 'referral', 'inventory', 'location')`);
        await queryRunner.query(`CREATE TABLE "product_qr_codes" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "product_id" uuid NOT NULL, "qr_url" character varying(255) NOT NULL, "target_url" character varying(255) NOT NULL, "type" "public"."product_qr_codes_type_enum" NOT NULL DEFAULT 'default', "scan_count" integer DEFAULT '0', "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_8385a25d3aff67ddd396b2e052e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "product_combos" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying(100) NOT NULL, "description" text, "discount_price" numeric(12,2) NOT NULL, "original_price" numeric(12,2), "start_date" TIMESTAMP NOT NULL, "end_date" TIMESTAMP NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "created_by" uuid, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_caa231a8ec235d62c086c22eced" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "order_status_logs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "order_id" uuid NOT NULL, "from_status" character varying(50), "to_status" character varying(50) NOT NULL, "changed_by" uuid, "role_code" character varying(20), "note" text, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_8e29911f9edfb67c6810f56ac5a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."order_payments_method_enum" AS ENUM('cash', 'tap_wallet', 'bank_transfer', 'qr_code')`);
        await queryRunner.query(`CREATE TYPE "public"."order_payments_status_enum" AS ENUM('pending', 'success', 'failed', 'refunded')`);
        await queryRunner.query(`CREATE TABLE "order_payments" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "order_id" uuid NOT NULL, "method" "public"."order_payments_method_enum" NOT NULL, "amount" numeric(12,2) NOT NULL, "status" "public"."order_payments_status_enum" NOT NULL, "transaction_code" character varying(100), "paid_at" TIMESTAMP, "confirmed_by" uuid, "note" text, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_bc14b014a69d39c7bbc4a154b69" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "flash_sales" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying(150) NOT NULL, "description" text, "start_time" TIMESTAMP NOT NULL, "end_time" TIMESTAMP NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "created_by" uuid NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_70299593044ffcba05cc30b97dc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "favorites" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "product_id" uuid NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_5ed74ffd0060ad5a01107ea552f" UNIQUE ("user_id", "product_id"), CONSTRAINT "PK_890818d27523748dd36a4d1bdc8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_003e599a9fc0e8f154b6313639" ON "favorites" ("product_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_35a6b05ee3b624d0de01ee5059" ON "favorites" ("user_id") `);
        await queryRunner.query(`CREATE TABLE "feedback_replies" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "feedback_id" uuid NOT NULL, "responder_id" uuid NOT NULL, "role_code" character varying(20), "content" text NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_5eef64ef617712845589e66c63e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."ai_training_data_data_type_enum" AS ENUM('text', 'image')`);
        await queryRunner.query(`CREATE TABLE "ai_training_data" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "data_type" "public"."ai_training_data_data_type_enum" NOT NULL, "source" character varying(100), "content" text NOT NULL, "label" character varying(100), "created_by" uuid, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_7457152719d8016698e4d141d20" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "cart_items" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "product_id" uuid NOT NULL, "quantity" integer NOT NULL, "unit_price" numeric(12,2) NOT NULL, "version_id" uuid, "selected_options" jsonb, "is_checked" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_6fccf5ec03c172d27a28a82928b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "ai_suggestions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "suggestion_type" character varying(50) NOT NULL, "target_id" uuid, "content" jsonb, "score" numeric(5,2), "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_5c769622a5d4b1e17e34983f75d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."ai_search_logs_query_type_enum" AS ENUM('text', 'image')`);
        await queryRunner.query(`CREATE TABLE "ai_search_logs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid, "query_type" "public"."ai_search_logs_query_type_enum" NOT NULL, "query_data" text NOT NULL, "result_count" integer NOT NULL, "latency_ms" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_b9977ee86d67cfdea6c8bca4485" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "permissions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "code" character varying NOT NULL, "name" character varying NOT NULL, CONSTRAINT "UQ_8dad765629e83229da6feda1c1d" UNIQUE ("code"), CONSTRAINT "PK_920331560282b8bd21bb02290df" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "role_group_permissions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "role_group_id" uuid, "permission_id" uuid, CONSTRAINT "PK_5d8865253364269d97039bc745c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "role_scopes" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "locationType" character varying NOT NULL, "locationCode" character varying NOT NULL, "role_group_id" uuid, CONSTRAINT "PK_0fb7eeea9da98155e16f2f8fdf1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "otp_verification" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "target" character varying NOT NULL, "otpCode" character varying NOT NULL, "expiredAt" TIMESTAMP NOT NULL, "status" character varying NOT NULL DEFAULT 'valid', "isVerified" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_090ea63a8ef4f33b1a5f29924f8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "user_affiliates" ADD CONSTRAINT "FK_991a61a7312a5ea81682ea05a3a" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_affiliates" ADD CONSTRAINT "FK_1b48b5a6b988a3f98535fef0e21" FOREIGN KEY ("referrerId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_locations" ADD CONSTRAINT "FK_437edca703095b237b5bdb35e22" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_groups" ADD CONSTRAINT "FK_8c30723ab3b9a95e7e0bbc1b925" FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_profiles" ADD CONSTRAINT "FK_8481388d6325e752cd4d7e26c6d" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_qr" ADD CONSTRAINT "FK_0522ed0b725c92444efc82c6e26" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_providers" ADD CONSTRAINT "FK_66144f0536826f644ce18baac3a" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_2fa_settings" ADD CONSTRAINT "FK_1cd42214eda91b0a75b1baef647" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "auth_sessions" ADD CONSTRAINT "FK_50ccaa6440288a06f0ba693ccc6" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_categories" ADD CONSTRAINT "FK_5f151d414daab0290f65b517ed4" FOREIGN KEY ("parent_id") REFERENCES "product_categories"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ai_image_vectors" ADD CONSTRAINT "FK_d7b7efa63b24f65cdd6135fde75" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_tags" ADD CONSTRAINT "FK_5b0c6fc53c574299ecc7f9ee22e" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_tags" ADD CONSTRAINT "FK_f2cd3faf2e129a4c69c05a291e8" FOREIGN KEY ("tag_id") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_versions" ADD CONSTRAINT "FK_f01d66474378b5aff362e6b0e89" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_items" ADD CONSTRAINT "FK_145532db85752b29c57d2b7b1f1" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_items" ADD CONSTRAINT "FK_9263386c35b6b242540f9493b00" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_items" ADD CONSTRAINT "FK_db2d0ea722e16e0fe8ab3bce111" FOREIGN KEY ("variant_id") REFERENCES "product_versions"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inventory" ADD CONSTRAINT "FK_732fdb1f76432d65d2c136340dc" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "inventory" ADD CONSTRAINT "FK_8a6bf0eacea3c44845f499ebf21" FOREIGN KEY ("version_id") REFERENCES "product_versions"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "combo_items" ADD CONSTRAINT "FK_4256f97167da7da758920d62870" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "geo_discounts" ADD CONSTRAINT "FK_cfc9c566ee512500e0a7df1dec5" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "geo_discounts" ADD CONSTRAINT "FK_b696c8a9594f700a734a4ad3093" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_reviews" ADD CONSTRAINT "FK_1d3fbb451c2b63d0a763f3ff5b1" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_reviews" ADD CONSTRAINT "FK_8306941b81cb5be7d521bdc0834" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_reviews" ADD CONSTRAINT "FK_73c546e34234fe4da7485579e2f" FOREIGN KEY ("parent_id") REFERENCES "product_reviews"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_comments" ADD CONSTRAINT "FK_310f5a0059db779ef661d04b31b" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_comments" ADD CONSTRAINT "FK_e31d9690c86f4e08410446bbf16" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_attributes" ADD CONSTRAINT "FK_f5a6700abd0494bae3032cf5bbd" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "products" ADD CONSTRAINT "FK_176b502c5ebd6e72cafbd9d6f70" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "products" ADD CONSTRAINT "FK_9a5f6868c96e0069e699f33e124" FOREIGN KEY ("category_id") REFERENCES "product_categories"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "products" ADD CONSTRAINT "FK_fadfdf0a24e9dc5b8d5b85f4ee8" FOREIGN KEY ("status_id") REFERENCES "statuses"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_image" ADD CONSTRAINT "FK_dbc7d9aa7ed42c9141b968a9ed3" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_image" ADD CONSTRAINT "FK_882126f0524e18388f4dd5b3f36" FOREIGN KEY ("status_id") REFERENCES "statuses"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_a922b820eeef29ac1c6800e826a" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_03a801095cb90cf148e474cfcb7" FOREIGN KEY ("status_id") REFERENCES "statuses"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_5b3e94bd2aedc184f9ad8c10439" FOREIGN KEY ("payment_id") REFERENCES "payments"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "users" ADD CONSTRAINT "FK_a2cecd1a3531c0b041e29ba46e1" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vouchers" ADD CONSTRAINT "FK_a359255ef8c2525926201995525" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_feedbacks" ADD CONSTRAINT "FK_687b71029451e0bb0b7f62dfb1d" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_behaviors" ADD CONSTRAINT "FK_9dc2815b94a502c999a5dcf98c7" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "violation_reports" ADD CONSTRAINT "FK_ee6efd2393662006c5759ed2672" FOREIGN KEY ("resolved_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_qr_codes" ADD CONSTRAINT "FK_89ff8059847ecf2db45ae8524b8" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_combos" ADD CONSTRAINT "FK_8166398f235dd8d452bd640e540" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_status_logs" ADD CONSTRAINT "FK_8c1f89df3c814ef51531320be75" FOREIGN KEY ("changed_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_payments" ADD CONSTRAINT "FK_f97f5777f4b79b5b7b3a0b2f759" FOREIGN KEY ("confirmed_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "flash_sales" ADD CONSTRAINT "FK_2517b1754835a33c236e8b1dc5c" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ai_training_data" ADD CONSTRAINT "FK_52ff686fb81634a668ba2f5a823" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cart_items" ADD CONSTRAINT "FK_b7213c20c1ecdc6597abc8f1212" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cart_items" ADD CONSTRAINT "FK_30e89257a105eab7648a35c7fce" FOREIGN KEY ("product_id") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cart_items" ADD CONSTRAINT "FK_e9961ed8dd96472ef42856f5758" FOREIGN KEY ("version_id") REFERENCES "product_versions"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ai_suggestions" ADD CONSTRAINT "FK_043d3a77d24ce1e0ebf9823433c" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "ai_search_logs" ADD CONSTRAINT "FK_aa87db9803078a20ee48326dabb" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_group_permissions" ADD CONSTRAINT "FK_4732b3a47d1bb93a7013ce7a499" FOREIGN KEY ("role_group_id") REFERENCES "role_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_group_permissions" ADD CONSTRAINT "FK_fd6c7f5019195b37c8d181ea51a" FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "role_scopes" ADD CONSTRAINT "FK_321f45b8fc75061907187c765d6" FOREIGN KEY ("role_group_id") REFERENCES "role_groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "role_scopes" DROP CONSTRAINT "FK_321f45b8fc75061907187c765d6"`);
        await queryRunner.query(`ALTER TABLE "role_group_permissions" DROP CONSTRAINT "FK_fd6c7f5019195b37c8d181ea51a"`);
        await queryRunner.query(`ALTER TABLE "role_group_permissions" DROP CONSTRAINT "FK_4732b3a47d1bb93a7013ce7a499"`);
        await queryRunner.query(`ALTER TABLE "ai_search_logs" DROP CONSTRAINT "FK_aa87db9803078a20ee48326dabb"`);
        await queryRunner.query(`ALTER TABLE "ai_suggestions" DROP CONSTRAINT "FK_043d3a77d24ce1e0ebf9823433c"`);
        await queryRunner.query(`ALTER TABLE "cart_items" DROP CONSTRAINT "FK_e9961ed8dd96472ef42856f5758"`);
        await queryRunner.query(`ALTER TABLE "cart_items" DROP CONSTRAINT "FK_30e89257a105eab7648a35c7fce"`);
        await queryRunner.query(`ALTER TABLE "cart_items" DROP CONSTRAINT "FK_b7213c20c1ecdc6597abc8f1212"`);
        await queryRunner.query(`ALTER TABLE "ai_training_data" DROP CONSTRAINT "FK_52ff686fb81634a668ba2f5a823"`);
        await queryRunner.query(`ALTER TABLE "flash_sales" DROP CONSTRAINT "FK_2517b1754835a33c236e8b1dc5c"`);
        await queryRunner.query(`ALTER TABLE "order_payments" DROP CONSTRAINT "FK_f97f5777f4b79b5b7b3a0b2f759"`);
        await queryRunner.query(`ALTER TABLE "order_status_logs" DROP CONSTRAINT "FK_8c1f89df3c814ef51531320be75"`);
        await queryRunner.query(`ALTER TABLE "product_combos" DROP CONSTRAINT "FK_8166398f235dd8d452bd640e540"`);
        await queryRunner.query(`ALTER TABLE "product_qr_codes" DROP CONSTRAINT "FK_89ff8059847ecf2db45ae8524b8"`);
        await queryRunner.query(`ALTER TABLE "violation_reports" DROP CONSTRAINT "FK_ee6efd2393662006c5759ed2672"`);
        await queryRunner.query(`ALTER TABLE "user_behaviors" DROP CONSTRAINT "FK_9dc2815b94a502c999a5dcf98c7"`);
        await queryRunner.query(`ALTER TABLE "user_feedbacks" DROP CONSTRAINT "FK_687b71029451e0bb0b7f62dfb1d"`);
        await queryRunner.query(`ALTER TABLE "vouchers" DROP CONSTRAINT "FK_a359255ef8c2525926201995525"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_a2cecd1a3531c0b041e29ba46e1"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_5b3e94bd2aedc184f9ad8c10439"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_03a801095cb90cf148e474cfcb7"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_a922b820eeef29ac1c6800e826a"`);
        await queryRunner.query(`ALTER TABLE "product_image" DROP CONSTRAINT "FK_882126f0524e18388f4dd5b3f36"`);
        await queryRunner.query(`ALTER TABLE "product_image" DROP CONSTRAINT "FK_dbc7d9aa7ed42c9141b968a9ed3"`);
        await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "FK_fadfdf0a24e9dc5b8d5b85f4ee8"`);
        await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "FK_9a5f6868c96e0069e699f33e124"`);
        await queryRunner.query(`ALTER TABLE "products" DROP CONSTRAINT "FK_176b502c5ebd6e72cafbd9d6f70"`);
        await queryRunner.query(`ALTER TABLE "product_attributes" DROP CONSTRAINT "FK_f5a6700abd0494bae3032cf5bbd"`);
        await queryRunner.query(`ALTER TABLE "product_comments" DROP CONSTRAINT "FK_e31d9690c86f4e08410446bbf16"`);
        await queryRunner.query(`ALTER TABLE "product_comments" DROP CONSTRAINT "FK_310f5a0059db779ef661d04b31b"`);
        await queryRunner.query(`ALTER TABLE "product_reviews" DROP CONSTRAINT "FK_73c546e34234fe4da7485579e2f"`);
        await queryRunner.query(`ALTER TABLE "product_reviews" DROP CONSTRAINT "FK_8306941b81cb5be7d521bdc0834"`);
        await queryRunner.query(`ALTER TABLE "product_reviews" DROP CONSTRAINT "FK_1d3fbb451c2b63d0a763f3ff5b1"`);
        await queryRunner.query(`ALTER TABLE "geo_discounts" DROP CONSTRAINT "FK_b696c8a9594f700a734a4ad3093"`);
        await queryRunner.query(`ALTER TABLE "geo_discounts" DROP CONSTRAINT "FK_cfc9c566ee512500e0a7df1dec5"`);
        await queryRunner.query(`ALTER TABLE "combo_items" DROP CONSTRAINT "FK_4256f97167da7da758920d62870"`);
        await queryRunner.query(`ALTER TABLE "inventory" DROP CONSTRAINT "FK_8a6bf0eacea3c44845f499ebf21"`);
        await queryRunner.query(`ALTER TABLE "inventory" DROP CONSTRAINT "FK_732fdb1f76432d65d2c136340dc"`);
        await queryRunner.query(`ALTER TABLE "order_items" DROP CONSTRAINT "FK_db2d0ea722e16e0fe8ab3bce111"`);
        await queryRunner.query(`ALTER TABLE "order_items" DROP CONSTRAINT "FK_9263386c35b6b242540f9493b00"`);
        await queryRunner.query(`ALTER TABLE "order_items" DROP CONSTRAINT "FK_145532db85752b29c57d2b7b1f1"`);
        await queryRunner.query(`ALTER TABLE "product_versions" DROP CONSTRAINT "FK_f01d66474378b5aff362e6b0e89"`);
        await queryRunner.query(`ALTER TABLE "product_tags" DROP CONSTRAINT "FK_f2cd3faf2e129a4c69c05a291e8"`);
        await queryRunner.query(`ALTER TABLE "product_tags" DROP CONSTRAINT "FK_5b0c6fc53c574299ecc7f9ee22e"`);
        await queryRunner.query(`ALTER TABLE "ai_image_vectors" DROP CONSTRAINT "FK_d7b7efa63b24f65cdd6135fde75"`);
        await queryRunner.query(`ALTER TABLE "product_categories" DROP CONSTRAINT "FK_5f151d414daab0290f65b517ed4"`);
        await queryRunner.query(`ALTER TABLE "auth_sessions" DROP CONSTRAINT "FK_50ccaa6440288a06f0ba693ccc6"`);
        await queryRunner.query(`ALTER TABLE "user_2fa_settings" DROP CONSTRAINT "FK_1cd42214eda91b0a75b1baef647"`);
        await queryRunner.query(`ALTER TABLE "user_providers" DROP CONSTRAINT "FK_66144f0536826f644ce18baac3a"`);
        await queryRunner.query(`ALTER TABLE "user_qr" DROP CONSTRAINT "FK_0522ed0b725c92444efc82c6e26"`);
        await queryRunner.query(`ALTER TABLE "user_profiles" DROP CONSTRAINT "FK_8481388d6325e752cd4d7e26c6d"`);
        await queryRunner.query(`ALTER TABLE "role_groups" DROP CONSTRAINT "FK_8c30723ab3b9a95e7e0bbc1b925"`);
        await queryRunner.query(`ALTER TABLE "user_locations" DROP CONSTRAINT "FK_437edca703095b237b5bdb35e22"`);
        await queryRunner.query(`ALTER TABLE "user_affiliates" DROP CONSTRAINT "FK_1b48b5a6b988a3f98535fef0e21"`);
        await queryRunner.query(`ALTER TABLE "user_affiliates" DROP CONSTRAINT "FK_991a61a7312a5ea81682ea05a3a"`);
        await queryRunner.query(`DROP TABLE "otp_verification"`);
        await queryRunner.query(`DROP TABLE "role_scopes"`);
        await queryRunner.query(`DROP TABLE "role_group_permissions"`);
        await queryRunner.query(`DROP TABLE "permissions"`);
        await queryRunner.query(`DROP TABLE "ai_search_logs"`);
        await queryRunner.query(`DROP TYPE "public"."ai_search_logs_query_type_enum"`);
        await queryRunner.query(`DROP TABLE "ai_suggestions"`);
        await queryRunner.query(`DROP TABLE "cart_items"`);
        await queryRunner.query(`DROP TABLE "ai_training_data"`);
        await queryRunner.query(`DROP TYPE "public"."ai_training_data_data_type_enum"`);
        await queryRunner.query(`DROP TABLE "feedback_replies"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_35a6b05ee3b624d0de01ee5059"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_003e599a9fc0e8f154b6313639"`);
        await queryRunner.query(`DROP TABLE "favorites"`);
        await queryRunner.query(`DROP TABLE "flash_sales"`);
        await queryRunner.query(`DROP TABLE "order_payments"`);
        await queryRunner.query(`DROP TYPE "public"."order_payments_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."order_payments_method_enum"`);
        await queryRunner.query(`DROP TABLE "order_status_logs"`);
        await queryRunner.query(`DROP TABLE "product_combos"`);
        await queryRunner.query(`DROP TABLE "product_qr_codes"`);
        await queryRunner.query(`DROP TYPE "public"."product_qr_codes_type_enum"`);
        await queryRunner.query(`DROP TABLE "product_vector_embedding"`);
        await queryRunner.query(`DROP TABLE "violation_reports"`);
        await queryRunner.query(`DROP TABLE "report_flags"`);
        await queryRunner.query(`DROP TYPE "public"."report_flags_type_enum"`);
        await queryRunner.query(`DROP TABLE "user_behaviors"`);
        await queryRunner.query(`DROP TYPE "public"."user_behaviors_event_type_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_786933de9ff616704f55c2e506"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_dab14175bc34902d14510a4f88"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_687b71029451e0bb0b7f62dfb1"`);
        await queryRunner.query(`DROP TABLE "user_feedbacks"`);
        await queryRunner.query(`DROP TYPE "public"."user_feedbacks_type_enum"`);
        await queryRunner.query(`DROP TABLE "user_vector_embedding"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a359255ef8c252592620199552"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_703399826badfbf3e7e0a79fc2"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9f297aed56938e360af20100b4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f7485b501fc28a1894c46c44cc"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_efc30b2b9169e05e0e1e19d6dd"`);
        await queryRunner.query(`DROP TABLE "vouchers"`);
        await queryRunner.query(`DROP TYPE "public"."vouchers_type_enum"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TABLE "orders"`);
        await queryRunner.query(`DROP TYPE "public"."orders_source_enum"`);
        await queryRunner.query(`DROP TABLE "payments"`);
        await queryRunner.query(`DROP TYPE "public"."payments_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."payments_method_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_eb8b346692d17faf642f86812b"`);
        await queryRunner.query(`DROP TABLE "statuses"`);
        await queryRunner.query(`DROP TABLE "product_image"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_16f0ab8bcb20b14e406c9072bb"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fadfdf0a24e9dc5b8d5b85f4ee"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9a5f6868c96e0069e699f33e12"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_464f927ae360106b783ed0b410"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4c9fb58de893725258746385e1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_176b502c5ebd6e72cafbd9d6f7"`);
        await queryRunner.query(`DROP TABLE "products"`);
        await queryRunner.query(`DROP TYPE "public"."products_source_enum"`);
        await queryRunner.query(`DROP TABLE "product_attributes"`);
        await queryRunner.query(`DROP TABLE "product_comments"`);
        await queryRunner.query(`DROP TABLE "product_reviews"`);
        await queryRunner.query(`DROP TABLE "geo_discounts"`);
        await queryRunner.query(`DROP TYPE "public"."geo_discounts_discount_type_enum"`);
        await queryRunner.query(`DROP TABLE "combo_items"`);
        await queryRunner.query(`DROP TABLE "inventory"`);
        await queryRunner.query(`DROP TABLE "order_items"`);
        await queryRunner.query(`DROP TABLE "product_versions"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8ca809b37ff76596b63fe60ac4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f2cd3faf2e129a4c69c05a291e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5b0c6fc53c574299ecc7f9ee22"`);
        await queryRunner.query(`DROP TABLE "product_tags"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b3aa10c29ea4e61a830362bd25"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d90243459a697eadb8ad56e909"`);
        await queryRunner.query(`DROP TABLE "tags"`);
        await queryRunner.query(`DROP TABLE "ai_image_vectors"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5f151d414daab0290f65b517ed"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f314a8b42f88d87b2dcb7fc491"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a75bfadcd8291a0538ab7abfdc"`);
        await queryRunner.query(`DROP TABLE "product_categories"`);
        await queryRunner.query(`DROP TABLE "auth_sessions"`);
        await queryRunner.query(`DROP TABLE "user_2fa_settings"`);
        await queryRunner.query(`DROP TABLE "user_providers"`);
        await queryRunner.query(`DROP TABLE "user_qr"`);
        await queryRunner.query(`DROP TABLE "user_profiles"`);
        await queryRunner.query(`DROP TABLE "roles"`);
        await queryRunner.query(`DROP TABLE "role_groups"`);
        await queryRunner.query(`DROP TABLE "user_locations"`);
        await queryRunner.query(`DROP TABLE "user_affiliates"`);
        await queryRunner.query(`DROP TABLE "system_logs"`);
    }

}
