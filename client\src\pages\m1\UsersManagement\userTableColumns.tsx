import React from 'react'
import { Ava<PERSON>, Tag, <PERSON>, <PERSON>con<PERSON>rm, Button } from 'antd'
import {
  EyeOutlined,
  EditOutlined,
  TeamOutlined,
  DeleteOutlined,
  UserOutlined,
} from '@ant-design/icons'
import styles from './UserManagement.module.scss'
import { getAvatarUrl } from '@/utils/avatar'

// Type definitions (copy from main file for type safety)
export interface User {
  id: string
  fullName?: string
  full_name?: string
  name?: string
  uidCode?: string
  uid_code?: string
  email?: string
  phone?: string
  phoneNumber?: string
  avatarUrl?: string
  role?: any
  role_id?: string
  status?: 'active' | 'locked' | 'pending'
  createdAt?: string
  updatedAt?: string
}

export function getUserColumns({
  page,
  limit,
  getFullName,
  getRoleName,
  getRoleColor,
  getStatusColor,
  getStatusText,
  handleView,
  handleEdit,
  setSelectedUserId,
  setIsAssignRoleGroupModalVisible,
  handleDelete,
}: {
  page: number
  limit: number
  getFullName: (user: User) => string
  getRoleName: (role: any) => string
  getRoleColor: (roleName: string) => string
  getStatusColor: (status: string | undefined) => string
  getStatusText: (status: string | undefined) => string
  handleView: (user: User) => void
  handleEdit: (user: User) => void
  setSelectedUserId: (id: string) => void
  setIsAssignRoleGroupModalVisible: (v: boolean) => void
  handleDelete: (id: string) => void
}) {
  return [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      render: (_: any, record: User, index: number) => (
        <span style={{ fontWeight: 'bold' }}>{(page - 1) * limit + index + 1}</span>
      ),
    },
    {
      title: 'Avatar',
      dataIndex: 'avatarUrl',
      key: 'avatar',
      width: 60,
      render: (avatarUrl: string) => (
        <Avatar
          size={40}
          src={getAvatarUrl(avatarUrl)}
          icon={<UserOutlined />}
          className={styles.userAvatar}
        />
      ),
    },
    {
      title: 'Thông tin người dùng',
      key: 'userInfo',
      render: (_: any, record: User) => (
        <div className={styles.userInfo}>
          <div className={styles.userName}>{getFullName(record)}</div>
          <div className={styles.userCode}>{record.uidCode || record.uid_code}</div>
          <div className={styles.userContact}>
            <span>{record.email}</span>
            <span>{record.phone}</span>
          </div>
        </div>
      ),
    },
    {
      title: 'Vai trò',
      dataIndex: 'role',
      key: 'role',
      render: (role: any) => {
        const roleName = getRoleName(role)
        let color = getRoleColor(roleName)
        return <Tag color={color}>{roleName}</Tag>
      },
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = getStatusColor(status)
        let text = getStatusText(status)
        return <Tag color={color}>{text}</Tag>
      },
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => {
        if (!date) return 'N/A'
        return new Date(date).toLocaleDateString('vi-VN')
      },
    },
    {
      title: 'Cập nhật cuối',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date: string) => {
        if (!date) return 'N/A'
        return new Date(date).toLocaleDateString('vi-VN')
      },
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      render: (_: any, record: User) => (
        <Space size="small">
          <Button
            color="primary"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            variant="outlined"
            size="small"
          >
            Xem
          </Button>
          <Button
            color="gold"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
            variant="outlined"
          >
            Sửa
          </Button>
          <Button
            icon={<TeamOutlined />}
            onClick={() => {
              setSelectedUserId(record.id)
              setIsAssignRoleGroupModalVisible(true)
            }}
            variant="outlined"
            color="green"
            size="small"
          >
            Bổ nhiệm
          </Button>
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa người dùng này?"
            description="Hành động này không thể hoàn tác!"
            onConfirm={() => handleDelete(record.id)}
            okText="Xóa"
            cancelText="Hủy"
            okType="danger"
          >
            <Button color="red" variant="outlined" icon={<DeleteOutlined />} size="small">
              Xóa
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]
}
