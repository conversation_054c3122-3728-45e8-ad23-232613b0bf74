# Script: Sinh vector embedding cho sản phẩm từ file JSON
# Yêu cầu: pip install pandas sentence-transformers
import pandas as pd
from sentence_transformers import SentenceTransformer
import json

# 1. <PERSON><PERSON><PERSON> <PERSON>ữ liệu sản phẩm
products = pd.read_json('product_info_for_vector.json')

def product_to_text(row):
    return f"Product {row['product_id']}: {row['name']} - {row['description']}"

products['text'] = products.apply(product_to_text, axis=1)

# 2. Load model embedding
model = SentenceTransformer('all-MiniLM-L6-v2')
embeddings = model.encode(products['text'].tolist(), show_progress_bar=True)

# 3. Lưu vector + metadata ra file
output = []
for i, row in products.iterrows():
    output.append({
        'product_id': row['product_id'],
        'embedding': embeddings[i].tolist(),
    })

with open('product_vectors.json', 'w') as f:
    json.dump(output, f, indent=2)

print('Đã sinh vector và lưu ra product_vectors.json')
