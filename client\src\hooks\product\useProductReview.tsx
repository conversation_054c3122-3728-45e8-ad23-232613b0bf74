import {
  useGetToReviewQuery,
  useGetMyReviewsQuery,
  useCreateReviewMutation,
  useUpdateReviewMutation,
  useDeleteReviewMutation,
  type ProductReview,
  type ProductReviewUpdate,
  type ProductReviewResponse,
} from '@/services/M2/ProductManagement/productReview';
import { useToast } from '@/components/Toast';
import { useLoadingStore } from '@/utils/loadingStore';
import { useEffect } from 'react';

// Hook GET: L<PERSON>y danh sách sản phẩm cần đánh giá
export const useGetToReviewHook = () => {
  const { data, error, isLoading, refetch, isFetching } = useGetToReviewQuery();
  const { setLoading } = useLoadingStore();
  useEffect(() => {
    setLoading(isLoading || isFetching);
  }, [isLoading, isFetching, setLoading]);
  return { data, error, isLoading, refetch };
};

// Hook GET: L<PERSON>y danh sách đánh giá của user
export const useGetMyReviewsHook = () => {
  const { data, error, isLoading, refetch, isFetching } = useGetMyReviewsQuery();
  const { setLoading } = useLoadingStore();
  useEffect(() => {
    setLoading(isLoading || isFetching);
  }, [isLoading, isFetching, setLoading]);
  return { data, error, isLoading, refetch };
};

// Hook POST: Tạo đánh giá hoặc trả lời đánh giá
export const useCreateReviewHook = () => {
  const { showSuccess, showError } = useToast();
  const [createFunc] = useCreateReviewMutation();
  const create = async (data: ProductReview): Promise<boolean> => {
    try {
      const res = await createFunc(data);
      if (!res?.error) {
        showSuccess('Tạo đánh giá thành công');
        return true;
      } else {
        const messageDes = (res.error as { data?: { message?: string } })?.data?.message || 'Unknown error';
        showError('Tạo đánh giá thất bại: ' + messageDes);
        return false;
      }
    } catch (e) {
      showError('Lỗi kết nối - Kiểm tra server backend');
      return false;
    }
  };
  return create;
};

// Hook PUT: Chỉnh sửa đánh giá
export const useUpdateReviewHook = () => {
  const { showSuccess, showError } = useToast();
  const [updateFunc] = useUpdateReviewMutation();
  const update = async (id: string, data: ProductReviewUpdate): Promise<boolean> => {
    try {
      const res = await updateFunc({ id, data });
      if (!res?.error) {
        showSuccess('Cập nhật đánh giá thành công');
        return true;
      } else {
        const messageDes = (res.error as { data?: { message?: string } })?.data?.message || 'Unknown error';
        showError('Cập nhật đánh giá thất bại: ' + messageDes);
        return false;
      }
    } catch (e) {
      showError('Lỗi kết nối - Kiểm tra server backend');
      return false;
    }
  };
  return update;
};

// Hook DELETE: Xóa đánh giá
export const useDeleteReviewHook = () => {
  const { showSuccess, showError } = useToast();
  const [deleteFunc] = useDeleteReviewMutation();
  const remove = async (id: string): Promise<boolean> => {
    try {
      const res = await deleteFunc(id);
      if (!res?.error) {
        showSuccess('Xóa đánh giá thành công');
        return true;
      } else {
        const messageDes = (res.error as { data?: { message?: string } })?.data?.message || 'Unknown error';
        showError('Xóa đánh giá thất bại: ' + messageDes);
        return false;
      }
    } catch (e) {
      showError('Lỗi kết nối - Kiểm tra server backend');
      return false;
    }
  };
  return remove;
};
