import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CartItemResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  user_id: string;

  @ApiProperty()
  product_id: string;

  @ApiPropertyOptional({ nullable: true })
  version_id?: string;

  @ApiProperty()
  quantity: number;

  @ApiProperty()
  unit_price: string;

  @ApiPropertyOptional({ type: 'object', additionalProperties: true, nullable: true })
  selected_options?: any;

  @ApiProperty()
  is_checked: boolean;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;
}
