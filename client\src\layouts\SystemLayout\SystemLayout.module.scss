.wrapper {
  display: flex;
  height: 100vh;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  padding-left: 7rem;
  transition: 0.3s;

  &.toggle-nav {
    padding-left: 28rem;
    transition: 0.3s;
  }

  .content {
    position: relative;
    flex: 1;
    background-color: #2c2c2c0a !important;
    transition: margin 0.2s linear;
    width: 100%;
    padding: 1rem 1rem 1rem 1rem;
    margin-top: 8rem;
  }
}

@media only screen and (max-width: 768px) {
  .content-wrapper {
    padding-left: 0;
  }
}
