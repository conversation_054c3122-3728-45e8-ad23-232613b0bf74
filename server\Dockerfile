# Stage 1: Build
FROM node:20-alpine AS build

WORKDIR /app
COPY . .

# Fix lỗi Multer types và native module
RUN rm -rf node_modules package-lock.json && \
    npm install --legacy-peer-deps && \
    npm run build

# Stage 2: Production runtime
FROM node:20-alpine

WORKDIR /app
COPY --from=build /app /app

# Cài đúng deps cho production
RUN npm install --omit=dev --legacy-peer-deps

# Thiết lập biến môi trường cho production
ENV NODE_ENV=production

# Chạy app NestJS production
CMD ["node", "dist/src/main.js"]
