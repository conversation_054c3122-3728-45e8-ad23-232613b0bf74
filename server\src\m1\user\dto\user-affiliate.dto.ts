import { ApiProperty } from '@nestjs/swagger';

export class AffiliateInfoDto {
  @ApiProperty()
  userId: number;

  @ApiProperty()
  referralId: number;

  @ApiProperty({ required: false })
  level_1?: number;

  @ApiProperty({ required: false })
  level_2?: number;

  @ApiProperty({ required: false })
  level_3?: number;

  @ApiProperty({ required: false })
  level_4?: number;

  @ApiProperty({ required: false })
  level_5?: number;
}

export class ReferralSummaryDto {
  @ApiProperty()
  totalF1: number;

  @ApiProperty()
  totalSystem: number;

  @ApiProperty()
  userId: string;
}
