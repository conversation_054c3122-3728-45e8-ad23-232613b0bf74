import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ProductCategoryResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  slug: string;

  @ApiPropertyOptional({ nullable: true })
  parentId?: string;

  @ApiPropertyOptional({ nullable: true })
  level?: number;

  @ApiPropertyOptional({ nullable: true })
  order_index?: number;

  @ApiPropertyOptional({ nullable: true })
  icon_url?: string;

  @ApiProperty({ default: true })
  is_active: boolean;

  @ApiProperty()
  created_at: Date;

  @ApiPropertyOptional({ type: () => [ProductCategoryResponseDto], nullable: true })
  children?: ProductCategoryResponseDto[];
}
