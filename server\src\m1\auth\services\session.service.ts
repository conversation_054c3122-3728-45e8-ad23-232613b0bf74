import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { MailService } from 'src/mail/mail.service';
import { AuthSession } from '../../user/entities/auth_sessions.entity';
import e, { Request } from 'express';
import { AuthSessionResponse } from '../dto/auth-session-response.dto';

@Injectable()
export class SessionService {
  constructor(
    @InjectRepository(AuthSession)
    private readonly authSessionRepo: Repository<AuthSession>,
    private readonly mailService: MailService,
  ) {}

  async checkUnrecognizedDevice(user: User, request: Request): Promise<void> {
    const userAgent = request.headers['user-agent'] || 'unknown';

    // Lấy IP từ x-forwarded-for hoặc socket address
    const forwarded = request.headers['x-forwarded-for'];
    const ip = Array.isArray(forwarded)
      ? forwarded[0]
      : typeof forwarded === 'string'
        ? forwarded.split(',')[0].trim()
        : request.socket?.remoteAddress || 'unknown';

    const previousSession = await this.authSessionRepo.findOne({
      where: {
        user_id: user.id,
        user_agent: userAgent,
        ip_address: ip,
      },
    });

    if (!previousSession) {
      await this.authSessionRepo.save({
        user_id: user.id,
        user_agent: userAgent,
        ip_address: ip,
        isTrusted: false,
      });

      if (user.email) {
        const fullName = user?.profile?.fullName || 'bạn';
        await this.mailService.waringLoginNewDevice(user.email, fullName, ip, userAgent);
      }
    }
  }

  /**
   * Lấy danh sách tất cả các phiên truy cập theo userId
   */
  async getSessions(userId: string): Promise<AuthSessionResponse[]> {
    const sessions = await this.authSessionRepo.find({
      where: { user_id: userId },
      order: { created_at: 'DESC' },
    });

    return sessions.map((s) => ({
      id: s.id,
      ip_address: s.ip_address,
      user_agent: s.user_agent,
      isTrusted: s.is_recognized,
      createdAt: s.created_at,
      location: s.location,
      fingerprint: s.device_fingerprint,
    }));
  }
}
