// Container chính
.container {
  // padding: 24px;
  // background-color: #f5f5f5;
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: 12px;
  }
}

// Header section
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
    padding: 16px;
  }
}

.headerTitle {
  h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    color: #262626;
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: 22px;
    }
  }

  p {
    margin: 4px 0 0;
    color: #8c8c8c;
    font-size: 14px;
  }
}

.headerActions {
  display: flex;
  gap: 12px;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: space-between;
    gap: 8px;
  }
}

.actionBtn {
  border-radius: 8px;
  height: 36px;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  color: #595959;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    background-color: #f8f9ff;
    transform: translateY(-1px);
  }

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  @media (max-width: 768px) {
    height: 32px;
    padding: 0 8px;
    font-size: 12px;
  }
}

.addButton {
  border-radius: 8px;
  height: 36px;
  background-color: #1890ff;
  border-color: #1890ff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transition: all 0.2s ease;

  &:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    transform: translateY(-1px);
  }

  &:focus {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  @media (max-width: 768px) {
    height: 32px;
    padding: 0 12px;
    font-size: 14px;
    flex: 1;
  }
}

// Mobile Search Container
.mobileSearchContainer {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;

  @media (min-width: 769px) {
    display: none;
  }
}

.mobileSearchInput {
  flex: 1;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  height: 36px;
  transition: all 0.2s ease;

  &:focus,
  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;
  }
}

.mobileFilterButton {
  border-radius: 8px;
  height: 36px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  color: #595959;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    background-color: #f8f9ff;
  }
}

// Stats
.statsRow {
  margin-bottom: 24px;

  @media (max-width: 768px) {
    display: none;
  }
}

.mobileStatsContainer {
  margin-bottom: 16px;

  @media (min-width: 769px) {
    display: none;
  }

  :global(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header) {
    padding: 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    margin-bottom: 8px;
  }

  :global(
    .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box
  ) {
    padding: 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
  }
}

.mobileStatCard {
  text-align: center;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.mobileStatValue {
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.mobileStatLabel {
  font-size: 12px;
  color: #8c8c8c;
}

.statCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  :global(.ant-card-body) {
    padding: 20px;
  }

  :global(.ant-statistic-title) {
    color: #8c8c8c;
    font-weight: 500;
    margin-bottom: 8px;
  }

  :global(.ant-statistic-content) {
    font-size: 24px;
    font-weight: 600;
  }
}

// Filters
.filterCard {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-body) {
    padding: 20px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }
}

.filterHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 0;
  }
}

.filterLeft {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filterRight {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filterRow {
  margin-top: 16px;
}

.searchInput {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  height: 36px;
  transition: all 0.2s ease;

  &:focus,
  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;
  }
}

.filterSelect {
  width: 100%;
  border-radius: 8px;

  :global(.ant-select-selector) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: 36px;
    transition: all 0.2s ease;

    &:focus,
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

.flatViewControls {
  margin: 16px 0;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

// Bulk Actions
.bulkCard {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #1890ff;
  background-color: #f8f9ff;

  :global(.ant-card-body) {
    padding: 16px 20px;

    @media (max-width: 768px) {
      padding: 12px 16px;
    }
  }
}

.bulkActions {
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  span {
    color: #1890ff;
    font-weight: 500;
    font-size: 14px;

    @media (max-width: 768px) {
      font-size: 12px;
    }
  }
}

// Content Card
.contentCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-body) {
    padding: 0;
  }
}

// Table
.categoryTable {
  :global(.ant-table) {
    border-radius: 12px;
    overflow: hidden;
  }

  :global(.ant-table-thead > tr > th) {
    background-color: #fafafa;
    border-bottom: 1px solid #f5f5f5;
    color: #262626;
    font-weight: 600;
    padding: 16px;

    &:first-child {
      border-top-left-radius: 12px;
    }

    &:last-child {
      border-top-right-radius: 12px;
    }

    @media (max-width: 768px) {
      padding: 12px 8px;
      font-size: 12px;
    }
  }

  :global(.ant-table-tbody > tr > td) {
    padding: 16px;
    border-bottom: 1px solid #f5f5f5;

    @media (max-width: 768px) {
      padding: 12px 8px;
    }
  }

  :global(.ant-table-tbody > tr:hover > td) {
    background-color: #f8f9ff;
  }

  :global(.ant-table-pagination) {
    padding: 16px 24px;
    border-top: 1px solid #f5f5f5;

    @media (max-width: 768px) {
      padding: 12px 16px;
    }
  }

  :global(.ant-table-row-expand-icon) {
    color: #1890ff;
    border: 1px solid #1890ff;
    background-color: #ffffff;

    &:hover {
      color: #40a9ff;
      border-color: #40a9ff;
    }
  }

  :global(.ant-table-expanded-row > td) {
    background-color: #fafafa;
  }
}

// Custom mask for image preview
:global(.custom-mask) {
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  font-size: 12px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

// Category Info
.categoryInfo {
  width: 100%;
}

.categoryHeader {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.categoryAvatar {
  border: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.categoryDetails {
  flex: 1;
  min-width: 0;
}

.categoryName {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;

  @media (max-width: 768px) {
    font-size: 13px;
  }
}

.categorySlug {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #1890ff;
  font-size: 12px;
  margin-bottom: 4px;

  @media (max-width: 768px) {
    font-size: 11px;
  }
}

.sortOrder {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #595959;
  font-size: 13px;
}

.dateInfo {
  color: #8c8c8c;
  font-size: 12px;
}

// Tree View
.categoryTree {
  :global(.ant-tree) {
    background: transparent;
  }

  :global(.ant-tree-node-content-wrapper) {
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f9ff;
    }
  }

  :global(.ant-tree-node-selected) {
    :global(.ant-tree-node-content-wrapper) {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
    }
  }

  :global(.ant-tree-switcher) {
    color: #1890ff;

    &:hover {
      background-color: #f8f9ff;
    }
  }
}

.treeNodeTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 4px 0;
}

.treeNodeAvatar {
  margin-right: 8px;
  border: 1px solid #f0f0f0;
}

.treeNodeName {
  flex: 1;
  font-weight: 500;
  color: #262626;
  margin-right: 12px;
}

.treeNodeActions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;

  .treeNodeTitle:hover & {
    opacity: 1;
  }
}

// Filter Drawer
.filterDrawer {
  :global(.ant-drawer-body) {
    padding: 16px;
  }
}

.filtersContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mobileFilterItem {
  display: flex;
  flex-direction: column;
  gap: 8px;

  label {
    font-weight: 500;
    color: #262626;
    font-size: 14px;
  }
}

.drawerActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;

  button {
    border-radius: 8px;
    height: 36px;
  }
}

// Modal
.categoryModal {
  :global(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :global(.ant-modal-header) {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 20px 24px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }

  :global(.ant-modal-title) {
    font-size: 18px;
    font-weight: 600;
    color: #262626;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }

  :global(.ant-modal-body) {
    padding: 24px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }

  @media (max-width: 768px) {
    :global(.ant-modal) {
      margin: 0;
      max-width: 100%;
      height: 100vh;
      border-radius: 0;
    }

    :global(.ant-modal-content) {
      height: 100vh;
      border-radius: 0;
      display: flex;
      flex-direction: column;
    }

    :global(.ant-modal-body) {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
    }
  }
}

.categoryForm {
  :global(.ant-form-item-label > label) {
    color: #262626;
    font-weight: 500;
  }

  :global(.ant-form-item) {
    margin-bottom: 16px;

    @media (max-width: 768px) {
      margin-bottom: 12px;
    }
  }

  :global(.ant-input),
  :global(.ant-select-selector),
  :global(.ant-input-number) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: 36px;
    transition: all 0.2s ease;

    &:focus,
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    @media (max-width: 768px) {
      height: 40px;
    }
  }

  :global(.ant-input-number) {
    width: 100%;
  }

  :global(.ant-tree-select-selector) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    min-height: 36px;
    transition: all 0.2s ease;

    &:focus,
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    @media (max-width: 768px) {
      min-height: 40px;
    }
  }
}

.modalActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;

  @media (max-width: 768px) {
    margin-top: 16px;
    padding-top: 16px;
    justify-content: space-between;
  }

  button {
    border-radius: 8px;
    height: 36px;
    padding: 0 16px;
    font-weight: 500;

    @media (max-width: 768px) {
      height: 40px;
      flex: 1;
    }
  }
}

// Mobile category card styles
.mobileCategoryCard {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  width: 100%;
  overflow: hidden;
  display: block !important;

  &:last-child {
    border-bottom: none;
  }

  // Override table cell behavior
  * {
    word-break: normal !important;
    white-space: normal !important;
    overflow-wrap: normal !important;
  }
}

.mobileCategoryHeader {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.categoryIconMobile {
  border-radius: 6px;
  object-fit: cover;
  flex-shrink: 0;
}

.mobileCategoryInfo {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.mobileCategoryName {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #333 !important;
  margin-bottom: 4px !important;
  line-height: 1.3 !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  white-space: normal !important;
  display: block !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  unicode-bidi: normal !important;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

.mobileCategorySlug {
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Courier New', monospace;
  margin-bottom: 6px;
}

.mobileCategoryLevel {
  margin-bottom: 8px;
}

.mobileActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 8px;
}

.mobileActionButton {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;

  &:hover {
    transform: scale(1.05);
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .container {
    padding: 8px;
  }

  .header {
    margin-bottom: 12px;
    padding: 12px;
  }

  .headerTitle h1 {
    font-size: 20px;
  }

  .mobileSearchContainer {
    margin-bottom: 12px;
  }

  .mobileStatsContainer {
    margin-bottom: 12px;
  }

  .mobileStatCard {
    padding: 8px;
  }

  .mobileStatValue {
    font-size: 16px;
  }

  .mobileStatLabel {
    font-size: 11px;
  }

  .bulkCard :global(.ant-card-body) {
    padding: 8px 12px;
  }

  .bulkActions span {
    font-size: 11px;
  }

  .categoryTable {
    :global(.ant-table-thead > tr > th),
    :global(.ant-table-tbody > tr > td) {
      padding: 8px 4px;
      font-size: 11px;
    }
  }

  .categoryModal {
    :global(.ant-modal-header) {
      padding: 12px;
    }

    :global(.ant-modal-body) {
      padding: 12px;
    }
  }

  .modalActions {
    margin-top: 12px;
    padding-top: 12px;
    gap: 8px;
  }
}

// Landscape orientation for mobile
@media (max-width: 768px) and (orientation: landscape) {
  .categoryModal {
    :global(.ant-modal) {
      height: 100vh;
    }

    :global(.ant-modal-content) {
      height: 100vh;
      max-height: 100vh;
    }

    :global(.ant-modal-body) {
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
  }
}
