import { Controller, Post, Body, Req, UseGuards, Get, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthService } from '../services/auth.service';
import { RegisterDto } from '../dto/register.dto';
import { LoginDto } from '../dto/login.dto';
import { AuthResponse, Login2FARequire } from '../dto/auth-response.dto';
import { VerifyOtpDto } from '../dto/verify-otp.dto';
import { RefreshTokenDto } from '../dto/refresh-token.dto';
import { SendOtpDto } from '../dto/send-otp.dto';
import { AuthMeRequest } from '../dto/auth-me-request.dto';
import { ResetPasswordDto } from '../dto/reset-password.dto';
import { SocialLoginDto } from '../dto/social-login.dto';
import { TotpVerifyDto } from '../dto/totp-verify.dto';
import { VerifyLogin2FADto } from '../dto/verify-login-2fa.dto';
import { AuthSessionResponse } from '../dto/auth-session-response.dto';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import { AllowAnonymous } from 'src/common/decorators/allow-anonymous.decorator';
import { TwofaService } from '../services/twofa.service';
import { OtpService } from '../services/otp.service';
import { SessionService } from '../services/session.service';
import { TokenService } from '../services/token.service';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly twoFAService: TwofaService,
    private readonly otpService: OtpService,
    private readonly sessionService: SessionService,
    private readonly tokenService: TokenService,
  ) {}

  /**
   * 🔐 Đăng nhập tài khoản
   * @param dto Email và mật khẩu người dùng
   * @returns Access token + Refresh token nếu hợp lệ
   */
  @Post('login')
  @AllowAnonymous()
  @ApiOperation({ summary: 'Đăng nhập tài khoản' })
  @ApiResponse({ status: 200, type: AuthResponse })
  async login(@Body() dto: LoginDto): Promise<AuthResponse | Login2FARequire> {
    return this.authService.login(dto);
  }

  @AllowAnonymous()
  @Post('verify-2fa')
  @ApiOperation({ summary: 'Xác minh mã OTP hoặc TOTP khi đăng nhập (2FA)' })
  @ApiResponse({ status: 200, type: AuthResponse })
  async verify2FA(@Body() dto: VerifyLogin2FADto, @Req() req: Request): Promise<AuthResponse> {
    return this.twoFAService.verifyLogin2FA(dto, req);
  }

  @AllowAnonymous()
  @Post('login/social')
  @ApiOperation({ summary: 'Đăng nhập bằng Google/Facebook' })
  async loginWithProvider(@Body() dto: SocialLoginDto): Promise<AuthResponse> {
    return this.authService.loginWithProvider(dto);
  }

  // @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Lấy thông tin người dùng qua token' })
  @AllowAnonymous()
  @Post('me')
  async getProfile(@Body() body: AuthMeRequest) {
    return this.tokenService.getProfileFromToken(body.token);
  }

  /**
   * 🔁 Làm mới accessToken bằng refreshToken
   * @param dto Refresh token hợp lệ
   * @returns Cặp accessToken mới + refreshToken mới
   */
  @AllowAnonymous()
  @Post('refresh-token')
  @ApiOperation({ summary: 'Làm mới accessToken' })
  @ApiResponse({ status: 200, type: AuthResponse })
  async refresh(@Body() dto: RefreshTokenDto): Promise<AuthResponse> {
    return this.tokenService.refreshToken(dto.refreshToken);
  }

  /**
   * 📩 Gửi mã OTP xác minh email (trước khi đăng ký)
   * @param dto Địa chỉ email cần xác minh
   * @returns 200 nếu gửi thành công
   */
  @AllowAnonymous()
  @Post('send-otp')
  @ApiOperation({ summary: 'Gửi mã OTP xác minh' })
  async sendOtp(@Body() dto: SendOtpDto): Promise<{ message: string }> {
    await this.otpService.sendOtp(dto.target);
    return { message: 'Đã gửi mã xác minh' };
  }

  /**
   * ✅ Xác minh mã OTP đã gửi tới email
   * @param dto Email + mã OTP người dùng nhập
   * @returns Thông báo xác minh thành công hoặc lỗi
   */
  @AllowAnonymous()
  @Post('verify-otp')
  @ApiOperation({ summary: 'Xác minh mã OTP' })
  async verifyOtp(@Body() dto: VerifyOtpDto): Promise<{ message: string }> {
    const msg = await this.otpService.verifyOtp(dto.target, dto.otp);
    return { message: msg };
  }

  /**
   * 📝 Đăng ký tài khoản mới (sau khi đã xác minh OTP)
   * @param dto Thông tin đăng ký
   * @returns Access token + Refresh token
   */
  @AllowAnonymous()
  @Post('register')
  @ApiOperation({ summary: 'Đăng ký tài khoản mới' })
  @ApiResponse({ status: 201, type: AuthResponse })
  async register(@Body() dto: RegisterDto): Promise<AuthResponse> {
    return this.authService.register(dto);
  }

  /**
   * 📤 Gửi mã OTP đến email để khôi phục mật khẩu
   */
  @AllowAnonymous()
  @Post('forgot-password/send-otp')
  @ApiOperation({ summary: 'Gửi OTP khôi phục mật khẩu' })
  async sendResetOtp(@Body() dto: SendOtpDto) {
    return this.otpService.sendResetPasswordOtp(dto.target);
  }

  /**
   * ✅ Xác minh mã OTP khôi phục mật khẩu
   */
  @AllowAnonymous()
  @Post('forgot-password/verify-otp')
  @ApiOperation({ summary: 'Xác thực OTP khôi phục mật khẩu' })
  async verifyResetOtp(@Body() dto: VerifyOtpDto) {
    const result = await this.otpService.verifyResetOtp(dto.target, dto.otp);
    if (!result) {
      throw new BadRequestException('Mã xác minh không đúng hoặc đã hết hạn');
    }
    return { message: 'Mã xác minh hợp lệ' };
  }

  /**
   * 🔒 Đặt lại mật khẩu mới (sau khi xác minh OTP thành công)
   */
  @AllowAnonymous()
  @Post('forgot-password/reset')
  @ApiOperation({ summary: 'Đổi mật khẩu' })
  async resetPassword(@Body() dto: ResetPasswordDto) {
    return await this.otpService.resetPassword(dto.email, dto.newPassword);
  }
  /**
   * Gửi mã OTP qua email hoặc SĐT
   */
  @AllowAnonymous()
  @Post('2fa/otp/request')
  @ApiOperation({ summary: 'Gửi mã OTP qua email/sđt' })
  async requestOtp(@Body() dto: SendOtpDto) {
    return this.twoFAService.sendOtp2FA(dto);
  }

  /**
   * Xác minh mã OTP và bật 2FA kiểu OTP
   */
  @AllowAnonymous()
  @Post('2fa/otp/verify')
  @ApiOperation({ summary: 'Xác minh mã OTP và kích hoạt 2FA OTP' })
  async verifyOtp2fa(@Body() dto: VerifyOtpDto) {
    return this.twoFAService.verifyOtp2FA(dto);
  }

  /**
   * Tạo mã bí mật TOTP và trả về mã QR để quét bằng Google Authenticator
   */
  @AllowAnonymous()
  @Post('2fa/totp/setup')
  @ApiOperation({ summary: 'Tạo mã QR và secret key TOTP' })
  async setupTotp(@Body('userId') userId: string) {
    return this.twoFAService.setupTotp(userId);
  }

  /**
   * Xác minh mã 6 số từ app TOTP để bật 2FA kiểu TOTP
   */
  @AllowAnonymous()
  @Post('2fa/totp/verify')
  @ApiOperation({ summary: 'Xác minh mã TOTP từ Google Authenticator' })
  async verifyTotp(@Body() dto: TotpVerifyDto) {
    return this.twoFAService.verifyTotp(dto);
  }

  @Get('sessions')
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({ summary: 'Xem danh sách phiên truy cập của người dùng hiện tại' })
  async getSessions(@Req() req: Request & { user?: any }): Promise<AuthSessionResponse[]> {
    const userId = req.user?.userId;
    return await this.sessionService.getSessions(userId);
  }

  @Post('2fa/otp/confirm-disable')
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({ summary: 'Xác minh mã OTP để tắt 2FA OTP' })
  async confirmDisableOtp(@Body() dto: VerifyOtpDto) {
    return this.twoFAService.confirmDisableOtp(dto);
  }

  /**
   * Tắt một trong hai phương thức 2FA: OTP hoặc TOTP
   */
  @Post('2fa/otp/disable-method')
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @ApiOperation({ summary: 'Tắt phương thức 2FA cụ thể (otp hoặc totp)' })
  async disableMethod(@Req() req: Request & { user?: any }) {
    const userId = req.user?.userId;
    return this.twoFAService.sendOtpToDisable2FA(userId);
  }
}
