# AI User Behavior Vector Pipeline - T<PERSON><PERSON> hợp các bước đã triển khai

## 1. Ghi log hành vi người dùng
- Tích hợp ghi log hành vi (user behavior) tại các controller (product, cart, order, ...).
- <PERSON><PERSON> dụng RabbitMQ để emit log không ảnh hưởng luồng chính.
- <PERSON><PERSON> gồm các trường: user_id, event_type, product_id, event_data, ...

## 2. Export log ra file JSON phục vụ AI
- Tạo script TypeScript (`pipeline.behavior-to-vector.ts`) để export log từ DB ra file `user_behavior_logs.json`.
- <PERSON><PERSON>m bảo log đầy đủ các trường cần thiết cho AI training.

## 3. Sinh vector embedding từ log
- Viết script Python (`behavior_to_vector.py`) dùng sentence-transformers để sinh embedding cho từng log.
- <PERSON><PERSON><PERSON> kết quả ra file `user_behavior_vectors.json`.

## 4. <PERSON><PERSON>u vector vào PostgreSQL
- <PERSON><PERSON><PERSON> bảng `user_vector_embedding` với các trường: id, user_id (unique), vector (float8[]), updated_at.
- Viết script TypeScript (`pipeline.insert-user-embeddings.ts`) để insert/update vector vào bảng này, gom vector theo user_id và tính trung bình nếu có nhiều vector.

## 5. Xây dựng API gợi ý user tương tự
- Tạo entity `UserVectorEmbedding` cho bảng vector.
- Tạo service `M2BotService` với các hàm:
  - getUserVector: lấy vector của user.
  - suggestSimilarUsers: tính cosine similarity, trả về danh sách user tương tự.
- Tạo controller `M2BotController` với endpoint:
  - `GET /m2-bot/suggest-similar-users/:user_id?topN=5`

## 6. Hướng sử dụng và ứng dụng
- API gợi ý user tương tự dùng cho các trang: profile, admin, cộng đồng, phân tích hành vi...
- Nếu muốn gợi ý sản phẩm, cần xây dựng pipeline và API tương tự cho vector sản phẩm.

## 7. Nhận xét về yếu tố ML
- Hệ thống hiện tại sử dụng vector embedding (AI infrastructure) và vector search (cosine similarity).
- Để thực sự ML, cần train/fine-tune model embedding hoặc recommendation riêng cho dữ liệu của bạn.

---
**Tài liệu này tổng hợp toàn bộ pipeline AI user behavior vector đã triển khai trong dự án.**
