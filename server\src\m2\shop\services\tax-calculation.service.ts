import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TaxCalculation, TaxType, UserRole } from '../entities/tax-calculation.entity';
import { User } from '../../../m1/user/entities/user.entity';
import { Order } from '../entities/order.entity';

interface TaxPolicyConfig {
  [key: string]: {
    taxTypes: TaxType[];
    rates: { [key in TaxType]?: number };
    thresholds: { [key: string]: number };
    notes: string;
    htxEligible: boolean;
  };
}

@Injectable()
export class TaxCalculationService {
  constructor(
    @InjectRepository(TaxCalculation)
    private readonly taxRepo: Repository<TaxCalculation>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    @InjectRepository(Order)
    private readonly orderRepo: Repository<Order>,
  ) {}

  // C<PERSON><PERSON> hình ch<PERSON>h sách thuế theo vai trò từ markdown
  private readonly TAX_POLICY: TaxPolicyConfig = {
    [UserRole.R01]: {
      // Thành viên HTX – sản xuất nông nghiệp
      taxTypes: [TaxType.COOPERATIVE],
      rates: { [TaxType.COOPERATIVE]: 0.15 }, // HTX nộp 15% TNDN
      thresholds: { annual_revenue: 0 }, // Không có ngưỡng
      notes: 'HTX đại diện kê khai thuế TNDN/GTGT, chia lại lợi nhuận sau thuế',
      htxEligible: true,
    },
    [UserRole.R02]: {
      // Người bán hàng TMĐT (hộ KD, DN nhỏ)
      taxTypes: [TaxType.PERSONAL_INCOME, TaxType.VAT],
      rates: {
        [TaxType.PERSONAL_INCOME]: 0.1,
        [TaxType.VAT]: 0.1,
      },
      thresholds: {
        vat_annual: *********, // 200M VND/năm
        personal_income_annual: *********, // 100M VND/năm
      },
      notes: 'TNCN / GTGT (nếu vượt ngưỡng). Có thể hợp tác ủy quyền HTX',
      htxEligible: true,
    },
    [UserRole.R03]: {
      // Người tiêu dùng / khách mua hàng
      taxTypes: [],
      rates: {},
      thresholds: {},
      notes: 'Không chịu thuế - chỉ phát sinh giao dịch thanh toán đơn thuần',
      htxEligible: false,
    },
    [UserRole.R04]: {
      // Chuyên gia – Bác sĩ – Giảng viên
      taxTypes: [TaxType.PERSONAL_INCOME, TaxType.VAT],
      rates: {
        [TaxType.PERSONAL_INCOME]: 0.1,
        [TaxType.VAT]: 0.1,
      },
      thresholds: {
        vat_annual: *********,
        personal_income_annual: *********,
      },
      notes: 'TNCN / GTGT nếu tự doanh. Có thể cộng tác với HTX dưới dạng hợp đồng dịch vụ',
      htxEligible: true,
    },
    [UserRole.R05]: {
      // Quản lý nội bộ, admin hệ thống
      taxTypes: [],
      rates: {},
      thresholds: {},
      notes: 'Không chịu thuế trực tiếp - thu nhập qua HTX nếu có',
      htxEligible: false,
    },
    [UserRole.R06]: {
      // Tài xế, người giao hàng
      taxTypes: [TaxType.PERSONAL_INCOME],
      rates: { [TaxType.PERSONAL_INCOME]: 0.05 },
      thresholds: { personal_income_annual: ********* },
      notes: 'TNCN (nếu thu nhập cao). Nên ký hợp đồng giao khoán với HTX',
      htxEligible: true,
    },
    [UserRole.R07]: {
      // Thành viên góp vốn (tổ chức/cá nhân)
      taxTypes: [TaxType.INVESTMENT_INCOME],
      rates: { [TaxType.INVESTMENT_INCOME]: 0.05 },
      thresholds: {},
      notes: 'Thuế thu nhập từ đầu tư vốn - HTX khấu trừ trước khi chia cổ tức',
      htxEligible: false,
    },
    [UserRole.R08]: {
      // Ban điều hành HTX
      taxTypes: [TaxType.PERSONAL_INCOME],
      rates: { [TaxType.PERSONAL_INCOME]: 0.1 },
      thresholds: { personal_income_annual: ********* },
      notes: 'TNCN (theo lương, phụ cấp) - HTX khai và nộp thay',
      htxEligible: false,
    },
  };

  /**
   * Tính thuế cho một user trong kỳ nhất định
   */
  async calculateTaxForUser(
    userId: string,
    period: string, // Format: YYYY-MM or YYYY-QQ
    revenue: number,
    userRole?: UserRole,
    regionCode?: string,
  ): Promise<TaxCalculation[]> {
    // Lấy thông tin user nếu không có userRole
    let role = userRole;
    if (!role) {
      const user = await this.userRepo.findOne({
        where: { id: userId },
        // TODO: Add relation to get user role from profile or roles table
      });
      role = UserRole.R02; // Default fallback
    }

    const policy = this.TAX_POLICY[role];
    if (!policy || policy.taxTypes.length === 0) {
      return []; // No tax liability
    }

    const calculations: TaxCalculation[] = [];

    for (const taxType of policy.taxTypes) {
      const taxRate = policy.rates[taxType] || 0;
      let taxableRevenue = revenue;
      let shouldCalculate = true;

      // Check thresholds
      switch (taxType) {
        case TaxType.VAT:
          const annualRevenue = this.estimateAnnualRevenue(revenue, period);
          shouldCalculate = annualRevenue >= (policy.thresholds.vat_annual || 0);
          break;
        case TaxType.PERSONAL_INCOME:
          const annualIncome = this.estimateAnnualRevenue(revenue, period);
          shouldCalculate = annualIncome >= (policy.thresholds.personal_income_annual || 0);
          break;
        case TaxType.COOPERATIVE:
          // HTX members don't pay individual tax
          if (policy.htxEligible) {
            shouldCalculate = false; // HTX will handle
          }
          break;
      }

      if (shouldCalculate && taxRate > 0) {
        const taxAmount = taxableRevenue * taxRate;

        const calculation = this.taxRepo.create({
          user_id: userId,
          user_role: role,
          tax_type: taxType,
          taxable_revenue: taxableRevenue,
          tax_rate: taxRate,
          tax_amount: taxAmount,
          calculation_period: period,
          htx_coverage: policy.htxEligible && role === UserRole.R01,
          region_code: regionCode,
          calculation_note: policy.notes,
          metadata: {
            thresholds: policy.thresholds,
            estimated_annual: this.estimateAnnualRevenue(revenue, period),
            policy_applied: policy,
          },
        });

        calculations.push(calculation);
      }
    }

    // Save calculations
    if (calculations.length > 0) {
      await this.taxRepo.save(calculations);
    }

    return calculations;
  }

  /**
   * Lấy tổng hợp thuế theo khu vực
   */
  async getTaxSummaryByRegion(regionCode: string, period: string): Promise<any> {
    const taxes = await this.taxRepo
      .createQueryBuilder('tax')
      .where('tax.region_code = :regionCode', { regionCode })
      .andWhere('tax.calculation_period = :period', { period })
      .leftJoinAndSelect('tax.user', 'user')
      .getMany();

    const summary = {
      region_code: regionCode,
      period,
      total_revenue: 0,
      total_tax: 0,
      user_breakdown: {} as any,
      tax_type_breakdown: {} as any,
      htx_vs_individual: {
        htx_covered: { revenue: 0, tax: 0, count: 0 },
        individual: { revenue: 0, tax: 0, count: 0 },
      },
    };

    taxes.forEach((tax) => {
      summary.total_revenue += Number(tax.taxable_revenue);
      summary.total_tax += Number(tax.tax_amount);

      // By user role
      if (!summary.user_breakdown[tax.user_role]) {
        summary.user_breakdown[tax.user_role] = { revenue: 0, tax: 0, count: 0 };
      }
      summary.user_breakdown[tax.user_role].revenue += Number(tax.taxable_revenue);
      summary.user_breakdown[tax.user_role].tax += Number(tax.tax_amount);
      summary.user_breakdown[tax.user_role].count += 1;

      // By tax type
      if (!summary.tax_type_breakdown[tax.tax_type]) {
        summary.tax_type_breakdown[tax.tax_type] = { revenue: 0, tax: 0, count: 0 };
      }
      summary.tax_type_breakdown[tax.tax_type].revenue += Number(tax.taxable_revenue);
      summary.tax_type_breakdown[tax.tax_type].tax += Number(tax.tax_amount);
      summary.tax_type_breakdown[tax.tax_type].count += 1;

      // HTX vs Individual
      if (tax.htx_coverage) {
        summary.htx_vs_individual.htx_covered.revenue += Number(tax.taxable_revenue);
        summary.htx_vs_individual.htx_covered.tax += Number(tax.tax_amount);
        summary.htx_vs_individual.htx_covered.count += 1;
      } else {
        summary.htx_vs_individual.individual.revenue += Number(tax.taxable_revenue);
        summary.htx_vs_individual.individual.tax += Number(tax.tax_amount);
        summary.htx_vs_individual.individual.count += 1;
      }
    });

    return summary;
  }

  /**
   * Lấy gợi ý tối ưu thuế cho user
   */
  async getTaxOptimizationRecommendations(
    userId: string,
    currentRole: UserRole,
    annualRevenue: number,
  ): Promise<any> {
    const currentPolicy = this.TAX_POLICY[currentRole];
    const recommendations: any[] = [];

    // Nếu chưa là thành viên HTX và có thể join
    if (!currentPolicy.htxEligible || currentRole !== UserRole.R01) {
      const r01Policy = this.TAX_POLICY[UserRole.R01];
      const currentTax = this.calculateTaxAmount(annualRevenue, currentPolicy);
      const htxTax = annualRevenue * 0.15; // HTX tax rate
      const memberShare = annualRevenue - htxTax; // After HTX tax

      if (memberShare > annualRevenue - currentTax) {
        recommendations.push({
          type: 'join_htx',
          description: 'Gia nhập HTX để tối ưu thuế',
          current_tax: currentTax,
          htx_tax: htxTax,
          savings: currentTax - htxTax,
          member_share_after_tax: memberShare,
        });
      }
    }

    // Cảnh báo ngưỡng thuế
    Object.entries(currentPolicy.thresholds).forEach(([key, threshold]) => {
      if (annualRevenue >= threshold * 0.8 && annualRevenue < threshold) {
        recommendations.push({
          type: 'threshold_warning',
          description: `Gần đạt ngưỡng ${key}: ${threshold.toLocaleString()} VND`,
          current_revenue: annualRevenue,
          threshold,
          remaining: threshold - annualRevenue,
        });
      }
    });

    return recommendations;
  }

  private estimateAnnualRevenue(revenue: number, period: string): number {
    if (period.includes('-Q')) {
      return revenue * 4; // Quarterly to annual
    } else {
      return revenue * 12; // Monthly to annual
    }
  }

  private calculateTaxAmount(revenue: number, policy: any): number {
    let totalTax = 0;
    policy.taxTypes.forEach((taxType: TaxType) => {
      const rate = policy.rates[taxType] || 0;
      totalTax += revenue * rate;
    });
    return totalTax;
  }
}
