import { Controller, Get, Param, Query } from '@nestjs/common';
import { M2BotService } from '../services/m2Bot.service';

@Controller('m2-bot')
export class M2BotController {
  constructor(private readonly m2BotService: M2BotService) {}

  // Bước 3: API gợi ý user tương tự
  @Get('suggest-similar-users/:user_id')
  async suggestSimilarUsers(@Param('user_id') user_id: string, @Query('topN') topN?: string) {
    const n = topN ? parseInt(topN, 10) : 5;
    return this.m2BotService.suggestSimilarUsers(user_id, n);
  }
}
