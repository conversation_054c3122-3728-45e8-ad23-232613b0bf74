// Script: Insert product_vectors.json vào bảng product_vector_embedding (PostgreSQL)
// Chạy: npx ts-node src/m2/ai_trainning/pipeline.insert-product-embeddings.ts
import { AppDataSource } from '../../data-source';
import { ProductVectorEmbedding } from '../shop/entities/product_vector_embedding.entity';
import * as fs from 'fs';
import * as path from 'path';

interface ProductVector {
  product_id: string;
  embedding: number[];
}

async function main() {
  await AppDataSource.initialize();
  const raw = fs.readFileSync(path.join(__dirname, 'product_vectors.json'), 'utf8');
  const vectors: ProductVector[] = JSON.parse(raw);

  for (const { product_id, embedding } of vectors) {
    await AppDataSource.query(
      `INSERT INTO product_vector_embedding (product_id, vector, updated_at)
       VALUES ($1, $2, now())
       ON CONFLICT (product_id) DO UPDATE SET vector = EXCLUDED.vector, updated_at = now()`,
      [product_id, embedding],
    );
  }
  await AppDataSource.destroy();
}

main().catch(console.error);
