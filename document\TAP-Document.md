# Ghi chú dự án TAP

## 1. Database:

### 1.1 Kết nối database:

**1.1.1. Kết nối database với docker:**
Dùng Docker cho PostgreSQL (nếu chưa cài local)

```bash
docker run --name pg-nest -e POSTGRES_PASSWORD=123456 -e POSTGRES_DB=TABDB -p 5432:5432 -d postgres

```

**1.1.2. <PERSON><PERSON>u hình port cho database:**

```bash
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=123456
DB_NAME=TAPDB
```

**1.1.3. Tạo database từ code:**

- **Tạo database tên:  TAPDB**

```bash
**- Đ<PERSON>ng tại thư mục /server:**
npm run build
npm run migration:generate src/migrations/ten_migration
npm run migration:run
```

**1.1.4. <PERSON><PERSON><PERSON> vụ thông báo:**

```csharp
docker run -d --hostname rabbit --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management
```

### 1.2. Thiết kế database

### 1.2.1 Module 1 - Quản lí người dùng:

## 📊 **M1 bao gồm 7 bảng chính:**

| STT | Tên bảng | Mô tả chức năng chính |
| --- | --- | --- |
| 1 | `users` | Thông tin đăng nhập, phân quyền (`role`), trạng thái, token |
| 2 | `user_profiles` | Thông tin cá nhân chi tiết (tên, giới tính, avatar, ngày sinh, sđt...) |
| 3 | `roles` | Danh sách nhóm người dùng: R01–R13, mô tả quyền, phân quyền hiển thị |
| 4 | `user_affiliates` | Cây hệ thống F1–F5, tuyến giới thiệu người dùng |
| 5 | `user_locations` | Liên kết tài khoản với khu vực hành chính (tỉnh, xã) |
| 6 | `user_qr` | Mã QR cá nhân, chứa mã giới thiệu và link mời đăng ký |
| 7 | `otp_verification` | Mã xác thực OTP gửi qua email/số điện thoại |
|  |  |  |

## 🧩 **1. Bảng `users` – Tài khoản người dùng**

| Cột | Kiểu dữ liệu | Ràng buộc | Ghi chú |
| --- | --- | --- | --- |
| `id` | `uuid` | PK, NOT NULL | Sinh qua `uuid_generate_v4()` |
| `uid_code` | `varchar` | UNIQUE, NOT NULL | Mã định danh nội bộ |
| `phone` | `varchar` | UNIQUE, NOT NULL | Số điện thoại đăng ký |
| `email` | `varchar` | UNIQUE, NULLABLE | Email cá nhân |
| `password` | `varchar` | NOT NULL | Mã hóa SHA/Bcrypt |
| `refreshToken` | `varchar` | NULLABLE | Lưu JWT refresh token |
| `referral_by` | `varchar` | NULLABLE | Mã người giới thiệu |
| `status` | `varchar` | NOT NULL | default `'active'` |
| `role_id` | `uuid` | FK → `roles(id)` | Vai trò hệ thống |
| `created_at` | `timestamp` | default: `now()` |  |
| `updated_at` | `timestamp` | default: `now()` |  |

📌 **Indexes & FK**:

- `UNIQUE`: uid_code, phone, email
- `FK`: `role_id → roles(id)`
- `CASCADE`: Nhiều bảng khác liên kết

---

## 🧩 **2. Bảng `roles` – Vai trò người dùng**

| Cột | Kiểu dữ liệu | Ghi chú |
| --- | --- | --- |
| `id` | `uuid` | PK |
| `role_code` | `varchar(10)` | UNIQUE |
| `name` | `varchar(255)` | Tên hiển thị |
| `description` | `text` | Mô tả |

🔗 `users.role_id` → dùng để phân quyền (Admin, User, CTV...)

---

## 🧩 **3. Bảng `otp_verification` – Mã xác thực OTP**

| Cột | Kiểu dữ liệu | Ghi chú |
| --- | --- | --- |
| `id` | `uuid` | PK |
| `target` | `varchar` | Email/SĐT nhận mã |
| `otpCode` | `varchar` | Mã OTP |
| `expiredAt` | `timestamp` | Hết hạn OTP |
| `status` | `varchar` | `valid`, `used`, `expired` |
| `isVerified` | `boolean` | default `false` |
| `createdAt` | `timestamp` | default `now()` |

✅ Dùng để xác minh đăng nhập, lấy lại mật khẩu...

---

## 🧩 **4. Bảng `user_affiliates` – Quan hệ giới thiệu**

| Cột | Kiểu dữ liệu | Ghi chú |
| --- | --- | --- |
| `id` | `uuid` | PK |
| `userId` | `uuid` | Người được mời |
| `referrerId` | `uuid` | Người giới thiệu trực tiếp |
| `level_1` → `level_5` | `varchar` | Tuyến trên cấp 1 → cấp 5 |

🔗 `userId`, `referrerId` liên kết tới bảng `users(id)`

📌 Dùng để tính hoa hồng, xây hệ thống affiliate, thưởng cấp bậc...

---

## 🧩 **5. Bảng `user_qr` – Mã QR cá nhân**

| Cột | Kiểu dữ liệu | Ghi chú |
| --- | --- | --- |
| `id` | `uuid` | PK |
| `userId` | `uuid` | FK → users |
| `qrCode` | `text` | Nội dung mã QR |
| `qrType` | `varchar` | `profile`, `referral`, `transaction`, etc. |
| `description` | `varchar` | Ghi chú thêm |
| `linkUrl` | `varchar` | URL đích nếu có |
| `isActive` | `boolean` | default: true |
| `createdAt` | `timestamp` |  |
| `updatedAt` | `timestamp` |  |

📌 Dùng để quét hồ sơ, mời đăng ký, chia sẻ...

---

## 🧩 **6. Bảng `user_locations` – Địa bàn hoạt động**

| Cột | Kiểu dữ liệu | Ghi chú |
| --- | --- | --- |
| `id` | `uuid` | PK |
| `user_id` | `uuid` | FK → users |
| `province_code` | `varchar` | Mã tỉnh |
| `district_code` | `varchar` | Mã huyện |
| `commune_code` | `varchar` | Mã xã |

📌 Dùng để phân chia địa bàn, lọc theo vùng, phân quyền quản trị xã/huyện/tỉnh

# 2. Nghiệp vụ chức năng

### 2.1 Đăng ký tài khoản

**Nghiệp vụ xử lý:**

- **Bước 1: Nhập thông tin đăng ký**
    - Gồm: số điện thoại (bắt buộc), email (nếu có), mật khẩu
    - Xác thực:
        - Số điện thoại: đúng định dạng Việt Nam, không trùng `users.phone`
        - Email (nếu có): đúng định dạng, không trùng `users.email`
        - Mật khẩu: tối thiểu 6 ký tự
    - Sinh `uid_code` duy nhất
    - Tạo mới `users` + `user_profiles` mặc định
    - Ghi nhận người giới thiệu (nếu có `referral_by`)
    - Ghi nhận vào bảng `user_affiliates`: tự động map tuyến `level_1` đến `level_5`
- **Bước 2: Tạo mã QR cá nhân**
    - Ghi vào `user_qr` với `qrType = 'profile'`, trạng thái `isActive = true`
    - Sinh `linkUrl` trỏ tới hồ sơ cá nhân
- **Bước 3: Ghi địa bàn hoạt động (nếu có)**
    - Ghi vào `user_locations` nếu người dùng chọn tỉnh/huyện/xã

---

### 2.2 Đăng nhập

**Nghiệp vụ xử lý:**

- **Bước 1: Nhập thông tin đăng nhập**
    - Chấp nhận đăng nhập bằng `phone` hoặc `email`
    - Kiểm tra tồn tại user, trạng thái `status = 'active'`
- **Bước 2: Xác thực mật khẩu**
    - So sánh hash mật khẩu
    - Nếu sai quá số lần cho phép → có thể khóa tạm thời (nâng cao)
- **Bước 3: Cấp token**
    - Trả về `accessToken`, `refreshToken`
    - Lưu `refreshToken` vào DB (field `users.refreshToken`)

---

### 2.3 Quên mật khẩu

**Nghiệp vụ xử lý:**

- **Bước 1: Nhập email hoặc số điện thoại**
    - Kiểm tra tồn tại trong bảng `users`
    - Sinh mã OTP ngẫu nhiên (6 số), thời hạn 5 phút
    - Ghi vào bảng `otp_verification` với trạng thái `valid`, `isVerified = false`
- **Bước 2: Gửi OTP**
    - Gửi qua ZNS (nếu là phone) hoặc email (nếu có)
    - Nội dung OTP kèm hướng dẫn, thời gian hiệu lực
- **Bước 3: Nhập mã OTP**
    - Xác thực: mã khớp, chưa hết hạn, chưa dùng (`status = 'valid'`, `isVerified = false`, `expiredAt > now()`)
- **Bước 4: Nhập mật khẩu mới**
    - Kiểm tra độ mạnh
    - Cập nhật `users.password`, hash lại
    - Đánh dấu OTP là `used`, `isVerified = true`

# 3. Triển khai

### 3.1 Docker compose

```csharp
# Server
docker build -t thanhduong1311/tap-server:latest ./server

# Client
docker build -t thanhduong1311/tap-client:latest ./client

# Đăng nhập Docker
docker login

# Push lên Dockerhub
docker push thanhduong1311/tap-server:latest
docker push thanhduong1311/tap-client:latest

```


Dưới đây là kế hoạch triển khai chi tiết nhóm chức năng AI 1 trong mô-đun M2 của dự án TAP, bao gồm các tính năng: Tìm kiếm sản phẩm bằng hình ảnh (M2.31), Gán nhãn ảnh (M2.33), và Vector hóa ảnh (M2.34).

I. 🎯 MỤC TIÊU CHUNG
Cho phép người dùng tìm sản phẩm bằng cách chụp ảnh hoặc chọn ảnh.

Hệ thống sẽ gán nhãn, phân loại, và vector hóa ảnh để tìm sản phẩm tương đồng.

Áp dụng AI để cải thiện trải nghiệm tìm kiếm – quản lý ảnh sản phẩm thông minh.

II. 🔍 PHẠM VI CÁC TÍNH NĂNG
Mã	Tên tính năng	Mô tả ngắn gọn
M2.31	Tìm kiếm SP bằng hình ảnh	So sánh ảnh người dùng với ảnh sản phẩm đã có trong kho
M2.33	Gán nhãn ảnh	Cho phép người gắn nhãn hoặc AI dự đoán nhóm sản phẩm, đặc điểm
M2.34	Vector hóa ảnh	Chuyển ảnh thành vector số để so sánh và lưu trữ hiệu quả

III. 🧱 KIẾN TRÚC TỔNG THỂ
txt
Sao chép
Chỉnh sửa
[FE Upload ảnh] → [API: /vectorize] → [Sinh vector ảnh (M2.34)]
                            ↓
                    [So sánh ảnh (M2.31)]
                            ↓
                 [Trả về kết quả sản phẩm gần giống]

         ↘ Gán nhãn thủ công hoặc AI → Lưu metadata (M2.33)
IV. 📌 CÁC BƯỚC TRIỂN KHAI CHI TIẾT
🔹 Giai đoạn 1: Chuẩn bị dữ liệu & mô hình
Việc cần làm	Người thực hiện	Ghi chú
✅ Thu thập ảnh mẫu sản phẩm	Đội nội dung / HTX	10 ảnh / sản phẩm
✅ Chuẩn hóa kích thước, tên file	Kỹ sư dữ liệu	Lưu dạng: product-slug.jpg
✅ Chọn mô hình vector hóa (CLIP / ViT)	Eva / AI engineer	Ưu tiên dùng pretrained
✅ Tạo script sinh vector ảnh	Backend engineer	Dùng Python + Torch + FAISS

🔹 Giai đoạn 2: Triển khai API Backend
API	Mô tả	Trả về
POST /api/vectorize	Nhận ảnh → trả về vector	vector (array)
POST /api/search-image	Nhận ảnh → trả về sản phẩm giống nhất	list sản phẩm (top 5)
POST /api/label-image	Gán nhãn ảnh theo nhóm	metadata ảnh
GET /api/products/vector-db	Lấy danh sách vector ảnh sản phẩm	Dùng để so sánh ảnh

⚙ Công nghệ gợi ý: NestJS + Python module tách riêng xử lý AI

🔹 Giai đoạn 3: Giao diện người dùng (FE)
Giao diện	Chức năng chính
Tìm sản phẩm bằng ảnh	Input ảnh → hiển thị top 5 sản phẩm gợi ý
Trang quản trị ảnh sản phẩm	Cho phép gán nhãn, xóa, sửa vector
Lịch sử tìm ảnh	Ghi lại lịch sử tìm kiếm của người dùng (tuỳ chọn)

🔹 Giai đoạn 4: Tối ưu & kiểm thử
Việc cần làm	Ghi chú
Benchmark tốc độ tìm kiếm ảnh	FAISS top K search
Kiểm thử độ chính xác AI gán nhãn	Nếu dùng auto labeling
Lưu vector ảnh vào cache (Redis / file)	Để tăng tốc độ
Tạo cronjob tái vector hóa khi ảnh thay đổi	Tự động cập nhật DB vector

V. 🛠 CÔNG CỤ VÀ CÔNG NGHỆ ĐỀ XUẤT
Thành phần	Công nghệ
Vector hóa ảnh	CLIP, ViT, hoặc ResNet pretrained
Tìm kiếm ảnh tương đồng	FAISS
Backend	NestJS (API chính), Python (xử lý AI)
FE	NextJS + Tailwind + Upload ảnh
Lưu trữ vector	PostgreSQL + FAISS index (hoặc Redis cache)

VI. 📅 TIẾN ĐỘ ĐỀ XUẤT (GỢI Ý)
Giai đoạn	Thời gian (ngày)	Mốc chính
G1: Chuẩn bị dữ liệu + mô hình	3–5 ngày	Có dữ liệu ảnh mẫu + mô hình vector hóa
G2: API backend xử lý ảnh	4–6 ngày	Có endpoint /vectorize, /search-image hoạt động
G3: FE giao diện tìm kiếm	2–3 ngày	Giao diện chọn ảnh + xem kết quả
G4: Kiểm thử, chỉnh UI, tối ưu	2–4 ngày	Hoạt động ổn định, demo được

⏳ Tổng thời gian dự kiến: ~10–14 ngày (tuỳ số lượng ảnh và nhân lực)

VII. 🎯 KẾT QUẢ ĐẦU RA
✔️ Người dùng tìm được sản phẩm bằng hình ảnh

✔️ Hệ thống sinh vector ảnh sản phẩm để quản lý & tìm kiếm nhanh

✔️ Có trang quản trị gán nhãn & chỉnh sửa ảnh

✔️ Có thể mở rộng dùng AI tự gán nhãn sau này (nâng cấp M2.33)


Dưới đây là kế hoạch triển khai chi tiết Nhóm chức năng AI 2 trong dự án TAP – tập trung vào việc kiểm duyệt ảnh sản phẩm, phát hiện vi phạm, và phân loại ảnh bằng AI, phục vụ mục tiêu an toàn nội dung, phân loại tự động, cảnh báo vi phạm hình ảnh.

I. 🎯 MỤC TIÊU CHUNG
Giúp hệ thống tự động phát hiện ảnh vi phạm (ví dụ ảnh có người, logo cấm, hàng giả…).

Phân loại ảnh sản phẩm vào các nhóm cụ thể (rau – thịt – điện máy – sản phẩm cấm…).

Cảnh báo cho người quản trị/HTX khi ảnh sai phạm được đăng lên.

II. 🔍 PHẠM VI CÁC TÍNH NĂNG
Mã	Tên tính năng	Mô tả ngắn gọn
M2.19	Phát hiện đối tượng trong ảnh – xử lý vi phạm	Kiểm tra ảnh có chứa các đối tượng cấm (người, hình phản cảm, logo sai luật...)
M2.33	Phân loại ảnh	AI tự phân loại ảnh theo nhóm ngành, nhóm kiểm duyệt (dựa trên ảnh thực tế)

III. 🧱 KIẾN TRÚC TỔNG THỂ
txt
Sao chép
Chỉnh sửa
[FE Upload ảnh sản phẩm]
          ↓
     [API: /check-image]
          ↓
+-----------------------------+
| Tầng AI kiểm duyệt & phân loại ảnh |
| • Nhận diện đối tượng (YOLOv5)     |
| • Phân loại nhóm ảnh (Classifier)  |
+-----------------------------+
          ↓
  [Trả về kết quả: loại ảnh, vi phạm hay không, nhóm nội dung]
IV. 📌 CÁC BƯỚC TRIỂN KHAI CHI TIẾT
🔹 Giai đoạn 1: Xây dựng bộ dữ liệu và label
Việc cần làm	Ai thực hiện	Ghi chú
✅ Thu thập ảnh thật từ HTX/người dùng	Đội nội dung	Mỗi nhóm sản phẩm: ≥50 ảnh
✅ Dán nhãn thủ công (ngành hàng, nhóm cấm)	Admin/CTV	Dùng gán nhãn đơn giản: ['Thịt', 'Cá', 'Ảnh chứa người', 'Logo lạ']
✅ Gộp ảnh vi phạm từ Google (cho model học)	Kỹ sư AI / Eva hỗ trợ	Ảnh có người, có watermark, phản cảm, hàng giả...

🔹 Giai đoạn 2: Huấn luyện AI
Mục tiêu	Mô hình gợi ý	Ghi chú
Nhận diện đối tượng trong ảnh (người, logo)	YOLOv5	Phát hiện vùng ảnh chứa đối tượng
Phân loại ảnh thành nhóm sản phẩm	ResNet / MobileNet / ViT	Phân loại đơn hoặc đa nhãn (Thịt, Trái cây, Thiết bị, Ảnh lỗi)

❗ Có thể dùng mô hình nhẹ để xử lý nhanh (nếu cần chạy real-time)

🔹 Giai đoạn 3: Triển khai API kiểm duyệt ảnh
API	Chức năng	Trả về
POST /api/check-image	Nhận ảnh → phân tích → trả về kết quả	{ valid: true/false, labels: [], warnings: [] }
GET /api/violated-images	Lọc ảnh vi phạm gần đây	Phục vụ dashboard admin

🔹 Giai đoạn 4: Kết nối với frontend
Giao diện	Chức năng
Trang đăng ảnh sản phẩm	Gọi check-image → nếu vi phạm thì cảnh báo người dùng
Trang quản lý ảnh vi phạm	Cho admin kiểm duyệt, xoá, tạm ẩn ảnh sai phạm
Log lịch sử cảnh báo	Theo dõi theo tài khoản, thống kê vi phạm ảnh

V. 🛠 CÔNG CỤ VÀ CÔNG NGHỆ ĐỀ XUẤT
Thành phần	Gợi ý công cụ
Nhận diện đối tượng (người/logo...)	YOLOv5 / YOLOv8
Phân loại ảnh	MobileNet, ViT, CLIP classifier
Annotation tool	CVAT / LabelImg
Backend	Python Flask/FastAPI cho AI, NestJS gọi API
Giao diện	React/NextJS + bảng vi phạm + xem ảnh

VI. 📅 TIẾN ĐỘ TRIỂN KHAI (GỢI Ý)
Giai đoạn	Thời gian	Mốc
G1: Dữ liệu & label	5–7 ngày	Có bộ ảnh nhãn đầy đủ
G2: Huấn luyện AI	5–10 ngày	Có model chạy được YOLO + phân loại
G3: Tạo API kiểm duyệt	3–5 ngày	Có endpoint /check-image hoạt động
G4: Tích hợp frontend + dashboard	3–4 ngày	Có UI cảnh báo, kiểm duyệt
G5: Kiểm thử toàn bộ	2–3 ngày	Demo đầy đủ luồng

📌 Tổng thời gian dự kiến: ~15–25 ngày

VII. 🎯 KẾT QUẢ ĐẦU RA
✅ Có thể cảnh báo ảnh vi phạm ngay khi người dùng đăng

✅ Tự động phân loại ảnh sản phẩm (giảm công sức admin)

✅ Giao diện dễ kiểm duyệt, lọc sai phạm

✅ Mở rộng huấn luyện AI nếu ảnh nhiều lên


[FE Upload ảnh]
    ↓
[NestJS Backend]
    → Lưu ảnh vào storage (cloudynary) 
    → Gửi message vào hàng đợi RabbitMQ:
       { productId, imagePath }

[RabbitMQ Queue: image-vectorize_]

[Worker Python FastAPI / Script]
    ← Nhận message
    → Đọc ảnh → gọi CLIP → sinh vector
    → Gửi POST /api/save-vector (hoặc lưu DB trực tiếp)