import React from 'react'
import {
  Modal,
  Badge,
  Typography,
  Row,
  Col,
  Image,
  Empty,
  Tag,
  Descriptions,
  Tabs,
  Card,
} from 'antd'
import {
  EyeOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  TagOutlined,
  PictureOutlined,
} from '@ant-design/icons'
import ModalButtonGroup from '@/components/ModalButtonGroup'
import styles from './ProductManagement.module.scss'

const { Title, Text, Paragraph } = Typography

interface ProductDetailModalProps {
  open: boolean
  onCancel: () => void
  onEdit: () => void
  loading: boolean
  product: any
  statusConfig: any
  productStatusList: any[]
  categoryMap: Record<string, string>
  isMobile: boolean
}

const ProductDetailModal: React.FC<ProductDetailModalProps> = ({
  open,
  onCancel,
  onEdit,
  loading,
  product,
  statusConfig,
  productStatusList,
  categoryMap,
  isMobile,
}) => {
  const renderProductDetailContent = () => {
    if (loading) {
      return (
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}>
            <div className={styles.spinner} />
            <Text>Đang tải thông tin sản phẩm...</Text>
          </div>
        </div>
      )
    }
    if (!product) {
      return (
        <div className={styles.emptyContainer}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="Không tìm thấy thông tin sản phẩm"
          />
        </div>
      )
    }
    const statusInfo = statusConfig[product.status as keyof typeof statusConfig]
    const tabItems = [
      {
        key: 'overview',
        label: (
          <span style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <InfoCircleOutlined />
            Thông tin chung
          </span>
        ),
        children: (
          <div className={styles.tabContent}>
            <Row gutter={24}>
              <Col xs={24} md={10}>
                <div className={styles.productImageSection}>
                  <div className={styles.mainImageContainer}>
                    <Image
                      src={
                        product.thumbnail_url ||
                        (product.product_images && product.product_images[0]?.image_url) ||
                        '/images/no-image.png'
                      }
                      alt={product.name}
                      className={styles.mainProductImage}
                      fallback="/images/no-image.png"
                    />
                  </div>
                  {product.product_images && product.product_images.length > 1 && (
                    <div className={styles.thumbnailContainer}>
                      {product.product_images.map((img: any, idx: number) => (
                        <Image
                          key={idx}
                          src={img.image_url || '/images/no-image.png'}
                          alt={`${product.name} ${idx + 1}`}
                          className={styles.thumbnailImage}
                          fallback="/images/no-image.png"
                        />
                      ))}
                    </div>
                  )}
                </div>
              </Col>
              <Col xs={24} md={14}>
                <div className={styles.productInfo}>
                  <div className={styles.productHeader}>
                    <Title level={3} className={styles.productTitle}>
                      {product.name}
                    </Title>
                    <div className={styles.productMeta}>
                      <Badge
                        color={statusInfo?.color}
                        text={statusInfo?.text}
                        className={styles.statusBadge}
                      />
                      <Text type="secondary" className={styles.productSku}>
                        SKU: {product.slug || product.id}
                      </Text>
                    </div>
                  </div>
                  <Descriptions column={1} className={styles.productDescriptions}>
                    <Descriptions.Item label="Danh mục">
                      <Tag color="blue">{categoryMap[product.category_id] || 'Chưa phân loại'}</Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="Đơn vị">
                      {product.unit || 'Chưa xác định'}
                    </Descriptions.Item>
                    <Descriptions.Item label="Giá bán">
                      <Text strong className={styles.priceText}>
                        {Number(product.price).toLocaleString()}₫
                      </Text>
                    </Descriptions.Item>
                    <Descriptions.Item label="Tồn kho">
                      {(() => {
                        const totalStock = Array.isArray(product.product_versions)
                          ? product.product_versions.reduce(
                              (sum: number, v: any) => sum + (Number(v.stock) || 0),
                              0
                            )
                          : 0
                        return (
                          <span className={totalStock === 0 ? styles.outOfStock : styles.inStock}>
                            {totalStock} {product.unit}
                          </span>
                        )
                      })()}
                    </Descriptions.Item>
                    <Descriptions.Item label="Trạng thái">
                      {(() => {
                        let statusLabel = ''
                        let statusColor = ''
                        if (productStatusList.length > 0) {
                          const found = productStatusList.find(
                            (s: any) =>
                              s.id === product.status_id ||
                              s.id === product.status ||
                              s.code === product.status ||
                              s.label === product.status
                          )
                          if (found) {
                            statusLabel = found.label
                            statusColor = found.color || 'default'
                          }
                        }
                        if (statusLabel) {
                          return <Tag color={statusColor}>{statusLabel}</Tag>
                        }
                        if (product.status_label) {
                          return (
                            <Tag color={product.status_label_color || 'default'}>
                              {product.status_label}
                            </Tag>
                          )
                        }
                        if (
                          typeof product.status === 'object' &&
                          product.status !== null &&
                          'label' in product.status
                        ) {
                          return (
                            <Tag color={product.status.color || 'default'}>
                              {product.status.label}
                            </Tag>
                          )
                        }
                        if (
                          statusInfo?.text ||
                          (typeof product.status === 'string' && product.status)
                        ) {
                          return (
                            <Tag color={statusInfo?.color} icon={statusInfo?.icon}>
                              {statusInfo?.text ||
                                (typeof product.status === 'string' ? product.status : '')}
                            </Tag>
                          )
                        }
                        return <Tag color="default">Không xác định</Tag>
                      })()}
                    </Descriptions.Item>
                    <Descriptions.Item label="Công khai">
                      <Tag color={product.is_public ? 'green' : 'red'}>
                        {product.is_public ? 'Có' : 'Không'}
                      </Tag>
                    </Descriptions.Item>
                  </Descriptions>
                  {product.description && (
                    <div className={styles.productDescription}>
                      <Title level={5}>Mô tả sản phẩm</Title>
                      <Paragraph className={styles.descriptionText}>
                        {product.description}
                      </Paragraph>
                    </div>
                  )}
                </div>
              </Col>
            </Row>
          </div>
        ),
      },
      {
        key: 'attributes',
        label: (
          <span style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <SettingOutlined />
            Thuộc tính
          </span>
        ),
        children: (
          <div className={styles.tabContent}>
            {product.product_attribute && product.product_attribute.length > 0 ? (
              <div className={styles.attributesList}>
                {product.product_attribute.map((attr: any, idx: number) => (
                  <Card key={idx} className={styles.attributeCard}>
                    <Row align="middle">
                      <Col span={6}>
                        <Text strong>{attr.attribute_name || attr.name || 'Thuộc tính'}</Text>
                      </Col>
                      <Col span={10}>
                        <Text>{attr.value}</Text>
                      </Col>
                      <Col span={4}>
                        <Text type="secondary">{attr.unit}</Text>
                      </Col>
                      <Col span={4}>
                        {attr.is_searchable && <Tag color="green">Có thể tìm kiếm</Tag>}
                      </Col>
                    </Row>
                  </Card>
                ))}
              </div>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="Chưa có thuộc tính nào" />
            )}
          </div>
        ),
      },
      {
        key: 'versions',
        label: (
          <span style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <TagOutlined />
            Phiên bản
          </span>
        ),
        children: (
          <div className={styles.tabContent}>
            {product.product_versions && product.product_versions.length > 0 ? (
              <div className={styles.versionsList}>
                {product.product_versions.map((version: any, idx: number) => (
                  <Card key={idx} className={styles.versionCard}>
                    <Row gutter={16}>
                      <Col span={8}>
                        <Text strong>{version.name}</Text>
                        <br />
                        <Text type="secondary" className={styles.versionSku}>
                          SKU: {version.sku_code}
                        </Text>
                      </Col>
                      <Col span={6}>
                        <Text strong className={styles.versionPrice}>
                          {Number(version.price).toLocaleString()}₫
                        </Text>
                      </Col>
                      <Col span={6}>
                        <Text>Tồn kho: </Text>
                        <span className={version.stock === 0 ? styles.outOfStock : styles.inStock}>
                          {version.stock}
                        </span>
                      </Col>
                      <Col span={4}>
                        <Tag color={version.stock > 0 ? 'green' : 'red'}>
                          {version.stock > 0 ? 'Còn hàng' : 'Hết hàng'}
                        </Tag>
                      </Col>
                    </Row>
                  </Card>
                ))}
              </div>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="Chưa có phiên bản nào" />
            )}
          </div>
        ),
      },
      {
        key: 'images',
        label: (
          <span style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <PictureOutlined />
            Thư viện ảnh
          </span>
        ),
        children: (
          <div className={styles.tabContent}>
            {product.product_images && product.product_images.length > 0 ? (
              <div className={styles.imageGallery}>
                {product.product_images.map((img: any, idx: number) => (
                  <div key={idx} className={styles.galleryItem}>
                    <Image
                      src={img.image_url}
                      alt={`${product.name} ${idx + 1}`}
                      className={styles.galleryImage}
                      fallback="/images/no-image.png"
                    />
                    <div className={styles.imageInfo}>
                      <Text type="secondary" className={styles.imageId}>
                        ID: {img.public_image_id || img.image_id}
                      </Text>
                      {img.is_thumbnail && <Tag color="orange">Ảnh chính</Tag>}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="Chưa có ảnh nào" />
            )}
          </div>
        ),
      },
    ]
    return (
      <div className={styles.productDetailContent}>
        <Tabs defaultActiveKey="overview" items={tabItems} className={styles.productTabs} />
      </div>
    )
  }

  return (
    <Modal
      title={
        <div className={styles.modalTitleContainer}>
          <EyeOutlined className={styles.modalTitleIcon} />
          <span>Chi tiết sản phẩm</span>
        </div>
      }
      open={open}
      onCancel={onCancel}
      footer={null}
      width={isMobile ? '100%' : 1000}
      className={styles.productDetailModal}
      destroyOnClose
    >
      {renderProductDetailContent()}
      <ModalButtonGroup
        onCancel={onCancel}
        onConfirm={onEdit}
        cancelText="Đóng"
        confirmText="Chỉnh sửa"
        confirmVariant="primary"
      />
    </Modal>
  )
}

export default ProductDetailModal
