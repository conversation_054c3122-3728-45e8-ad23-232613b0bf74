// Simple NLU server (Express.js)

const express = require('express');
const fs = require('fs');
const path = require('path');
const stringSimilarity = require('string-similarity');

const app = express();
app.use(express.json());

const modelPath = path.join(__dirname, 'model.json');
let model = null;
if (fs.existsSync(modelPath)) {
  model = JSON.parse(fs.readFileSync(modelPath, 'utf8'));
}

// Simple intent matcher (rule-based, Vietnamese, case-insensitive)
function matchIntent(text) {
  if (!model) return { intent: null, score: 0 };
  let bestIntent = null;
  let bestScore = 0;
  let bestSim = 0;
  for (const [intent, intentDef] of Object.entries(model.intents)) {
    const utterances = Array.isArray(intentDef.utterances) ? intentDef.utterances : [];
    for (const pattern of utterances) {
      const sim = stringSimilarity.compareTwoStrings(text.toLowerCase(), pattern.toLowerCase());
      if (sim > bestSim) {
        bestSim = sim;
        bestIntent = intent;
        bestScore = sim;
      }
    }
  }
  // Ngưỡng nhận diện intent, có thể điều chỉnh (ví dụ: 0.6)
  const THRESHOLD = 0.6;
  return bestSim >= THRESHOLD
    ? { intent: bestIntent, score: bestScore }
    : { intent: null, score: bestScore };
}

// Entity extraction (regex)
function extractEntities(text) {
  if (!model) return {};
  const entities = {};
  for (const [name, def] of Object.entries(model.entities)) {
    if (def.type === 'regex') {
      const regex = new RegExp(def.pattern, 'gi');
      const found = text.match(regex);
      if (found) entities[name] = found;
    }
  }
  return entities;
}

// Health check endpoint
app.get('/status', (req, res) => {
  res.json({ status: 'ok' });
});

app.post('/nlu', (req, res) => {
  const { text } = req.body;
  if (!text) return res.status(400).json({ error: 'Missing text' });
  const intentResult = matchIntent(text);
  const entities = extractEntities(text);

  // Auto-reply: lấy từ model nếu có answers và suggestedReplies
  let reply = undefined;
  let suggestedReplies = [];
  if (model && intentResult.intent && model.intents && model.intents[intentResult.intent]) {
    const intentDef = model.intents[intentResult.intent];
    if (Array.isArray(intentDef.answers) && intentDef.answers.length > 0) {
      // Chọn ngẫu nhiên 1 câu trả lời
      reply = intentDef.answers[Math.floor(Math.random() * intentDef.answers.length)];
    }
    if (Array.isArray(intentDef.suggestedReplies)) {
      suggestedReplies = intentDef.suggestedReplies;
    }
  }

  res.json({
    intent: intentResult.intent,
    score: intentResult.score,
    entities,
    reply,
    suggestedReplies,
  });
});

const PORT = process.env.PORT || 5005;
app.listen(PORT, () => {
  console.log('🤖🤖🤖 NLU server running on port', PORT);
});
