import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, Unique } from 'typeorm';

@Entity('user_vector_embedding')
@Unique(['user_id'])
export class UserVectorEmbedding {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  user_id: string;

  @Column('float8', { array: true })
  vector: number[];

  @CreateDateColumn({ type: 'timestamp' })
  updated_at: Date;
}
