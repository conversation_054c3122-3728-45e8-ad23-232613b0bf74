import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

export const userManagementApi = createApi({
    reducerPath: 'userManagementApi',
    baseQuery: axiosBaseQuery({ baseUrl }),
    endpoints: (build) => ({
        getUserManagement: build.query({
            query: (params: GetUserManagementParams) => ({
                url: '/users-management/admin/user-management',
                method: 'GET',
                params: params,
                authRequired: true,
            }),
        }),

        getUserManagementById: build.query({
            query: (id: string) => ({
                url: `/users-management/admin/user-management/${id}`,
                method: 'GET',
                authRequired: true,
            }),
        }),

        updateUserManagement: build.mutation({
            query: ({ id, ...data }: UpdateUserManagementRequest) => ({
                url: `/users-management/admin/user-management/${id}`,
                method: 'PUT',
                data: data,
                authRequired: true,
            }),
        }),

        deleteUserManagement: build.mutation({
            query: (id: string) => ({
                url: `/users-management/admin/user-management/${id}`,
                method: 'DELETE',
                authRequired: true,
            }),
        }),

        assignRoleToUser: build.mutation({
            query: (data: AssignRoleRequest) => ({
                url: '/users/assign-role',
                method: 'POST',
                data: data,
                authRequired: true,
            }),
        }),

        // API mới: Lấy danh sách roles
        getAllRoles: build.query({
            query: () => ({
                url: '/roles',
                method: 'GET',
                authRequired: true,
            }),
        }),

        // API mới: Tạo role mới
        createRole: build.mutation({
            query: (data: CreateRoleRequest) => ({
                url: '/roles',
                method: 'POST',
                data: data,
                authRequired: true,
            }),
        }),
    }),
})

export const {
    useGetUserManagementQuery,
    useGetUserManagementByIdQuery,
    useUpdateUserManagementMutation,
    useDeleteUserManagementMutation,
    useAssignRoleToUserMutation,
    useGetAllRolesQuery,
    useCreateRoleMutation,
} = userManagementApi

// Types cũ...
export interface GetUserManagementParams {
    roleCode?: string
    status?: 'active' | 'locked' | 'pending'
    keyword?: string
    page?: number
    limit?: number
}

export interface UserManagementResponse {
    data: UserManagementItem[]
    total: number
    page: number
    limit: number
    totalPages: number
}

export interface UserManagementItem {
    id: string
    fullName: string
    phone: string
    email: string
    roleId: string
    status: string
    createdAt: string
    updatedAt: string
}

export interface UserManagementDetailResponse {
    id: string
    fullName: string
    phone: string
    email: string
    roleId: string
    status: string
    createdAt: string
    updatedAt: string
}

export interface UpdateUserManagementRequest {
    id: string
    fullName?: string
    phone?: string
    email?: string
    roleId?: string
    status?: 'active' | 'locked' | 'pending'
}

export interface AssignRoleRequest {
    userId: string
    roleId: string
}

// Types mới cho role
export interface RoleItem {
    id: string
    role_code: string
    name: string
    description: string
    createdAt: string
    updatedAt: string
}

export interface RoleResponse {
    data: RoleItem[]
    total: number
}

export interface CreateRoleRequest {
    role_code: string
    name: string
    description: string
}
