import { ApiProperty } from '@nestjs/swagger';

export class ImageSearchRequestDto {
  @ApiProperty({ type: 'string', format: 'binary', description: 'File ảnh cần tìm kiếm' })
  file: any;
}

export class ImageSearchResponseDto {
  @ApiProperty({ description: 'ID sản phẩm tìm được' })
  id: string;

  @ApiProperty({ description: 'Tên sản phẩm' })
  name: string;

  @ApiProperty({ description: 'Giá sản phẩm', type: 'number' })
  price: number;

  @ApiProperty({ description: 'URL ảnh thumbnail', required: false })
  thumbnail_url?: string;
}
