import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional } from 'class-validator';

export class AddCartItemDto {
  @IsString()
  @ApiProperty()
  product_id: string;

  @IsNumber()
  @ApiProperty()
  quantity: number;

  @IsNumber()
  @ApiProperty()
  unit_price: number;

  @IsString()
  @IsOptional()
  @ApiProperty()
  version_id?: string;

  @IsOptional()
  @ApiProperty()
  selected_options?: any;
}

export class UpdateCartItemDto {
  @IsString()
  @ApiProperty()
  cart_item_id: string;

  @IsNumber()
  @ApiProperty()
  quantity: number;
}
