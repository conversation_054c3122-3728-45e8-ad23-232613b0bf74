import { <PERSON>, Logger } from '@nestjs/common';
import { Ctx, EventPattern, MessagePattern, Payload, RmqContext } from '@nestjs/microservices';
import { LogService } from './log.service';
import { UserBehaviorLogService } from './user-behavior-log.service';
import { SystemLog } from '../entities/log.entity';
import { CreateLogDto } from '../dto/create-log.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserBehavior } from '../../m2/shop/entities/user_behavior.entity';
import { AiImageVector } from 'src/m2/shop/entities';
import axios from 'axios';

@Controller()
export class LogController {
  constructor(
    private readonly logService: LogService,
    private readonly userBehaviorLogService: UserBehaviorLogService,
  ) {}

  /**
   * 🎯 Consumer cho RabbitMQ nhận log
   */
  @MessagePattern('log_queue')
  async handleLog(log: Partial<SystemLog>) {
    return this.logService.saveLog(log);
  }

  /**
   * Consumer cho RabbitMQ nhận user_behavior và lưu vào bảng user_behavior
   */
  @MessagePattern('user_behavior')
  async handleUserBehavior(payload: Partial<UserBehavior>) {
    return this.userBehaviorLogService.saveUserBehavior(payload);
  }

  @EventPattern('log.update') // Lắng nghe sự kiện gửi từ các service khác
  async handleLogCreate(@Payload() payload: CreateLogDto) {
    return this.logService.saveLog(payload);
  }
}
