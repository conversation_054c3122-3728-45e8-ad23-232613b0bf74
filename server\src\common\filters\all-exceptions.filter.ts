// src/common/filters/all-exceptions.filter.ts
import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { QueryFailedError } from 'typeorm';

// @Catch()
// export class AllExceptionsFilter implements ExceptionFilter {
//   catch(exception: unknown, host: ArgumentsHost) {
//     const ctx = host.switchToHttp();
//     const response = ctx.getResponse<Response>();

//     const status =
//       exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;

//     const message =
//       exception instanceof HttpException
//         ? exception.message
//         : 'Lỗi hệ thống. Vui lòng thử lại sau.';

//     response.status(status).json({
//       data: null,
//       message,
//       statusCode: status,
//     });
//   }
// }

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message: string = 'Lỗi hệ thống. Vui lòng thử lại sau.';

    // Ưu tiên lấy message gốc nhất có thể
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const res = exception.getResponse();
      if (typeof res === 'string') {
        message = res;
      } else if (typeof res === 'object' && res !== null && (res as any).message) {
        message = (res as any).message;
      } else if (exception.message) {
        message = exception.message;
      }
    } else if (exception instanceof QueryFailedError) {
      // Ưu tiên lấy detail/message từ QueryFailedError
      const err: any = exception;
      message = err.detail || err.message || message;
    } else if (exception && typeof exception === 'object' && 'message' in exception) {
      // Các lỗi custom khác
      message = (exception as any).message || message;
    }

    // Nếu message là object hoặc mảng thì join lại
    if (Array.isArray(message)) {
      message = message.join(', ');
    } else if (typeof message === 'object' && message !== null) {
      message = JSON.stringify(message);
    }

    response.status(status).json({
      data: null,
      message,
      statusCode: status,
    });
  }
}
