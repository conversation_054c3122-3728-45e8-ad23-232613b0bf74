@import '../../styles/variables.scss';

.button {
  border-radius: $button-border-radius;
  transition: $button-transition;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  min-width: 60px;
  box-sizing: border-box;
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Variants
.primary {
  background-color: $button-primary-bg;
  border-color: $button-primary-bg;
  color: $button-primary-text;
  min-width: 60px;
  
  &:hover:not(:disabled) {
    background-color: $button-primary-hover-bg;
    border-color: $button-primary-hover-bg;
    color: $button-primary-text;
  }
  
  &:active:not(:disabled) {
    background-color: $button-primary-active-bg;
    border-color: $button-primary-active-bg;
  }
}

.default {
  background-color: $button-default-bg;
  border-color: $button-default-border;
  color: $button-default-text;
  min-width: 60px;
  
  &:hover:not(:disabled) {
    border-color: $button-default-hover-border;
    color: $button-default-hover-text;
  }
  
  &:active:not(:disabled) {
    border-color: $button-primary-active-bg;
    color: $button-primary-active-bg;
  }
}

.danger {
  background-color: $button-danger-bg;
  border-color: $button-danger-bg;
  color: $button-danger-text;
  min-width: 60px;
  
  &:hover:not(:disabled) {
    background-color: $button-danger-hover-bg;
    border-color: $button-danger-hover-bg;
    color: $button-danger-text;
  }
  
  &:active:not(:disabled) {
    background-color: $button-danger-active-bg;
    border-color: $button-danger-active-bg;
  }
}

.success {
  background-color: $success-color;
  border-color: $success-color;
  color: white;
  
  &:hover:not(:disabled) {
    background-color: #73d13d;
    border-color: #73d13d;
    color: white;
  }
  
  &:active:not(:disabled) {
    background-color: #389e0d;
    border-color: #389e0d;
  }
}

.warning {
  background-color: $warning-color;
  border-color: $warning-color;
  color: white;
  
  &:hover:not(:disabled) {
    background-color: #ffc53d;
    border-color: #ffc53d;
    color: white;
  }
  
  &:active:not(:disabled) {
    background-color: #d48806;
    border-color: #d48806;
  }
}

// Sizes - Touch-friendly
.small {
  height: $button-height-sm !important;
  padding: $button-padding-sm !important;
  font-size: $button-font-size-sm !important;
  min-width: 80px !important;
  width: auto !important;
  line-height: 1.2 !important;
}

.middle {
  height: $button-height-md !important;
  padding: $button-padding-md !important;
  font-size: $button-font-size-md !important;
  min-width: 80px !important;
  width: auto !important;
  line-height: 1.2 !important;
}

.large {
  height: $button-height-lg !important;
  padding: $button-padding-lg !important;
  font-size: $button-font-size-lg !important;
  min-width: 100px !important;
  width: auto !important;
  line-height: 1.2 !important;
}

// States
.fullWidth {
  width: 100%;
}

.loading {
  pointer-events: none;
}

.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  
  &:hover {
    background-color: inherit;
    border-color: inherit;
    color: inherit;
  }
}

// Modal button group styles
.modalButtonGroup {
  display: flex;
  gap: $modal-button-gap;
  justify-content: flex-end;
  align-items: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

// Responsive - Ensure touch-friendly sizes on mobile
@media (max-width: 768px) {
  .button {
    min-width: 80px;
    min-height: 44px; // Ensure minimum touch target
  }

  .small {
    min-width: 80px;
    height: 44px !important;
    padding: 12px 16px !important;
  }

  .middle {
    min-width: 80px;
    height: 44px !important;
  }

  .large {
    min-width: 100px;
    height: 48px !important;
  }

  .modalButtonGroup {
    flex-direction: column;
    gap: $spacing-md;

    .button {
      width: 100%;
      min-height: 44px;
    }
  }
}