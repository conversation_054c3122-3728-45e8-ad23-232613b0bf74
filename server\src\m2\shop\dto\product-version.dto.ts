import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber, IsUUID } from 'class-validator';

export class ProductVersionDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  id?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  price: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  stock: number;

  @ApiProperty({ required: false, description: 'Phân loại key-value: size, màu' })
  @IsOptional()
  options?: Record<string, string>;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sku_code?: string;
}

export class CreateProductVersionDto extends ProductVersionDto {}
export class UpdateProductVersionDto extends ProductVersionDto {}
