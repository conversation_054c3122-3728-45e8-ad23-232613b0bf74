import Cookies from 'js-cookie'
import { useState, useEffect, useCallback } from 'react'
import { Button, Form, FormGroup, Label, Input, FormFeedback, Alert } from 'reactstrap'
import { useNavigate, Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { LogIn, MailPlus, Key, Eye, EyeOff, Shield } from 'lucide-react'
import { useGoogleLogin } from '@react-oauth/google'
import loginIllustration from '../../../assets/images/logo.png'
import googleLogo from '../../../assets/images/google-logo.png'
import facebookLogo from '../../../assets/images/facebook-logo.png'
import { useSendOtpResetPasswordHook } from '@/hooks/auth/useResetPassword'
import { toast } from 'react-toastify'
import { useLogin, useSocialLogin, useVerify2FALogin } from '@/hooks/auth/useLogin'
import { ROUTES } from '@/constants/routes'
import styles from './Login.module.scss'

interface TwoFactorFormProps {
  otpCode: string;
  setOtpCode: (code: string) => void;
  twoFAError: string;
  isLoading: boolean;
  onSubmit: (e: React.FormEvent) => void;
  onBack: () => void;
}
// TÁCH COMPONENT RA NGOÀI
const TwoFactorForm = ({ otpCode, setOtpCode, twoFAError, isLoading, onSubmit, onBack }: TwoFactorFormProps) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.4 }}
  >
    <div className={styles.twoFAHeader}>
      <Shield className={styles.twoFAIcon} />
      <p className={styles.twoFADesc}>Vui lòng nhập mã OTP đã được gửi đến email của bạn</p>
    </div>

    <Form onSubmit={onSubmit}>
      <FormGroup>
        <Label className={styles.label}>Mã OTP từ email</Label>
        <div className={styles.inputWrapper}>
          <MailPlus className={styles.inputIcon} />
          <Input
            type="text"
            value={otpCode}
            onChange={(e) => setOtpCode(e.target.value)}
            placeholder="Nhập mã 6 số từ email"
            maxLength={6}
            required
            className={styles.input}
            invalid={!!twoFAError}
            autoFocus // Tự động focus khi component mount
          />
        </div>
      </FormGroup>

      {twoFAError && (
        <Alert color="danger" className={styles.errorAlert}>
          {twoFAError}
        </Alert>
      )}

      <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
        <Button
          color="primary"
          block
          type="submit"
          disabled={isLoading || !otpCode}
          className={styles.submitButton}
        >
          <Shield
            style={{
              width: '1.25rem',
              height: '1.25rem',
              marginRight: '0.5rem',
              display: 'inline-block',
            }}
          />
          {isLoading ? 'Đang xác thực...' : 'Xác thực'}
        </Button>
      </motion.div>

      <div className={styles.registerContainer}>
        <div className={styles.link} onClick={onBack}>
          Quay lại đăng nhập
        </div>
      </div>
    </Form>
  </motion.div>
)

export default function Login() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const [isForgotPassword, setIsForgotPassword] = useState(false)
  const [forgotEmail, setForgotEmail] = useState('')
  const [forgotError, setForgotError] = useState('')
  const [forgotSuccess, setForgotSuccess] = useState('')

  // States cho 2FA
  const [show2FA, setShow2FA] = useState(false)
  const [userId, setUserId] = useState('')
  const [otpCode, setOtpCode] = useState('')
  const [twoFAError, setTwoFAError] = useState('')

  const navigate = useNavigate()

  const { login } = useLogin()
  const { socialLogin } = useSocialLogin()
  const verify2FA = useVerify2FALogin()
  const sendOtp = useSendOtpResetPasswordHook()

  // Check Google Client ID
  useEffect(() => {
    const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID
  }, [])

  // Scroll to top on mount and unmount
  useEffect(() => {
    // Check nếu đã có token thì redirect về dashboard
    const accessToken = Cookies.get('accessToken')
    const refreshToken = Cookies.get('refreshToken')

    if (accessToken && refreshToken) {
      navigate(ROUTES.DASHBOARD, { replace: true })
    }
  }, [navigate])

  useEffect(() => {
    window.scrollTo(0, 0)
    return () => {
      window.scrollTo(0, 0)
    }
  }, [])

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)
    try {
      await login({ email, password })
      toast.success('Đăng nhập thành công!')
    } catch (err: any) {
      // Mở rộng điều kiện check 2FA
      if (
        err?.data?.requiresTwoFA ||
        err?.data?.requires2FA ||
        err?.data?.needsTwoFA ||
        err?.message?.includes('2FA') ||
        err?.message?.includes('xác thực 2 bước') ||
        err?.data?.message?.includes('2FA')
      ) {
        const userIdFromError = err?.data?.userId || err?.userId

        if (userIdFromError) {
          setUserId(userIdFromError)
          setShow2FA(true)
          setError('')
          toast.info('Vui lòng nhập mã xác thực 2 bước')
        } else {
          // Nếu không có userId, có thể backend trả về format khác
          // Thử extract từ email hoặc set một giá trị tạm
          setUserId(email) // Tạm thời dùng email
          setShow2FA(true)
          setError('')
          toast.info('Vui lòng nhập mã xác thực 2 bước')
        }
      } else {
        const errorMessage =
          err?.data?.message ||
          err?.message ||
          'Đăng nhập thất bại. Vui lòng kiểm tra email/mật khẩu.'
        toast.error(errorMessage)
        setError(errorMessage)
      }
    } finally {
      setIsLoading(false)
    }
  }

  // SỬ DỤNG useCallback để tránh re-create function
  const handle2FAVerification = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      setTwoFAError('')
      setIsLoading(true)

      try {
        // Chỉ gửi otpCode, totpCode để trống hoặc null
        const success = await verify2FA(userId, otpCode, '')
        if (success) {
          // Navigation được handle tự động trong hook
          setShow2FA(false)
          setOtpCode('')
        }
      } catch (err: any) {
        const errorMessage = err?.message || 'Xác thực 2FA thất bại'
        setTwoFAError(errorMessage)
      } finally {
        setIsLoading(false)
      }
    },
    [userId, otpCode, verify2FA]
  )

  const handleBack2FA = useCallback(() => {
    setShow2FA(false)
    setOtpCode('')
    setTwoFAError('')
    setUserId('')
  }, [])

  // SỬ DỤNG useCallback cho setOtpCode
  const handleOtpCodeChange = useCallback((value: string) => {
    setOtpCode(value)
  }, [])

  // Google Login với useGoogleLogin để lấy real access token
  const googleLogin = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      setIsLoading(true)
      try {
        await socialLogin({
          provider: 'google',
          accessToken: tokenResponse.access_token,
        })

        toast.success('Đăng nhập bằng Google thành công!')
      } catch (err: any) {
        const errorMessage = err?.message || 'Đăng nhập bằng Google thất bại'
        toast.error(errorMessage)
      } finally {
        setIsLoading(false)
      }
    },
    onError: (error) => {
      toast.error('Không thể kết nối với Google. Vui lòng thử lại.')
    },
    scope:
      'https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile',
  })

  const handleFacebookLogin = async () => {
    toast.info('Chức năng đăng nhập Facebook đang được phát triển')
  }

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setForgotError('')

    if (!validateEmail(forgotEmail)) {
      setForgotError('Email không hợp lệ')
      setIsLoading(false)
      return
    }
    await sendOtp({ target: forgotEmail })
    setForgotSuccess('Mã OTP đã được gửi đến email của bạn')
    setForgotEmail('')
  }

  const handleRegisterClick = () => {
    navigate(ROUTES.REGISTER)
  }

  const toggleShowPassword = () => {
    setShowPassword(!showPassword)
  }

  const toggleForm = () => {
    setIsForgotPassword(!isForgotPassword)
    setError('')
    setForgotError('')
    setForgotSuccess('')
    setEmail('')
    setPassword('')
    setForgotEmail('')
  }

  return (
    <motion.div
      className={styles.container}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className={styles.header}
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <img src={loginIllustration} alt="Logo TAP" className={styles.logo} />
      </motion.div>

      <div className={styles.formContainer}>
        <motion.h3
          className={styles.subTitle}
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4 }}
        >
          {show2FA
            ? 'Xác thực 2 bước'
            : isForgotPassword
              ? 'Đặt lại mật khẩu'
              : 'Đăng nhập để tiếp tục'}
        </motion.h3>

        {show2FA ? (
          <TwoFactorForm
            otpCode={otpCode}
            setOtpCode={handleOtpCodeChange}
            twoFAError={twoFAError}
            isLoading={isLoading}
            onSubmit={handle2FAVerification}
            onBack={handleBack2FA}
          />
        ) : !isForgotPassword ? (
          <>
            <div className={styles.socialButtons}>
              {/* Google Login Button - REAL ACCESS TOKEN */}
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={styles.socialButton}
                onClick={() => googleLogin()}
              >
                <img
                  src={googleLogo}
                  alt="Google Logo"
                  style={{ width: '1.25rem', height: '1.25rem' }}
                />
                Google
              </motion.div>

              {/* Facebook Button - TẠM THỜI */}
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={styles.socialButton}
                style={{ opacity: 0.6, cursor: 'not-allowed' }}
                onClick={handleFacebookLogin}
              >
                <img
                  src={facebookLogo}
                  alt="Facebook Logo"
                  style={{ width: '1.25rem', height: '1.25rem' }}
                />
                Facebook (Soon)
              </motion.div>
            </div>

            <div className={styles.divider}>
              <span className={`${styles.dividerLine} ${styles.left}`} />
              <span className={styles.dividerText}>Hoặc</span>
              <span className={`${styles.dividerLine} ${styles.right}`} />
            </div>

            <Form onSubmit={handleLogin}>
              <FormGroup>
                <Label className={styles.label}>Địa chỉ email hoặc số điện thoại</Label>
                <div className={styles.inputWrapper}>
                  <MailPlus className={styles.inputIcon} />
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    invalid={!!error && error.includes('Email')}
                    required
                    className={styles.input}
                    onFocus={() => setFocusedField('email')}
                    onBlur={() => setFocusedField(null)}
                  />
                </div>
                {error && error.includes('Email') && <FormFeedback>{error}</FormFeedback>}
              </FormGroup>

              <FormGroup>
                <Label className={styles.label}>Mật khẩu</Label>
                <div className={styles.inputWrapper}>
                  <Key className={styles.inputIcon} />
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="********"
                    invalid={!!error && error.includes('mật khẩu')}
                    required
                    className={styles.input}
                    onFocus={() => setFocusedField('password')}
                    onBlur={() => setFocusedField(null)}
                  />
                  <div className={styles.passwordToggle} onClick={toggleShowPassword}>
                    {showPassword ? (
                      <EyeOff style={{ width: '1.25rem', height: '1.25rem' }} />
                    ) : (
                      <Eye style={{ width: '1.25rem', height: '1.25rem' }} />
                    )}
                  </div>
                </div>
                {error && error.includes('mật khẩu') && <FormFeedback>{error}</FormFeedback>}
              </FormGroup>

              <div className={styles.checkboxContainer}>
                <label className={styles.checkbox}>
                  <input
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  />
                  Ghi nhớ tôi
                </label>
                <div className={styles.link} onClick={() => navigate(ROUTES.FORGOT_PASSWORD)}>
                  Quên mật khẩu?
                </div>
              </div>

              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  color="primary"
                  block
                  type="submit"
                  disabled={isLoading}
                  className={styles.submitButton}
                >
                  <LogIn
                    style={{
                      width: '1.25rem',
                      height: '1.25rem',
                      marginRight: '0.5rem',
                      display: 'inline-block',
                    }}
                  />
                  {isLoading ? 'Đang đăng nhập...' : 'Đăng nhập'}
                </Button>
              </motion.div>

              <div className={styles.registerContainer}>
                Chưa có tài khoản?{' '}
                <Link to={ROUTES.REGISTER} className={styles.link} onClick={handleRegisterClick}>
                  Đăng ký
                </Link>
              </div>
            </Form>
          </>
        ) : (
          <Form onSubmit={handleForgotPassword}>
            {forgotSuccess && <div className={styles.successMessage}>{forgotSuccess}</div>}
            <FormGroup>
              <Label className={styles.label}>Địa chỉ email</Label>
              <div className={styles.inputWrapper}>
                <MailPlus className={styles.inputIcon} />
                <Input
                  id="forgotEmail"
                  type="email"
                  value={forgotEmail}
                  onChange={(e) => setForgotEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  invalid={!!forgotError}
                  required
                  className={styles.input}
                  onFocus={() => setFocusedField('forgotEmail')}
                  onBlur={() => setFocusedField(null)}
                />
              </div>
              {forgotError && <FormFeedback>{forgotError}</FormFeedback>}
            </FormGroup>

            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                color="primary"
                block
                type="submit"
                disabled={isLoading}
                className={styles.submitButton}
              >
                <MailPlus
                  style={{
                    width: '1.25rem',
                    height: '1.25rem',
                    marginRight: '0.5rem',
                    display: 'inline-block',
                  }}
                />
                {isLoading ? 'Đang gửi...' : 'Gửi liên kết đặt lại'}
              </Button>
            </motion.div>

            <div className={styles.registerContainer}>
              <div className={styles.link} onClick={toggleForm}>
                Quay lại đăng nhập
              </div>
            </div>
          </Form>
        )}
      </div>
    </motion.div>
  )
}
