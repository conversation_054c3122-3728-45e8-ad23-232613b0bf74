.checkoutContainer {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;

  h1 {
    margin-bottom: 2rem;
    font-size: 2rem;
    font-weight: 600;
    color: #333;
  }
}

// Form validation styling
:global(.ant-form-item-has-error .ant-input) {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
}

:global(.ant-form-item-has-error .ant-input:focus) {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

:global(.ant-form-item-explain-error) {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

// Card styling
:global(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:global(.ant-card-head) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e8e8e8;
}

:global(.ant-card-head-title) {
  font-weight: 600;
  color: #333;
}

// Input styling
:global(.ant-input) {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

:global(.ant-input:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

:global(.ant-input::placeholder) {
  color: #bfbfbf;
}

// Button styling
:global(.ant-btn-primary) {
  background-color: #1890ff;
  border-color: #1890ff;
  border-radius: 6px;
  height: 48px;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s;
}

:global(.ant-btn-primary:hover) {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

:global(.ant-btn-primary:active) {
  background-color: #096dd9;
  border-color: #096dd9;
}
  
  .orderSummary {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .summaryItem {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .itemImage {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
  }
  
  .itemDetails {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  
  .itemName {
    font-weight: 500;
  }
  
  .itemQuantity {
    font-size: 0.9rem;
    color: #888;
  }
  
  .itemPrice {
    font-weight: 600;
  }
  
  .summaryRow {
    display: flex;
    justify-content: space-between;
    font-size: 1rem;
  
    &.discount {
      color: #f5222d;
    }
  
    &.total {
      font-size: 1.25rem;
      font-weight: bold;
    }
  }
  
// Responsive design
@media (max-width: 768px) {
  .checkoutContainer {
    margin: 1rem auto;
    padding: 0 0.5rem;
    
    h1 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }
  }
  
  .summaryItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    
    .itemImage {
      width: 80px;
      height: 80px;
    }
    
    .itemDetails {
      width: 100%;
    }
    
    .itemPrice {
      align-self: flex-end;
    }
  }
  
  .summaryRow {
    font-size: 0.9rem;
    
    &.total {
      font-size: 1.1rem;
    }
  }
  
  // Mobile form styling
  :global(.ant-form-item) {
    margin-bottom: 16px;
  }
  
  :global(.ant-input) {
    font-size: 16px; // Prevent zoom on iOS
  }
  
  :global(.ant-btn-primary) {
    height: 44px;
    font-size: 16px;
  }
}
  