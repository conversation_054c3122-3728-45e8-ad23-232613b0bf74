// src/modules/user/dto/get-user-query.dto.ts
import { ApiPropertyOptional } from '@nestjs/swagger';

export class GetUserQueryDto {
  @ApiPropertyOptional({ description: '<PERSON><PERSON><PERSON> theo role code (VD: R01, R02)' })
  roleCode?: string;

  @ApiPropertyOptional({ description: 'Lọ<PERSON> theo trạng thái (active, locked)' })
  status?: string;

  @ApiPropertyOptional({ description: 'Từ khóa tìm kiếm (SĐT hoặc email)' })
  keyword?: string;

  @ApiPropertyOptional({ description: 'Trang hiện tại', default: 1 })
  page?: number;

  @ApiPropertyOptional({ description: 'Số lượng mỗi trang', default: 20 })
  limit?: number;
}
