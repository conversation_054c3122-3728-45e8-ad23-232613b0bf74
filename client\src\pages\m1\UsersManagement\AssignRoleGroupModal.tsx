import React from 'react'
import { Modal, Select } from 'antd'

const { Option } = Select

interface RoleGroup {
  code: string
  name: string
}

interface AssignRoleGroupModalProps {
  open: boolean
  loading: boolean
  roleGroups: RoleGroup[]
  selectedRoleGroupCode: string
  onChange: (value: string) => void
  onCancel: () => void
  onOk: () => void
}

const AssignRoleGroupModal: React.FC<AssignRoleGroupModalProps> = ({
  open,
  loading,
  roleGroups,
  selectedRoleGroupCode,
  onChange,
  onCancel,
  onOk,
}) => {
  return (
    <Modal
      title="Bổ nhiệm nhóm vai trò"
      open={open}
      onCancel={onCancel}
      onOk={onOk}
      okText="Bổ nhiệm"
      confirmLoading={loading}
    >
      <div style={{ marginBottom: 8, fontWeight: 500 }}>Nhóm vai trò</div>
      <Select
        placeholder="Chọn nhóm vai trò"
        value={selectedRoleGroupCode}
        onChange={onChange}
        style={{ width: '100%' }}
      >
        {roleGroups.map((group) => (
          <Option key={group.code} value={group.code}>
            {group.code} - {group.name}
          </Option>
        ))}
      </Select>
    </Modal>
  )
}

export default AssignRoleGroupModal
