# Step 1: Build app
FROM node:20-alpine as build

# 👉 Cài git tr<PERSON><PERSON><PERSON> <PERSON>hi install
RUN apk add --no-cache git

WORKDIR /app
COPY . .

RUN rm -rf node_modules package-lock.json && \
    npm install --legacy-peer-deps && \
    npm run build

# Step 2: Serve build with nginx
FROM nginx:alpine

COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
