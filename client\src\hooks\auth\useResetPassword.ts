import {
  useCreateRoleMutation,
  useGetAllRoleQuery,
  useGetUserProfileQuery,
  useUpdateProfileMutation,
  type CreateRoleRequest,
  type UpdateProfileProps,
} from '@/services/M1/user'
import { useToast } from '../../components/Toast'
import { useEffect } from 'react'
import { useLoadingStore } from '@/utils/loadingStore'
import { getCookie } from '@/private/cookies'
import {
  useResetPasswordMutation,
  useSendOtpResetPasswordMutation,
  useVerifyOtpPasswordMutation,
  type ResetPasswordRequest,
  type SendOtpProps,
  type VerifyOtpProps,
} from '@/services/M1/auth'

// Dùng cho POST, PUT, DELETE
const useSendOtpResetPasswordHook = () => {
  const { showSuccess, showError } = useToast()
  const [createFunc] = useSendOtpResetPasswordMutation()
  const sendOtp = async (data: SendOtpProps): Promise<boolean> => {
    try {
      const res = await createFunc(data)
      if (!res?.error) {
        showSuccess('Gửi OTP thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Gửi OTP thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Gửi OTP thất bại' + e)
      return false
    }
  }
  return sendOtp
}

const useVerifyOtpPasswordHook = () => {
  const { showSuccess, showError } = useToast()
  const [createFunc] = useVerifyOtpPasswordMutation()
  const create = async (data: VerifyOtpProps): Promise<boolean> => {
    try {
      const res = await createFunc(data)
      if (!res?.error) {
        showSuccess('Xác nhận OTP thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Xác nhận OTP thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Xác nhận OPT thất bại' + e)
      return false
    }
  }
  return create
}

const useResetPasswordHook = () => {
  const { showSuccess, showError } = useToast()
  const [createFunc] = useResetPasswordMutation()
  const create = async (data: ResetPasswordRequest): Promise<boolean> => {
    try {
      const res = await createFunc(data)
      if (!res?.error) {
        showSuccess('Đặt lại mật khẩu thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Đặt lại mật khẩu thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Đặt lại mật khẩu thất bại' + e)
      return false
    }
  }
  return create
}

// Xuất ra
export { useSendOtpResetPasswordHook, useVerifyOtpPasswordHook, useResetPasswordHook }
