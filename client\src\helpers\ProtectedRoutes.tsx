// // File: helpers/ProtectedRoutes.tsx

// import { Route, Navigate, Routes, useLocation } from 'react-router-dom'
// import { useEffect, useState } from 'react'
// import { useSelector } from 'react-redux'
// import Cookies from 'js-cookie'
// import { privateRoutes, publicRoutes } from '@/routes'
// import { configAuthorise, ROUTES } from '@/constants/routes'
// import NullLayout from '@/layouts/NullLayout'
// import NotFound from '@/pages/Error/Notfound'
// import { checkPermission } from './permission'
// import { baseRedirect } from '@/constants/sidebarItems'
// import type { PrivateRouteProps } from '@/types/components/route'
// import type { RootState } from '@/libs/state/store'

// const ProtectedRoutes = ({ isAuthenticated }: PrivateRouteProps) => {
//   const pathName = useLocation().pathname
//   const [shouldRender, setShouldRender] = useState<boolean>(true)

//   const authUser = useSelector((state: RootState) => state.auth)

//   const isRouteProtected = (path: string) =>
//     configAuthorise.matcher.some((pattern) =>
//       new RegExp(pattern.replace(':path*', '.*')).test(path)
//     )

//   useEffect(() => {
//     setShouldRender(true)
//   }, [isAuthenticated, authUser.permissions, pathName])

//   const isBaseRoute = () => pathName === '/' || pathName === ROUTES.HOME

//   const hasAccept = (): boolean => {
//     const isAuthen = !!Cookies.get('accessToken') && !!Cookies.get('refreshToken')
//     const hasPerm = authUser?.permissions?.length > 0
//     return isAuthen && hasPerm && isBaseRoute()
//   }

//   const getRedirectPath = (): string => {
//     if (authUser.role?.role_code) {
//       return baseRedirect[authUser.role.role_code] || ROUTES.HOME
//     }
//     return ROUTES.HOME
//   }

//   return (
//     <Routes>
//       {/* Base path handling */}
//       {isBaseRoute() && (
//         <Route
//           path={pathName}
//           element={
//             hasAccept() ? <Navigate to={getRedirectPath()} /> : <Navigate to={ROUTES.HOME} />
//           }
//         />
//       )}

//       {/* Protected routes with permission check */}
//       {privateRoutes.map((route, index) => {
//         const Page = route.component
//         const Layout = route.layout ?? NullLayout
//         const isAllowPermission = route.hasPermission ?? true

//         // Redirect to login if not authenticated and route is protected
//         if (isRouteProtected(route.path) && !isAuthenticated) {
//           return <Route key={index} path={route.path} element={<Navigate to={ROUTES.LOGIN} />} />
//         }

//         // Check permission if required
//         const hasPermission =
//           !route.requiredPermissions ||
//           checkPermission(authUser.permissions, route.requiredPermissions)

//         if (hasPermission && isAllowPermission) {
//           return (
//             <Route
//               key={index}
//               path={route.path}
//               element={
//                 <Layout>
//                   <Page />
//                 </Layout>
//               }
//             />
//           )
//         } else {
//           return (
//             <Route key={index} path={route.path} element={<Navigate to={ROUTES.UNAUTHORIZE} />} />
//           )
//         }
//       })}

//       {/* Public routes */}
//       {shouldRender &&
//         publicRoutes.map((route, index) => {
//           const Page = route.component
//           const Layout = route.layout ?? NullLayout
//           return (
//             <Route
//               key={index}
//               path={route.path}
//               element={
//                 <Layout>
//                   <Page />
//                 </Layout>
//               }
//             />
//           )
//         })}

//       <Route path="*" element={<NotFound />} />
//     </Routes>
//   )
// }

// export default ProtectedRoutes

// File: helpers/ProtectedRoutes.tsx

import type { ReactNode } from 'react'
import { Navigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { checkPermission } from '@/helpers/permission'
import { ROUTES } from '@/constants/routes'
import type { RootState } from '@/libs/state/store'

type PermissionGuardProps = {
  children: ReactNode
  requiredPermissions?: string[]
}

export const PermissionGuard = ({ children, requiredPermissions = [] }: PermissionGuardProps) => {
  console.log('PermissionGuard rendered with permissions:', requiredPermissions)
  const auth = useSelector((state: RootState) => state.auth)
  const isAuthenticated = !!auth?.id && !!auth?.permissions?.length

  // Nếu không có requiredPermissions (route public), không kiểm tra đăng nhập
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return <>{children}</>
  }

  const hasPermission = checkPermission(
    auth?.permissions || [],
    requiredPermissions,
    auth?.role?.role_code
  )

  if (!isAuthenticated) return <Navigate to={ROUTES.LOGIN} replace />
  if (!hasPermission) return <Navigate to={ROUTES.UNAUTHORIZE} replace />

  return <>{children}</>
}
