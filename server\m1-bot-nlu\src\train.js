// Simple NLU training script (rule-based, intent/entity)
const fs = require('fs');
const path = require('path');

const dataPath = path.join(__dirname, 'training_data.json');
const outPath = path.join(__dirname, 'model.json');

const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));

// Convert corpus format to model format
const model = { intents: {}, entities: {} };

// Entities
if (Array.isArray(data.entities)) {
  for (const ent of data.entities) {
    model.entities[ent.name] = { type: ent.type, pattern: ent.regex };
  }
}

// Intents
if (data.intents && typeof data.intents === 'object') {
  for (const [intent, def] of Object.entries(data.intents)) {
    model.intents[intent] = {
      utterances: Array.isArray(def.utterances) ? def.utterances : [],
      answers: Array.isArray(def.answers) ? def.answers : [],
      suggestedReplies: Array.isArray(def.suggestedReplies) ? def.suggestedReplies : [],
    };
  }
}

fs.writeFileSync(outPath, JSON.stringify(model, null, 2));
