import { useEffect } from 'react'
import { Form, Input, Button, DatePicker, Select, Typography, Divider, Space, Card } from 'antd'
import type { FormInstance } from 'antd/es/form'
import { EditOutlined } from '@ant-design/icons'
import { normalizeDateForAntD } from '@/utils/formatDate'
import dayjs from 'dayjs'
import { useUpdateUserProfileHook } from '@/hooks/user'
import { Col, Row } from 'reactstrap'

const { Title } = Typography
const { Option } = Select

interface Props {
  form: FormInstance
  isEditing: boolean
  setIsEditing: (val: boolean) => void
  userData: any
  showOtpEmail: boolean
  setShowOtpEmail: (val: boolean) => void
}

const ProfileUpdateForm = ({
  form,
  isEditing,
  setIsEditing,
  userData,
  showOtpEmail,
  setShowOtpEmail,
}: Props) => {
  const update = useUpdateUserProfileHook()

  const handleSave = async () => {
    try {
      const values = await form.validateFields()
      // Gọi mutation cập nhật hồ sơ ở đây nếu muốn
      const res = await update(values)
      if (res) setIsEditing(false)
    } catch (err) {}
  }

  useEffect(() => {
    if (userData && Object.keys(userData).length > 0) {
      const dob = normalizeDateForAntD(userData.dateOfBirth)

      form.setFieldsValue({
        ...userData,
        dateOfBirth: dob,
      })
    }
  }, [userData, form])

  return (
    <Card>
      <Title level={3}>Hồ sơ cá nhân</Title>
      <Form form={form} layout="vertical">
        <Row>
          <Col xs={12} md={6}>
            <Form.Item
              label="Họ và tên"
              name="fullName"
              rules={[{ required: true, message: 'Vui lòng nhập họ và tên' }]}
            >
              <Input disabled={!isEditing} />
            </Form.Item>
          </Col>
          <Col xs={12} md={6}>
            <Form.Item
              label="CCCD/CMND"
              name="citizenId"
              rules={[{ required: true, message: 'Vui lòng nhập số CCCD/CMND' }]}
            >
              <Input disabled={!isEditing} />
            </Form.Item>
          </Col>
          <Col xs={12} md={6}>
            <Form.Item
              label="Ngày sinh"
              name="dateOfBirth"
              rules={[{ required: true, message: 'Vui lòng chọn ngày sinh' }]}
              getValueProps={(value) => ({
                value: normalizeDateForAntD(value),
              })}
            >
              <DatePicker
                format="DD/MM/YYYY"
                size="large"
                disabled={!isEditing}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col xs={12} md={6}>
            <Form.Item
              label="Giới tính"
              name="gender"
              rules={[{ required: true, message: 'Vui lòng nhập giới tính' }]}
            >
              <Select size="large" disabled={!isEditing}>
                <Option value="Nam">Nam</Option>
                <Option value="Nữ">Nữ</Option>
                <Option value="Khác">Khác</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col xs={12} md={6}>
            <Form.Item
              label="Số điện thoại"
              name="phone"
              rules={[{ required: true, message: 'Vui lòng nhập số điện thoại' }]}
            >
              <Input disabled={!isEditing} />
            </Form.Item>
          </Col>
          <Col sx={12} md={6}>
            <Form.Item
              label="Email"
              name="email"
              rules={[
                { type: 'email', message: 'Email không hợp lệ' },
                { required: true, message: 'Vui lòng nhập email' },
              ]}
            >
              <Input disabled={!isEditing} />
              {isEditing && (
                <Button
                  type="link"
                  onClick={() => setShowOtpEmail(true)}
                  style={{ paddingLeft: 0 }}
                >
                  Xác thực email
                </Button>
              )}
            </Form.Item>
          </Col>
          <Col sx={12} md={12}>
            <Form.Item
              label="Địa chỉ"
              name="address"
              rules={[{ required: true, message: 'Vui lòng nhập đầy đủ địa chỉ' }]}
            >
              <Input
                placeholder="Số nhà, tên đường"
                size="large"
                disabled={!isEditing}
                // onFocus={() => setFocusedField('address')}
                // onBlur={() => setFocusedField(null)}
                onChange={(e) => {}}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <Divider />
      <Space>
        {isEditing ? (
          <Button type="primary" onClick={handleSave} size="large">
            Lưu thay đổi
          </Button>
        ) : (
          <Button
            type="primary"
            onClick={() => setIsEditing(true)}
            icon={<EditOutlined />}
            size="large"
          >
            Chỉnh sửa
          </Button>
        )}
      </Space>
    </Card>
  )
}

export default ProfileUpdateForm
