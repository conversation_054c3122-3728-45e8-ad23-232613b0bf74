import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

export const inventoryShopApi = createApi({
  reducerPath: 'inventoryShopApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['InventoryShop', 'StockHistory', 'Alerts'],

  endpoints: (build) => ({
    // ✅ Lấy danh sách sản phẩm tồn kho của shop
    getShopInventory: build.query<any, any>({
      query: (params) => ({
        url: '/inventory', // Sử dụng endpoint hiện tại, server sẽ filter theo shop
        method: 'GET',
        params: params,
        authRequired: true,
      }),
      providesTags: ['InventoryShop'],
    }),

    // ✅ Lấy chi tiết sản phẩm tồn kho của shop
    getShopInventoryDetail: build.query<any, string>({
      query: (id: string) => ({
        url: `/inventory/${id}`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['InventoryShop'],
    }),

    // ✅ Cập nhật tồn kho cho từng biến thể của shop
    updateShopVariants: build.mutation<any, { id: string; variants: any[] }>({
      query: ({ id, variants }) => ({
        url: `/inventory/${id}/variants`, // Sử dụng endpoint hiện tại
        method: 'PUT',
        data: variants,
        authRequired: true,
      }),
      invalidatesTags: ['InventoryShop'],
    }),

    // ✅ Cập nhật nhanh tồn kho (tăng/giảm số lượng) của shop
    quickUpdateShopStock: build.mutation<any, { id: string; variantId: string; change: number }>({
      query: ({ id, variantId, change }) => ({
        url: `/inventory/${id}/variant/${variantId}/stock`, // Sử dụng endpoint hiện tại
        method: 'PATCH',
        data: { change },
        authRequired: true,
      }),
      invalidatesTags: ['InventoryShop'],
    }),

    // ✅ Lấy lịch sử nhập/xuất kho của shop
    getShopInventoryHistory: build.query<any, string>({
      query: (id: string) => ({
        url: `/inventory/${id}/history`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['StockHistory'],
    }),

    // ✅ Import tồn kho từ file Excel cho shop
    importShopInventory: build.mutation<any, FormData>({
      query: (formData: FormData) => ({
        url: '/inventory/import', // Sử dụng endpoint hiện tại
        method: 'POST',
        data: formData,
        authRequired: true,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }),
      invalidatesTags: ['InventoryShop'],
    }),

    // ✅ Export tồn kho ra file Excel cho shop
    exportShopInventory: build.query<any, any>({
      query: (params) => ({
        url: '/inventory/export', // Sử dụng endpoint hiện tại
        method: 'GET',
        params: params,
        authRequired: true,
      }),
      providesTags: ['InventoryShop'],
    }),

    // ✅ Lấy danh sách cảnh báo hết hàng/sắp hết hàng của shop
    getShopAlerts: build.query<any, void>({
      query: () => ({
        url: '/inventory/alerts', // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['Alerts'],
    }),
  }),
})

export const {
  useGetShopInventoryQuery,
  useGetShopInventoryDetailQuery,
  useUpdateShopVariantsMutation,
  useQuickUpdateShopStockMutation,
  useGetShopInventoryHistoryQuery,
  useImportShopInventoryMutation,
  useExportShopInventoryQuery,
  useGetShopAlertsQuery,
} = inventoryShopApi
