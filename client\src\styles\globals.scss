@import 'variables';

body {
  margin: 0;
  font-family: $font-main;
  background-color: $white;
  color: $text;
}

.device-large-desktop {
  background-color: $white;
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  
  .div {
    background-color: $white;
    width: 1920px;
    height: 3497px;
    position: relative;
  }
}

// Global button size consistency - Touch-friendly
.ant-btn {
  &.ant-btn-sm {
    height: 44px !important; // Minimum touch target
    padding: 12px 16px !important;
    font-size: 14px !important;
    min-width: 80px !important;
    line-height: 1.2 !important;
  }

  &.ant-btn-default {
    height: 44px !important;
    min-width: 80px !important;
    padding: 12px 16px !important;
  }

  &.ant-btn-primary {
    height: 44px !important;
    min-width: 80px !important;
    padding: 12px 16px !important;
  }

  &.ant-btn-dangerous {
    height: 44px !important;
    min-width: 80px !important;
    padding: 12px 16px !important;
  }

  &.ant-btn-lg {
    height: 48px !important;
    padding: 14px 20px !important;
    font-size: 16px !important;
    min-width: 100px !important;
  }
}

// Mobile-specific button adjustments
@media (max-width: 768px) {
  .ant-btn {
    min-height: 44px !important;

    &.ant-btn-sm {
      height: 44px !important;
      padding: 12px 16px !important;
      min-width: 80px !important;
    }
  }
}

// Global form input consistency - Touch-friendly
.ant-input {
  min-height: 44px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;

  &:focus {
    border-color: #4CAF50 !important;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2) !important;
  }

  &::placeholder {
    color: #bfbfbf !important;
  }

  @media (max-width: 768px) {
    font-size: 16px !important; // Prevent zoom on iOS
    min-height: 44px !important;
  }
}

.ant-input-affix-wrapper {
  min-height: 44px !important;
  padding: 0 16px !important;
  border-radius: 8px !important;

  .ant-input {
    padding: 12px 0 !important;
  }

  &:focus,
  &.ant-input-affix-wrapper-focused {
    border-color: #4CAF50 !important;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2) !important;
  }
}

.ant-input-password {
  min-height: 44px !important;
  border-radius: 8px !important;

  .ant-input {
    min-height: auto !important;
  }
}

// Form labels
.ant-form-item-label > label {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #333 !important;
}

// Form validation
.ant-form-item-has-error {
  .ant-input,
  .ant-input-affix-wrapper {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
  }
}

// Global table responsive styles
.ant-table-wrapper {
  @media (max-width: 768px) {
    .ant-table {
      font-size: 12px !important;
      table-layout: auto !important; // Prevent fixed layout issues
    }

    .ant-table-thead > tr > th {
      padding: 8px 4px !important;
      font-size: 11px !important;
    }

    .ant-table-tbody > tr > td {
      padding: 8px 4px !important;
      font-size: 12px !important;
      word-break: keep-all !important;
      white-space: nowrap !important;
      overflow-wrap: normal !important;
      text-overflow: ellipsis !important;

      // Strong override for mobile cards
      div[class*="mobileUserCard"],
      div[class*="mobileProductCard"],
      div[class*="mobileCategoryCard"] {
        word-break: keep-all !important;
        white-space: normal !important;
        overflow: visible !important;
        display: block !important;
        width: 100% !important;

        * {
          word-break: keep-all !important;
          white-space: normal !important;
          display: inline-block !important;
          width: auto !important;
        }

        div, span, p {
          display: block !important;
          white-space: normal !important;
        }
      }
    }

    .ant-table-pagination {
      .ant-pagination-item,
      .ant-pagination-prev,
      .ant-pagination-next {
        min-width: 28px !important;
        height: 28px !important;
        line-height: 26px !important;
      }

      .ant-pagination-options {
        display: none !important;
      }

      .ant-pagination-total-text {
        font-size: 12px !important;
      }
    }
  }

  @media (max-width: 480px) {
    .ant-table {
      font-size: 11px !important;
      table-layout: auto !important; // Prevent fixed layout issues
    }

    .ant-table-thead > tr > th {
      padding: 6px 2px !important;
      font-size: 10px !important;
    }

    .ant-table-tbody > tr > td {
      padding: 6px 2px !important;
      font-size: 11px !important;
      word-break: keep-all !important;
      white-space: nowrap !important;
      overflow-wrap: normal !important;
      text-overflow: ellipsis !important;

      // Strong override for mobile cards
      div[class*="mobileUserCard"],
      div[class*="mobileProductCard"],
      div[class*="mobileCategoryCard"] {
        word-break: keep-all !important;
        white-space: normal !important;
        overflow: visible !important;
        display: block !important;
        width: 100% !important;

        * {
          word-break: keep-all !important;
          white-space: normal !important;
          display: inline-block !important;
          width: auto !important;
        }

        div, span, p {
          display: block !important;
          white-space: normal !important;
        }
      }
    }

    .ant-table-pagination {
      .ant-pagination-item,
      .ant-pagination-prev,
      .ant-pagination-next {
        min-width: 24px !important;
        height: 24px !important;
        line-height: 22px !important;
        font-size: 10px !important;
      }

      .ant-pagination-jump-prev,
      .ant-pagination-jump-next {
        display: none !important;
      }
    }
  }
}