import { Controller, Get, Req, UseGuards, Query, Param, ParseUUIDPipe } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ReferralSummaryDto } from '../dto/user-affiliate.dto';
import { UserAffiliateService } from '../services/user_affiliate.service';

@ApiBearerAuth('access-token')
@ApiTags('Affiliate')
@UseGuards(AuthGuard('jwt'))
@Controller('users/affiliate')
export class UserAffiliateController {
  constructor(private readonly affiliateService: UserAffiliateService) {}

  /**
   * 📌 Lấy mã giới thiệu và thông tin hệ thống của người dùng hiện tại
   */
  @Get('me')
  @ApiOperation({ summary: 'L<PERSON>y mã giới thiệu của người dùng hiện tại' })
  getMyReferralInfo(@Req() req: Request & { user?: any }) {
    return this.affiliateService.getMyReferralInfo(req.user.userId);
  }

  /**
   * 🌳 Cây hệ thống của người dùng hiện tại
   */
  @Get('tree')
  @ApiOperation({ summary: 'Lấy cây hệ thống của người dùng hiện tại' })
  getAffiliateTree(@Req() req: Request & { user?: any }) {
    return this.affiliateService.getAffiliateTree(req.user.userId);
  }

  /**
   * 📊 Thống kê tổng quan cây hệ thống
   */
  @Get('stats')
  @ApiOperation({ summary: 'Thống kê tổng quan hệ thống giới thiệu' })
  getAffiliateStats(@Req() req: Request & { user?: any }) {
    return this.affiliateService.getAffiliateStats(req.user.userId);
  }

  /**
   * 📃 Danh sách người được mời trực tiếp (F1)
   */
  @Get('direct')
  @ApiOperation({ summary: 'Danh sách người được mời trực tiếp (F1)' })
  getDirectInvitees(@Req() req: Request & { user?: any }) {
    return this.affiliateService.getDirectInvitees(req.user.userId);
  }

  /**
   * 📃 Danh sách người được mời gián tiếp theo cấp (F2, F3...)
   */
  @Get('indirect')
  @ApiOperation({ summary: 'Danh sách người được mời gián tiếp theo cấp độ (F2, F3...)' })
  getIndirectInvitees(@Req() req: Request & { user?: any }, @Query('level') level: number) {
    return this.affiliateService.getIndirectInvitees(req.user.userId, level);
  }

  /**
   * 📦 Lấy thống kê tổng hợp số người F1 và toàn hệ thống của một user (theo UUID)
   */
  @Get(':userId/summary')
  @ApiOperation({ summary: 'Lấy thống kê hệ thống F1 và toàn bộ hệ thống theo UUID user' })
  @ApiParam({ name: 'userId', required: true, description: 'UUID của user' })
  async getSummary(@Param('userId', ParseUUIDPipe) userId: string): Promise<ReferralSummaryDto> {
    return this.affiliateService.getReferralSummary(userId);
  }
}
