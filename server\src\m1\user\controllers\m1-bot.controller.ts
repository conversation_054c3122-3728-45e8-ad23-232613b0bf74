import { Controller, Post, Body } from '@nestjs/common';
import { M1BotService } from '../services/m1-bot.service';
import { AllowAnonymous } from 'src/common/decorators/allow-anonymous.decorator';
import { UserSupportRequestDto } from '../dto/user-support-request.dto';
import { ApiTags, ApiBody, ApiOperation } from '@nestjs/swagger';

@ApiTags('M1Bot')
@Controller('m1-bot')
@AllowAnonymous()
export class M1BotController {
  constructor(private readonly m1BotService: M1BotService) {}

  /**
   * Nhận text và trả về intent/entities, không cần đăng nhập
   */
  @Post('analyze')
  @ApiOperation({ summary: 'Phân tích ý định/ngữ nghĩa từ texBot, không cần đăng nhập' })
  @ApiBody({ type: UserSupportRequestDto })
  async analyze(@Body() requestBody: UserSupportRequestDto) {
    try {
      return this.m1BotService.analyze(requestBody.text);
    } catch (error) {
      console.error('Error analyzing text:', error);
      throw error;
    }
  }
}
