import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSIONS_KEY } from '../decorators/permissions.decorator'; // <PERSON><PERSON><PERSON> bảo đúng path

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const req = context.switchToHttp().getRequest();
    const user = req.user;

    if (!requiredPermissions?.length) return true;
    if (!user?.permissions) return false;

    // Admin role (R05) has full access to all routes
    if (user?.role?.role_code === 'R05') {
      return true;
    }

    // Users with admin full access permission have access to all routes
    if (user.permissions.includes('PERM_ADMIN_FULL_ACCESS')) {
      return true;
    }

    return requiredPermissions.every((p) => user.permissions.includes(p));
  }
}
