import {
  useAddToCartMutation,
  useUpdateCartQuantityMutation,
  useDeleteCartItemMutation,
  useClearCartMutation,
  useGetCartQuery,
  // ✅ BƯỚC 1: Import hook mới từ service
  useCheckInventoryMutation,
  useFetchCartMutation,
  type AddToCartRequest,
  type UpdateCartQuantityRequest,
  type DeleteCartItemRequest,
} from '@/services/M2/CartManagement/cart'; // Thay bằng đường dẫn thực tế của cartApi

import { useToast } from '../../components/Toast';
import { useCallback, useEffect, useState } from 'react';
import { useLoadingStore } from '@/utils/loadingStore';

// Hook cho GET: Lấy giỏ hàng
const useGetCartHook = () => {
  const { data, error, isLoading, refetch, isFetching } = useGetCartQuery(undefined, {
    refetchOnMountOrArgChange: true,
    skip: false,
    refetchOnReconnect: true,
  });

  const { setLoading } = useLoadingStore();

  useEffect(() => {
    setLoading(isLoading || isFetching);
  }, [isLoading, isFetching, setLoading]);

  return {
    data,
    isLoading,
    error,
    refetch,
  };
};

// Hook cho GET: Lấy giỏ hàng (lazy load - chỉ gọi khi cần)
const useGetCartLazyHook = () => {
  const [cartData, setCartData] = useState<any>(null);
  const [fetchCart, { isLoading }] = useFetchCartMutation();
  const { setLoading } = useLoadingStore();

  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading, setLoading]);

  // Hàm để fetch cart data
  const refetch = useCallback(async () => {
    try {
      const result = await fetchCart().unwrap();
      setCartData(result);
      return result;
    } catch (error) {
      console.error('Error fetching cart:', error);
      throw error;
    }
  }, [fetchCart]);

  return {
    data: cartData,
    isLoading,
    error: null, // Có thể cải thiện error handling sau
    refetch,
  };
};

// Hook cho POST: Thêm vào giỏ hàng
const useAddToCartHook = () => {
  const { showSuccess, showError } = useToast();
  const [addFunc] = useAddToCartMutation();
  const add = async (data: AddToCartRequest): Promise<boolean> => {
    try {
      const res = await addFunc(data);
      
      if (!res?.error) {
        showSuccess('Thêm vào giỏ hàng thành công');
        return true;
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message;
        if (messageDes) {
          showError(messageDes);
        } else {
          showError('Thêm vào giỏ hàng thất bại');
        }
        return false;
      }
    } catch (e: any) {
      if (e?.response?.data?.message) {
        showError(e.response.data.message);
      } else {
        showError('Thêm vào giỏ hàng thất bại');
      }
      return false;
    }
  };
  return add;
};

// Hook cho POST: Cập nhật số lượng
const useUpdateCartQuantityHook = () => {
  const { showSuccess, showError } = useToast();
  const [updateFunc] = useUpdateCartQuantityMutation();
  const update = async (data: UpdateCartQuantityRequest): Promise<boolean> => {
    try {
      const res = await updateFunc(data);
      if (!res?.error) {
        // Cập nhật số lượng thường không cần thông báo để tránh làm phiền người dùng
        // showSuccess('Cập nhật số lượng thành công');
        return true;
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message;
        showError('Cập nhật số lượng thất bại ' + messageDes);
        return false;
      }
    } catch (e) {
      showError('Cập nhật số lượng thất bại ' + e);
      return false;
    }
  };
  return update;
};

// Hook cho DELETE: Xóa một item
const useDeleteCartItemHook = () => {
  const { showSuccess, showError } = useToast();
  const [deleteFunc] = useDeleteCartItemMutation();
  const remove = async (data: DeleteCartItemRequest): Promise<boolean> => {
    try {
      const res = await deleteFunc(data);
      if (!res?.error) {
        showSuccess('Xóa sản phẩm thành công');
        return true;
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message;
        showError('Xóa sản phẩm thất bại ' + messageDes);
        return false;
      }
    } catch (e) {
      showError('Xóa sản phẩm thất bại ' + e);
      return false;
    }
  };
  return remove;
};

// Hook cho POST: Xóa toàn bộ giỏ
const useClearCartHook = () => {
  const { showSuccess, showError } = useToast();
  const [clearFunc] = useClearCartMutation();
  const clear = async (): Promise<boolean> => {
    try {
      const res = await clearFunc();
      if (!res?.error) {
        showSuccess('Xóa giỏ hàng thành công');
        return true;
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message;
        showError('Xóa giỏ hàng thất bại ' + messageDes);
        return false;
      }
    } catch (e) {
      showError('Xóa giỏ hàng thất bại ' + e);
      return false;
    }
  };
  return clear;
};

// ✅ BƯỚC 2: Thêm custom hook cho API kiểm tra tồn kho
const useCheckInventoryHook = () => {
  const { showError } = useToast();
  const [checkFunc] = useCheckInventoryMutation();

  // ✅ 2. Bọc hàm `check` trong `useCallback`
  const check = useCallback(async (): Promise<boolean> => {
    try {
      const response = await checkFunc().unwrap();

      if (response && Array.isArray(response.data)) {
        const allItemsAreSufficient = response.data.every(item => item.enough === true);
        return allItemsAreSufficient;
      }
      
      return true;
    } catch (error: any) {
      return true; // Trả về true để không chặn luồng thanh toán khi API lỗi
    }
  }, [checkFunc]); // ✅ 3. Thêm dependency cho useCallback

  return check; // Trả về hàm đã được ghi nhớ (memoized)
};

// ✅ BƯỚC 3: Export hook mới
export {
  useGetCartHook,
  useGetCartLazyHook,
  useAddToCartHook,
  useUpdateCartQuantityHook,
  useDeleteCartItemHook,
  useClearCartHook,
  useCheckInventoryHook,
};
