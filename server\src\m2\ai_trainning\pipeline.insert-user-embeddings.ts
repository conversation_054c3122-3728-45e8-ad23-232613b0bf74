// Script: Insert user_behavior_vectors.json vào bảng user_vector_embedding (PostgreSQL)
// Chạy: npx ts-node src/m2/ai_trainning/pipeline.insert-user-embeddings.ts
import { AppDataSource } from '../../data-source';
import * as fs from 'fs';
import * as path from 'path';

interface UserVector {
  user_id: string;
  embedding: number[];
}

async function main() {
  await AppDataSource.initialize();
  const raw = fs.readFileSync(path.join(__dirname, 'user_behavior_vectors.json'), 'utf8');
  const vectors: UserVector[] = JSON.parse(raw);

  // Gom vector theo user_id (lấy trung bình nếu có nhiều vector cho 1 user)
  const userVectors: Record<string, number[][]> = {};
  for (const v of vectors) {
    if (!userVectors[v.user_id]) userVectors[v.user_id] = [];
    userVectors[v.user_id].push(v.embedding);
  }

  // T<PERSON>h trung bình vector cho mỗi user
  const avgVectors = Object.entries(userVectors).map(([user_id, vecs]) => {
    const dim = vecs[0].length;
    const avg = Array(dim).fill(0);
    for (const v of vecs) for (let i = 0; i < dim; ++i) avg[i] += v[i];
    for (let i = 0; i < dim; ++i) avg[i] /= vecs.length;
    return { user_id, vector: avg };
  });

  // Insert/update vào bảng user_vector_embedding
  for (const { user_id, vector } of avgVectors) {
    await AppDataSource.query(
      `INSERT INTO user_vector_embedding (user_id, vector, updated_at)
       VALUES ($1, $2, now())
       ON CONFLICT (user_id) DO UPDATE SET vector = EXCLUDED.vector, updated_at = now()`,
      [user_id, vector],
    );
  }
  console.log('Đã insert/update user_vector_embedding thành công!');
  await AppDataSource.destroy();
}

main().catch(console.error);
