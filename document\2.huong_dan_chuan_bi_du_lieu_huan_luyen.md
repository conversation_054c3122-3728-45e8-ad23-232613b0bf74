# HƯỚNG DẪN CHUẨN BỊ DỮ LIỆU ĐÀO TẠO AI – TÌM KIẾM SẢN PHẨM BẰNG HÌNH ẢNH

**Mụ<PERSON> đích:**

- Hướng dẫn chi tiết để nhiều thành viên trong nhóm cùng chuẩn bị và tổ chức dữ liệu hình ảnh trước khi huấn luyện mô hình AI (fine-tune CLIP hoặc metric-learning).
- Đảm bảo dữ liệu đầu vào đồng nhất, đủ chất lượ<PERSON>, dễ kiểm soát và có thể mở rộng.

---

## 1. Phạm vi và vai trò

| Thành viên            | Trách nhiệm chính                                                       |
| --------------------- | ----------------------------------------------------------------------- |
| **Quản trị dự án**    | <PERSON><PERSON> duyệt kế hoạch, theo dõi tiến độ và chất lượng dữ liệu.             |
| **Nhóm thu thập ảnh** | Tìm kiếm, chụp, tải xuống ảnh phù hợp với danh mục sản phẩm.            |
| **Nhóm chuẩn hóa**    | Chỉnh sửa kích thước, định dạng, đặt tên file theo quy chuẩn.           |
| **Nhóm gán nhãn**     | Phân loại và tổ chức thư mục theo `product_id` để tạo tập train/val.    |
| **Nhóm kiểm duyệt**   | Kiểm tra chất lượng ảnh, loại bỏ ảnh lỗi, trùng lặp, không đạt yêu cầu. |
| **Nhóm dữ liệu AI**   | Chuẩn bị dataset: thu thập, phân tích yêu cầu, chuẩn hóa, gán nhãn.     |
| **Developer (Dev)**   | Tích hợp script, API và pipeline AI; không trực tiếp chuẩn bị dataset.  |

---

## 2. Cấu trúc thư mục dữ liệu

```
ml_clip_module/
└─ data/
   ├─ train/
   │   ├─ product_001/
   │   │   ├─ img1.jpg
   │   │   ├─ img2.jpg
   │   │   └─ ...
   │   ├─ product_002/
   │   │   └─ ...
   │   └─ ...
   └─ val/
       ├─ product_001/
       ├─ product_002/
       └─ ...
```

- Mỗi `product_xxx` tương ứng một mã sản phẩm duy nhất (Product ID).
- Train: ≥5–10 ảnh/folder; Val: ≥2–3 ảnh/folder.

---

## 3. Quy ước đặt tên và định dạng

1. **Định dạng ảnh:** JPEG hoặc PNG.
2. **Kích thước ảnh:** 224×224 px (tối thiểu) đến 1024×1024 px (tối đa).
3. **Tên file:** `productId_XX.jpg` (ví dụ: `P0001_01.jpg`).
4. **Nền & bố cục:** Nền trắng hoặc đồng nhất; sản phẩm nằm chính giữa.

---

## 4. Quy trình thu thập & chuẩn hóa

1. **Thu thập ảnh gốc:**
   - Chụp tại kho/HTX hoặc lấy từ nguồn tin cậy.
   - Đảm bảo ánh sáng đủ, tránh bóng đổ.
2. **Lưu tạm:**
   - Upload vào folder `raw_images/`.
3. **Chuẩn hóa ảnh:**
   - Resize, crop, cân chỉnh màu (ImageMagick/Photoshop/script).
   - Loại bỏ watermark/logo không liên quan.
4. **Phân chia:**
   - Đổi tên theo quy ước.
   - Chuyển vào `data/train/product_xxx/` hoặc `data/val/product_xxx/`.

---

## 5. Hướng dẫn gán nhãn (dành cho train)

- Mỗi folder chứa ảnh của đúng một sản phẩm.
- Loại bỏ ảnh thiếu góc nhìn, mờ, rung.
- Tránh trộn sản phẩm khác trong cùng folder.

---

## 6. Tiêu chí kiểm duyệt chất lượng

| Tiêu chí         | Yêu cầu                      | Hành động                    |
| ---------------- | ---------------------------- | ---------------------------- |
| **Số lượng**     | ≥5 ảnh/train, ≥2 ảnh/val     | Kiểm tra & bổ sung ảnh       |
| **Độ phân giải** | ≥224×224, ≤1024×1024         | Resize hoặc loại bỏ          |
| **Chất lượng**   | Rõ nét, không mờ, không rung | Di chuyển ảnh xấu sang `bad` |
| **Độ đồng nhất** | Góc chụp tương đồng          | Kiểm tra outlier             |

---

## 7. Giao nộp & chuẩn bị cho dev

1. Hoàn thành folder `data/` với `train/` và `val/`.
2. Nén data: `zip ml_clip_module/data`.
3. Upload lên Drive/SharePoint hoặc storage chung.
4. Thông báo trong kênh chat kèm checklist.

---

## 8. Checklist kiểm tra trước khi bàn giao

-

---

## 9. Tạo và lưu chỉ mục FAISS từ database hoặc qua FAISS service

### A. Build index qua API FAISS service

1. **Main service** truy xuất vectors: `SELECT product_id, vector FROM image_vectors`.
2. **POST JSON** về FAISS service `/faiss/build`.
3. **FAISS service** build & lưu file `faiss_products.index`.

### B. Build index cục bộ rồi upload

1. Main service load vectors & build index (faiss-node/Python).
2. Upload file index lên storage (S3/NFS).

---

## 10. Yêu cầu dataset cho TMĐT

1. **Đa dạng ngành hàng:** ≥10–20 nhóm, mỗi nhóm ≥50–100 sản phẩm.
2. **Số ảnh:** Train 10–20 ảnh/SP; Val 3–5 ảnh/SP.
3. **Góc & bối cảnh:** Studio & thực tế, nhiều góc độ.
4. **Ánh sáng:** Tự nhiên & nhân tạo, có nhiễu.
5. **Định dạng:** JPEG/PNG, ≥224×224 px.
6. **Metadata:** product\_id, tên, nhãn đặc tính.
7. **Ảnh tiêu cực:** Thêm ảnh không phải SP.
8. **Cân bằng dữ liệu** để tránh bias.

---

## 11. Môi trường training tạm thời (Workspace riêng)

- Tạo workspace clone code training & data.
- Cài env riêng (torch, open\_clip, faiss).
- Chạy `python train.py --data_dir data/ --output_model clip_finetuned.pt`.
- Quản lý checkpoint & upload `.pt`.
- Copy file `.pt` vào dự án chính để deploy.

---

## 12. Các bước có thể thực hiện trước khi dataset hoàn thiện

- **Thiết lập môi trường dev:** Cài dependencies FE/BE, Python package.
- **Xây dựng API stub & mockup:** Stub `/search-image`, `/vectorize` trả dữ liệu giả.
- **Script validate dataset:** Kiểm cấu trúc folder, định dạng, kích thước.
- **CI/CD pipeline AI service:** Dockerfile & config CI.
- **FAISS service skeleton:** FastAPI project, import faiss, endpoint placeholder.

---

*Trân trọng,*\
*Đội Sản Phẩm AI – Dự án TAP*

