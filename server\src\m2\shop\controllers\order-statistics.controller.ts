import { Controller, Get, Query, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import {
  OrderStatisticsDto,
  OrderTrendDto,
  PaymentMethodStatsDto,
  RevenueDetailsDto,
  CancelledOrderListDto,
  OrderStatisticsQueryDto,
} from '../dto/order-statistics.dto';
import { OrderStatisticsService } from '../services/order-statistics.service';

@ApiTags('Order Statistics')
@Controller('order-statistics')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
export class OrderStatisticsController {
  constructor(private readonly service: OrderStatisticsService) {}

  // SUPERADMIN ENDPOINTS
  @Get('superadmin')
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> thống kê tổng quan đơn hàng (superadmin - toàn hệ thống + thuế)' })
  @ApiResponse({ status: 200, type: OrderStatisticsDto })
  async getSuperadminStatistics(@Query() query: OrderStatisticsQueryDto): Promise<any> {
    return this.service.getStatisticsForSuperAdmin(query);
  }

  @Get('superadmin/revenue-details')
  @ApiOperation({ summary: 'Lấy chi tiết doanh thu (superadmin - xem tất cả)' })
  @ApiResponse({ status: 200, type: RevenueDetailsDto })
  async getSuperadminRevenueDetails(
    @Query() query: OrderStatisticsQueryDto,
  ): Promise<RevenueDetailsDto> {
    return this.service.getRevenueDetails(query);
  }

  // ADMIN ENDPOINTS
  @Get('admin')
  @ApiOperation({ summary: 'Lấy thống kê tổng quan đơn hàng (admin - theo khu vực + người bán)' })
  @ApiResponse({ status: 200, type: OrderStatisticsDto })
  async getAdminStatistics(@Query() query: OrderStatisticsQueryDto, @Req() req: any): Promise<any> {
    // TODO: Get admin's region from req.user
    const adminRegion = req.user?.region || '01'; // Default to Hanoi
    return this.service.getStatisticsForAdmin({ ...query, adminRegion });
  }

  @Get('admin/revenue-details')
  @ApiOperation({ summary: 'Lấy chi tiết doanh thu (admin - theo khu vực)' })
  @ApiResponse({ status: 200, type: RevenueDetailsDto })
  async getAdminRevenueDetails(
    @Query() query: OrderStatisticsQueryDto,
  ): Promise<RevenueDetailsDto> {
    // TODO: Filter by admin's area/region
    return this.service.getRevenueDetails(query);
  }

  // SHOP ENDPOINTS
  @Get('shop')
  @ApiOperation({
    summary: 'Lấy thống kê tổng quan đơn hàng (shop - chỉ đơn hàng của shop + thuế)',
  })
  @ApiResponse({ status: 200, type: OrderStatisticsDto })
  async getShopStatistics(@Query() query: OrderStatisticsQueryDto, @Req() req: any): Promise<any> {
    const shopId = req.user?.userId;
    return this.service.getStatisticsForShop({ ...query, shopId });
  }

  @Get('shop/revenue-details')
  @ApiOperation({ summary: 'Lấy chi tiết doanh thu (shop - chỉ doanh thu của shop)' })
  @ApiResponse({ status: 200, type: RevenueDetailsDto })
  async getShopRevenueDetails(
    @Query() query: OrderStatisticsQueryDto,
    @Req() req: any,
  ): Promise<RevenueDetailsDto> {
    // TODO: Filter by shop's revenue only - implement shopId filtering in service
    return this.service.getRevenueDetails(query);
  }

  // LEGACY ENDPOINTS (để tương thích ngược)

  @Get()
  @ApiOperation({ summary: 'Lấy thống kê tổng quan đơn hàng (legacy)' })
  @ApiResponse({ status: 200, type: OrderStatisticsDto })
  async getStatistics(@Query() query: OrderStatisticsQueryDto): Promise<OrderStatisticsDto> {
    return this.service.getStatistics(query);
  }

  @Get('revenue-details')
  @ApiOperation({ summary: 'Lấy chi tiết doanh thu (legacy)' })
  @ApiResponse({ status: 200, type: RevenueDetailsDto })
  async getRevenueDetails(@Query() query: OrderStatisticsQueryDto): Promise<RevenueDetailsDto> {
    return this.service.getRevenueDetails(query);
  }

  @Get('trend')
  @ApiOperation({ summary: 'Lấy dữ liệu xu hướng đơn hàng & doanh thu theo ngày' })
  @ApiResponse({ status: 200, type: [OrderTrendDto] })
  async getTrend(@Query() query: OrderStatisticsQueryDto): Promise<OrderTrendDto[]> {
    return this.service.getTrend(query);
  }

  @Get('payment-methods')
  @ApiOperation({ summary: 'Lấy dữ liệu phương thức thanh toán' })
  @ApiResponse({ status: 200, type: [PaymentMethodStatsDto] })
  async getPaymentMethods(
    @Query() query: OrderStatisticsQueryDto,
  ): Promise<PaymentMethodStatsDto[]> {
    return this.service.getPaymentMethods(query);
  }

  @Get('cancelled-orders')
  @ApiOperation({ summary: 'Lấy danh sách đơn hàng đã hủy trong khoảng thời gian' })
  @ApiResponse({ status: 200, type: [CancelledOrderListDto] })
  async getCancelledOrders(
    @Query() query: OrderStatisticsQueryDto,
  ): Promise<CancelledOrderListDto[]> {
    return this.service.getCancelledOrders(query);
  }
}
