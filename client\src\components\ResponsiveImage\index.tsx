import React, { useState, useRef, useEffect } from 'react';
import styles from './ResponsiveImage.module.scss';

interface ResponsiveImageProps {
  src: string;
  alt: string;
  className?: string;
  aspectRatio?: 'square' | '16-9' | '4-3' | '3-2' | 'auto';
  sizes?: string;
  priority?: boolean;
  lazy?: boolean;
  fallback?: string;
  onLoad?: () => void;
  onError?: () => void;
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down';
}

const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  className = '',
  aspectRatio = 'auto',
  sizes = '(max-width: 480px) 100vw, (max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw',
  priority = false,
  lazy = true,
  fallback = '/images/placeholder.png',
  onLoad,
  onError,
  objectFit = 'cover'
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(!lazy || priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || priority || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px', // Load image 50px before it comes into view
        threshold: 0.1
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, priority, isInView]);

  // Generate responsive image sources
  const generateSrcSet = (baseSrc: string) => {
    if (!baseSrc || baseSrc.includes('placeholder') || baseSrc.includes('logo')) {
      return undefined;
    }

    // For external URLs or if we don't have image optimization
    return undefined;
  };

  const handleLoad = () => {
    setIsLoaded(true);
    setHasError(false);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  const containerClasses = [
    styles.container,
    styles[`aspect-${aspectRatio}`],
    className,
    isLoaded ? styles.loaded : '',
    hasError ? styles.error : ''
  ].filter(Boolean).join(' ');

  const imageClasses = [
    styles.image,
    styles[`fit-${objectFit}`]
  ].filter(Boolean).join(' ');

  return (
    <div ref={containerRef} className={containerClasses}>
      {/* Loading placeholder */}
      {!isLoaded && !hasError && (
        <div className={styles.placeholder}>
          <div className={styles.skeleton} />
        </div>
      )}

      {/* Main image */}
      {isInView && (
        <img
          ref={imgRef}
          src={hasError ? fallback : src}
          srcSet={generateSrcSet(src)}
          sizes={sizes}
          alt={alt}
          className={imageClasses}
          loading={priority ? 'eager' : 'lazy'}
          onLoad={handleLoad}
          onError={handleError}
          style={{
            opacity: isLoaded ? 1 : 0,
            transition: 'opacity 0.3s ease'
          }}
        />
      )}

      {/* Error state */}
      {hasError && (
        <div className={styles.errorState}>
          <div className={styles.errorIcon}>📷</div>
          <span className={styles.errorText}>Không thể tải ảnh</span>
        </div>
      )}
    </div>
  );
};

export default ResponsiveImage;
