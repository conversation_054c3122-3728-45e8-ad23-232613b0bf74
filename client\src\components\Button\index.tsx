import React from 'react';
import { Button as AntButton, type ButtonProps as AntButtonProps } from 'antd';
import styles from './Button.module.scss';

export interface CustomButtonProps extends Omit<AntButtonProps, 'variant'> {
  variant?: 'primary' | 'default' | 'danger' | 'success' | 'warning';
  size?: 'small' | 'middle' | 'large';
  fullWidth?: boolean;
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  htmlType?: 'button' | 'submit' | 'reset';
  icon?: React.ReactNode;
  className?: string;
}

const Button = React.forwardRef<HTMLButtonElement, CustomButtonProps>(({
  variant = 'default',
  size = 'middle',
  fullWidth = false,
  loading = false,
  disabled = false,
  children,
  onClick,
  htmlType = 'button',
  icon,
  className = '',
  ...restProps
}, ref) => {
  const buttonClassName = `
    ${styles.button}
    ${styles[variant]}
    ${styles[size]}
    ${fullWidth ? styles.fullWidth : ''}
    ${loading ? styles.loading : ''}
    ${disabled ? styles.disabled : ''}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  return (
    <AntButton
      ref={ref}
      className={buttonClassName}
      type={variant === 'primary' ? 'primary' : variant === 'danger' ? 'primary' : 'default'}
      size={size}
      loading={loading}
      disabled={disabled}
      onClick={onClick}
      htmlType={htmlType}
      icon={icon}
      danger={variant === 'danger'}
      {...restProps}
    >
      {children}
    </AntButton>
  );
});

export default Button; 