import React from 'react'
import { useSelector } from 'react-redux'
import { Button, Card, Empty } from 'antd'
import { ShoppingCartOutlined, LoginOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import type { RootState } from '@/libs/state/store'
import { ROUTES } from '@/constants/routes'
import styles from './Cart.module.scss'

const CartGuest: React.FC = () => {
  const navigate = useNavigate()
  const authUser = useSelector((state: RootState) => state.auth)
  const isAuthenticated = !!authUser?.id

  // Nếu đã login thì redirect về Cart chính
  if (isAuthenticated) {
    navigate(ROUTES.CART, { replace: true })
    return null
  }

  const handleLogin = () => {
    navigate(ROUTES.LOGIN, { state: { from: ROUTES.CART } })
  }

  const handleRegister = () => {
    navigate(ROUTES.REGISTER, { state: { from: ROUTES.CART } })
  }

  return (
    <div className={styles.cartContainer}>
      <div className={styles.cartHeader}>
        <h1>Giỏ hàng của tôi</h1>
        <div className={styles.headerActions}>
          <Button 
            type="link" 
            onClick={() => navigate('/mua-sam')}
            className={styles.backToShopping}
          >
            ← Tiếp tục mua sắm
          </Button>
        </div>
      </div>

      <Card className={styles.guestCard}>
        <Empty
          image={<ShoppingCartOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
          description={
            <div>
              <h3>Bạn cần đăng nhập để xem giỏ hàng</h3>
              <p>Đăng nhập để quản lý giỏ hàng và đặt hàng</p>
            </div>
          }
        >
          <div style={{ display: 'flex', gap: 12, justifyContent: 'center', marginTop: 24 }}>
            <Button
              type="primary"
              icon={<LoginOutlined />}
              size="large"
              onClick={handleLogin}
            >
              Đăng nhập
            </Button>
            <Button
              type="primary"
              ghost
              size="large"
              onClick={handleRegister}
            >
              Đăng ký
            </Button>
          </div>
        </Empty>
      </Card>
    </div>
  )
}

export default CartGuest 