import React from 'react'
import { Card, Col, Row, Statistic } from 'antd'
import styles from './UserManagement.module.scss'

interface UserStatisticsProps {
  stats: {
    total: number
    active: number
    locked: number
    pending: number
    admins: number
  }
}

const UserStatistics: React.FC<UserStatisticsProps> = ({ stats }) => (
  <Row gutter={[16, 16]} className={styles.statsRow}>
    <Col xs={12} sm={6}>
      <Card className={styles.statCard}>
        <Statistic title="Tổng người dùng" value={stats.total} valueStyle={{ color: '#1890ff' }} />
      </Card>
    </Col>
    <Col xs={12} sm={6}>
      <Card className={styles.statCard}>
        <Statistic title="Đang hoạt động" value={stats.active} valueStyle={{ color: '#52c41a' }} />
      </Card>
    </Col>
    <Col xs={12} sm={6}>
      <Card className={styles.statCard}>
        <Statistic title="Đang xử lý" value={stats.pending} valueStyle={{ color: '#faad14' }} />
      </Card>
    </Col>
    <Col xs={12} sm={6}>
      <Card className={styles.statCard}>
        <Statistic title="Không hoạt động" value={stats.locked} valueStyle={{ color: '#ff4d4f' }} />
      </Card>
    </Col>
  </Row>
)

export default UserStatistics
