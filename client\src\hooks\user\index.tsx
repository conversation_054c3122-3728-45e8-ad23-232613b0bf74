import {
  useAssignRoleGroupToUserMutation,
  useCreateRoleMutation,
  useGetAllRoleGroupsQuery,
  useGetAllRoleQuery,
  useGetAllRolesForAdminQuery,
  useGetUserProfileQuery,
  useUpdateAvatarMutation,
  useUpdateProfileMutation,
  type CreateRoleRequest,
  type UpdateProfileProps,
} from '@/services/M1/user'

import { useToast } from '../../components/Toast'
import { useEffect } from 'react'
import { useLoadingStore } from '@/utils/loadingStore'
import { getCookie } from '@/private/cookies'

// Hook lấy toàn bộ vai trò và quyền cho admin
const useGetAllRolesForAdminHook = () => {
  const { data, error, isLoading, refetch, isFetching } = useGetAllRolesForAdminQuery(null)
  const { setLoading } = useLoadingStore()
  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching])
  return {
    data,
    isLoading,
    error,
    refetch,
  }
}
// Dùng cho get
const useGetAllRoleHook = () => {
  const { data, error, isLoading, refetch, isFetching } = useGetAllRoleQuery(null)
  // {
  //   ...req,
  // },
  // {
  //   skip: req.classroomId === '' || req.semesterId === '' || req.subjectId === '',
  //   refetchOnMountOrArgChange: true,
  // }

  const { setLoading } = useLoadingStore()
  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy danh sách role group
const useGetAllRoleGroupsHook = (roleId?: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetAllRoleGroupsQuery(roleId)
  const { setLoading } = useLoadingStore()
  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching])
  return { data, isLoading, error, refetch }
}

// Hook bổ nhiệm nhóm vai trò cho user
const useAssignRoleGroupToUserHook = () => {
  const { showSuccess, showError } = useToast()
  const [assignFunc] = useAssignRoleGroupToUserMutation()
  const assign = async (userId: string, roleGroupCode: string): Promise<boolean> => {
    try {
      const res = await assignFunc({ userId, roleGroupCode })
      if (!res?.error) {
        showSuccess('Bổ nhiệm nhóm vai trò thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Bổ nhiệm thất bại: ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Bổ nhiệm thất bại: ' + e)
      return false
    }
  }
  return assign
}

// Dùng cho POST, PUT, DELETE
const useCreateRoleMutationHook = () => {
  const { showSuccess, showError } = useToast()
  const [createFunc] = useCreateRoleMutation()
  const create = async (data: CreateRoleRequest): Promise<boolean> => {
    try {
      const res = await createFunc(data)
      if (!res?.error) {
        showSuccess('Tạo vai trò thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Tạo vai trò thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Tạo vai trò thất bại' + e)
      return false
    }
  }
  return create
}

const useGetUserProfileHook = () => {
  // const token = getCookie('accessToken')
  const { data, error, isLoading, refetch, isFetching } = useGetUserProfileQuery(null, {
    // Chỉ dùng các options có sẵn trong RTK Query
    refetchOnMountOrArgChange: true,
    skip: false,
    refetchOnReconnect: true,
  })

  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

const useUpdateUserProfileHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useUpdateProfileMutation()
  const update = async (data: UpdateProfileProps): Promise<boolean> => {
    try {
      const res = await updateFunc(data)
      if (!res?.error) {
        showSuccess('Cập nhật hồ sơ thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Cập nhật hồ sơ thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Cập nhật hồ sơ thất bại' + e)
      return false
    }
  }
  return update
}

const useUpdateAvatarHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useUpdateAvatarMutation()
  const update = async (file: File): Promise<boolean> => {
    try {
      const res = await updateFunc(file)
      if (!res?.error) {
        showSuccess('Cập nhật ảnh thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Cập nhật ảnh thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Cập nhật ảnh thất bại' + e)
      return false
    }
  }
  return update
}

// Xuất ra
export {
  useGetAllRoleHook,
  useGetAllRolesForAdminHook,
  useCreateRoleMutationHook,
  useGetUserProfileHook,
  useUpdateUserProfileHook,
  useUpdateAvatarHook,
  useAssignRoleGroupToUserHook,
  useGetAllRoleGroupsHook,
}
