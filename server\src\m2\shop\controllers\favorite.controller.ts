import { <PERSON>, Get, Post, Put, Body, Req, UseGuards, Param } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { FavoriteService } from '../services/favorite.service';
import { UserBehaviorLogService } from '../../../m_log/log/user-behavior-log.service';

@ApiTags('Favorites')
@Controller('favorites')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
export class FavoriteController {
  constructor(
    private readonly favoriteService: FavoriteService,
    private readonly userBehaviorLogService: UserBehaviorLogService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Xem danh sách sản phẩm yêu thích' })
  @ApiResponse({ status: 200 })
  async getFavorites(@Req() req: any) {
    const userId = req.user?.userId;
    return this.favoriteService.getFavorites(userId);
  }

  @Put('toggle')
  @ApiOperation({ summary: 'Thích hoặc bỏ thích sản phẩm' })
  @ApiResponse({ status: 200 })
  @ApiBody({ schema: { properties: { product_id: { type: 'string' } } } })
  async toggleFavorite(@Req() req: any, @Body('product_id') product_id: string) {
    const userId = req.user?.userId;
    const result = await this.favoriteService.toggleFavorite(userId, product_id);
    // Ghi log click yêu thích
    this.userBehaviorLogService.emitUserBehavior({
      user_id: userId,
      event_type: 'click',
      target_type: 'favorite',
      target_id: product_id,
    });
    return result;
  }
}
