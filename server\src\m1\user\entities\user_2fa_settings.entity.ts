import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PrimaryColumn, OneToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { User } from './user.entity';

@Entity('user_2fa_settings')
export class User2FASettings {
  @PrimaryColumn('uuid')
  user_id: string;

  @OneToOne(() => User, (user) => user.twoFactorSettings, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ default: false })
  is_otp_enabled: boolean;

  @Column({ default: false })
  is_totp_enabled: boolean;

  @Column({ type: 'text', nullable: true })
  totp_secret_key: string;

  @Column({ type: 'text', array: true, nullable: true })
  backup_codes: string[];

  @Column({ type: 'timestamp', nullable: true })
  verified_at: Date;
}
