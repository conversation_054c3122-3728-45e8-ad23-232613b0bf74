import { axiosBaseQuery } from '@/libs/axios/axiosBase'
import { createApi } from '@reduxjs/toolkit/query/react'

const baseUrl = 'http://localhost:8080'

export const chatBotApi = createApi({
  reducerPath: 'chatBotApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  // tagTypes: ['Chatbot'],
  endpoints: (build) => ({
    // ✅ Cập nhật sản phẩm
    chatBotAnalyze: build.mutation<ChatBotResponse, UserSupportRequestDto>({
      query: (data) => ({
        url: `m1-bot/analyze`,
        method: 'POST',
        data: data,
        authRequired: true,
      }),
    }),
  }),
})

export const { useChatBotAnalyzeMutation } = chatBotApi

export interface UserSupportRequestDto {
  text: string
}

export interface ChatBotResponse {
  intent: string
  score: number
  entities: {}
  reply: string
  suggestedReplies: string[]
}
