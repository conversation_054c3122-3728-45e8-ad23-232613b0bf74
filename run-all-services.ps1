# === THU MUC GOC ===
# $baseDir = "D:\WORK\TAP\tapviet-M1-M4"
$baseDir = "D:\M1-Duy\tapviet-M1-M4"

# === HAM: Mo cua so rieng de chay service ===
function Start-ServiceShell($ten, $duongDan, $lenh) {
    $noiDungLenh = "Write-Host 'Dang chay: $ten...'; cd '$duongDan'; $lenh"
    Start-Process powershell -ArgumentList "-NoExit", "-Command", $noiDungLenh
    Start-Sleep -Seconds 1
}

# === HAM: Kiem tra va chay container Docker neu chua chay ===
function Start-DockerContainerIfNeeded($tenContainer) {
    $dangChay = docker ps --filter "name=$tenContainer" --format "{{.Names}}"
    if ($dangChay -ne $tenContainer) {
        Write-Host "Dang bat container Docker: $tenContainer..."
        docker start $tenContainer | Out-Null
    } else {
        Write-Host "Container '$tenContainer' da chay roi. Bo qua."
    }
}

# === MENU CHON SERVICE ===
Write-Host "`nChon service muon chay (nhap so cach nhau boi dau phay, Enter de chay tat ca):"
Write-Host "1. Client"
Write-Host "2. NLU Bot"
Write-Host "3. Faiss"
Write-Host "4. Clip"
Write-Host "5. Server"
Write-Host "6. Docker: pg-nest"
Write-Host "7. Docker: rabbitmq"
Write-Host "8. Object Detection Service (YOLOv5)"
$chon = Read-Host ">>"


if ([string]::IsNullOrWhiteSpace($chon)) {
    $chon = "3,4,8,1,2,6,7,5"
}

$dsService = $chon -split "," | ForEach-Object { $_.Trim() } | Where-Object { $_ -match '^\d+$' }


# === 3. FAISS ===
if ($dsService -contains "3") {
    $faiss = @"
python -m venv .venv
.\.venv\Scripts\Activate.ps1
cd .\faiss_service\
pip install -r requirements.txt
python app.py
"@ -join "; "
    Start-ServiceShell "Faiss" "$baseDir" $faiss
}

# === 4. CLIP ===
if ($dsService -contains "4") {
    $clip = @"
cd .\clip_service\
python -m venv .venv
.\.venv\Scripts\Activate.ps1
pip install -r requirements.txt
python main.py
"@ -join "; "
    Start-ServiceShell "Clip" "$baseDir" $clip
}

# === 8. OBJECT DETECTION SERVICE ===
if ($dsService -contains "8") {
    $objDetect = @"
cd .\object_detection_service\
python -m venv .venv
.\.venv\Scripts\Activate.ps1
pip install -r requirements.txt
python -m uvicorn main:app --host 0.0.0.0 --port 8003
"@ -join "; "
    Start-ServiceShell "Object Detection Service (YOLOv5)" "$baseDir" $objDetect
}

# === 1. CLIENT ===
if ($dsService -contains "1") {
    Start-ServiceShell "Client" "$baseDir\client" "npm run dev"
}

# === 2. NLU BOT ===
if ($dsService -contains "2") {
    Start-ServiceShell "NLU Bot" "$baseDir\server" "npm run m1-bot-nlu:start"
}

# === 6. DOCKER: pg-nest ===
if ($dsService -contains "6") {
    Start-DockerContainerIfNeeded "pg-nest"
}

# === 7. DOCKER: rabbitmq ===
if ($dsService -contains "7") {
    Start-DockerContainerIfNeeded "rabbitmq"
}

# === 5. SERVER ===
if ($dsService -contains "5") {
    Start-ServiceShell "Server" "$baseDir\server" "npm run start:dev"
}

# === DOI TEN CUA SO CHINH ===
[console]::Title = "DA BAT XONG - Duonq Launcher"
