import { ApiProperty } from '@nestjs/swagger';

export class OrderStatisticsQueryDto {
  @ApiProperty({ example: '2025-07-01', required: false })
  from?: string;

  @ApiProperty({ example: '2025-07-28', required: false })
  to?: string;
}

export class OrderStatisticsDto {
  @ApiProperty() totalOrders: number;
  @ApiProperty() completedOrders: number;
  @ApiProperty() cancelledOrders: number;
  @ApiProperty() totalRevenue: number;
  @ApiProperty() actualRevenue: number;
  @ApiProperty() averageOrderValue: number;
  @ApiProperty() previousPeriodComparison: number;
}

export class OrderTrendDto {
  @ApiProperty() date: string;
  @ApiProperty() orders: number;
  @ApiProperty() revenue: number;
  @ApiProperty() completedOrders: number;
  @ApiProperty() cancelledOrders: number;
}

export class PaymentMethodStatsDto {
  @ApiProperty() method: string;
  @ApiProperty() value: number;
  @ApiProperty() percentage: number;
  @ApiProperty() color: string;
}

export class RevenueDetailsDto {
  @ApiProperty() actualRevenue: number;
  @ApiProperty() totalRevenue: number;
}

export class CancelledOrderListDto {
  @ApiProperty() id: string;
  @ApiProperty() cancelledAt: string;
  @ApiProperty() amount: number;
  @ApiProperty() customer: string;
}
