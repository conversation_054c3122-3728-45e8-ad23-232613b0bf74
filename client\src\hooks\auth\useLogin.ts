import { useNavigate, useLocation } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import {
  useLoginMutation,
  useSocialLoginMutation,
  // Thêm các hook 2FA và Sessions
  useRequest2FAOTPMutation,
  useVerify2FAOTPMutation,
  useVerify2FALoginMutation,
  useDisable2FAMutation,
  useChangePasswordMutation,
  useGetSessionsQuery,
  type SignInProps,
  type SocialLoginProps,
  // Thêm types 2FA và Sessions
  type Request2FAOTPProps,
  type Verify2FAOTPProps,
  type Verify2FALoginProps,
  type Disable2FAProps,
  type SessionsResponse,
  useGetAuthUserMutation,
  type GetUserProps,
  type AuthUser,
} from '../../services/M1/auth'
import { setCookie, removeCookie, getCookie } from '../../private/cookies'
import { logout, setCredentials } from '../../libs/features/auth/authSlice'
import { ROUTES } from '../../constants/routes'
import { useToast } from '../../components/Toast'
import { useEffect } from 'react'
import { useLoadingStore } from '@/utils/loadingStore'
import { baseRedirect } from '@/constants/sidebarItems'

export const useLogin = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  const [loginMutation] = useLoginMutation()
  const [getUserMutation] = useGetAuthUserMutation()

  const login = async ({ email, password }: SignInProps) => {
    try {
      const loginRes = await loginMutation({ email, password }).unwrap()

      // Check nếu cần 2FA
      if (loginRes?.requiresTwoFA || loginRes?.requires2FA || loginRes?.needsTwoFA) {
        throw {
          data: {
            requiresTwoFA: true,
            userId: loginRes.userId || loginRes.user?.id,
            message: 'Requires 2FA verification',
          },
        }
      }

      // Chỉ gọi getUserMutation nếu có accessToken
      if (loginRes?.accessToken) {
        const userRes = await getUserMutation({ token: loginRes.accessToken }).unwrap()

        setCookie('accessToken', loginRes.accessToken)
        setCookie('refreshToken', loginRes.refreshToken)

        dispatch(setCredentials(userRes))

        // Kiểm tra có trang trước đó để redirect không
        const from = (location.state as any)?.from
        if (from && from !== ROUTES.LOGIN) {
          navigate(from, { replace: true })
        } else {
          const roleCode = userRes?.role?.role_code
          const redirectPath = roleCode ? baseRedirect[roleCode] : ROUTES.HOME
          navigate(redirectPath, { replace: true })
        }
        // navigate(ROUTES.DASHBOARD)
      } else {
        throw new Error('Không nhận được access token')
      }
    } catch (error: any) {
      // Nếu server trả về yêu cầu 2FA
      if (error?.data?.requiresTwoFA || error?.status === 401) {
        const errorData = error?.data || {}

        // Check xem có phải lỗi 2FA không
        if (
          errorData.message?.includes('2FA') ||
          errorData.message?.includes('xác thực 2 bước') ||
          errorData.requiresTwoFA
        ) {
          throw {
            data: {
              requiresTwoFA: true,
              userId: errorData.userId,
              message: 'Requires 2FA verification',
            },
          }
        }
      }

      const message =
        error?.data?.message || error?.message || 'Đăng nhập thất bại. Vui lòng thử lại.'
      throw new Error(message)
    }
  }
  return { login }
}

export const useSocialLogin = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  const [socialLoginMutation] = useSocialLoginMutation()
  const [getUserMutation] = useGetAuthUserMutation()

  const socialLogin = async ({ provider, accessToken }: SocialLoginProps) => {
    try {
      const loginRes = await socialLoginMutation({ provider, accessToken }).unwrap()
      const userRes = await getUserMutation({ token: loginRes.accessToken }).unwrap()

      setCookie('accessToken', loginRes.accessToken)
      setCookie('refreshToken', loginRes.refreshToken)

      dispatch(setCredentials(userRes))

      // Kiểm tra có trang trước đó để redirect không
      const from = (location.state as any)?.from
      if (from && from !== ROUTES.LOGIN) {
        navigate(from, { replace: true })
      } else {
        const roleCode = userRes?.role?.role_code
        const redirectPath = roleCode ? baseRedirect[roleCode] : ROUTES.HOME
        navigate(redirectPath, { replace: true })
      }
    } catch (error: any) {
      const message =
        error?.data?.message || 'Đăng nhập bằng mạng xã hội thất bại. Vui lòng thử lại.'
      console.error('Lỗi đăng nhập social:', error)
      throw new Error(message)
    }
  }

  return { socialLogin }
}

export const useLogout = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()

  const Logoutfunc = async () => {
    try {
      // Clear all cookies
      removeCookie('accessToken')
      removeCookie('refreshToken')

      // Clear all localStorage data
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')

      // Clear Redux persist storage (auth data)
      localStorage.removeItem('persist:auth')

      // Dispatch logout action to reset Redux state
      dispatch(logout())

      // Use navigate instead of window.location to avoid errors
      navigate(ROUTES.LOGIN, { replace: true })
    } catch (err) {
      console.error('Logout failed:', err)
      // Fallback - use navigate instead of window.location
      navigate(ROUTES.LOGIN, { replace: true })
    }
  }
  return { Logoutfunc }
}

// Hook gửi OTP qua email
export const useRequest2FAOTP = () => {
  const { showSuccess, showError } = useToast()
  const [requestOTPFunc] = useRequest2FAOTPMutation()

  const requestOTP = async (email: string): Promise<boolean> => {
    try {
      const res = await requestOTPFunc({ target: email })
      if (!res?.error) {
        showSuccess('Đã gửi mã OTP qua email!')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Gửi OTP thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Gửi OTP thất bại' + e)
      return false
    }
  }

  return requestOTP
}

// Hook xác minh OTP và kích hoạt 2FA
export const useVerify2FAOTP = () => {
  const { showSuccess, showError } = useToast()
  const [verifyOTPFunc] = useVerify2FAOTPMutation()

  const verifyOTP = async (email: string, otp: string): Promise<boolean> => {
    try {
      const res = await verifyOTPFunc({ target: email, otp })
      if (!res?.error) {
        showSuccess('Kích hoạt 2FA thành công!')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Xác minh OTP thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Xác minh OTP thất bại' + e)
      return false
    }
  }

  return verifyOTP
}

// Hook xác thực 2FA khi đăng nhập
export const useVerify2FALogin = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const dispatch = useDispatch()
  const { showSuccess, showError } = useToast()
  const [verify2FAFunc] = useVerify2FALoginMutation()
  const [getUserMutation] = useGetAuthUserMutation()

  const verify2FA = async (userId: string, otpCode: string, totpCode: string): Promise<boolean> => {
    try {
      const res = await verify2FAFunc({ userId, otpCode, totpCode })
      if (!res?.error && res.data) {
        // Lưu tokens
        setCookie('accessToken', res.data.accessToken)
        setCookie('refreshToken', res.data.refreshToken)

        // Lấy thông tin user và set vào Redux
        const userRes = await getUserMutation({ token: res.data.accessToken }).unwrap()
        dispatch(setCredentials(userRes))

        showSuccess('Đăng nhập thành công!')

        // Kiểm tra có trang trước đó để redirect không
        const from = (location.state as any)?.from
        if (from && from !== ROUTES.LOGIN) {
          navigate(from, { replace: true })
        } else {
          const roleCode = userRes?.role?.role_code
          const redirectPath = roleCode ? baseRedirect[roleCode] : ROUTES.HOME
          navigate(redirectPath, { replace: true })
        }
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Xác thực 2FA thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Xác thực 2FA thất bại' + e)
      return false
    }
  }

  return verify2FA
}

// Hook disable 2FA mới - sử dụng API confirm-disable
export const useDisable2FA = () => {
  const { showSuccess, showError } = useToast()
  const [requestOTPFunc] = useRequest2FAOTPMutation() // Bước 1: Gửi OTP
  const [disable2FAFunc] = useDisable2FAMutation() // Bước 2: Disable với OTP

  // Bước 1: Gửi OTP
  const sendOTP = async (email: string): Promise<boolean> => {
    try {
      const res = await requestOTPFunc({ target: email })
      if (!res?.error) {
        showSuccess('Đã gửi mã OTP qua email!')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Gửi OTP thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Gửi OTP thất bại' + e)
      return false
    }
  }

  // Bước 2: Disable 2FA với OTP (API mới)
  const disable2FA = async (email: string, otp: string): Promise<boolean> => {
    try {
      const res = await disable2FAFunc({
        target: email, // email thay vì userId
        otp: otp, // OTP code
      })

      if (!res?.error) {
        showSuccess('Tắt xác thực 2 bước thành công!')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Tắt 2FA thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Tắt 2FA thất bại' + e)
      return false
    }
  }

  return { sendOTP, disable2FA }
}

export const useChangePassword = () => {
  const { showSuccess, showError } = useToast()
  const [changePasswordFunc] = useChangePasswordMutation()

  const changePassword = async (currentPassword: string, newPassword: string): Promise<boolean> => {
    try {
      const res = await changePasswordFunc({
        currentPassword,
        newPassword,
      })

      if (!res?.error) {
        showSuccess('Đổi mật khẩu thành công!')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Đổi mật khẩu thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      console.error('Change password error:', e)
      showError('Đổi mật khẩu thất bại' + e)
      return false
    }
  }

  return changePassword
}

// Hook cho Sessions - theo pattern useGetAllRoleHook
export const useGetSessions = () => {
  const { data, error, isLoading, refetch, isFetching } = useGetSessionsQuery(null, {
    refetchOnMountOrArgChange: true,
    skip: false,
    refetchOnReconnect: true,
  })

  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

export const useGetAuthUserHook = () => {
  const dispatch = useDispatch()
  const [getAuthUser] = useGetAuthUserMutation()

  useEffect(() => {
    const token = getCookie('accessToken')
    if (token) {
      getAuthUser({ token })
        .unwrap()
        .then((res) => {
          dispatch(setCredentials(res))
        })
        .catch((err) => {
          // Silent fail - không cần log lỗi
        })
    }
  }, [])

  return null // không cần return gì nếu chỉ là side effect
}
