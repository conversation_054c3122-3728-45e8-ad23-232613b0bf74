import React from 'react'
import { useSelector } from 'react-redux'
import type { RootState } from '@/libs/state/store'
import ProtectedCart from './ProtectedCart'
import CartGuest from './CartGuest'

const SmartCart: React.FC = () => {
  const authUser = useSelector((state: RootState) => state.auth)
  const isAuthenticated = !!authUser?.id

  return isAuthenticated ? <ProtectedCart /> : <CartGuest />
}

export default SmartCart 