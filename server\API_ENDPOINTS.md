# API Endpoints Documentation

## Tổng quan

Đây là danh sách tất cả endpoints từ hai module chính:

- **M1 Module**: Authentication và User Management
- **M2 Module**: Shop/E-commerce

---

## M1 Module - Authentication & User Management

### 🔐 Auth Controller (`/auth`)

| Method | Endpoint                           | Mô tả                                         |
| ------ | ---------------------------------- | --------------------------------------------- |
| POST   | `/auth/login`                      | Đăng nhập tài khoản                           |
| POST   | `/auth/verify-2fa`                 | Xác minh mã OTP hoặc TOTP khi đăng nhập (2FA) |
| POST   | `/auth/login/social`               | Đăng nhập qua mạng xã hội                     |
| POST   | `/auth/me`                         | L<PERSON><PERSON> thông tin user hiện tại                   |
| POST   | `/auth/refresh-token`              | Làm mới access token                          |
| POST   | `/auth/send-otp`                   | Gửi mã OTP                                    |
| POST   | `/auth/verify-otp`                 | Xác minh mã OTP                               |
| POST   | `/auth/register`                   | Đăng ký tài khoản                             |
| POST   | `/auth/forgot-password/send-otp`   | Gửi OTP quên mật khẩu                         |
| POST   | `/auth/forgot-password/verify-otp` | Xác minh OTP quên mật khẩu                    |
| POST   | `/auth/forgot-password/reset`      | Đặt lại mật khẩu                              |
| POST   | `/auth/2fa/otp/request`            | Yêu cầu mã OTP 2FA                            |
| POST   | `/auth/2fa/otp/verify`             | Xác minh mã OTP 2FA                           |
| POST   | `/auth/2fa/totp/setup`             | Thiết lập TOTP 2FA                            |
| POST   | `/auth/2fa/totp/verify`            | Xác minh TOTP 2FA                             |
| GET    | `/auth/sessions`                   | Lấy danh sách phiên đăng nhập                 |
| POST   | `/auth/2fa/otp/confirm-disable`    | Xác nhận tắt OTP 2FA                          |
| POST   | `/auth/2fa/otp/disable-method`     | Tắt phương thức 2FA                           |

### 👤 User Profile Controller (`/users`)

| Method | Endpoint                 | Mô tả                                                                       |
| ------ | ------------------------ | --------------------------------------------------------------------------- |
| POST   | `/users/ai-onboarding`   | AI Onboarding Assistant: Phân tích ý định và thực thể từ câu hỏi người dùng |
| GET    | `/users/profile`         | Lấy thông tin profile                                                       |
| PUT    | `/users/profile`         | Cập nhật thông tin profile                                                  |
| POST   | `/users/change-password` | Đổi mật khẩu                                                                |
| POST   | `/users/profile/qr`      | Tạo mã QR cho profile                                                       |
| GET    | `/users/qrcodes`         | Lấy danh sách mã QR                                                         |
| DELETE | `/users/qrcodes/:id`     | Xóa mã QR                                                                   |
| POST   | `/users/avatar`          | Upload avatar                                                               |
| POST   | `/users/assign-role`     | Gán role cho user                                                           |

### 👥 User Management Controller (`/users-management`)

| Method | Endpoint                                      | Mô tả                            |
| ------ | --------------------------------------------- | -------------------------------- |
| GET    | `/users-management/admin/user-management`     | Lấy danh sách người dùng (admin) |
| GET    | `/users-management/admin/user-management/:id` | Lấy user theo ID                 |
| PUT    | `/users-management/admin/user-management/:id` | Cập nhật user                    |
| DELETE | `/users-management/admin/user-management/:id` | Xóa user                         |

### 🏢 User Affiliate Controller (`/users/affiliate`)

| Method | Endpoint                           | Mô tả                                     |
| ------ | ---------------------------------- | ----------------------------------------- |
| GET    | `/users/affiliate/me`              | Lấy thông tin affiliate của user hiện tại |
| GET    | `/users/affiliate/tree`            | Lấy cây affiliate                         |
| GET    | `/users/affiliate/stats`           | Lấy thống kê affiliate                    |
| GET    | `/users/affiliate/direct`          | Lấy danh sách affiliate trực tiếp         |
| GET    | `/users/affiliate/indirect`        | Lấy danh sách affiliate gián tiếp         |
| GET    | `/users/affiliate/:userId/summary` | Lấy tóm tắt affiliate của user            |

### 🎭 Role Controller (`/roles`)

| Method | Endpoint              | Mô tả                     |
| ------ | --------------------- | ------------------------- |
| POST   | `/roles/assign-group` | Gán nhóm role             |
| GET    | `/roles/groups`       | Lấy danh sách nhóm role   |
| GET    | `/roles/admin`        | Lấy roles admin           |
| GET    | `/roles/scopes`       | Lấy danh sách scopes      |
| GET    | `/roles/permissions`  | Lấy danh sách permissions |
| POST   | `/roles/assign`       | Gán role                  |
| POST   | `/roles`              | Tạo role mới              |
| GET    | `/roles`              | Lấy danh sách roles       |

### 🤖 M1 Bot Controller (`/m1-bot`)

| Method | Endpoint          | Mô tả                        |
| ------ | ----------------- | ---------------------------- |
| POST   | `/m1-bot/analyze` | Phân tích văn bản với AI Bot |

---

## M2 Module - Shop/E-commerce

### 📦 Product Controller (`/products`)

| Method | Endpoint                                | Mô tả                                    |
| ------ | --------------------------------------- | ---------------------------------------- |
| GET    | `/products/public`                      | Lấy danh sách sản phẩm công khai         |
| GET    | `/products/public/search-by-image`      | Tìm kiếm sản phẩm bằng hình ảnh (public) |
| POST   | `/products/search-by-image`             | Tìm kiếm sản phẩm bằng hình ảnh          |
| GET    | `/products/public/:id`                  | Lấy chi tiết sản phẩm công khai          |
| GET    | `/products/shop`                        | Lấy danh sách sản phẩm của shop          |
| GET    | `/products/admin`                       | Lấy danh sách sản phẩm (admin)           |
| GET    | `/products/superadmin`                  | Lấy danh sách sản phẩm (superadmin)      |
| GET    | `/products/:id/inventory`               | Lấy thông tin tồn kho sản phẩm           |
| GET    | `/products/:id/stats`                   | Lấy thống kê sản phẩm                    |
| POST   | `/products`                             | Tạo sản phẩm mới                         |
| PUT    | `/products/:id`                         | Cập nhật sản phẩm                        |
| PUT    | `/products/:id/images`                  | Cập nhật hình ảnh sản phẩm               |
| PUT    | `/products/:id/attributes`              | Cập nhật thuộc tính sản phẩm             |
| PUT    | `/products/:id/disable`                 | Vô hiệu hóa sản phẩm                     |
| DELETE | `/products/:id`                         | Xóa sản phẩm                             |
| GET    | `/products/:id`                         | Lấy chi tiết sản phẩm                    |
| GET    | `/products/recommend-for-user/:user_id` | Gợi ý sản phẩm cho user                  |
| POST   | `/products/log-ai-suggestion-click`     | Log click AI suggestion                  |

### 🛒 Cart Controller (`/cart`)

| Method | Endpoint                | Mô tả                                |
| ------ | ----------------------- | ------------------------------------ |
| GET    | `/cart/me`              | Lấy giỏ hàng của user hiện tại       |
| POST   | `/cart/add`             | Thêm sản phẩm vào giỏ hàng           |
| POST   | `/cart/update-quantity` | Cập nhật số lượng sản phẩm trong giỏ |
| DELETE | `/cart/item`            | Xóa sản phẩm khỏi giỏ hàng           |
| POST   | `/cart/clear`           | Xóa toàn bộ giỏ hàng                 |
| POST   | `/cart/check-inventory` | Kiểm tra tồn kho giỏ hàng            |

### 📋 Order Controller (`/orders`)

| Method | Endpoint                       | Mô tả                                    |
| ------ | ------------------------------ | ---------------------------------------- |
| GET    | `/orders/me`                   | Lấy danh sách đơn hàng của user hiện tại |
| GET    | `/orders/:id`                  | Lấy chi tiết đơn hàng theo id            |
| POST   | `/orders`                      | Tạo đơn hàng mới                         |
| POST   | `/orders/:id/request-cancel`   | Yêu cầu hủy đơn hàng                     |
| POST   | `/orders/:id/confirm-received` | Xác nhận đã nhận hàng                    |

### 🏪 Admin Order Controller (`/orders-management`)

| Method | Endpoint                                         | Mô tả                          |
| ------ | ------------------------------------------------ | ------------------------------ |
| GET    | `/orders-management/admin/order`                 | Lấy danh sách đơn hàng (admin) |
| GET    | `/orders-management/admin/order/:id`             | Lấy chi tiết đơn hàng (admin)  |
| GET    | `/orders-management/admin/cancel-order-requests` | Lấy yêu cầu hủy đơn (admin)    |
| GET    | `/orders-management/shop/order`                  | Lấy danh sách đơn hàng (shop)  |
| GET    | `/orders-management/shop/order/:id`              | Lấy chi tiết đơn hàng (shop)   |
| GET    | `/orders-management/shop/cancel-order-requests`  | Lấy yêu cầu hủy đơn (shop)     |
| POST   | `/orders-management/:id/accept-cancel-requests`  | Chấp nhận yêu cầu hủy đơn      |
| POST   | `/orders-management/:id/status`                  | Cập nhật trạng thái đơn hàng   |

### 📊 Order Statistics Controller (`/order-statistics`)

| Method | Endpoint                                       | Mô tả                           |
| ------ | ---------------------------------------------- | ------------------------------- |
| GET    | `/order-statistics/superadmin`                 | Thống kê đơn hàng (superadmin)  |
| GET    | `/order-statistics/superadmin/revenue-details` | Chi tiết doanh thu (superadmin) |
| GET    | `/order-statistics/admin`                      | Thống kê đơn hàng (admin)       |
| GET    | `/order-statistics/admin/revenue-details`      | Chi tiết doanh thu (admin)      |
| GET    | `/order-statistics/shop`                       | Thống kê đơn hàng (shop)        |
| GET    | `/order-statistics/shop/revenue-details`       | Chi tiết doanh thu (shop)       |
| GET    | `/order-statistics`                            | Thống kê đơn hàng               |
| GET    | `/order-statistics/revenue-details`            | Chi tiết doanh thu              |
| GET    | `/order-statistics/trend`                      | Xu hướng đơn hàng               |
| GET    | `/order-statistics/payment-methods`            | Thống kê phương thức thanh toán |
| GET    | `/order-statistics/cancelled-orders`           | Thống kê đơn hàng bị hủy        |

### 📂 Product Categories Controller (`/categories`)

| Method | Endpoint                  | Mô tả                            |
| ------ | ------------------------- | -------------------------------- |
| GET    | `/categories/public`      | Lấy danh sách danh mục công khai |
| GET    | `/categories/public/:id`  | Lấy chi tiết danh mục công khai  |
| GET    | `/categories`             | Lấy danh sách danh mục           |
| POST   | `/categories`             | Tạo danh mục mới                 |
| GET    | `/categories/:id`         | Lấy chi tiết danh mục            |
| PUT    | `/categories/:id`         | Cập nhật danh mục                |
| DELETE | `/categories/:id`         | Xóa danh mục                     |
| POST   | `/categories/upload-icon` | Upload icon danh mục             |

### 🏷️ Product Version Controller (`/products/:productId/versions`)

| Method | Endpoint                            | Mô tả                            |
| ------ | ----------------------------------- | -------------------------------- |
| POST   | `/products/:productId/versions`     | Tạo phiên bản sản phẩm mới       |
| PUT    | `/products/:productId/versions/:id` | Cập nhật phiên bản sản phẩm      |
| DELETE | `/products/:productId/versions/:id` | Xóa phiên bản sản phẩm           |
| GET    | `/products/:productId/versions`     | Lấy danh sách phiên bản sản phẩm |

### ⭐ Product Review Controller (`/product-reviews`)

| Method | Endpoint                     | Mô tả                               |
| ------ | ---------------------------- | ----------------------------------- |
| GET    | `/product-reviews/to-review` | Lấy danh sách sản phẩm cần đánh giá |
| POST   | `/product-reviews`           | Tạo đánh giá sản phẩm               |
| PUT    | `/product-reviews/:id`       | Cập nhật đánh giá                   |
| DELETE | `/product-reviews/:id`       | Xóa đánh giá                        |
| GET    | `/product-reviews/my`        | Lấy đánh giá của user hiện tại      |

### 🖼️ Product Images Controller (`/product-images`)

| Method | Endpoint                       | Mô tả                    |
| ------ | ------------------------------ | ------------------------ |
| POST   | `/product-images/upload`       | Upload hình ảnh sản phẩm |
| POST   | `/product-images/delete-image` | Xóa hình ảnh sản phẩm    |

### 📦 Inventory Controller (`/inventory`)

| Method | Endpoint                                  | Mô tả                           |
| ------ | ----------------------------------------- | ------------------------------- |
| GET    | `/inventory/admin`                        | Lấy danh sách tồn kho (admin)   |
| GET    | `/inventory/shop`                         | Lấy danh sách tồn kho (shop)    |
| GET    | `/inventory`                              | Lấy danh sách tồn kho           |
| GET    | `/inventory/:id`                          | Lấy chi tiết tồn kho            |
| PUT    | `/inventory/:id/variants`                 | Cập nhật biến thể tồn kho       |
| PATCH  | `/inventory/:id/variant/:variantId/stock` | Cập nhật nhanh số lượng tồn kho |
| GET    | `/inventory/:id/history`                  | Lấy lịch sử tồn kho             |
| POST   | `/inventory/import`                       | Import dữ liệu tồn kho          |
| GET    | `/inventory/export`                       | Export dữ liệu tồn kho          |
| GET    | `/inventory/alerts`                       | Lấy cảnh báo tồn kho thấp       |

### ❤️ Favorite Controller (`/favorites`)

| Method | Endpoint            | Mô tả                                |
| ------ | ------------------- | ------------------------------------ |
| GET    | `/favorites`        | Lấy danh sách sản phẩm yêu thích     |
| PUT    | `/favorites/toggle` | Toggle trạng thái yêu thích sản phẩm |

### 🔍 Image Search Controller (`/image-search`)

| Method | Endpoint               | Mô tả                  |
| ------ | ---------------------- | ---------------------- |
| POST   | `/image-search/search` | Tìm kiếm bằng hình ảnh |

### 💰 Tax Controller (`/tax`)

| Method | Endpoint                          | Mô tả                  |
| ------ | --------------------------------- | ---------------------- |
| POST   | `/tax/calculate`                  | Tính toán thuế         |
| GET    | `/tax/summary/region/:regionCode` | Tóm tắt thuế theo vùng |
| GET    | `/tax/recommendations`            | Gợi ý thuế             |
| GET    | `/tax/policy`                     | Chính sách thuế        |

### 📊 Status Controller (`/status`)

| Method | Endpoint  | Mô tả                   |
| ------ | --------- | ----------------------- |
| GET    | `/status` | Lấy trạng thái hệ thống |

### 🤖 M2 Bot Controller (`/m2-bot`)

| Method | Endpoint                                 | Mô tả                     |
| ------ | ---------------------------------------- | ------------------------- |
| GET    | `/m2-bot/suggest-similar-users/:user_id` | Gợi ý người dùng tương tự |

### 🎯 M2 Product Recommender Controller (`/m2-recommender`)

| Method | Endpoint                                    | Mô tả                   |
| ------ | ------------------------------------------- | ----------------------- |
| GET    | `/m2-recommender/suggest-products/:user_id` | Gợi ý sản phẩm cho user |

---

## Ghi chú

- Tất cả endpoints có 🔒 yêu cầu authentication (JWT token)
- Endpoints có `public` trong URL không yêu cầu authentication
- Một số endpoints yêu cầu quyền admin/superadmin
- File này được tạo tự động từ source code controllers

**Tổng cộng**:

- **M1 Module**: 35+ endpoints
- **M2 Module**: 65+ endpoints
- **Tổng**: 100+ endpoints

Cập nhật lần cuối: Ngày 31 tháng 7 năm 2025
