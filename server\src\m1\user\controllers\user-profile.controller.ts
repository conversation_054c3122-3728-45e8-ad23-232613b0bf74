import {
  Controller,
  Get,
  Put,
  Req,
  Body,
  UseGuards,
  Delete,
  Post,
  Param,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { UserService } from '../services/user-profile.service';
import { M1BotService } from '../services/m1-bot.service';
import { UserBehaviorLogService } from 'src/m_log/log/user-behavior-log.service';
import { UserBehaviorEventType } from 'src/m2/shop/entities/user_behavior.entity';
import { UpdateUserProfileDto } from '../dto/update-user-profile.dto';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadAvatarResponseDto } from '../dto/avatar-upload.dto';
import { AssignRoleDto } from '../dto/assign-role.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';

@ApiTags('Users Profile')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
@Controller('users')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly m1BotService: M1BotService,
    private readonly userBehaviorLogService: UserBehaviorLogService,
  ) {}
  /**
   * 🤖 AI Onboarding Assistant: Analyze user input and return intent/entities
   */
  @Post('ai-onboarding')
  @ApiOperation({
    summary: 'AI Onboarding Assistant: Phân tích ý định và thực thể từ câu hỏi người dùng',
  })
  async aiOnboarding(@Req() req: Request & { user?: any }, @Body('text') text: string) {
    const userId = req.user?.userId;
    const result = await this.m1BotService.analyze(text);
    // Log to user behavior log (ai_chat_log)
    await this.userBehaviorLogService.saveUserBehavior({
      user_id: userId,
      event_type: UserBehaviorEventType.AI_ONBOARDING,
      event_data: { text, ...result },
    });
    return result;
  }

  /**
   * 🔍 Lấy thông tin hồ sơ người dùng hiện tại
   */
  @Get('profile')
  @ApiOperation({ summary: 'Hồ sơ người dùng đã đăng nhập' })
  getProfile(@Req() req: Request & { user?: any }) {
    const userId = req.user?.userId;
    return this.userService.getProfile(userId);
  }

  /**
   * ✏️ Cập nhật hồ sơ cá nhân người dùng
   */
  @Put('profile')
  @ApiOperation({ summary: 'Cập nhật hồ sơ người dùng đã đăng nhập' })
  updateProfile(@Req() req: Request & { user?: any }, @Body() dto: UpdateUserProfileDto) {
    const userId = req.user?.userId;
    return this.userService.updateProfile(userId, dto);
  }

  /**
   * ✏️ Cập nhật mật khẩu
   */
  @Post('change-password')
  @ApiOperation({ summary: 'Đổi mật khẩu người dùng (yêu cầu mật khẩu cũ)' })
  async changePassword(@Req() req: Request & { user?: any }, @Body() dto: ChangePasswordDto) {
    const userId = req.user?.userId;
    await this.userService.changePassword(userId, dto);
  }

  /**
   * 🧾 Tạo mới mã QR dạng danh thiếp chứa thông tin người dùng
   */
  @Post('profile/qr')
  @ApiOperation({ summary: 'QR hồ sơ người dùng đã đăng nhập' })
  regenerateProfileQRCode(@Req() req: Request & { user?: any }) {
    const userId = req.user?.userId;
    return this.userService.regenerateProfileQRCode(userId);
  }

  /**
   * 📎 Lấy danh sách tất cả mã QR của người dùng
   */
  @Get('qrcodes')
  @ApiOperation({ summary: 'Danh sách QR người dùng đã đăng nhập' })
  getAllQRCodes(@Req() req: Request & { user?: any }) {
    const userId = req.user?.userId;
    return this.userService.getAllUserQRCodes(userId);
  }

  /**
   * ❌ Xoá mã QR cụ thể theo ID (phải đúng chủ sở hữu)
   */
  @Delete('qrcodes/:id')
  @ApiOperation({ summary: 'Xóa QR người dùng' })
  deleteQRCode(@Req() req: Request & { user?: any }, @Param('id') qrId: string) {
    const userId = req.user?.userId;
    return this.userService.deleteQRCode(userId, qrId);
  }

  @Post('avatar')
  @ApiOperation({ summary: 'Upload avatar' })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async uploadAvatar(
    @UploadedFile() file: Express.Multer.File,
    @Req() req: Request & { user?: any },
  ): Promise<UploadAvatarResponseDto> {
    const userId = req.user['userId'];
    return this.userService.uploadAvatar(userId, file);
  }

  /**
   * ✅ Gán quyền vai trò cho người dùng
   */
  @Post('assign-role')
  @ApiOperation({ summary: 'Gán quyền cho người dùng' })
  assignRole(@Body() dto: AssignRoleDto) {
    return this.userService.assignRoleToUser(dto);
  }
}
