import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiConsumes,
} from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { FileInterceptor } from '@nestjs/platform-express';
import { ProductCategoriesService } from '../services/product-categories.service';
import { CreateProductCategoryDto } from '../dto/create-product-category.dto';
import { UpdateProductCategoryDto } from '../dto/update-product-category.dto';
import { AllowAnonymous } from 'src/common/decorators/allow-anonymous.decorator';

@ApiTags('Product Categories')
@Controller('categories')
export class ProductCategoriesController {
  constructor(private readonly categoriesService: ProductCategoriesService) {}

  // PUBLIC ENDPOINTS
  @Get('public')
  @ApiOperation({ summary: 'L<PERSON>y danh sách danh mục sản phẩm (công khai)' })
  @ApiResponse({ status: 200, description: 'Danh sách danh mục công khai' })
  @AllowAnonymous()
  async getPublicCategories(@Query('type') type?: string) {
    return this.categoriesService.getCategories(type);
  }

  @Get('public/:id')
  @AllowAnonymous()
  @ApiOperation({ summary: 'Lấy chi tiết danh mục theo ID (công khai)' })
  @ApiResponse({ status: 200, description: 'Chi tiết danh mục công khai' })
  async getPublicCategoryById(@Param('id') id: string) {
    return this.categoriesService.getCategoryById(id);
  }

  // ADMIN MANAGEMENT ENDPOINTS
  @Get()
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Lấy danh sách danh mục sản phẩm (quản lý)' })
  @ApiResponse({ status: 200, description: 'Danh sách danh mục cho quản lý' })
  async getAdminCategories(@Query('type') type?: string) {
    return this.categoriesService.getCategories(type);
  }

  @Post()
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Tạo danh mục sản phẩm mới' })
  @ApiResponse({ status: 201, description: 'Tạo danh mục thành công' })
  async createCategory(@Body() createCategoryDto: CreateProductCategoryDto) {
    return this.categoriesService.createCategory(createCategoryDto);
  }

  @Get(':id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Lấy chi tiết danh mục theo ID (quản lý)' })
  @ApiResponse({ status: 200, description: 'Chi tiết danh mục cho quản lý' })
  async getAdminCategoryById(@Param('id') id: string) {
    return this.categoriesService.getCategoryById(id);
  }

  @Put(':id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Cập nhật danh mục sản phẩm' })
  @ApiResponse({ status: 200, description: 'Cập nhật thành công' })
  async updateCategory(
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateProductCategoryDto,
  ) {
    return this.categoriesService.updateCategory(id, updateCategoryDto);
  }

  @Delete(':id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Xóa danh mục sản phẩm' })
  @ApiResponse({ status: 200, description: 'Xóa thành công' })
  async deleteCategory(@Param('id') id: string) {
    return this.categoriesService.deleteCategory(id);
  }

  @Post('upload-icon')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Upload icon cho danh mục' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: { fileSize: 2 * 1024 * 1024 }, // 2MB limit
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/^image\/(jpeg|png|jpg|webp|svg)$/)) {
          return cb(
            new BadRequestException('Chỉ cho phép upload ảnh jpeg, png, jpg, webp, svg'),
            false,
          );
        }
        cb(null, true);
      },
    }),
  )
  async uploadIcon(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('Không có file nào được upload');
    }
    return this.categoriesService.uploadIcon(file);
  }
}
