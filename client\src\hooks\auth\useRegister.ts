import { useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import {
  useRegisterMutation,
  useSendOtpMutation,
  useVerifyOtpMutation,
  type SendOtpProps,
  type SignUpProps,
  type VerifyOtpProps,
} from '../../services/M1/auth'
import { ROUTES } from '../../constants/routes'
import { useToast } from '@/components/Toast'

export const useRegister = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const [registerMutation] = useRegisterMutation()
  const [sendOtpMutation] = useSendOtpMutation()
  const [verifyOtpMutation] = useVerifyOtpMutation()
  const { showError, showSuccess } = useToast()

  const register = async (data: SignUpProps) => {
    try {
      const loginRes = await registerMutation(data).unwrap()
      showSuccess('Đăng ký thành công! Vui lòng đăng nhập.')
      navigate(ROUTES.LOGIN)
    } catch (error: any) {
      // Ưu tiên lấy message chi tiết từ BE
      const message = error?.data?.message || error?.message || 'Đăng ký thất bại. Vui lòng thử lại.'
      showError(message)
      throw new Error(message)
    }
  }

  const emailVerification = async (data: VerifyOtpProps) => {
    try {
      await verifyOtpMutation(data).unwrap()
      // Điều hướng hoặc thông báo thành công nếu cần
    } catch (error: any) {
      const message = error?.message || 'Gửi mã xác minh thất bại. Vui lòng thử lại.'
      showError(message)
      throw new Error(message)
    }
  }

  const sendOtpEmailVerification = async (data: SendOtpProps) => {
    try {
      await sendOtpMutation(data).unwrap()
      // Thông báo hoặc logic bổ sung
    } catch (error: any) {
      const message = error?.message || 'Gửi mã OTP thất bại. Vui lòng thử lại.'
      showError(message)
      throw new Error(message)
    }
  }

  return { register, emailVerification, sendOtpEmailVerification }
}
