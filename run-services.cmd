@echo off
setlocal

:: BASE DIR
set BASE_DIR=D:\M1 Duy\tapviet-M1-M4

:: MENU
echo.
echo ==== CHON SERVICE MUON CHAY ====
echo [3] Faiss
echo [4] Clip
echo [8] Object Detection Service (YOLOv5)
echo [1] Client
echo [2] NLU Bot
echo [6] Docker: pg-nest
echo [7] Docker: rabbitmq
echo [5] Server
echo.

set /p SELECTED=Nhap cac so (cach nhau boi dau phay, Enter de chay tat ca): 

if "%SELECTED%"=="" set SELECTED=3,4,8,1,2,6,7,5

:: Ham kiem tra co chua


call :contains "%SELECTED%" "3"
if !errorlevel! == 0 (
    echo Dang bat Faiss Service...
    start powershell -NoExit -Command "cd '%BASE_DIR%'; python -m venv .venv; .\.venv\Scripts\Activate.ps1; cd .\faiss_service; pip install -r requirements.txt; python app.py"
)

call :contains "%SELECTED%" "4"
if !errorlevel! == 0 (
    echo Dang bat Clip Service...
    start powershell -NoExit -Command "cd '%BASE_DIR%\clip_service'; pip install -r requirements.txt; python main.py"
)

call :contains "%SELECTED%" "8"
if !errorlevel! == 0 (
    echo Dang bat Object Detection Service (YOLOv5)...
    start powershell -NoExit -Command "cd '%BASE_DIR%\object_detection_service'; pip install -r requirements.txt; uvicorn main:app --host 0.0.0.0 --port 8003"
)

call :contains "%SELECTED%" "1"
if !errorlevel! == 0 (
    echo Dang bat Client...
    start powershell -NoExit -Command "cd '%BASE_DIR%\client'; npm run dev"
)

call :contains "%SELECTED%" "2"
if !errorlevel! == 0 (
    echo Dang bat NLU Bot Service...
    start powershell -NoExit -Command "cd '%BASE_DIR%\server'; npm run m1-bot-nlu:start"
)

call :contains "%SELECTED%" "6"
if !errorlevel! == 0 (
    echo Kiem tra Docker container pg-nest...
    docker ps -q -f name=pg-nest >nul || docker start pg-nest
)

call :contains "%SELECTED%" "7"
if !errorlevel! == 0 (
    echo Kiem tra Docker container rabbitmq...
    docker ps -q -f name=rabbitmq >nul || docker start rabbitmq
)

call :contains "%SELECTED%" "5"
if !errorlevel! == 0 (
    echo Dang bat Server...
    start powershell -NoExit -Command "cd '%BASE_DIR%\server'; npm run start:dev"
)

call :contains "%SELECTED%" "6"
if !errorlevel! == 0 (
    echo Kiem tra Docker container pg-nest...
    docker ps -q -f name=pg-nest >nul || docker start pg-nest
)


call :contains "%SELECTED%" "7"
if !errorlevel! == 0 (
    echo Kiem tra Docker container rabbitmq...
    docker ps -q -f name=rabbitmq >nul || docker start rabbitmq
)

call :contains "%SELECTED%" "8"
if !errorlevel! == 0 (
    echo Dang bat Object Detection Service (YOLOv5)...
    start powershell -NoExit -Command "cd '%BASE_DIR%\object_detection_service'; pip install -r requirements.txt; uvicorn main:app --host 0.0.0.0 --port 8003"
)

echo.
echo Chon service muon chay (nhap so cach nhau boi dau phay, Enter de chay tat ca):
echo 1. Client
echo 2. NLU Bot
echo 3. Faiss
echo 4. Clip
echo 5. Server
echo 6. Docker: pg-nest
echo 7. Docker: rabbitmq
echo 8. Object Detection Service (YOLOv5)
set /p USER_CHOICE=>> 

if "%USER_CHOICE%"=="" (
    set "USER_CHOICE=3,4,8,1,2,6,7,5"
)

call :has %USER_CHOICE% 6
if %errorlevel%==0 docker start pg-nest

call :has %USER_CHOICE% 7
if %errorlevel%==0 docker start rabbitmq

call :has %USER_CHOICE% 3
if %errorlevel%==0 start "Faiss" cmd /k "cd /d %BASE_DIR% && python -m venv .venv && call .venv\Scripts\activate && cd faiss_service && pip install -r requirements.txt && python app.py"

call :has %USER_CHOICE% 4
if %errorlevel%==0 start "Clip" cmd /k "cd /d %BASE_DIR%\clip_service && pip install -r requirements.txt && python main.py"

call :has %USER_CHOICE% 8
if %errorlevel%==0 start "YOLOv5" cmd /k "cd /d %BASE_DIR%\object_detection_service && pip install -r requirements.txt && uvicorn main:app --host 0.0.0.0 --port 8003"

call :has %USER_CHOICE% 1
if %errorlevel%==0 start "Client" cmd /k "cd /d %BASE_DIR%\client && npm run dev"

call :has %USER_CHOICE% 2
if %errorlevel%==0 start "NLU Bot" cmd /k "cd /d %BASE_DIR%\server && npm run m1-bot-nlu:start"

call :has %USER_CHOICE% 5
if %errorlevel%==0 start "Server" cmd /k "cd /d %BASE_DIR%\server && npm run start:dev"

title DA BAT XONG - Duonq Launcher
goto :eof

:has
echo %~1 | findstr /r "\b%~2\b" >nul
exit /b %errorlevel%
