// cloudinary.service.ts
import { Inject, Injectable } from '@nestjs/common';
import { UploadApiResponse, v2 as cloudinary } from 'cloudinary';

@Injectable()
export class CloudinaryService {
  constructor(@Inject('CLOUDINARY') private cloudinary) {}

  // async uploadFile(file: Express.Multer.File): Promise<UploadApiResponse> {
  //   return new Promise((resolve, reject) => {
  //     const stream = this.cloudinary.uploader.upload_stream((error, result) => {
  //       if (error) return reject(error);
  //       resolve(result);
  //     });
  //     stream.end(file.buffer);
  //   });
  // }

  async uploadFile(file: Express.Multer.File): Promise<UploadApiResponse> {
    try {
      return new Promise((resolve, reject) => {
        const stream = this.cloudinary.uploader.upload_stream(
          {
            transformation: [
              {
                overlay: 'tpfxkuzci3esoewenq2x',
                gravity: 'south_east',
                width: 150,
                opacity: 60,
                x: 10,
                y: 10,
                crop: 'scale',
              },
            ],
          },
          (error, result) => {
            if (error) return reject(error);
            resolve(result);
          },
        );
        stream.end(file.buffer);
      });
    } catch (error) {
      console.log('Error uploading file to Cloudinary:', error);
      throw new Error('Lỗi upload file: ' + (error?.message || error));
    }
  }

  async deleteFile(publicId: string): Promise<any> {
    return this.cloudinary.uploader.destroy(publicId);
  }

  /**
   * Upload nhiều file lên Cloudinary
   * @param files Danh sách file Multer
   * @returns Danh sách kết quả upload
   */
  async uploadFiles(files: Express.Multer.File[]): Promise<UploadApiResponse[]> {
    const results: UploadApiResponse[] = [];
    for (const file of files) {
      const uploaded = await this.uploadFile(file);
      results.push(uploaded);
    }
    return results;
  }

  /**
   * Xoá nhiều file trên Cloudinary
   * @param publicIds Danh sách publicId
   * @returns Kết quả xoá
   */
  async deleteFiles(publicIds: string[]): Promise<any[]> {
    const results: any[] = [];
    for (const id of publicIds) {
      const res: any = await this.deleteFile(id);
      results.push(res);
    }
    return results;
  }
}
