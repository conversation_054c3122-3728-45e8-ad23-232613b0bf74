.container {
  // padding: 24px;
  // background-color: #f5f5f5;
  min-height: 100vh;

  // Global fix cho tất cả Ant Design inputs trong component này
  :global(.ant-input),
  :global(.ant-input-affix-wrapper) {
    display: flex !important;
    align-items: center !important;

    input {
      line-height: 1.5715 !important;
      vertical-align: middle !important;
    }

    &::placeholder {
      line-height: normal !important;
      vertical-align: middle !important;
    }
  }

  :global(.ant-select-selector) {
    display: flex !important;
    align-items: center !important;

    .ant-select-selection-search-input {
      height: 100% !important;
      line-height: 1.5715 !important;
    }

    .ant-select-selection-placeholder {
      line-height: 38px !important;
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 0 4px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  @media (max-width: 480px) {
    margin-bottom: 16px;
    gap: 12px;
  }
}

.headerLeft {
  flex: 1;
}

.title {
  font-size: 28px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 24px;
  }

  @media (max-width: 480px) {
    font-size: 20px;
    margin: 0 0 6px 0;
  }
}

.subtitle {
  font-size: 16px;
  color: #8c8c8c;
  margin: 0;
  line-height: 1.5;

  @media (max-width: 768px) {
    font-size: 14px;
  }

  @media (max-width: 480px) {
    font-size: 13px;
  }
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 12px;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: stretch;
  }

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 8px;
  }
}

.roleBtn,
.createRoleBtn {
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    flex: 1;
    height: 44px;
  }

  @media (max-width: 480px) {
    width: 100%;
    height: 48px;
    font-size: 14px;
  }
}

.createRoleBtn {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  }

  @media (max-width: 480px) {
    &:hover {
      transform: none;
    }
  }
}

.mobileSearchContainer {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;

  @media (min-width: 769px) {
    display: none;
  }
}

.mobileSearchInput {
  flex: 1;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  height: 36px;
  transition: all 0.2s ease;

  &:focus,
  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;
  }
}

.mobileFilterButton {
  border-radius: 8px;
  height: 36px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  color: #595959;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    background-color: #f8f9ff;
  }
}

.statsRow {
  margin-bottom: 24px;

  @media (max-width: 768px) {
    margin-bottom: 16px;
  }

  @media (max-width: 480px) {
    margin-bottom: 12px;
  }
}

.statCard {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  background-color: #ffffff;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  @media (max-width: 480px) {
    &:hover {
      transform: none;
    }
  }

  :global(.ant-card-body) {
    padding: 20px;

    @media (max-width: 768px) {
      padding: 16px;
    }

    @media (max-width: 480px) {
      padding: 12px;
    }
  }

  :global(.ant-statistic-title) {
    font-size: 14px;
    color: #8c8c8c;
    margin-bottom: 8px;
    font-weight: 500;

    @media (max-width: 480px) {
      font-size: 12px;
      margin-bottom: 6px;
    }
  }

  :global(.ant-statistic-content) {
    font-size: 24px;
    font-weight: 600;

    @media (max-width: 768px) {
      font-size: 20px;
    }

    @media (max-width: 480px) {
      font-size: 18px;
    }
  }
}

.filterCard {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  background-color: #ffffff;

  @media (max-width: 768px) {
    margin-bottom: 16px;
  }

  @media (max-width: 480px) {
    margin-bottom: 12px;
  }

  :global(.ant-card-body) {
    padding: 20px;

    @media (max-width: 768px) {
      padding: 16px;
    }

    @media (max-width: 480px) {
      padding: 12px;
    }
  }
}

.searchInput {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  height: 36px;
  transition: all 0.2s ease;

  &:focus,
  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;
  }
}

.filterSelect {
  width: 100%;
  border-radius: 8px;

  :global(.ant-select-selector) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: 36px;
    transition: all 0.2s ease;

    &:focus,
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

.filterActions {
  display: flex;
  gap: 8px;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: space-between;
  }

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 8px;
  }

  .ant-btn {
    height: 40px;
    border-radius: 8px;
    font-weight: 500;

    @media (max-width: 480px) {
      height: 44px;
      width: 100%;
      font-size: 14px;
    }
  }
}

.filterCard {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  background-color: #ffffff;

  @media (max-width: 768px) {
    margin-bottom: 16px;
  }

  @media (max-width: 480px) {
    margin-bottom: 12px;
  }

  :global(.ant-card-body) {
    padding: 20px;

    @media (max-width: 768px) {
      padding: 16px;
    }

    @media (max-width: 480px) {
      padding: 12px;
    }
  }
}

.tableCard {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  background-color: #ffffff;
  overflow: hidden;

  :global(.ant-card-body) {
    padding: 0;
    overflow-x: auto;
    width: 100%;
  }

  @media (max-width: 768px) {
    :global(.ant-card-body) {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      width: 100%;
    }
  }

  @media (max-width: 480px) {
    :global(.ant-card-body) {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      width: 100%;
    }
  }
}

.userTable {
  :global(.ant-table) {
    border-radius: 12px;
    overflow: hidden;
    background-color: #ffffff;
    width: 100% !important;
  }

  :global(.ant-table-container) {
    width: 100% !important;
  }

  :global(.ant-table-content) {
    width: 100% !important;
  }

  :global(.ant-table-thead) > :global(tr) > :global(th) {
    background-color: #fafafa;
    border-bottom: 2px solid #f0f0f0;
    font-weight: 600;
    color: #262626;
    padding: 16px;

    @media (max-width: 768px) {
      padding: 12px 8px;
      font-size: 13px;
    }

    @media (max-width: 480px) {
      padding: 8px 4px;
      font-size: 12px;
    }
  }

  :global(.ant-table-tbody) > :global(tr) > :global(td) {
    padding: 16px;
    border-bottom: 1px solid #f5f5f5;
    background-color: #ffffff;

    @media (max-width: 768px) {
      padding: 12px 8px;
    }

    @media (max-width: 480px) {
      padding: 8px 4px;
    }
  }

  :global(.ant-table-tbody) > :global(tr):hover > :global(td) {
    background-color: #f8f9ff;
  }

  :global(.ant-pagination) {
    margin: 20px;
    text-align: center;
    padding-right: 16px !important;

    @media (max-width: 768px) {
      margin: 16px;
    }

    @media (max-width: 480px) {
      margin: 12px;
      font-size: 12px;
    }
  }

  // Mobile table optimizations
  @media (max-width: 768px) {
    :global(.ant-table) {
      font-size: 13px;
      width: 100% !important;
      table-layout: auto !important; // Changed from fixed to auto
      min-width: 600px !important;
    }

    // Override for mobile cards
    :global(.ant-table-tbody) {
      :global(.ant-table-cell) {
        word-break: normal !important;
        white-space: normal !important;
        overflow: visible !important;
      }
    }

    :global(.ant-table-container) {
      width: 100% !important;
      overflow-x: auto !important;
    }

    :global(.ant-table-content) {
      width: 100% !important;
      min-width: 600px !important;
    }

    // Optimize column widths for mobile
    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(1)) {
      width: 50px !important;
      min-width: 50px !important;
      max-width: 50px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(2)) {
      width: 50px !important;
      min-width: 50px !important;
      max-width: 50px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(3)) {
      width: auto !important;
      min-width: 120px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(4)) {
      width: 80px !important;
      min-width: 80px !important;
      max-width: 80px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(5)) {
      width: 80px !important;
      min-width: 80px !important;
      max-width: 80px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(6)) {
      width: 80px !important;
      min-width: 80px !important;
      max-width: 80px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(7)) {
      width: 80px !important;
      min-width: 80px !important;
      max-width: 80px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(8)) {
      width: 80px !important;
      min-width: 80px !important;
      max-width: 80px !important;
    }

    // Apply same widths to tbody cells
    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(1)) {
      width: 50px !important;
      min-width: 50px !important;
      max-width: 50px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(2)) {
      width: 50px !important;
      min-width: 50px !important;
      max-width: 50px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(3)) {
      width: auto !important;
      min-width: 120px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(4)) {
      width: 80px !important;
      min-width: 80px !important;
      max-width: 80px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(5)) {
      width: 80px !important;
      min-width: 80px !important;
      max-width: 80px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(6)) {
      width: 80px !important;
      min-width: 80px !important;
      max-width: 80px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(7)) {
      width: 80px !important;
      min-width: 80px !important;
      max-width: 80px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(8)) {
      width: 80px !important;
      min-width: 80px !important;
      max-width: 80px !important;
    }
  }

  @media (max-width: 480px) {
    :global(.ant-table) {
      font-size: 12px;
      width: 100% !important;
      table-layout: auto !important; // Changed from fixed to auto
      min-width: 400px !important;
    }

    // Override for mobile cards
    :global(.ant-table-tbody) {
      :global(.ant-table-cell) {
        word-break: normal !important;
        white-space: normal !important;
        overflow: visible !important;
      }
    }

    :global(.ant-table-container) {
      width: 100% !important;
      overflow-x: auto !important;
    }

    :global(.ant-table-content) {
      width: 100% !important;
      min-width: 400px !important;
    }

    // Hide less important columns on mobile
    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(6)),
    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(7)),
    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(6)),
    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(7)) {
      display: none !important;
    }

    // Further optimize column widths for small mobile
    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(1)) {
      width: 40px !important;
      min-width: 40px !important;
      max-width: 40px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(2)) {
      width: 40px !important;
      min-width: 40px !important;
      max-width: 40px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(3)) {
      width: auto !important;
      min-width: 100px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(4)) {
      width: 70px !important;
      min-width: 70px !important;
      max-width: 70px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(5)) {
      width: 70px !important;
      min-width: 70px !important;
      max-width: 70px !important;
    }

    :global(.ant-table-thead) > :global(tr) > :global(th:nth-child(8)) {
      width: 70px !important;
      min-width: 70px !important;
      max-width: 70px !important;
    }

    // Apply same widths to tbody cells
    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(1)) {
      width: 40px !important;
      min-width: 40px !important;
      max-width: 40px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(2)) {
      width: 40px !important;
      min-width: 40px !important;
      max-width: 40px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(3)) {
      width: auto !important;
      min-width: 100px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(4)) {
      width: 70px !important;
      min-width: 70px !important;
      max-width: 70px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(5)) {
      width: 70px !important;
      min-width: 70px !important;
      max-width: 70px !important;
    }

    :global(.ant-table-tbody) > :global(tr) > :global(td:nth-child(8)) {
      width: 70px !important;
      min-width: 70px !important;
      max-width: 70px !important;
    }
  }
}

.userAvatar {
  border: 2px solid #f0f0f0;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    transform: scale(1.05);
  }

  @media (max-width: 480px) {
    &:hover {
      transform: none;
    }
  }
}

.userInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;

  @media (max-width: 480px) {
    gap: 2px;
  }
}

.userName {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;

  @media (max-width: 768px) {
    font-size: 15px;
  }

  @media (max-width: 480px) {
    font-size: 14px;
  }
}

.userCode {
  font-size: 13px;
  color: #8c8c8c;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  width: fit-content;

  @media (max-width: 480px) {
    font-size: 11px;
    padding: 1px 4px;
  }
}

.userContact {
  display: flex;
  flex-direction: column;
  gap: 2px;

  span {
    font-size: 14px;
    color: #595959;
    line-height: 1.4;

    &:first-child {
      color: #1890ff;
    }

    @media (max-width: 768px) {
      font-size: 13px;
    }

    @media (max-width: 480px) {
      font-size: 12px;
    }
  }
}

.actionSpace {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 100%;
}

.actionBtn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &:hover {
    background-color: #f0f0f0;
    transform: scale(1.1);
  }

  &.ant-btn-dangerous:hover {
    background-color: #fff2f0;
    color: #ff4d4f;
  }

  @media (max-width: 768px) {
    width: 28px;
    height: 28px;
  }

  @media (max-width: 480px) {
    width: 24px;
    height: 24px;
    font-size: 12px;

    &:hover {
      transform: none;
    }
  }
}

.userModal {
  :global(.ant-modal-header) {
    border-bottom: 1px solid #f0f0f0;
    padding: 20px 24px;
    border-radius: 8px 8px 0 0;
    background-color: #ffffff;

    @media (max-width: 480px) {
      padding: 12px 16px;
    }
  }

  :global(.ant-modal-title) {
    font-size: 18px;
    font-weight: 600;
    color: #262626;

    @media (max-width: 480px) {
      font-size: 16px;
    }
  }

  :global(.ant-modal-body) {
    padding: 24px;
    background-color: #ffffff;

    @media (max-width: 768px) {
      padding: 20px;
    }

    @media (max-width: 480px) {
      padding: 16px;
    }
  }

  :global(.ant-modal-footer) {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;
    border-radius: 0 0 8px 8px;
    background-color: #ffffff;

    @media (max-width: 480px) {
      padding: 12px 16px;
    }
  }

  :global(.ant-form-item-label) > :global(label) {
    font-weight: 500;
    color: #262626;
    font-size: 14px;

    @media (max-width: 480px) {
      font-size: 13px;
    }
  }

  :global(.ant-input),
  :global(.ant-select-selector) {
    height: 40px !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    line-height: 1.5715 !important;
    background-color: #ffffff;

    @media (max-width: 480px) {
      height: 44px !important;
      font-size: 16px !important; // Prevent zoom on iOS
    }

    &::placeholder {
      line-height: normal !important;
      vertical-align: middle !important;
    }
  }

  :global(.ant-select-selector) {
    padding: 0 12px !important;

    .ant-select-selection-search-input {
      height: 100% !important;
      line-height: 1.5715 !important;
    }

    .ant-select-selection-placeholder {
      line-height: 38px !important;
      display: flex !important;
      align-items: center !important;

      @media (max-width: 480px) {
        line-height: 42px !important;
      }
    }

    .ant-select-selection-item {
      line-height: 38px !important;
      display: flex !important;
      align-items: center !important;

      @media (max-width: 480px) {
        line-height: 42px !important;
      }
    }
  }

  :global(.ant-btn) {
    height: 36px;
    border-radius: 6px;
    font-weight: 500;

    @media (max-width: 480px) {
      height: 40px;
      font-size: 14px;
    }
  }
}

// Mobile table wrapper - FORCE NORMAL TEXT DISPLAY
.mobileTableWrapper {
  width: 100% !important;
  overflow-x: auto !important;

  .ant-table {
    table-layout: auto !important;
    width: 100% !important;
    min-width: 100% !important;

    .ant-table-tbody {
      .ant-table-cell {
        word-break: keep-all !important;
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: unset !important;
        width: auto !important;
        min-width: 200px !important;

        // Force normal text display for all content
        * {
          word-break: keep-all !important;
          white-space: normal !important;
          overflow-wrap: normal !important;
          text-overflow: unset !important;
          display: inline !important;
          width: auto !important;
          max-width: none !important;
        }

        // Block elements should remain block
        div,
        p,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          display: block !important;
          white-space: normal !important;
          word-break: keep-all !important;
        }
      }
    }
  }
}

.mobileTable {
  .ant-table-tbody {
    .ant-table-cell {
      padding: 12px !important;

      // Override any inherited styles
      * {
        font-family: inherit !important;
        letter-spacing: normal !important;
        word-spacing: normal !important;
      }
    }
  }
}

// Custom mobile layout - NO TABLE
.mobileUsersList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0;
  margin: 0;
}

.mobileUserCardCustom {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: block;
  width: 100%;
  box-sizing: border-box;
}

.mobileUserHeaderCustom {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
  flex-wrap: nowrap;
}

.mobileUserInfoCustom {
  flex: 1;
  min-width: 0;
  display: block;
}

.mobileUserNameCustom {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  line-height: 1.4;
  display: block;
  white-space: normal;
  word-break: keep-all;
  overflow-wrap: break-word;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.mobileUserCodeCustom {
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Courier New', monospace;
  margin-bottom: 6px;
  display: block;
}

.mobileUserContactCustom {
  font-size: 13px;
  color: #666;

  div {
    margin-bottom: 3px;
    display: block;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.mobileUserDetailsCustom {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.mobileActionsCustom {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.mobilePagination {
  margin-top: 16px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  display: flex;
  justify-content: center;
}

// Mobile user card styles - COMPLETE OVERRIDE
.mobileUserCard {
  padding: 12px !important;
  border-bottom: 1px solid #f0f0f0 !important;
  width: 100% !important;
  overflow: visible !important;
  display: block !important;
  position: relative !important;
  background: white !important;

  &:last-child {
    border-bottom: none;
  }

  // ULTIMATE OVERRIDE - FORCE NORMAL TEXT DISPLAY
  *,
  *::before,
  *::after {
    all: unset !important;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    color: inherit !important;
    line-height: inherit !important;
    text-align: left !important;
    direction: ltr !important;
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    unicode-bidi: normal !important;
    word-break: keep-all !important;
    white-space: normal !important;
    overflow-wrap: normal !important;
    text-overflow: unset !important;
    display: inline !important;
    width: auto !important;
    max-width: none !important;
    min-width: 0 !important;
    box-sizing: border-box !important;
  }

  // Block elements
  div,
  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    display: block !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

.mobileUserHeader {
  display: flex !important;
  align-items: flex-start !important;
  gap: 12px !important;
  margin-bottom: 8px !important;
  width: 100% !important;
  overflow: visible !important;
  flex-wrap: nowrap !important;
}

.userAvatarMobile {
  flex-shrink: 0;
}

.mobileUserInfo {
  flex: 1 1 auto !important;
  min-width: 120px !important;
  overflow: visible !important;
  width: auto !important;
}

.mobileUserName {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #333 !important;
  margin-bottom: 4px !important;
  line-height: 1.3 !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  white-space: nowrap !important;
  display: inline-block !important;
  width: auto !important;
  max-width: 100% !important;
  text-overflow: ellipsis !important;
  overflow: hidden !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  unicode-bidi: normal !important;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

.mobileUserCode {
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Courier New', monospace;
  margin-bottom: 4px;
}

.mobileUserContact {
  font-size: 12px;
  color: #666;

  div {
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.mobileUserDetails {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.mobileActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.mobileActionButton {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;

  &:hover {
    transform: scale(1.05);
  }
}

// Responsive Design - Clean breakpoints
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 8px;
  }
}

// Animation keyframes
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  animation: fadeIn 0.3s ease-out;
}

// Print styles
@media print {
  .container {
    background-color: white;
    padding: 0;
  }

  .headerRight,
  .filterCard,
  .actionBtn {
    display: none;
  }

  .userTable {
    :global(.ant-pagination) {
      display: none;
    }
  }
}

// Touch device optimizations
@media (hover: none) and (pointer: coarse) {
  .statCard:hover,
  .actionBtn:hover,
  .createRoleBtn:hover {
    transform: none;
  }

  .actionBtn {
    min-width: 44px;
    min-height: 44px;
  }
}
