import {
  Entity,
  Column,
  PrimaryColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  Index,
} from 'typeorm';
import { Product } from './products.entity';
import { Tag } from './tag.entity';

@Index(['product_id', 'tag_id'], { unique: true })
@Entity('product_tags')
export class ProductTag {
  @Index()
  @PrimaryColumn('uuid')
  product_id: string;

  @Index()
  @PrimaryColumn('uuid')
  tag_id: string;

  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @ManyToOne(() => Tag, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'tag_id' })
  tag: Tag;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;
}
