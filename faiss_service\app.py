from flask import Flask, request, jsonify
import numpy as np
import faiss
import os
import sqlite3

app = Flask(__name__)

# Khởi tạo FAISS index (giả sử vector 512 chiều)
d = 512
index_file = 'faiss_products.index'
id_map_file = 'id_map.npy'
index = None
id_map = []

def build_index_from_vectors(vectors, image_ids):
    global index, id_map
    print('[FAISS] Building index from POSTed vectors...')
    index = faiss.IndexFlatL2(d)
    id_map = []
    for vector, image_id in zip(vectors, image_ids):
        v = np.array(vector, dtype='float32').reshape(1, -1)
        index.add(v)
        id_map.append(image_id)
    print(f'[FAISS] Loaded {len(id_map)} vectors from POST.')

    # Xóa các hàm lưu/đọc index từ file, chỉ dùng index trong RAM

@app.route('/add_vector', methods=['POST'])
def add_vector():
    global index, id_map
    data = request.json
    vector = np.array(data['vector'], dtype='float32').reshape(1, -1)
    image_id = data['image_id']
    index.add(vector)
    id_map.append(image_id)
    return jsonify({'status': 'ok'})
@app.route('/build_index', methods=['POST'])
def build_index():
    # Nhận vectors và image_ids từ main service
    data = request.json
    vectors = data['vectors']
    image_ids = data['image_ids']
    build_index_from_vectors(vectors, image_ids)
    return jsonify({'status': 'built', 'count': len(image_ids)})

@app.route('/search_vector', methods=['POST'])
def search_vector():
    global index, id_map
    data = request.json
    vector = np.array(data['vector'], dtype='float32').reshape(1, -1)
    k = data.get('top_k', 1)
    D, I = index.search(vector, k)
    results = [id_map[i] for i in I[0]]
    return jsonify({'results': results, 'distances': D[0].tolist()})

    # Xóa các route lưu/đọc index từ file

if __name__ == '__main__':
    # Khởi động với index rỗng, chờ main service POST vectors lên qua /build_index
    index = faiss.IndexFlatL2(d)
    id_map = []
    app.run(host='0.0.0.0', port=8002)
