@import '../../../styles/variables.scss';

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

// Logo Section
.logoSection {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.tagline {
  font-size: 14px;
  color: #666;
  max-width: 120px;
  line-height: 1.3;
}

// Search Section
.searchSection {
  flex: 1;
  max-width: 500px;
  margin: 0 $spacing-xl;
  display: flex;
  align-items: center;
  gap: $spacing-md;

  @media (max-width: 768px) {
    margin: 0 $spacing-md;
    gap: $spacing-sm;
  }

  @media (max-width: 480px) {
    margin: 0 $spacing-sm;
    gap: $spacing-xs;
  }
}

.searchForm {
  position: relative;
  display: flex;
  flex: 1;
  max-width: 600px;

  @media (max-width: 768px) {
    max-width: none;
  }
}

.searchInput {
  flex: 1;
  height: 44px; // Touch-friendly
  padding: $spacing-md $spacing-lg;
  padding-right: 52px; // Space for button
  border: 2px solid #e0e0e0;
  border-radius: 24px;
  font-size: $font-size-base;
  outline: none;
  transition: all 0.3s ease;
  background: white;

  &:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
  }

  &::placeholder {
    color: #999;
    font-weight: $font-weight-normal;
  }

  // Prevent zoom on iOS
  @media (max-width: 768px) {
    font-size: 16px !important;
    height: 44px;
    padding: $spacing-md $spacing-md;
    padding-right: 48px;
  }

  @media (max-width: 480px) {
    padding: $spacing-sm $spacing-md;
    padding-right: 44px;
  }
}

.searchButton {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  background: #4CAF50;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #45a049;
    transform: translateY(-50%) scale(1.05);
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }

  @media (max-width: 768px) {
    width: 36px;
    height: 36px;
  }

  @media (max-width: 480px) {
    width: 32px;
    height: 32px;
  }
}

.searchIcon {
  width: 20px;
  height: 20px;
  fill: white;
}

// Image Search Button
.imageSearchButton {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  padding: $spacing-md $spacing-md;
  background: #f8f9fa;
  border: 2px solid #e0e0e0;
  border-radius: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: #333;
  white-space: nowrap;
  min-width: fit-content;
  min-height: 44px; // Touch-friendly

  &:hover {
    background: #e9ecef;
    border-color: #4CAF50;
    color: #4CAF50;
    transform: scale(1.02);
  }

  &:active {
    transform: scale(0.98);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f5f5f5;
    border-color: #ddd;
    color: #999;

    &:hover {
      transform: none;
    }
  }

  .imageSearchIcon {
    width: 18px;
    height: 18px;
  }

  .imageSearchText {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
  }

  @media (max-width: 768px) {
    padding: $spacing-sm $spacing-sm;
    font-size: $font-size-sm;
    min-height: 40px;

    .imageSearchIcon {
      width: 16px;
      height: 16px;
    }

    .imageSearchText {
      font-size: $font-size-sm;
    }
  }

  @media (max-width: 480px) {
    padding: $spacing-xs $spacing-sm;

    // Hide text on very small screens, show only icon
    .imageSearchText {
      display: none;
    }
  }
}

// User Actions
.userActions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notificationBtn,
.accountBtn,
.favoritesBtn,
.cartBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.3s;
  font-size: 14px;
  color: #333;

  &:hover {
    background: #f5f5f5;
  }
}

.cartBtn {
  position: relative;
}

.cartBadge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ff4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

// Navigation
.navigation {
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}

.navList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  height: 48px;
}

.navItem {
  display: flex;
  align-items: center;
}

.navLink {
  padding: 0 24px;
  height: 48px;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #666;
  font-weight: 500;
  transition: all 0.3s;
  border-bottom: 3px solid transparent;

  &:hover {
    color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
  }

  &.active {
    color: #4CAF50;
    border-bottom-color: #4CAF50;
  }
}

// Desktop/Mobile Layout Control
.desktopHeader {
  display: block;
}

.mobileHeader {
  display: none;
}

// Tablet adjustments
@media (max-width: 1024px) and (min-width: 769px) {
  .container {
    padding: 0 24px;
  }
  
  .searchSection {
    margin: 0 24px;
    gap: 8px;
  }
  
  .imageSearchButton {
    padding: 10px 12px;
    
    .imageSearchText {
      font-size: 13px;
    }
    
    .imageSearchIcon {
      width: 16px;
      height: 16px;
    }
  }
  
  .userActions {
    gap: 12px;
  }
  
  .notificationBtn,
  .accountBtn,
  .cartBtn {
    padding: 6px 12px;
    font-size: 13px;
  }
}

// Mobile Styles - Theo thiết kế cũ
@media (max-width: 768px) {
  .desktopHeader {
    display: none;
  }

  .mobileHeader {
    display: block;
  }

  // Mobile Top Bar - Layout như trong hình
  .mobileTopBar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    padding: 0 16px;
    background: white;
  }

  .mobileLeft {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .mobileRight {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .menuButton {
    background: none;
    border: none;
    cursor: pointer;
    width: 44px; // Touch-friendly size
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background: #f5f5f5;
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
      background: #e8f5e8;
    }

    svg {
      width: 24px;
      height: 24px;
    }
  }

  .mobileLogo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }

  .mobileSearchBtn,
  .mobileFavoritesBtn,
  .mobileNotificationBtn,
  .mobileCartBtn,
  .mobileImageSearchBtn {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    border-radius: 8px;
    transition: background-color 0.3s;

    &:hover {
      background: #f5f5f5;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .icon {
      width: 22px;
      height: 22px;
    }
  }

  // Mobile Search Section - Thêm nút tìm bằng ảnh
  .searchSection {
    margin: 0 16px;
    gap: 8px;
    max-width: none;
  }

  .imageSearchButton {
    padding: 8px 12px;
    
    .imageSearchText {
      display: none; // Ẩn text trên mobile, chỉ hiện icon
    }
    
    .imageSearchIcon {
      width: 20px;
      height: 20px;
    }
  }

  // Mobile Menu Overlay - Improved animations
  .mobileMenuOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    opacity: 0;
    animation: fadeIn 0.3s ease-out forwards;
    backdrop-filter: blur(2px);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  .mobileMenuContent {
    background: white;
    width: 320px; // Slightly wider for better touch targets
    height: 100vh;
    box-shadow: 2px 0 16px rgba(0, 0, 0, 0.15);
    overflow-y: auto;
    transform: translateX(-100%);
    animation: slideIn 0.3s ease-out forwards;
    border-radius: 0 16px 16px 0;

    // Smooth scrolling
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  @keyframes slideIn {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }

  .mobileMenuHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg $spacing-md; // Using spacing scale
    border-bottom: 1px solid #e0e0e0;
    background: #fafafa;
    min-height: 64px; // Ensure adequate touch area
  }

  .closeMenuBtn {
    background: none;
    border: none;
    cursor: pointer;
    width: 44px; // Touch-friendly size
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    border-radius: 50%;
    transition: all 0.3s ease;

    &:hover {
      background: #f5f5f5;
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }

    svg {
      width: 22px; // Slightly larger for better visibility
      height: 22px;
    }
  }

  .mobileMenuNav {
    padding: $spacing-lg 0;
  }

  .mobileMenuList {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .mobileMenuLink {
    display: flex;
    align-items: center;
    padding: $spacing-lg $spacing-md; // Better touch targets
    text-decoration: none;
    color: #333;
    font-weight: $font-weight-medium;
    font-size: $font-size-md; // Larger text for better readability
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    min-height: 56px; // Ensure adequate touch area

    &:hover {
      background: #f8f9fa;
      border-left-color: #4CAF50;
      color: #4CAF50;
      transform: translateX(4px);
    }

    &:active {
      background: #e8f5e8;
    }

    &.active {
      background: #e8f5e8;
      border-left-color: #4CAF50;
      color: #4CAF50;
      font-weight: $font-weight-semibold;
    }
  }
}
