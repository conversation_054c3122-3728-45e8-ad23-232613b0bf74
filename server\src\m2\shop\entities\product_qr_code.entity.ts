import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../../m1/user/entities/user.entity';
import { Product } from './products.entity';

export enum ProductQrCodeType {
  DEFAULT = 'default',
  REFERRAL = 'referral',
  INVENTORY = 'inventory',
  LOCATION = 'location',
}

@Entity('product_qr_codes')
export class ProductQrCode {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  product_id: string;

  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column({ type: 'varchar', length: 255 })
  qr_url: string;

  @Column({ type: 'varchar', length: 255 })
  target_url: string;

  @Column({ type: 'enum', enum: ProductQrCodeType, default: ProductQrCodeType.DEFAULT })
  type: ProductQrCodeType;

  @Column({ type: 'int', nullable: true, default: 0 })
  scan_count: number;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;
}
