import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTaxCalculationsTable1753942421491 implements MigrationInterface {
    name = 'CreateTaxCalculationsTable1753942421491'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."tax_calculations_user_role_enum" AS ENUM('R01', 'R02', 'R03', 'R04', 'R05', 'R06', 'R07', 'R08')`);
        await queryRunner.query(`CREATE TYPE "public"."tax_calculations_tax_type_enum" AS ENUM('VAT', 'PERSONAL_INCOME', 'CORPORATE_INCOME', 'INVESTMENT_INCOME', 'COOPERATIVE')`);
        await queryRunner.query(`CREATE TABLE "tax_calculations" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid NOT NULL, "order_id" uuid, "user_role" "public"."tax_calculations_user_role_enum" NOT NULL, "tax_type" "public"."tax_calculations_tax_type_enum" NOT NULL, "taxable_revenue" numeric(15,2) NOT NULL, "tax_rate" numeric(5,4) NOT NULL, "tax_amount" numeric(15,2) NOT NULL, "calculation_period" character varying(7) NOT NULL, "is_paid" boolean NOT NULL DEFAULT false, "htx_coverage" boolean NOT NULL DEFAULT false, "region_code" character varying(10), "calculation_note" text, "metadata" json, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_2336d6892ee3f8738d374f10b76" PRIMARY KEY ("id")); COMMENT ON COLUMN "tax_calculations"."user_role" IS 'Vai trò người dùng theo chính sách thuế TAP'; COMMENT ON COLUMN "tax_calculations"."tax_type" IS 'Loại thuế áp dụng'; COMMENT ON COLUMN "tax_calculations"."taxable_revenue" IS 'Doanh thu chịu thuế'; COMMENT ON COLUMN "tax_calculations"."tax_rate" IS 'Thuế suất áp dụng (0.0000 -> 1.0000)'; COMMENT ON COLUMN "tax_calculations"."tax_amount" IS 'Số tiền thuế phải nộp'; COMMENT ON COLUMN "tax_calculations"."calculation_period" IS 'Kỳ tính thuế (YYYY-MM hoặc YYYY-QQ)'; COMMENT ON COLUMN "tax_calculations"."is_paid" IS 'Đã nộp thuế chưa'; COMMENT ON COLUMN "tax_calculations"."htx_coverage" IS 'Có được HTX đại diện không'; COMMENT ON COLUMN "tax_calculations"."region_code" IS 'Mã khu vực (province_code)'; COMMENT ON COLUMN "tax_calculations"."calculation_note" IS 'Ghi chú về cách tính thuế'; COMMENT ON COLUMN "tax_calculations"."metadata" IS 'Metadata bổ sung (threshold info, exemptions, etc.)'`);
        await queryRunner.query(`CREATE INDEX "IDX_0507b0e32c54150599d7092a20" ON "tax_calculations" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_941ba27011eaa68d30cd8cd968" ON "tax_calculations" ("calculation_period") `);
        await queryRunner.query(`CREATE INDEX "IDX_310c983546a79ce3db17bc764c" ON "tax_calculations" ("calculation_period", "tax_type") `);
        await queryRunner.query(`CREATE INDEX "IDX_31af58d94cd97c4f87b5580ace" ON "tax_calculations" ("user_id", "calculation_period") `);
        await queryRunner.query(`ALTER TABLE "tax_calculations" ADD CONSTRAINT "FK_0507b0e32c54150599d7092a207" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tax_calculations" ADD CONSTRAINT "FK_6345ea6418ab8e890b89de0f12f" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tax_calculations" DROP CONSTRAINT "FK_6345ea6418ab8e890b89de0f12f"`);
        await queryRunner.query(`ALTER TABLE "tax_calculations" DROP CONSTRAINT "FK_0507b0e32c54150599d7092a207"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_31af58d94cd97c4f87b5580ace"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_310c983546a79ce3db17bc764c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_941ba27011eaa68d30cd8cd968"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0507b0e32c54150599d7092a20"`);
        await queryRunner.query(`DROP TABLE "tax_calculations"`);
        await queryRunner.query(`DROP TYPE "public"."tax_calculations_tax_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."tax_calculations_user_role_enum"`);
    }

}
