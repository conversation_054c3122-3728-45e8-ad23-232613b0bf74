import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserVectorEmbedding } from '../entities/user_vector_embedding.entity';

@Injectable()
export class M2BotService {
  constructor(
    @InjectRepository(UserVectorEmbedding)
    private readonly userVectorRepo: Repository<UserVectorEmbedding>,
  ) {}

  // Bước 1: Lấy vector embedding của user theo user_id
  async getUserVector(user_id: string): Promise<number[] | null> {
    const record = await this.userVectorRepo.findOne({ where: { user_id } });
    return record?.vector || null;
  }

  // Bước 2: Tính cosine similarity giữa 2 vector
  private cosineSimilarity(a: number[], b: number[]): number {
    const dot = a.reduce((sum, ai, i) => sum + ai * b[i], 0);
    const normA = Math.sqrt(a.reduce((sum, ai) => sum + ai * ai, 0));
    const normB = Math.sqrt(b.reduce((sum, bi) => sum + bi * bi, 0));
    if (normA === 0 || normB === 0) return 0;
    return dot / (normA * normB);
  }

  // Gợi ý user tương tự dựa trên vector embedding
  async suggestSimilarUsers(
    user_id: string,
    topN = 5,
  ): Promise<{ user_id: string; score: number }[]> {
    const target = await this.userVectorRepo.findOne({ where: { user_id } });
    if (!target) return [];
    const all = await this.userVectorRepo.find();
    const results = all
      .filter((u) => u.user_id !== user_id)
      .map((u) => ({ user_id: u.user_id, score: this.cosineSimilarity(target.vector, u.vector) }))
      .sort((a, b) => b.score - a.score)
      .slice(0, topN);
    return results;
  }
}
