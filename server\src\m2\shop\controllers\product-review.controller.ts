import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Req,
  UseGuards,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { ProductReviewService } from '../services/product-review.service';
import { CreateProductReviewDto, UpdateProductReviewDto } from '../dto/product-review.dto';

@ApiTags('Product Review')
@Controller('product-reviews')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
export class ProductReviewController {
  constructor(private readonly reviewService: ProductReviewService) {}

  @Get('to-review')
  @ApiOperation({ summary: 'L<PERSON>y danh sách sản phẩm đã hoàn thành cần đánh giá' })
  @ApiResponse({ status: 200 })
  async getProductsToReview(@Req() req: any) {
    const userId = req.user?.userId;
    return this.reviewService.getProductsToReview(userId);
  }

  @Post()
  @ApiOperation({ summary: 'Tạo đánh giá hoặc rep lại đánh giá' })
  @ApiResponse({ status: 201 })
  async createReview(@Req() req: any, @Body() dto: CreateProductReviewDto) {
    const userId = req.user?.userId;
    return this.reviewService.createReview(userId, dto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Chỉnh sửa đánh giá' })
  @ApiResponse({ status: 200 })
  async updateReview(
    @Req() req: any,
    @Param('id') id: string,
    @Body() dto: UpdateProductReviewDto,
  ) {
    const userId = req.user?.userId;
    return this.reviewService.updateReview(userId, id, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa đánh giá' })
  @ApiResponse({ status: 200 })
  async deleteReview(@Req() req: any, @Param('id') id: string) {
    const userId = req.user?.userId;
    return this.reviewService.deleteReview(userId, id);
  }

  @Get('my')
  @ApiOperation({ summary: 'Lấy danh sách đánh giá của user để chỉnh sửa' })
  @ApiResponse({ status: 200 })
  async getMyReviews(@Req() req: any) {
    const userId = req.user?.userId;
    return this.reviewService.getMyReviews(userId);
  }
}
