import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';

@Entity('feedback_replies')
export class FeedbackReply {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  feedback_id: string;

  @Column('uuid')
  responder_id: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  role_code?: string;

  @Column({ type: 'text' })
  content: string;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;
}
