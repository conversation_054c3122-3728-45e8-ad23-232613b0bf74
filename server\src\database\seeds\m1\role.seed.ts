import { Permission } from 'src/m1/user/entities/permissions.entity';
import { Role } from 'src/m1/user/entities/role.entity';
import { RoleGroupPermission } from 'src/m1/user/entities/role_group_permissions.entity';
import { RoleGroup } from 'src/m1/user/entities/role_groups.entity';
import { DataSource } from 'typeorm';

export async function seedRBAC(dataSource: DataSource) {
  const permissionRepo = dataSource.getRepository(Permission);
  const roleGroupRepo = dataSource.getRepository(RoleGroup);
  const rgpRepo = dataSource.getRepository(RoleGroupPermission);
  const roleRepo = dataSource.getRepository(Role);

  // Seed permissions
  const permissions = [
    { code: 'PERM_USER_VIEW_SELF', name: 'Xem hồ sơ cá nhân' },
    { code: 'PERM_USER_UPDATE_SELF', name: '<PERSON><PERSON><PERSON> nh<PERSON><PERSON> hồ sơ cá nhân' },
    { code: 'PERM_USER_2FA_TOGGLE', name: 'Bật/tắt xác thực 2 yếu tố' },
    { code: 'PERM_SESSION_LIST', name: 'Xem phiên đăng nhập' },
    { code: 'PERM_AFFILIATE_LIST', name: 'Xem danh sách affiliate' },
    { code: 'PERM_AFFILIATE_VIEW_DETAIL', name: 'Xem chi tiết affiliate qua ID' },
    { code: 'PERM_AFFILIATE_UPDATE', name: 'Cập nhật affiliate' },
    { code: 'PERM_AFFILIATE_DELETE', name: 'Xóa affiliate' },
    { code: 'PERM_USER_LIST', name: 'Xem danh sách người dùng' },
    { code: 'PERM_USER_VIEW_DETAIL', name: 'Xem chi tiết người dùng qua ID' },
    { code: 'PERM_USER_UPDATE', name: 'Cập nhật người dùng' },
    { code: 'PERM_USER_DELETE', name: 'Xóa người dùng' },
    { code: 'PERM_ADMIN_FULL_ACCESS', name: 'Quyền truy cập toàn bộ hệ thống' },
    { code: 'PERM_ORDER_CREATE', name: 'Tạo đơn hàng' },
    { code: 'PERM_ORDER_VIEW_SELF', name: 'Xem đơn hàng của mình' },
    { code: 'PERM_ORDER_VIEW_ALL', name: 'Xem tất cả đơn hàng' },
    { code: 'PERM_ORDER_UPDATE', name: 'Cập nhật đơn hàng' },
    { code: 'PERM_ORDER_DELETE', name: 'Xóa đơn hàng' },
    { code: 'PERM_CART_MANAGE', name: 'Quản lý giỏ hàng' },
    { code: 'PERM_FAVORITE_MANAGE', name: 'Quản lý sản phẩm yêu thích' },
    { code: 'PERM_PRODUCT_VIEW', name: 'Xem sản phẩm' },
    { code: 'PERM_PRODUCT_MANAGE', name: 'Quản lý sản phẩm' },
    { code: 'PERM_CATEGORY_MANAGE', name: 'Quản lý danh mục' },
    { code: 'PERM_INVENTORY_MANAGE', name: 'Quản lý hàng tồn kho' },
  ];
  for (const p of permissions) {
    const exists = await permissionRepo.findOneBy({ code: p.code });
    if (!exists) {
      await permissionRepo.save(permissionRepo.create(p));
    }
  }

  // Seed roles
  const roles = [
    { role_code: 'R01', name: 'Nông dân', description: 'Người trực tiếp sản xuất' },
    { role_code: 'R02', name: 'Người bán hàng', description: 'Hộ kinh doanh, doanh nghiệp' },
    { role_code: 'R03', name: 'Người tiêu dùng', description: 'Người mua hàng hoặc dịch vụ' },
    { role_code: 'R04', name: 'Chuyên gia', description: 'Bác sĩ, giảng viên, chuyên gia' },
    { role_code: 'R05', name: 'Quản lý khu vực', description: 'Admin xã, huyện, tỉnh' },
    { role_code: 'R06', name: 'Tài xế', description: 'Giao hàng và gọi xe' },
    { role_code: 'R07', name: 'Nhà đầu tư', description: 'Góp vốn và cổ đông' },
    { role_code: 'R08', name: 'Ban điều hành', description: 'Chủ tịch, kế toán, giám đốc' },
  ];
  for (const r of roles) {
    const exists = await roleRepo.findOneBy({ role_code: r.role_code });
    if (!exists) {
      await roleRepo.save(roleRepo.create(r));
    }
  }

  // Seed roleGroups
  const roleGroupsRaw = [
    { code: 'R01.01', name: 'Nông dân trồng trọt', role_code: 'R01', moduleScope: ['M2', 'M6'] },
    { code: 'R01.02', name: 'Nông dân chăn nuôi', role_code: 'R01', moduleScope: ['M2', 'M6'] },
    { code: 'R01.03', name: 'Nuôi trồng thủy sản', role_code: 'R01', moduleScope: ['M2', 'M6'] },
    { code: 'R02.01', name: 'Hộ kinh doanh', role_code: 'R02', moduleScope: ['M4'] },
    { code: 'R02.02', name: 'Doanh nghiệp', role_code: 'R02', moduleScope: ['M4'] },
    { code: 'R03.01', name: 'Khách lẻ', role_code: 'R03', moduleScope: ['M4'] },
    { code: 'R03.02', name: 'Người mua dịch vụ', role_code: 'R03', moduleScope: ['M4', 'M7'] },
    { code: 'R04.01', name: 'Bác sĩ y tế', role_code: 'R04', moduleScope: ['M3'] },
    { code: 'R04.02', name: 'Chuyên gia nông nghiệp', role_code: 'R04', moduleScope: ['M1', 'M7'] },
    { code: 'R04.03', name: 'Giảng viên đào tạo', role_code: 'R04', moduleScope: ['M3', 'M6'] },
    { code: 'R05.01', name: 'Admin hệ thống', role_code: 'R05', moduleScope: ['M1'] },
    { code: 'R05.02', name: 'Admin nội dung', role_code: 'R05', moduleScope: ['M4', 'M7'] },
    { code: 'R05.03', name: 'Admin kỹ thuật', role_code: 'R05', moduleScope: ['M1'] },
    { code: 'R06.01', name: 'Tài xế giao hàng', role_code: 'R06', moduleScope: ['M4'] },
    { code: 'R06.02', name: 'Tài xế gọi xe', role_code: 'R06', moduleScope: ['M4'] },
    { code: 'R07.01', name: 'Cá nhân góp vốn', role_code: 'R07', moduleScope: ['M5'] },
    { code: 'R07.02', name: 'Nhà đầu tư tổ chức', role_code: 'R07', moduleScope: ['M5'] },
    { code: 'R08.01', name: 'Chủ tịch HĐQT', role_code: 'R08', moduleScope: ['M1', 'M5'] },
    { code: 'R08.02', name: 'Giám đốc tài chính', role_code: 'R08', moduleScope: ['M5'] },
    { code: 'R08.03', name: 'Kế toán', role_code: 'R08', moduleScope: ['M5'] },
    { code: 'R08.04', name: 'Nhân sự', role_code: 'R08', moduleScope: ['M1'] },
    { code: 'R08.05', name: 'Ban kiểm soát', role_code: 'R08', moduleScope: ['M1'] },
  ];
  const roleGroups: { code: string; name: string; roleId: string; moduleScope: string[] }[] = [];
  for (const rg of roleGroupsRaw) {
    const role = await roleRepo.findOneBy({ role_code: rg.role_code });
    if (!role) throw new Error(`Không tìm thấy role với code ${rg.role_code}`);
    roleGroups.push({
      code: rg.code,
      name: rg.name,
      roleId: role.id,
      moduleScope: rg.moduleScope,
    });
  }
  for (const rg of roleGroups) {
    const exists = await roleGroupRepo.findOneBy({ code: rg.code });
    if (!exists) {
      await roleGroupRepo.save(roleGroupRepo.create(rg));
    }
  }

  // Seed full permissions for rgAdmin (R05.01)
  const rgAdmin = await roleGroupRepo.findOneBy({ code: 'R05.01' });
  const allPerms = await permissionRepo.find();
  if (!rgAdmin)
    throw new Error(
      'Không tìm thấy roleGroup R05.01 – vui lòng kiểm tra dữ liệu seeding role_groups',
    );
  for (const perm of allPerms) {
    const exists = await rgpRepo.findOne({
      where: { roleGroup: { id: rgAdmin.id }, permission: { id: perm.id } },
      relations: ['roleGroup', 'permission'],
    });
    if (!exists) {
      await rgpRepo.save(rgpRepo.create({ roleGroup: rgAdmin, permission: perm }));
    }
  }

  // Seed default permissions for all other roleGroups (trừ R05.01)
  const defaultPermCodes = ['PERM_USER_VIEW_SELF', 'PERM_USER_UPDATE_SELF'];
  const allRoleGroups = await roleGroupRepo.find();
  for (const rg of allRoleGroups) {
    if (rg.code === 'R05.01') continue; // skip admin
    for (const code of defaultPermCodes) {
      const perm = await permissionRepo.findOneBy({ code });
      if (perm) {
        const exists = await rgpRepo.findOne({
          where: { roleGroup: { id: rg.id }, permission: { id: perm.id } },
          relations: ['roleGroup', 'permission'],
        });
        if (!exists) {
          await rgpRepo.save(rgpRepo.create({ roleGroup: rg, permission: perm }));
        }
      }
    }
  }
}
