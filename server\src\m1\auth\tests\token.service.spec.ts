import { Test, TestingModule } from '@nestjs/testing';
import { TokenService } from '../services/token.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { RoleGroup } from '../../user/entities/role_groups.entity';
import { RoleGroupPermission } from '../../user/entities/role_group_permissions.entity';
import * as bcrypt from 'bcrypt';

describe('TokenService', () => {
  let service: TokenService;
  let jwtService: JwtService;
  let userRepo: Repository<User>;
  let roleGroupRepo: Repository<RoleGroup>;
  let rgpRepo: Repository<RoleGroupPermission>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TokenService,
        {
          provide: JwtService,
          useValue: {
            signAsync: jest.fn().mockResolvedValue('signed-token'),
            verifyAsync: jest.fn().mockResolvedValue({ sub: 'user123' }),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('15m'),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            update: jest.fn(),
            findOneBy: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(RoleGroup),
          useValue: {
            findOne: jest.fn().mockResolvedValue({ id: 1, code: 'ADMIN' }),
          },
        },
        {
          provide: getRepositoryToken(RoleGroupPermission),
          useValue: {
            find: jest.fn().mockResolvedValue([]),
          },
        },
      ],
    }).compile();

    service = module.get<TokenService>(TokenService);
    jwtService = module.get<JwtService>(JwtService);
    userRepo = module.get(getRepositoryToken(User));
    roleGroupRepo = module.get(getRepositoryToken(RoleGroup));
    rgpRepo = module.get(getRepositoryToken(RoleGroupPermission));
  });

  it('should generate tokens for user', async () => {
    const user = { id: 'user123', email: '<EMAIL>', roleGroupCode: 'ADMIN' } as User;

    const result = await service.generateTokens(user);

    expect(result).toEqual({ accessToken: 'signed-token', refreshToken: 'signed-token' });
    expect(userRepo.update).toHaveBeenCalled();
  });

  it('should refresh token if valid', async () => {
    const user = { id: 'user123', refreshToken: await bcrypt.hash('signed-token', 10) } as User;
    (userRepo.findOneBy as jest.Mock).mockResolvedValue(user);

    const result = await service.refreshToken('signed-token');

    expect(result.accessToken).toBeDefined();
    expect(result.refreshToken).toBeDefined();
  });

  it('should get profile from token', async () => {
    const user = { id: 'user123', email: '<EMAIL>', role: 'R01', createdAt: new Date() };
    (userRepo.findOne as jest.Mock).mockResolvedValue(user);

    const result = await service.getProfileFromToken('token123');

    expect(result.email).toBe('<EMAIL>');
    expect(result.id).toBe('user123');
  });
});
