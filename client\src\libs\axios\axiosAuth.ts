import { ROUTES } from '@/constants/routes'
import axios, { AxiosError } from 'axios'
import type { AxiosRequestConfig } from 'axios'

import Cookies from 'js-cookie'
// Uncomment if you have logout action
// import { logout } from '@/libs/features/auth/authSlice';

let isRefreshing = false
let failedQueue: any[] = []

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (token) {
      prom.resolve(token)
    } else {
      prom.reject(error)
    }
  })
  failedQueue = []
}

const axiosAuth = axios.create({
  baseURL: 'http://localhost:8080', // ✅ Thêm baseURL
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 120000,
})

// Add request interceptor to attach access token to headers
axiosAuth.interceptors.request.use(
  async (config) => {
    const token = Cookies.get('accessToken')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor to handle 401 responses and refresh token logic
axiosAuth.interceptors.response.use(
  (response) => {
    return response
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean }

    if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        })
          .then((token) => {
            if (originalRequest && originalRequest.headers) {
              originalRequest.headers['Authorization'] = 'Bearer ' + token
            }
            return axiosAuth(originalRequest)
          })
          .catch((err) => Promise.reject(err))
      }

      originalRequest._retry = true
      isRefreshing = true

      try {
        const refreshToken = Cookies.get('refreshToken')
        if (!refreshToken) {
          handleLogout()
          return Promise.reject(error)
        }

        const { data } = await axiosAuth.post('/auth/refresh-token', { refreshToken: refreshToken })
        const { accessToken, refreshToken: newRefreshToken } = data.data

        // ✅ Update cả 2 tokens
        Cookies.set('accessToken', accessToken)
        if (newRefreshToken) {
          Cookies.set('refreshToken', newRefreshToken)
        }

        processQueue(null, accessToken)

        if (originalRequest.headers) {
          originalRequest.headers['Authorization'] = `Bearer ${accessToken}`
        }
        return axiosAuth(originalRequest)
      } catch (refreshError) {
        processQueue(refreshError, null)
        handleLogout()
        return Promise.reject(refreshError)
      } finally {
        isRefreshing = false
      }
    }

    return Promise.reject(error)
  }
)

const handleLogout = () => {
  // Clear cookies
  Cookies.remove('accessToken')
  Cookies.remove('refreshToken')
  
  // Clear localStorage
  localStorage.removeItem('accessToken')
  localStorage.removeItem('refreshToken')
  
  // Clear Redux persist storage
  localStorage.removeItem('persist:auth')
  
  // Force redirect to login
  window.location.href = ROUTES.LOGIN
}

export { axiosAuth }
