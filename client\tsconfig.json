{"compilerOptions": {"noEmit": true, "target": "ES2020", "module": "ESNext", "moduleResolution": "node", "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "verbatimModuleSyntax": true, "types": ["vite/client"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src", "src/types"], "exclude": ["node_modules"]}