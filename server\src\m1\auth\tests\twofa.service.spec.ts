import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../../user/entities/user.entity';
import { User2FASettings } from '../../user/entities/user_2fa_settings.entity';
import { OtpVerification } from '../../user/entities/otp_verification.entity';
import { MailService } from 'src/mail/mail.service';
import { Repository } from 'typeorm';
import { ClientProxy } from '@nestjs/microservices';
import * as speakeasy from 'speakeasy';
import { TotpVerifyDto } from '../dto/totp-verify.dto';
import { VerifyOtpDto } from '../dto/verify-otp.dto';
import { TwofaService } from '../services/twofa.service';
import { OtpService } from '../services/otp.service';
import { SessionService } from '../services/session.service';
import { TokenService } from '../services/token.service';

describe('TwoFAService', () => {
  let service: TwofaService;
  let userRepo: Repository<User>;
  let user2FARepo: Repository<User2FASettings>;
  let otpRepo: Repository<OtpVerification>;
  let mailService: MailService;
  let logClient: ClientProxy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TwofaService,
        {
          provide: getRepositoryToken(User),
          useValue: { findOne: jest.fn() },
        },
        {
          provide: getRepositoryToken(User2FASettings),
          useValue: { findOne: jest.fn(), save: jest.fn(), update: jest.fn() },
        },
        {
          provide: getRepositoryToken(OtpVerification),
          useValue: { findOne: jest.fn(), save: jest.fn() },
        },
        {
          provide: MailService,
          useValue: { send2FAActived: jest.fn() },
        },
        {
          provide: 'LOG_SERVICE',
          useValue: { emit: jest.fn() },
        },
        {
          provide: OtpService,
          useValue: {},
        },
        {
          provide: SessionService,
          useValue: {},
        },
        {
          provide: TokenService,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<TwofaService>(TwofaService);
    userRepo = module.get(getRepositoryToken(User));
    user2FARepo = module.get(getRepositoryToken(User2FASettings));
    otpRepo = module.get(getRepositoryToken(OtpVerification));
    mailService = module.get(MailService);
    logClient = module.get('LOG_SERVICE');
  });

  it('should verify valid TOTP', async () => {
    const dto: TotpVerifyDto = { userId: 'u1', token: '123456' };
    const secret = speakeasy.generateSecret();
    const setting: User2FASettings = {
      user_id: 'u1',
      user: { id: 'u1' } as User,
      is_otp_enabled: false,
      is_totp_enabled: false,
      verified_at: new Date(0),
      totp_secret_key: secret.base32,
      backup_codes: [''],
    };

    jest.spyOn(user2FARepo, 'findOne').mockResolvedValue(setting);
    jest.spyOn(speakeasy.totp, 'verify').mockReturnValue(true);
    jest.spyOn(user2FARepo, 'save').mockResolvedValue({
      user_id: 'u1',
      user: { id: 'u1' } as User,
      is_otp_enabled: false,
      is_totp_enabled: true,
      verified_at: new Date(),
      totp_secret_key: 'ABC123',
      backup_codes: [''],
    });

    const result = await service.verifyTotp(dto);
    expect(result.message).toBe('Xác minh TOTP thành công');
  });

  it('should fail TOTP if not set', async () => {
    jest.spyOn(user2FARepo, 'findOne').mockResolvedValue(null);
    await expect(service.verifyTotp({ userId: 'u1', token: '000000' })).rejects.toThrow();
  });

  it('should verify OTP 2FA and enable it', async () => {
    const dto: VerifyOtpDto = { target: '<EMAIL>', otp: '111111' };
    const otp: OtpVerification = {
      expiredAt: new Date(Date.now() + 100000),
      status: 'valid',
      isVerified: false,
      createdAt: new Date(0),
      otpCode: '111111',
      target: '',
      id: '',
    };

    const user = { id: 'u1' } as User;
    jest.spyOn(otpRepo, 'findOne').mockResolvedValue(otp);
    jest.spyOn(userRepo, 'findOne').mockResolvedValue(user);
    jest.spyOn(user2FARepo, 'findOne').mockResolvedValue(null);
    jest.spyOn(user2FARepo, 'save').mockResolvedValue({
      user_id: 'u1',
      user: { id: 'u1' } as User,
      is_otp_enabled: true,
      is_totp_enabled: false,
      verified_at: new Date(),
      totp_secret_key: '',
      backup_codes: [''],
    });

    const result = await service.verifyOtp2FA(dto);
    expect(result.message).toContain('Xác minh OTP 2FA thành công');
  });
});
