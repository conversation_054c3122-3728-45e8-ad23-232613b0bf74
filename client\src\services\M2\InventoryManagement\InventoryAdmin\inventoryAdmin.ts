import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

export const inventoryAdminApi = createApi({
  reducerPath: 'inventoryAdminApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['InventoryAdmin', 'StockHistory', 'Alerts'],

  endpoints: (build) => ({
    // ✅ Lấy danh sách sản phẩm tồn kho (admin - xem tất cả)
    getAdminInventory: build.query<any, any>({
      query: (params) => ({
        url: '/inventory', // Sử dụng endpoint hiện tại, admin có thể xem tất cả
        method: 'GET',
        params: params,
        authRequired: true,
      }),
      providesTags: ['InventoryAdmin'],
    }),

    // ✅ Lấy chi tiết sản phẩm tồn kho (admin)
    getAdminInventoryDetail: build.query<any, string>({
      query: (id: string) => ({
        url: `/inventory/${id}`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['InventoryAdmin'],
    }),

    // ✅ Cập nhật tồn kho cho từng biến thể (admin)
    updateAdminVariants: build.mutation<any, { id: string; variants: any[] }>({
      query: ({ id, variants }) => ({
        url: `/inventory/${id}/variants`, // Sử dụng endpoint hiện tại
        method: 'PUT',
        data: variants,
        authRequired: true,
      }),
      invalidatesTags: ['InventoryAdmin'],
    }),

    // ✅ Cập nhật nhanh tồn kho (tăng/giảm số lượng) (admin)
    quickUpdateAdminStock: build.mutation<any, { id: string; variantId: string; change: number }>({
      query: ({ id, variantId, change }) => ({
        url: `/inventory/${id}/variant/${variantId}/stock`, // Sử dụng endpoint hiện tại
        method: 'PATCH',
        data: { change },
        authRequired: true,
      }),
      invalidatesTags: ['InventoryAdmin'],
    }),

    // ✅ Lấy lịch sử nhập/xuất kho (admin)
    getAdminInventoryHistory: build.query<any, string>({
      query: (id: string) => ({
        url: `/inventory/${id}/history`, // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['StockHistory'],
    }),

    // ✅ Import tồn kho từ file Excel (admin)
    importAdminInventory: build.mutation<any, FormData>({
      query: (formData: FormData) => ({
        url: '/inventory/import', // Sử dụng endpoint hiện tại
        method: 'POST',
        data: formData,
        authRequired: true,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }),
      invalidatesTags: ['InventoryAdmin'],
    }),

    // ✅ Export tồn kho ra file Excel (admin)
    exportAdminInventory: build.query<any, any>({
      query: (params) => ({
        url: '/inventory/export', // Sử dụng endpoint hiện tại
        method: 'GET',
        params: params,
        authRequired: true,
      }),
      providesTags: ['InventoryAdmin'],
    }),

    // ✅ Lấy danh sách cảnh báo hết hàng/sắp hết hàng (admin)
    getAdminAlerts: build.query<any, void>({
      query: () => ({
        url: '/inventory/alerts', // Sử dụng endpoint hiện tại
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['Alerts'],
    }),
  }),
})

export const {
  useGetAdminInventoryQuery,
  useGetAdminInventoryDetailQuery,
  useUpdateAdminVariantsMutation,
  useQuickUpdateAdminStockMutation,
  useGetAdminInventoryHistoryQuery,
  useImportAdminInventoryMutation,
  useExportAdminInventoryQuery,
  useGetAdminAlertsQuery,
} = inventoryAdminApi
