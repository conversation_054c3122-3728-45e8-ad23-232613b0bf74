import { ProductReview } from './entities/product_review.entity';
import { ProductReviewService } from './services/product-review.service';
import { ProductReviewController } from './controllers/product-review.controller';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiImageVector } from './entities/ai_image_vectors.entity';
import { AiSearchLog } from './entities/ai_search_log.entity';
import { AiSuggestion } from './entities/ai_suggestion.entity';
import { CartItem } from './entities/cart_item.entity';
import { ComboItem } from './entities/combo_item.entity';
import { FeedbackReply } from './entities/feedback_reply.entity';
import { FlashSale } from './entities/flash_sale.entity';
import { GeoDiscount } from './entities/geo_discount.entity';
import { Inventory } from './entities/inventory.entity';
import { Order } from './entities/order.entity';
import { OrderItem } from './entities/order_item.entity';
import { OrderPayment } from './entities/order_payment.entity';
import { OrderStatusLog } from './entities/order_status_log.entity';
import { Product } from './entities/products.entity';
import { ProductAttribute } from './entities/product_attribute.entity';
import { ProductCategory } from './entities/product_categories.entity';
import { ProductCombo } from './entities/product_combo.entity';
import { ProductComment } from './entities/product_comment.entity';
import { ProductImage } from './entities/product_image.entity';
import { ProductQrCode } from './entities/product_qr_code.entity';
import { ProductTag } from './entities/product_tag.entity';
import { ProductVersion } from './entities/product_version.entity';
import { ReportFlag } from './entities/report_flag.entity';
import { Status } from './entities/status.entity';
import { Tag } from './entities/tag.entity';
import { UserBehavior } from './entities/user_behavior.entity';
import { UserFeedback } from './entities/user_feedback.entity';
import { ViolationReport } from './entities/violation_report.entity';
import { Voucher } from './entities/voucher.entity';
import { User } from 'src/m1/user/entities/user.entity';
import { LogClientModule } from 'src/m_log/log-client.module';
import { MLogModule } from 'src/m_log/m_log.module';
import { ProductController } from './controllers/product.controller';
import { OrderController } from './controllers/order.controller';
import { OrderStatisticsController } from './controllers/order-statistics.controller';
import { UserBehaviorLogService } from '../../m_log/log/user-behavior-log.service';
import { CartController } from './controllers/cart.controller';
import { ProductService } from './services/product.service';
import { OrderService } from './services/order.service';
import { OrderStatisticsService } from './services/order-statistics.service';
import { CartService } from './services/cart.service';
import { AiTrainingData } from './entities';
import { ProductCategoriesController } from './controllers/product-categories.controller';
import { ProductCategoriesService } from './services/product-categories.service';
import { ProductImagesController } from './controllers/product-images.controller';
import { ProductImagesService } from './services/product-images.service';
import { CloudinaryModule } from 'src/cloudinary/cloudinary.module';
import { StatusController } from './controllers/status.controller';
import { StatusService } from './services/status.service';
import { ProductVersionService } from './services/product-version.service';
import { ProductVersionController } from './controllers/product-version.controller';

import { ConfigModule } from '@nestjs/config';
import { AdminOrderController } from './controllers/admin-order.controller';
import { LogService } from 'src/m_log/log/log.service';
import { SystemLog } from 'src/m_log/entities/log.entity';
import { Payment } from './entities/payment.entity';
import { FavoriteController } from './controllers/favorite.controller';
import { FavoriteService } from './services/favorite.service';
import { FavoriteProduct } from './entities/favorite.entity';
import { LogController } from 'src/m_log/log/log.controller';
import { M2ProductRecommenderService } from './services/m2ProductRecommender.service';
import { M2ProductRecommenderController } from './controllers/m2ProductRecommender.controller';
import { UserVectorEmbedding } from './entities/user_vector_embedding.entity';
import { ProductVectorEmbedding } from './entities/product_vector_embedding.entity';
import { ImageVectorizeController } from './workers/image-vectorize.controller';
import { ImageSearchController } from './controllers/image-search.controller';
import { ImageSearchService } from './services/image-search.service';
import { InventoryController } from './controllers/inventory.controller';
import { InventoryService } from './services/inventory.service';
import { TaxCalculation } from './entities/tax-calculation.entity';
import { TaxCalculationService } from './services/tax-calculation.service';
import { TaxController } from './controllers/tax.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product,
      ProductCategory,
      ProductImage,
      ProductTag,
      Order,
      OrderItem,
      Inventory,
      ComboItem,
      GeoDiscount,
      ProductComment,
      ProductAttribute,
      ProductVersion,
      Status,
      Payment,
      ProductReview,
      AiImageVector,
      AiSearchLog,
      AiSuggestion,
      FeedbackReply,
      FlashSale,
      OrderPayment,
      OrderStatusLog,
      ProductCombo,
      ProductQrCode,
      ReportFlag,
      Tag,
      UserBehavior,
      UserFeedback,
      ViolationReport,
      Voucher,
      User,
      SystemLog,
      CartItem,
      FavoriteProduct,
      UserVectorEmbedding,
      ProductVectorEmbedding,
      TaxCalculation,
    ]),
    CloudinaryModule,
    ConfigModule,
    LogClientModule,
    MLogModule,
  ],
  controllers: [
    ProductController,
    FavoriteController,
    ProductReviewController,
    OrderController,
    CartController,
    ProductCategoriesController,
    ProductImagesController,
    StatusController,
    ProductVersionController,
    AdminOrderController,
    LogController,
    M2ProductRecommenderController,
    ImageVectorizeController,
    ImageSearchController,
    InventoryController,
    OrderStatisticsController,
    TaxController,
  ],
  providers: [
    ProductService,
    ProductReviewService,
    OrderService,
    CartService,
    ProductCategoriesService,
    ProductImagesService,
    StatusService,
    ProductVersionService,
    FavoriteService,
    LogService,
    UserBehaviorLogService,
    M2ProductRecommenderService,
    ImageSearchService,
    InventoryService,
    OrderStatisticsService,
    TaxCalculationService,
  ],
})
export class M2Module {}
