import { Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { AuthService } from './services/auth.service';
import { JwtStrategy } from './jwt.strategy';
import { AuthController } from './controllers/auth.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../user/entities/user.entity';
import { Role } from '../user/entities/role.entity';
import { OtpVerification } from '../user/entities/otp_verification.entity';
import { UserProfile } from '../user/entities/user_profile.entity';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MailModule } from 'src/mail/mail.module';
import { UserModule } from '../user/m1.module';
import { UserAffiliateService } from '../user/services/user_affiliate.service';
import { UserAffiliate } from '../user/entities/user_affiliates.entity';
import { UserProvider } from '../user/entities/user-provider.entity';
import { User2FASettings } from '../user/entities/user_2fa_settings.entity';
import { AuthSession } from '../user/entities/auth_sessions.entity';
import { LogClientModule } from 'src/m_log/log-client.module';
import { MLogModule } from 'src/m_log/m_log.module';
import { RoleGroup } from '../user/entities/role_groups.entity';
import { RoleScope } from '../user/entities/role_scopes.entity';
import { RoleGroupPermission } from '../user/entities/role_group_permissions.entity';
import { Permission } from '../user/entities/permissions.entity';
import { OtpService } from './services/otp.service';
import { TwofaService } from './services/twofa.service';
import { TokenService } from './services/token.service';
import { SessionService } from './services/session.service';

@Module({
  imports: [
    PassportModule,
    ConfigModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => ({
        secret: config.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: config.get<string>('JWT_EXPIRES_IN') || '1d',
        },
      }),
    }),
    TypeOrmModule.forFeature([
      User,
      Role,
      OtpVerification,
      UserProfile,
      UserAffiliate,
      UserProvider,
      User2FASettings,
      AuthSession,
      Permission,
      RoleGroupPermission,
      RoleGroup,
      RoleScope,
    ]),
    UserModule,
    MailModule,
    LogClientModule,
    MLogModule,
  ],
  providers: [
    AuthService,
    JwtStrategy,
    UserAffiliateService,
    OtpService,
    TwofaService,
    TokenService,
    SessionService,
    // Thêm các service cần thiết
    // MailService sẽ được cung cấp qua MailModule
    // RoleService sẽ được cung cấp qua UserModule
  ],
  controllers: [AuthController],
  exports: [AuthService, JwtModule, ConfigModule],
})
export class AuthModule {}
