import {
  Controller,
  Get,
  Put,
  Patch,
  Post,
  Body,
  Param,
  Query,
  UploadedFile,
  UseInterceptors,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiConsumes } from '@nestjs/swagger';
import { InventoryFilterDto } from '../dto/inventory-filter.dto';
import {
  ImportInventoryDto,
  QuickStockUpdateDto,
  UpdateInventoryVariantDto,
} from '../dto/inventory.dto';
import { InventoryService } from '../services/inventory.service';

@ApiTags('Inventory')
@Controller('inventory')
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  // ADMIN ENDPOINTS (xem theo khu vực quản lý)
  @Get('admin')
  @ApiOperation({ summary: 'Lấy danh sách sản phẩm tồn kho (admin - theo khu vực)' })
  async getAdminInventory(@Query() query: InventoryFilterDto) {
    // TODO: Filter by admin's region/area
    return this.inventoryService.getInventoryList(query);
  }

  // SHOP ENDPOINTS (xem inventory của shop riêng)
  @Get('shop')
  @ApiOperation({ summary: 'Lấy danh sách sản phẩm tồn kho (shop - chỉ của shop)' })
  async getShopInventory(@Query() query: InventoryFilterDto) {
    // TODO: Filter by shop's products only
    return this.inventoryService.getInventoryList(query);
  }

  // LEGACY/SHARED ENDPOINTS (dùng chung có đăng nhập là được)
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách sản phẩm tồn kho (legacy)' })
  async getInventory(@Query() query: InventoryFilterDto) {
    return this.inventoryService.getInventoryList(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết sản phẩm tồn kho' })
  async getInventoryDetail(@Param('id') id: string) {
    return this.inventoryService.getInventoryDetail(id);
  }

  @Put(':id/variants')
  @ApiOperation({ summary: 'Cập nhật tồn kho cho từng biến thể' })
  @ApiBody({ type: [UpdateInventoryVariantDto] })
  async updateVariants(@Param('id') id: string, @Body() variants: UpdateInventoryVariantDto[]) {
    return this.inventoryService.updateVariants(id, variants);
  }

  @Patch(':id/variant/:variantId/stock')
  @ApiOperation({ summary: 'Cập nhật nhanh tồn kho (tăng/giảm số lượng)' })
  @ApiBody({ type: QuickStockUpdateDto })
  async quickUpdateStock(
    @Param('id') id: string,
    @Param('variantId') variantId: string,
    @Body() dto: QuickStockUpdateDto,
  ) {
    return this.inventoryService.quickUpdateStock(id, variantId, dto.change);
  }

  @Get(':id/history')
  @ApiOperation({ summary: 'Lấy lịch sử nhập/xuất kho' })
  async getInventoryHistory(@Param('id') id: string) {
    return this.inventoryService.getInventoryHistory(id);
  }

  @Post('import')
  @ApiOperation({ summary: 'Import tồn kho từ file Excel' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: ImportInventoryDto })
  async importInventory(@UploadedFile() file: Express.Multer.File) {
    return this.inventoryService.importInventory(file);
  }

  @Get('export')
  @ApiOperation({ summary: 'Export tồn kho ra file Excel' })
  async exportInventory(@Query() query: InventoryFilterDto) {
    return this.inventoryService.exportInventory(query);
  }

  @Get('alerts')
  @ApiOperation({ summary: 'Lấy danh sách sản phẩm hết hàng/sắp hết hàng' })
  async getAlerts() {
    return this.inventoryService.getLowStockAlerts();
  }
}
