import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, Unique, Index } from 'typeorm';

@Entity('favorites')
@Unique(['user_id', 'product_id'])
@Index(['user_id'])
@Index(['product_id'])
export class FavoriteProduct {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  user_id: string;

  @Column('uuid')
  product_id: string;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;
}
