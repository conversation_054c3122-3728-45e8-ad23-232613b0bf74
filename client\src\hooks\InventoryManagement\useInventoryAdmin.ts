import { useEffect } from 'react'
import {
  useGetAdminInventoryQuery,
  useGetAdminInventoryDetailQuery,
  useUpdateAdminVariantsMutation,
  useQuickUpdateAdminStockMutation,
  useGetAdminInventoryHistoryQuery,
  useImportAdminInventoryMutation,
  useExportAdminInventoryQuery,
  useGetAdminAlertsQuery,
} from '../../services/M2/InventoryManagement/InventoryAdmin/inventoryAdmin'
import { useToast } from '../../components/Toast'
import { useLoadingStore } from '@/utils/loadingStore'

// Hook lấy danh sách tồn kho admin
const useGetAdminInventoryHook = (params: any) => {
  const { data, error, isLoading, refetch, isFetching } = useGetAdminInventoryQuery(params)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy chi tiết tồn kho admin
const useGetAdminInventoryDetailHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetAdminInventoryDetailQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true
  })
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy lịch sử tồn kho admin
const useGetAdminInventoryHistoryHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetAdminInventoryHistoryQuery(id, {
    skip: !id
  })
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy cảnh báo tồn kho admin
const useGetAdminAlertsHook = () => {
  const { data, error, isLoading, refetch, isFetching } = useGetAdminAlertsQuery()
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook export tồn kho admin
const useExportAdminInventoryHook = (params: any) => {
  const { data, error, isLoading, refetch, isFetching } = useExportAdminInventoryQuery(params)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook cập nhật variants tồn kho admin
const useUpdateAdminVariantsHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useUpdateAdminVariantsMutation()

  const update = async (id: string, variants: any[]): Promise<boolean> => {
    try {
      const res = await updateFunc({ id, variants })
      if (!res?.error) {
        showSuccess('Cập nhật variants thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Cập nhật variants thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Cập nhật variants thất bại' + e)
      return false
    }
  }

  return update
}

// Hook cập nhật nhanh tồn kho admin
const useQuickUpdateAdminStockHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useQuickUpdateAdminStockMutation()

  const update = async (id: string, variantId: string, change: number): Promise<boolean> => {
    try {
      const res = await updateFunc({ id, variantId, change })
      if (!res?.error) {
        showSuccess('Cập nhật tồn kho thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Cập nhật tồn kho thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Cập nhật tồn kho thất bại' + e)
      return false
    }
  }

  return update
}

// Hook import tồn kho admin
const useImportAdminInventoryHook = () => {
  const { showSuccess, showError } = useToast()
  const [importFunc] = useImportAdminInventoryMutation()

  const importInventory = async (file: File): Promise<any> => {
    const formData = new FormData()
    formData.append('file', file)

    try {
      const res = await importFunc(formData)
      if (!res?.error) {
        showSuccess('Import tồn kho thành công')
        return res.data
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Import tồn kho thất bại ' + messageDes)
        return null
      }
    } catch (e) {
      showError('Import tồn kho thất bại' + e)
      return null
    }
  }

  return importInventory
}

// Xuất ra
export {
  useGetAdminInventoryHook,
  useGetAdminInventoryDetailHook,
  useGetAdminInventoryHistoryHook,
  useGetAdminAlertsHook,
  useExportAdminInventoryHook,
  useUpdateAdminVariantsHook,
  useQuickUpdateAdminStockHook,
  useImportAdminInventoryHook,
}
