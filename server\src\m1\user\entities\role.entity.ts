import { RoleGroup } from './role_groups.entity';
import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { User } from './user.entity';

@Entity('roles')
export class Role {
  @OneToMany(() => RoleGroup, (roleGroup) => roleGroup.role)
  roleGroups: RoleGroup[];
  // Kh<PERSON>a chính
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Mã vai trò hệ thống (ví dụ: R01, R02...)
  @Column({ length: 10, unique: true })
  role_code: string;

  // Tên mô tả vai trò (hiển thị trong giao diện)
  @Column({ type: 'varchar', length: 255 })
  name: string;

  // Mô tả chi tiết (tùy chọn)
  @Column({ type: 'text', nullable: true })
  description: string;

  // Quan hệ: 1 role → nhiều user
  @OneToMany(() => User, (user) => user.role)
  users: User[];
}
