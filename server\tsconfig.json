{"compilerOptions": {"resolveJsonModule": true, "esModuleInterop": true, "module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "typeRoots": ["./node_modules/@types", "./src/@types"], "types": ["node", "multer", "jest"], "baseUrl": "./", "paths": {"src/*": ["src/*"]}}}