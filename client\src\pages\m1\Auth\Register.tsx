import { useState, useEffect } from 'react'
import { Button, Form, FormGroup, Label, Input, FormFeedback } from 'reactstrap'
import { useNavigate, Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { User, Phone, MailPlus, Key, Eye, EyeOff, FileCheck, Users, Loader2 } from 'lucide-react'
import { AxiosError } from 'axios'
import { useGoogleLogin } from '@react-oauth/google'
import { toast } from 'react-toastify'
import loginIllustration from '../../../assets/images/logo.png'
import googleLogo from '../../../assets/images/google-logo.png'
import facebookLogo from '../../../assets/images/facebook-logo.png'
import { useGetAllRoleHook } from '@/hooks/user'
import type { RoleResponse } from '@/services/M1/user'
import { useRegister } from '@/hooks/auth/useRegister'
import { useSocialLogin } from '@/hooks/auth/useLogin'
import { ROUTES } from '@/constants/routes'
import styles from './Register.module.scss'

export default function Register() {
  const [form, setForm] = useState({
    fullName: '',
    phone: '',
    email: '',
    password: '',
    confirmPassword: '',
    referralCode: '',
    agreeTerms: false,
    roleId: '',
  })

  // Auto-fill referralCode from URL param 'ref'
  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    const ref = params.get('ref')
    if (ref) {
      setForm((prev) => ({ ...prev, referralCode: ref }))
    }
  }, [])
  const [otp, setOtp] = useState('')
  const [isEmailVerified, setIsEmailVerified] = useState(false)
  const [isSendingVerification, setIsSendingVerification] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const navigate = useNavigate()
  const { register, emailVerification, sendOtpEmailVerification } = useRegister()
  const { socialLogin } = useSocialLogin()
  const { data: roles, isLoading: roleLoading } = useGetAllRoleHook()

  useEffect(() => {
    window.scrollTo(0, 0)
    return () => {
      window.scrollTo(0, 0)
    }
  }, [])

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^[0-9]{10}$/
    return phoneRegex.test(phone)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    if (name === 'otp') {
      setOtp(value)
    } else if (type === 'checkbox') {
      setForm({ ...form, [name]: (e.target as HTMLInputElement).checked })
    } else {
      setForm({ ...form, [name]: value })
    }
  }

  const handleSendVerificationEmail = async () => {
    if (!validateEmail(form.email)) {
      setError('Email không hợp lệ')
      return
    }
    setIsSendingVerification(true)
    try {
      await sendOtpEmailVerification({ target: form.email })
    } catch (err: unknown) {
      const error = err as AxiosError<{ message?: string }>
      setError(error.response?.data?.message || 'Lỗi khi gửi mã xác thực')
    } finally {
      setIsSendingVerification(false)
    }
  }

  const handleVerifyEmail = async () => {
    setIsVerifying(true)
    try {
      await emailVerification({ otp: otp, target: form.email })
      setIsEmailVerified(true)
      setError('')
    } catch (err: unknown) {
      const error = err as AxiosError<{ message?: string }>
      setError(error.response?.data?.message || 'Mã OTP không hợp lệ')
    } finally {
      setIsVerifying(false)
    }
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)
    if (!validateEmail(form.email)) {
      setError('Email không hợp lệ')
      setIsLoading(false)
      return
    }
    if (!validatePhone(form.phone)) {
      setError('Số điện thoại không hợp lệ (phải có 10 số)')
      setIsLoading(false)
      return
    }
    if (form.password !== form.confirmPassword) {
      setError('Mật khẩu xác nhận không khớp')
      setIsLoading(false)
      return
    }
    if (!form.agreeTerms) {
      setError('Vui lòng đồng ý với điều khoản sử dụng')
      setIsLoading(false)
      return
    }
    if (!isEmailVerified) {
      setError('Vui lòng xác thực email trước khi đăng ký')
      setIsLoading(false)
      return
    }
    try {
      await register({
        email: form.email,
        password: form.password,
        fullName: form.fullName,
        phone: form.phone,
        roleId: form.roleId,
        referralCode: form.referralCode || undefined,
      })
    } catch (err: unknown) {
      const error = err as AxiosError<{ message?: string }>
      const errorMessage = error.response?.data?.message || 'error'
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  // Google Login với useGoogleLogin để lấy real access token
  const googleLogin = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      setIsLoading(true)
      try {
        await socialLogin({
          provider: 'google',
          accessToken: tokenResponse.access_token,
        })

        toast.success('Đăng ký bằng Google thành công!')
      } catch (err: any) {
        const errorMessage = err?.message || 'Đăng ký bằng Google thất bại'
        toast.error(errorMessage)
      } finally {
        setIsLoading(false)
      }
    },
    onError: (error) => {
      toast.error('Không thể kết nối với Google. Vui lòng thử lại.')
    },
    scope:
      'https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile',
  })

  const handleFacebookRegister = async () => {
    toast.info('Chức năng đăng ký Facebook đang được phát triển')
  }

  const toggleShowPassword = () => {
    setShowPassword(!showPassword)
  }

  const toggleShowConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword)
  }

  return (
    <motion.div
      className={styles.container}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className={styles.header}
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <img src={loginIllustration} alt="Logo TAP" className={styles.logo} />
      </motion.div>

      <div className={styles.formContainer}>
        <motion.h3
          className={styles.subTitle}
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4 }}
        >
          Đăng ký tài khoản mới
        </motion.h3>

        <div className={styles.socialButtons}>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={styles.socialButton}
            onClick={() => googleLogin()}
          >
            <img
              src={googleLogo}
              alt="Google Logo"
              style={{ width: '1.25rem', height: '1.25rem' }}
            />
            Google
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={`${styles.socialButton}`}
            style={{ opacity: 0.6, cursor: 'not-allowed' }}
            onClick={handleFacebookRegister}
          >
            <img
              src={facebookLogo}
              alt="Facebook Logo"
              style={{ width: '1.25rem', height: '1.25rem' }}
            />
            Facebook (Soon)
          </motion.div>
        </div>

        <div className={styles.divider}>
          <span className={`${styles.dividerLine} ${styles.left}`} />
          <span className={styles.dividerText}>Hoặc</span>
          <span className={`${styles.dividerLine} ${styles.right}`} />
        </div>

        <Form onSubmit={handleRegister}>
          <FormGroup>
            <Label className={styles.label}>Họ và tên</Label>
            <div className={styles.inputWrapper}>
              <User className={styles.inputIcon} />
              <Input
                id="fullName"
                name="fullName"
                value={form.fullName}
                onChange={handleChange}
                placeholder="Nguyễn Văn A"
                required
                className={styles.input}
                onFocus={() => setFocusedField('fullName')}
                onBlur={() => setFocusedField(null)}
              />
            </div>
          </FormGroup>

          <FormGroup>
            <Label className={styles.label}>Số điện thoại</Label>
            <div className={styles.inputWrapper}>
              <Phone className={styles.inputIcon} />
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={form.phone}
                onChange={handleChange}
                placeholder="0123456789"
                required
                className={styles.input}
                onFocus={() => setFocusedField('phone')}
                onBlur={() => setFocusedField(null)}
                invalid={!!error && error.includes('Số điện thoại')}
              />
            </div>
            {error && error.includes('Số điện thoại') && <FormFeedback>{error}</FormFeedback>}
          </FormGroup>

          <FormGroup>
            <Label className={styles.label}>Email</Label>
            <div className={styles.inputWrapper}>
              <MailPlus className={styles.inputIcon} />
              <Input
                id="email"
                name="email"
                type="email"
                value={form.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                required
                disabled={isEmailVerified}
                className={styles.input}
                onFocus={() => setFocusedField('email')}
                onBlur={() => setFocusedField(null)}
                invalid={!!error && error.includes('Email')}
              />
            </div>
            {!isEmailVerified && (
              <motion.button
                type="button"
                className={`${styles.verifyButton} ${isSendingVerification ? styles.verifyButtonDisabled : ''}`}
                onClick={handleSendVerificationEmail}
                disabled={isSendingVerification}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isSendingVerification ? (
                  <>
                    <Loader2
                      className="animate-spin"
                      style={{ width: '1.25rem', height: '1.25rem' }}
                    />
                    Đang gửi...
                  </>
                ) : (
                  'Gửi mã xác thực'
                )}
              </motion.button>
            )}
            {error && error.includes('Email') && <FormFeedback>{error}</FormFeedback>}
          </FormGroup>

          {!isEmailVerified && (
            <FormGroup>
              <Label className={styles.label}>Mã OTP</Label>
              <div className={styles.otpInputWrapper}>
                <Input
                  id="otp"
                  name="otp"
                  value={otp}
                  onChange={handleChange}
                  placeholder="Nhập mã OTP"
                  className={styles.otpInput}
                  onFocus={() => setFocusedField('otp')}
                  onBlur={() => setFocusedField(null)}
                  invalid={!!error && error.includes('OTP')}
                />
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    color="primary"
                    onClick={handleVerifyEmail}
                    disabled={isVerifying || !otp}
                    className={styles.submitButton}
                  >
                    {isVerifying ? (
                      <>
                        <Loader2
                          className="animate-spin"
                          style={{ width: '1.25rem', height: '1.25rem', marginRight: '0.5rem' }}
                        />
                        Đang xác thực...
                      </>
                    ) : (
                      'Xác thực'
                    )}
                  </Button>
                </motion.div>
              </div>
              {error && error.includes('OTP') && <FormFeedback>{error}</FormFeedback>}
            </FormGroup>
          )}

          <FormGroup>
            <Label className={styles.label}>Mật khẩu</Label>
            <div className={styles.inputWrapper}>
              <Key className={styles.inputIcon} />
              <Input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={form.password}
                onChange={handleChange}
                placeholder="********"
                required
                className={styles.input}
                onFocus={() => setFocusedField('password')}
                onBlur={() => setFocusedField(null)}
                invalid={!!error && error.includes('mật khẩu')}
              />
              <div className={styles.passwordToggle} onClick={toggleShowPassword}>
                {showPassword ? (
                  <EyeOff style={{ width: '1.25rem', height: '1.25rem' }} />
                ) : (
                  <Eye style={{ width: '1.25rem', height: '1.25rem' }} />
                )}
              </div>
            </div>
            {error && error.includes('mật khẩu') && <FormFeedback>{error}</FormFeedback>}
          </FormGroup>

          <FormGroup>
            <Label className={styles.label}>Xác nhận mật khẩu</Label>
            <div className={styles.inputWrapper}>
              <Key className={styles.inputIcon} />
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={form.confirmPassword}
                onChange={handleChange}
                placeholder="********"
                required
                className={styles.input}
                onFocus={() => setFocusedField('confirmPassword')}
                onBlur={() => setFocusedField(null)}
                invalid={!!error && error.includes('khớp')}
              />
              <div className={styles.passwordToggle} onClick={toggleShowConfirmPassword}>
                {showConfirmPassword ? (
                  <EyeOff style={{ width: '1.25rem', height: '1.25rem' }} />
                ) : (
                  <Eye style={{ width: '1.25rem', height: '1.25rem' }} />
                )}
              </div>
            </div>
            {error && error.includes('khớp') && <FormFeedback>{error}</FormFeedback>}
          </FormGroup>

          <FormGroup>
            <Label className={styles.label}>Loại tài khoản</Label>
            <div className={styles.inputWrapper}>
              <Users className={styles.inputIcon} />
              <Input
                type="select"
                id="roleId"
                name="roleId"
                value={form.roleId}
                onChange={handleChange}
                required
                className={styles.input}
                onFocus={() => setFocusedField('roleId')}
                onBlur={() => setFocusedField(null)}
              >
                <option value="">Chọn loại tài khoản</option>
                {roles &&
                  roles.map((r: RoleResponse) => (
                    <option key={r.id} value={r.id}>
                      {r.name}
                    </option>
                  ))}
              </Input>
            </div>
          </FormGroup>

          <FormGroup>
            <Label className={styles.label}>Mã giới thiệu (nếu có)</Label>
            <div className={styles.inputWrapper}>
              <Users className={styles.inputIcon} />
              <Input
                id="referralCode"
                name="referralCode"
                value={form.referralCode}
                onChange={handleChange}
                placeholder="Mã giới thiệu"
                className={styles.input}
                onFocus={() => setFocusedField('referralCode')}
                onBlur={() => setFocusedField(null)}
              />
            </div>
          </FormGroup>

          <FormGroup>
            <div className={styles.checkboxContainer}>
              <label className={styles.checkbox}>
                <input
                  type="checkbox"
                  name="agreeTerms"
                  checked={form.agreeTerms}
                  onChange={handleChange}
                  required
                />
                Tôi đồng ý với{' '}
                <Link to={ROUTES.TERMS} className={styles.link}>
                   chính sách và điều khoản
                </Link>
              </label>
            </div>
            {error && error.includes('điều khoản') && <FormFeedback>{error}</FormFeedback>}
          </FormGroup>

          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              color="primary"
              block
              type="submit"
              disabled={isLoading || !isEmailVerified}
              className={styles.submitButton}
            >
              <FileCheck
                style={{
                  width: '1.25rem',
                  height: '1.25rem',
                  marginRight: '0.5rem',
                  display: 'inline-block',
                }}
              />
              {isLoading ? 'Đang đăng ký...' : 'Đăng ký'}
            </Button>
          </motion.div>

          <div className={styles.loginContainer}>
            Đã có tài khoản?{' '}
            <Link to={ROUTES.LOGIN} className={styles.link}>
              Đăng nhập
            </Link>
          </div>
        </Form>
      </div>
    </motion.div>
  )
}
