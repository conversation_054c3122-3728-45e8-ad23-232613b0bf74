import React from 'react';
import styles from '../ProductGrid/ProductCard.module.scss';
import logo from '@/assets/images/logo.png';

interface ProductCardProps {
  product: {
    id: number;
    name: string;
    price: number;
    image: string;
  };
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  return (
    <div className={styles.productCard}>
      <div className={styles.imageContainer}>
        <img src={product.image || logo} alt={product.name} />
      </div>
      <div className={styles.productInfo}>
        <h3>{product.name}</h3>
        <p className={styles.price}>{product.price.toLocaleString()}đ</p>
      </div>
    </div>
  );
};

export default ProductCard;