import type { AuthUser } from '@/services/M1/auth'
import { createSlice, type PayloadAction } from '@reduxjs/toolkit'

type Role = {
  name: string
  level: number
}

export type AuthState = {
  accountId: string | null
  username: string | null
  fullName?: string | null
  email: string | null
  phone: string | null
  address: string | null

  image: string | null

  roles: Role[]

  isFisrtLogin: boolean

  schoolId?: string | null
  schoolName?: string | null
  classroomID?: string | null
  studentType?: string | null
  teacherType?: string | null

  academicYearID?: string | null
}

const initialState: AuthUser = {
  sub: null,
  roleGroupCode: null,
  createdAt: null,
  exp: 0,
  iat: 0,
  email: null,
  id: null,
  role: null,
  permissions: [],
}

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    initialStore: () => initialState,
    setCredentials: (state, action: PayloadAction<AuthUser>) => {
      Object.assign(state, action.payload)
    },

    logout: () => initialState,
  },
})

export const { setCredentials, logout, initialStore } = authSlice.actions
export default authSlice.reducer
