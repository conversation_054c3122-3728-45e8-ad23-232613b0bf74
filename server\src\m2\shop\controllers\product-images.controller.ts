import {
  Controller,
  Post,
  UploadedFiles,
  UseInterceptors,
  Body,
  BadRequestException,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiConsumes, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ProductImagesService } from '../services/product-images.service';
import { AuthGuard } from '@nestjs/passport';

@ApiTags('Product Images')
@Controller('product-images')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
export class ProductImagesController {
  constructor(private readonly imagesService: ProductImagesService) {}

  @Post('upload')
  @ApiOperation({
    summary: 'Upload nhiều ảnh lên cloud (tối đa 10 ảnh, 5MB/ảnh), trả về mảng id và url',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: { type: 'string', format: 'binary' },
        },
      },
    },
  })
  @UseInterceptors(
    FilesInterceptor('files', 10, {
      limits: { fileSize: 5 * 1024 * 1024 },
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/^image\/(jpeg|png|jpg|webp)$/)) {
          return cb(new BadRequestException('Chỉ cho phép upload ảnh jpeg, png, jpg, webp'), false);
        }
        cb(null, true);
      },
    }),
  )
  async uploadImages(@UploadedFiles() files: Express.Multer.File[]) {
    if (!files || files.length === 0)
      throw new BadRequestException('Không có file nào được upload');
    return this.imagesService.uploadImages(files);
  }

  @Post('delete-image')
  @ApiOperation({ summary: 'Xóa 1 ảnh trên cloud theo id (public_id)' })
  @ApiBody({ schema: { type: 'object', properties: { imageId: { type: 'string' } } } })
  async deleteImage(@Body('imageId') imageId: string) {
    if (!imageId) throw new BadRequestException('Thiếu imageId');
    return this.imagesService.deleteImage(imageId);
  }
}
