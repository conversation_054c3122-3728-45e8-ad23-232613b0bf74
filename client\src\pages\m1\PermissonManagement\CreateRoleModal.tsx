import React from 'react'
import { Modal, Form, Input } from 'antd'
import ModalButtonGroup from '@/components/ModalButtonGroup'

const ROLE_CODE_PATTERN = /^[A-Z0-9_]+$/

interface CreateRoleModalProps {
  open: boolean
  form: any
  loading?: boolean
  onCancel: () => void
  onSubmit: () => void
}

const CreateRoleModal: React.FC<CreateRoleModalProps> = ({
  open,
  form,
  loading,
  onCancel,
  onSubmit,
}) => {
  return (
    <Modal title="Thêm Role Mới" open={open} onCancel={onCancel} footer={null} width={500}>
      <Form form={form} layout="vertical">
        <Form.Item
          name="role_code"
          label="Mã Role"
          rules={[
            { required: true, message: 'Vui lòng nhập mã role' },
            {
              pattern: ROLE_CODE_PATTERN,
              message: 'Mã role chỉ được chứa chữ hoa, số và dấu gạch dưới',
            },
          ]}
        >
          <Input placeholder="Ví dụ: R06, ADMIN_SPECIAL" />
        </Form.Item>

        <Form.Item
          name="name"
          label="Tên Role"
          rules={[{ required: true, message: 'Vui lòng nhập tên role' }]}
        >
          <Input placeholder="Ví dụ: Quản lý kho" />
        </Form.Item>

        <Form.Item name="description" label="Mô tả" rules={[{ required: false }]}>
          <Input.TextArea placeholder="Mô tả chi tiết về vai trò này..." rows={3} />
        </Form.Item>
      </Form>

      <ModalButtonGroup
        onCancel={onCancel}
        onConfirm={onSubmit}
        cancelText="Hủy"
        confirmText="Tạo Role"
        confirmVariant="primary"
        confirmLoading={loading}
      />
    </Modal>
  )
}

export default CreateRoleModal
