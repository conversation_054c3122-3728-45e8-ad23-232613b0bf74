import React, { useEffect, useState, useCallback } from 'react'
import { Tabs, Form } from 'antd'
import { motion } from 'framer-motion'
import styles from './Profile.module.scss'

// Import components
import { HeroSection, PersonalInfoTab, AffiliateTab, SecurityTab } from './components'

// Import hooks
import { useGetUserProfileHook, useUpdateAvatarHook } from '@/hooks/user'
import {
  useGetAffiliateMeHook,
  useGetAffiliateTreeHook,
  useGetAffiliateStatsHook,
  useGetAffiliateDirectHook,
  useGetAffiliateIndirectHook,
} from '@/hooks/affiliate/useAffiliate'
import {
  useRequest2FAOTP,
  useVerify2FAOTP,
  useDisable2FA,
} from '@/hooks/auth/useLogin'

// Import utilities
import { useToast } from '@/components/Toast'

const { TabPane } = Tabs

// Interfaces
interface UserData {
  id: string
  email: string
  gender: string
  fullName: string
  dateOfBirth: string
  citizenId: string
  phone: string
  address: string
  points: number
  qrCode: string
}

const ProfilePage = () => {
  // Form
  const [form] = Form.useForm()
  const [isEditing, setIsEditing] = useState(false)
  const [showOtpEmail, setShowOtpEmail] = useState(false)

  // 2FA States
  const [is2FAEnabled, setIs2FAEnabled] = useState(false)
  const [showOTPSetup, setShowOTPSetup] = useState(false)
  const [showDisableModal, setShowDisableModal] = useState(false)
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false)
  const [showSessionsModal, setShowSessionsModal] = useState(false)
  const [otpEmail, setOtpEmail] = useState('')
  const [otpCode, setOtpCode] = useState('')
  const [disableOtpCode, setDisableOtpCode] = useState('')
  const [disableOtpSent, setDisableOtpSent] = useState(false)
  const [otpSent, setOtpSent] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  // Location state
  const [currentLocation, setCurrentLocation] = useState<{
    latitude: number
    longitude: number
    address?: string
  } | null>(null)
  const [isGettingLocation, setIsGettingLocation] = useState(false)

  // Hooks
  const { data: profileData, refetch: refetchProfile } = useGetUserProfileHook()
  const updateAvatar = useUpdateAvatarHook()
  const { showSuccess, showError } = useToast()

  // Create userData from profileData
  const userData = profileData ? {
    id: profileData.id || '',
    email: profileData.email || '',
    gender: profileData.gender || '',
    fullName: profileData.fullName || '',
    dateOfBirth: profileData.dateOfBirth || '',
    citizenId: profileData.citizenId || '',
    phone: profileData.phoneNumber || '',
    address: profileData.address || '',
    points: 0,
    qrCode: profileData.uIdCode || '',
  } : null

  // Affiliate hooks
  const { data: affiliateMe } = useGetAffiliateMeHook()
  const { data: affiliateTree } = useGetAffiliateTreeHook()
  const { data: affiliateStats } = useGetAffiliateStatsHook()
  const { data: affiliateDirect } = useGetAffiliateDirectHook()
  const { data: affiliateIndirect } = useGetAffiliateIndirectHook(2) // Add default level

  // 2FA hooks
  const requestOTP = useRequest2FAOTP()
  const verifyOTP = useVerify2FAOTP()
  const { sendOTP: sendDisableOTP, disable2FA } = useDisable2FA()

  // Avatar upload handler
  const handleAvatarUpload = async (file: File, previewUrl: string) => {
    try {
      const success = await updateAvatar(file)
      if (success) {
        showSuccess('Cập nhật ảnh đại diện thành công')
        refetchProfile()
      }
    } catch (error) {
      console.error('Avatar upload error:', error)
      showError('Cập nhật ảnh đại diện thất bại')
    }
  }

  // 2FA handlers
  const handle2FAToggle = useCallback((checked: boolean) => {
    if (!userData) return
    if (checked) {
      setShowOTPSetup(true)
      setOtpEmail(userData.email)
    } else {
      setShowDisableModal(true)
    }
  }, [userData?.email])

  const handleSendOTP = useCallback(async () => {
    if (!otpEmail) return

    setIsProcessing(true)
    const success = await requestOTP(otpEmail)

    if (success) {
      setOtpSent(true)
    }
    setIsProcessing(false)
  }, [otpEmail, requestOTP])

  const handleVerifyOTP = useCallback(async () => {
    if (!otpCode) return

    setIsProcessing(true)
    const success = await verifyOTP(otpEmail, otpCode)

    if (success) {
      setIs2FAEnabled(true)
      setShowOTPSetup(false)
      setOtpSent(false)
      setOtpCode('')
    }
    setIsProcessing(false)
  }, [otpEmail, otpCode, verifyOTP])

  const handleCancelOTPSetup = useCallback(() => {
    setShowOTPSetup(false)
    setOtpSent(false)
    setOtpCode('')
  }, [])

  const handleSendDisableOTP = useCallback(async () => {
    if (!userData) return
    setIsProcessing(true)
    const success = await sendDisableOTP(userData.email)

    if (success) {
      setDisableOtpSent(true)
    }
    setIsProcessing(false)
  }, [userData?.email, sendDisableOTP])

  const handleDisable2FA = useCallback(async () => {
    if (!disableOtpCode || !userData) return

    setIsProcessing(true)
    const success = await disable2FA(userData.email, disableOtpCode)

    if (success) {
      setIs2FAEnabled(false)
      setShowDisableModal(false)
      setDisableOtpSent(false)
      setDisableOtpCode('')
    }
    setIsProcessing(false)
  }, [userData?.email, disableOtpCode, disable2FA])

  const handleCancelDisable = useCallback(() => {
    setShowDisableModal(false)
    setDisableOtpSent(false)
    setDisableOtpCode('')
  }, [])

  const handleShowChangePassword = useCallback(() => {
    setShowChangePasswordModal(true)
  }, [])

  const handleCancelChangePassword = useCallback(() => {
    setShowChangePasswordModal(false)
  }, [])

  const handleShowSessions = useCallback(async () => {
    setShowSessionsModal(true)
  }, [])

  const handleCancelSessions = useCallback(() => {
    setShowSessionsModal(false)
  }, [])

  const handleOtpEmailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setOtpEmail(e.target.value)
  }, [])

  const handleOtpCodeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setOtpCode(e.target.value)
  }, [])

  const handleDisableOtpCodeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setDisableOtpCode(e.target.value)
  }, [])

  // Location handler
  const handleGetCurrentLocation = useCallback(async () => {
    setIsGettingLocation(true)
    
    try {
      // Get actual geolocation
      // Check if geolocation is available
      if (!navigator.geolocation) {
        throw new Error('Trình duyệt không hỗ trợ định vị địa lý')
      }

      // Get actual geolocation with better error handling
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          (error) => {
            let errorMessage = 'Không thể lấy vị trí'
            switch(error.code) {
              case error.PERMISSION_DENIED:
                errorMessage = 'Bạn cần cấp quyền truy cập vị trí để sử dụng tính năng này'
                break
              case error.POSITION_UNAVAILABLE:
                errorMessage = 'Hệ thống định vị không khả dụng'
                break
              case error.TIMEOUT:
                errorMessage = 'Quá thời gian chờ lấy vị trí. Vui lòng thử lại'
                break
            }
            reject(new Error(errorMessage))
          },
          {
            enableHighAccuracy: true,
            timeout: 10000, // Tăng thời gian chờ lên 10 giây
            maximumAge: 0
          }
        )
      })

      // Call Nominatim API for reverse geocoding
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.coords.latitude}&lon=${position.coords.longitude}`
      )
      
      if (!response.ok) throw new Error('Lỗi khi lấy địa chỉ')
      
      const data = await response.json()
      
      setCurrentLocation({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        address: data.display_name
      })
      
      showSuccess(`Đã lấy vị trí thành công: ${data.display_name}`)
    } catch (error) {
      console.error('Location error:', error)
      let errorMessage = 'Không thể lấy vị trí'
      
      if (error instanceof GeolocationPositionError) {
        switch(error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Truy cập vị trí bị từ chối. Vui lòng kiểm tra cài đặt quyền của bạn'
            break
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Thông tin vị trí không khả dụng'
            break
          case error.TIMEOUT:
            errorMessage = 'Hết thời gian chờ lấy vị trí'
            break
        }
      } else if (error instanceof Error) {
        errorMessage = error.message
      }
      
      showError(errorMessage)
      setCurrentLocation(null)
    } finally {
      setIsGettingLocation(false)
    }
  }, [showSuccess, showError])

  if (!profileData || !userData) {
    return <div>Loading...</div>
  }

  return (
    <div className={styles.profilePage}>
      {/* Hero Section */}
      <HeroSection
        profileData={profileData}
        userData={userData}
        affiliateStats={affiliateStats}
        onAvatarUpload={handleAvatarUpload}
      />

      {/* Main Content */}
      <section className={styles.mainSection}>
        <div className={styles.container}>
          <Tabs defaultActiveKey="1" className={styles.profileTabs}>
            <TabPane tab="Thông tin cá nhân" key="1">
              <PersonalInfoTab
                userData={userData}
                profileData={profileData}
                form={form}
                isEditing={isEditing}
                setIsEditing={setIsEditing}
                showOtpEmail={showOtpEmail}
                setShowOtpEmail={setShowOtpEmail}
              />
            </TabPane>

            <TabPane tab="Affiliate" key="2">
              <AffiliateTab
                affiliateStats={affiliateStats}
                affiliateMe={affiliateMe}
                affiliateTree={affiliateTree}
                affiliateDirect={affiliateDirect}
                affiliateIndirect={affiliateIndirect}
              />
            </TabPane>

            <TabPane tab="Bảo mật" key="3">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <SecurityTab
                  is2FAEnabled={is2FAEnabled}
                  showOTPSetup={showOTPSetup}
                  showDisableModal={showDisableModal}
                  showChangePasswordModal={showChangePasswordModal}
                  showSessionsModal={showSessionsModal}
                  otpEmail={otpEmail}
                  otpCode={otpCode}
                  disableOtpCode={disableOtpCode}
                  disableOtpSent={disableOtpSent}
                  otpSent={otpSent}
                  isProcessing={isProcessing}
                  userData={userData}
                  styles={styles}
                  currentLocation={currentLocation}
                  isGettingLocation={isGettingLocation}
                  handle2FAToggle={handle2FAToggle}
                  handleSendOTP={handleSendOTP}
                  handleVerifyOTP={handleVerifyOTP}
                  handleCancelOTPSetup={handleCancelOTPSetup}
                  handleSendDisableOTP={handleSendDisableOTP}
                  handleDisable2FA={handleDisable2FA}
                  handleCancelDisable={handleCancelDisable}
                  handleShowChangePassword={handleShowChangePassword}
                  handleCancelChangePassword={handleCancelChangePassword}
                  handleShowSessions={handleShowSessions}
                  handleCancelSessions={handleCancelSessions}
                  handleOtpEmailChange={handleOtpEmailChange}
                  handleOtpCodeChange={handleOtpCodeChange}
                  handleDisableOtpCodeChange={handleDisableOtpCodeChange}
                  handleGetCurrentLocation={handleGetCurrentLocation}
                />
              </motion.div>
            </TabPane>
          </Tabs>
        </div>
      </section>
    </div>
  )
}

export default ProfilePage
