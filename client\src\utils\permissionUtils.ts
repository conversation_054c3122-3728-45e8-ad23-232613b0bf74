// import Cookies from 'js-cookie'
// import type { Permission } from '@/types/components/route'

// /**
//  * L<PERSON>y permissions từ JWT token
//  */
// export const getUserPermissions = (): Permission[] => {
//   try {
//     const token = Cookies.get('accessToken')
//     if (!token) return []
    
//     // Decode JWT token để lấy permissions
//     const payload = JSON.parse(atob(token.split('.')[1]))
//     return payload.permissions || []
//   } catch (error) {
//     // console.error('Error parsing token for permissions:', error)
//     return []
//   }
// }

// /**
//  * L<PERSON>y permissions từ user profile (fallback)
//  */
// export const getUserPermissionsFromProfile = (userInfo: any): Permission[] => {
//   return userInfo?.permissions || []
// }

// /**
//  * Check xem user có permission không
//  */
// export const hasPermission = (
//   userPermissions: Permission[],
//   requiredPermission: Permission
// ): boolean => {
//   return userPermissions.includes(requiredPermission)
// }

// /**
//  * Check xem user có ít nhất 1 trong các permissions
//  */
// export const hasAnyPermission = (
//   userPermissions: Permission[],
//   requiredPermissions: Permission[]
// ): boolean => {
//   return requiredPermissions.some(permission => 
//     userPermissions.includes(permission)
//   )
// }

// /**
//  * Check xem user có tất cả permissions
//  */
// export const hasAllPermissions = (
//   userPermissions: Permission[],
//   requiredPermissions: Permission[]
// ): boolean => {
//   return requiredPermissions.every(permission => 
//     userPermissions.includes(permission)
//   )
// }
