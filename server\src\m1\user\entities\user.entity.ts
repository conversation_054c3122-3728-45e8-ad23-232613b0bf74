import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
  OneToOne,
} from 'typeorm';
import { UserAffiliate } from './user_affiliates.entity';
import { UserLocation } from './user_locations..entity';
import { Role } from './role.entity';
import { UserProfile } from './user_profile.entity';
import { UserQR } from './user_qr.entity';
import { UserProvider } from './user-provider.entity';
import { User2FASettings } from './user_2fa_settings.entity';
import { AuthSession } from './auth_sessions.entity';

import { Order } from '../../../m2/shop/entities/order.entity';

@Entity('users')
export class User {
  @OneToMany(() => Order, (order) => order.user)
  orders: Order[];

  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'uid_code', unique: true })
  uidCode: string; // Mã định danh người dùng: TAP_xxxxx

  @Column({ unique: true, nullable: true })
  phone?: string;

  @Column({ unique: true, nullable: true })
  email: string;

  @Column({ nullable: true })
  password: string;

  @Column({ nullable: true })
  refreshToken: string;

  @Column({ name: 'referral_by', nullable: true })
  referralBy: string; // UID người giới thiệu

  @Column({ default: 'active' })
  status: 'active' | 'pending' | 'locked';

  @Column()
  role_id: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ nullable: true })
  roleGroupCode: string; // e.g., R01.01

  @ManyToOne(() => Role, (role) => role.users, { eager: true })
  @JoinColumn({ name: 'role_id' }) // FK tên là role_id
  role: Role;

  @OneToMany(() => UserQR, (qr) => qr.user)
  qrcodes: UserQR[];

  @OneToMany(() => UserAffiliate, (ua) => ua.user)
  affiliates: UserAffiliate[];

  @OneToMany(() => UserLocation, (ul) => ul.user)
  locations: UserLocation[];

  @OneToOne(() => UserProfile, (profile) => profile.user, {
    cascade: true,
  })
  profile: UserProfile;

  @OneToMany(() => UserProvider, (provider) => provider.user)
  providers: UserProvider[];

  @OneToOne(() => User2FASettings, (twofa) => twofa.user)
  twoFactorSettings: User2FASettings;

  @OneToMany(() => AuthSession, (session) => session.user)
  authSessions: AuthSession[];
}
