.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: 12px;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
  }

  &Title {
    h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      color: #262626;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 22px;
      }
    }

    p {
      margin: 4px 0 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  &Actions {
    display: flex;
    gap: 12px;
    align-items: center;

    @media (max-width: 768px) {
      width: 100%;
      justify-content: space-between;
    }
  }
}

.notificationButton {
  border-radius: 8px;
  height: 36px;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  color: #595959;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    background-color: #f8f9ff;
  }

  @media (max-width: 768px) {
    height: 32px;
    padding: 0 8px;
    font-size: 12px;
  }
}

.quickAccessButton {
  border-radius: 8px;
  height: 36px;
  background-color: #1890ff;
  border-color: #1890ff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transition: all 0.2s ease;

  &:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    transform: translateY(-1px);
  }

  @media (max-width: 768px) {
    height: 32px;
    padding: 0 12px;
    font-size: 12px;
  }
}

// Mobile Search Container
.mobileSearchContainer {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;

  @media (min-width: 769px) {
    display: none;
  }
}

.mobileSearchInput {
  flex: 1;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  height: 36px;
  transition: all 0.2s ease;

  &:focus,
  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;
  }
}

.mobileFilterButton {
  border-radius: 8px;
  height: 36px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  color: #595959;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    background-color: #f8f9ff;
  }
}

// Mobile Stats
.mobileStatsContainer {
  margin-bottom: 16px;

  @media (min-width: 769px) {
    display: none;
  }

  :global(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header) {
    padding: 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    margin-bottom: 8px;
  }

  :global(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box) {
    padding: 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
  }
}

.mobileStatCard {
  text-align: center;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.mobileStatValue {
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.mobileStatLabel {
  font-size: 12px;
  color: #8c8c8c;
}

.statsRow {
  margin-bottom: 24px;

  @media (max-width: 768px) {
    display: none;
  }
}

.statCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  :global(.ant-card-body) {
    padding: 20px;
  }

  :global(.ant-statistic-title) {
    color: #8c8c8c;
    font-weight: 500;
    margin-bottom: 8px;
  }

  :global(.ant-statistic-content) {
    font-size: 24px;
    font-weight: 600;
  }
}

.filterCard {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-body) {
    padding: 20px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }
}

.filterHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 0;
  }
}

.filterLeft {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filterRight {
  display: flex;
  align-items: center;
  gap: 12px;
}

.viewToggle {
  :global(.ant-radio-button-wrapper) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    
    &:first-child {
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
    }
    
    &:last-child {
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
    }
  }
}

.filterRow {
  margin-top: 16px;
}

.searchInput {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  height: 36px;
  transition: all 0.2s ease;

  &:focus,
  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;
  }
}

.filterSelect {
  width: 100%;
  border-radius: 8px;

  :global(.ant-select-selector) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: 36px;
    transition: all 0.2s ease;

    &:focus,
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

.dateRangePicker {
  width: 100%;
  border-radius: 8px;

  :global(.ant-picker) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: 36px;
    transition: all 0.2s ease;

    &:focus,
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

.bulkCard {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #1890ff;
  background-color: #f8f9ff;

  :global(.ant-card-body) {
    padding: 16px 20px;

    @media (max-width: 768px) {
      padding: 12px 16px;
    }
  }
}

.bulkActions {
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  span {
    color: #1890ff;
    font-weight: 500;
    font-size: 14px;

    @media (max-width: 768px) {
      font-size: 12px;
    }
  }
}

.tableCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-body) {
    padding: 0;
  }
}

.orderTable {
  :global(.ant-table) {
    border-radius: 12px;
    overflow: hidden;
  }

  :global(.ant-table-thead > tr > th) {
    background-color: #fafafa;
    border-bottom: 1px solid #f5f5f5;
    color: #262626;
    font-weight: 600;
    padding: 16px;
    
    &:first-child {
      border-top-left-radius: 12px;
    }
    
    &:last-child {
      border-top-right-radius: 12px;
    }

    @media (max-width: 768px) {
      padding: 12px 8px;
      font-size: 12px;
    }
  }

  :global(.ant-table-tbody > tr > td) {
    padding: 16px;
    border-bottom: 1px solid #f5f5f5;

    @media (max-width: 768px) {
      padding: 12px 8px;
    }
  }

  :global(.ant-table-tbody > tr:hover > td) {
    background-color: #f8f9ff;
  }

  :global(.ant-table-pagination) {
    padding: 16px 24px;
    border-top: 1px solid #f5f5f5;

    @media (max-width: 768px) {
      padding: 12px 16px;
    }
  }
}

// Order Code Button
.orderCodeButton {
  padding: 0;
  height: auto;
  font-weight: 600;
  color: #1890ff;
  
  &:hover {
    color: #40a9ff;
  }
}

.orderCodeButtonMobile {
  padding: 0;
  height: auto;
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
  
  &:hover {
    color: #40a9ff;
  }
}

// Customer Info
.customerInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.customerName {
  font-weight: 500;
  color: #262626;
}

.customerPhone {
  color: #8c8c8c;
  font-size: 12px;
}

.customerNameMobile {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.customerPhoneMobile {
  color: #8c8c8c;
  font-size: 12px;
}

.customerIcon {
  color: #8c8c8c;
  margin-right: 8px;
}

// Products List
.productsList {
  display: flex;
  align-items: center;
  gap: 8px;
}

.productAvatar {
  border: 1px solid #f0f0f0;
}

.productAvatarMobile {
  border: 1px solid #f0f0f0;
}

.itemCount {
  color: #8c8c8c;
  font-size: 12px;
}

.itemCountMobile {
  color: #8c8c8c;
  font-size: 11px;
}

// Date
.dateText {
  color: #595959;
  font-size: 13px;
}

// Total Amount
.totalAmount {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.totalAmountMobile {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

// Payment Info
.paymentInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.paymentInfoMobile {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

// Action Buttons
.actionButton {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9ff;
    border-color: #1890ff;
    color: #1890ff;
  }
}

.mobileActionButton {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 12px;

  &:hover {
    background-color: #f8f9ff;
    border-color: #1890ff;
    color: #1890ff;
  }
}

// Mobile Order Card
.mobileOrderCard {
  width: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background-color: #ffffff;
  padding: 12px;
  margin-bottom: 8px;
}

.mobileOrderHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.orderCodeMobile {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.orderDateMobile {
  color: #8c8c8c;
  font-size: 11px;
}

.orderStatusMobile {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.mobileOrderBody {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
}

.customerInfoMobile {
  display: flex;
  align-items: center;
  gap: 8px;
}

.productInfoMobile {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobileOrderFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f5f5f5;
}

.mobileOrderActions {
  display: flex;
  gap: 4px;
  justify-content: center;
  padding-top: 8px;
  border-top: 1px solid #f5f5f5;
  margin-top: 8px;
}

// Filter Drawer
.filterDrawer {
  :global(.ant-drawer-body) {
    padding: 16px;
  }
}

.filtersContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mobileFilterItem {
  display: flex;
  flex-direction: column;
  gap: 8px;

  label {
    font-weight: 500;
    color: #262626;
    font-size: 14px;
  }
}

.drawerActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;

  button {
    border-radius: 8px;
    height: 36px;
  }
}

// Order Detail Modal
.orderDetailModal {
  :global(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :global(.ant-modal-header) {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 20px 24px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }

  :global(.ant-modal-title) {
    font-size: 18px;
    font-weight: 600;
    color: #262626;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }

  :global(.ant-modal-body) {
    padding: 24px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }

  @media (max-width: 768px) {
    :global(.ant-modal) {
      margin: 0;
      max-width: 100%;
      height: 100vh;
      border-radius: 0;
    }

    :global(.ant-modal-content) {
      height: 100vh;
      border-radius: 0;
      display: flex;
      flex-direction: column;
    }

    :global(.ant-modal-body) {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
    }
  }
}

.orderDetailContainer {
  .orderDetailHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }

    h3 {
      margin: 0;
      color: #262626;
      font-size: 18px;
      font-weight: 600;
    }
  }
}

.detailCard {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-head) {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
    min-height: auto;
  }

  :global(.ant-card-head-title) {
    color: #262626;
    font-weight: 600;
    font-size: 14px;
  }

  :global(.ant-card-body) {
    padding: 16px;
  }

  :global(.ant-descriptions-item-label) {
    color: #8c8c8c;
    font-weight: 500;
    width: 120px;
  }

  :global(.ant-descriptions-item-content) {
    color: #262626;
  }
}

.productDetail {
  display: flex;
  align-items: center;
  gap: 12px;
}

.productInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.productName {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.productSku {
  color: #8c8c8c;
  font-size: 12px;
}

.financialInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.financialRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  
  &:last-child {
    font-size: 16px;
    color: #262626;
  }
}

.totalFinal {
  color: #ff4d4f;
  font-size: 18px;
}

// Responsive adjustments
@media (max-width: 480px) {
  .container {
    padding: 8px;
  }

  .header {
    margin-bottom: 12px;

    &Title h1 {
      font-size: 20px;
    }
  }

  .mobileSearchContainer {
    margin-bottom: 12px;
  }

  .mobileStatsContainer {
    margin-bottom: 12px;
  }

  .mobileStatCard {
    padding: 8px;
  }

  .mobileStatValue {
    font-size: 16px;
  }

  .mobileStatLabel {
    font-size: 11px;
  }

  .bulkCard :global(.ant-card-body) {
    padding: 8px 12px;
  }

  .bulkActions {
    span {
      font-size: 11px;
    }
  }

  .mobileOrderCard {
    padding: 8px;
  }

  .mobileOrderHeader {
    gap: 8px;
  }

  .mobileActionButton {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  .orderDetailModal {
    :global(.ant-modal-header) {
      padding: 12px;
    }

    :global(.ant-modal-body) {
      padding: 12px;
    }
  }

  .detailCard :global(.ant-card-body) {
    padding: 12px;
  }
}

// Landscape orientation for mobile
@media (max-width: 768px) and (orientation: landscape) {
  .orderDetailModal {
    :global(.ant-modal) {
      height: 100vh;
    }

    :global(.ant-modal-content) {
      height: 100vh;
      max-height: 100vh;
    }

    :global(.ant-modal-body) {
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
  }
}
