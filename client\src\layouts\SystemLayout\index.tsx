import classNames from 'classnames/bind'
import { useEffect, useState } from 'react'
import type { ReactNode } from 'react'

import styles from './SystemLayout.module.scss'
import FooterSystem from './Footer'
import SidebarSystem from './Sidebar'
import HeaderSystem from './Header'
import { Drawer, Layout } from 'antd'
import { Content } from 'antd/es/layout/layout'
import DrawerRight from './Drawer'

const cx = classNames.bind(styles)

type SystemLayoutProps = {
  children: ReactNode
}

const SystemLayout = ({ children }: SystemLayoutProps) => {
  // Use State -------------------------------------------------------------------------------------------------
  const [isToggleNavbar, setIsToggleNavbar] = useState(false)
  const [drawerRightState, setDrawerRightState] = useState(false)

  const [isMobile, setIsMobile] = useState(window.innerWidth < 768)

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div className={cx('wrapper')}>
      <SidebarSystem isToggleNavbar={isToggleNavbar} setIsToggleNavbar={setIsToggleNavbar} />

      <div className={cx('content-wrapper', `${isToggleNavbar && !isMobile ? 'toggle-nav' : ''}`)}>
        <HeaderSystem
          isToggleNavbar={isToggleNavbar}
          setToogleNavbar={setIsToggleNavbar}
          drawerRightState={drawerRightState}
          setDrawerRightState={setDrawerRightState}
        />
        <DrawerRight onClose={() => setDrawerRightState(false)} visible={drawerRightState} />
        <div className={cx('content')}>{children}</div>

        <FooterSystem />
      </div>
    </div>
  )
}

export default SystemLayout
