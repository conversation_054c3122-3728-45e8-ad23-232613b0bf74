@import '@styles/variables';

.header {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 100;
  background-color: #fff8f3;
  padding: 24px 0;

  .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .searchForm {
    flex-grow: 1;
    max-width: 760px;
    margin: 0 20px;

    .searchContainer {
      display: flex;
      align-items: center;
      background: $white;
      border-radius: 50px;
      overflow: hidden;

      .allCategories {
        display: flex;
        align-items: center;
        padding: 8px 24px;
        background: $gray-bg;
        border-radius: 50px;
        margin-right: 8px;
        cursor: pointer;
      }
    }
  }

  .nav {
    display: flex;
    gap: 24px;

    .helpButton {
      background: $primary;
      color: $white;
      padding: 12px 24px;
      border-radius: 50px;
    }
  }
}