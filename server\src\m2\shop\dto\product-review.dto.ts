import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, <PERSON>U<PERSON><PERSON>, <PERSON>, Min } from 'class-validator';

export class CreateProductReviewDto {
  @ApiProperty()
  @IsUUID()
  product_id: string;

  @ApiProperty({ minimum: 1, maximum: 5 })
  @IsInt()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiProperty({ required: false })
  @IsUUID()
  @IsOptional()
  parent_id?: string;

  @ApiProperty({ required: true })
  @IsUUID()
  @IsOptional()
  order_id: string;

  @ApiProperty({ required: false })
  @IsOptional()
  images?: any;
}

export class UpdateProductReviewDto {
  @ApiProperty({ required: false })
  @IsInt()
  @Min(1)
  @Max(5)
  @IsOptional()
  rating?: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  images?: any;
}
