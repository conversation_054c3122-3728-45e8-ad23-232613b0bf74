import { Container, Navbar } from 'reactstrap';
import { Outlet } from 'react-router-dom';

export default function PublicLayout() {
  return (
    <div style={{
      width: '100vw',
      minHeight: '100vh',
      margin: 0,
      padding: 0
    }}>
      <Navbar color="light" light expand="md" style={{ margin: 0, padding: 0 }}></Navbar>      <Container fluid className="p-0">  {/* `fluid` + padding:0 */}
        <Outlet />
      </Container>
    </div>
  );
}