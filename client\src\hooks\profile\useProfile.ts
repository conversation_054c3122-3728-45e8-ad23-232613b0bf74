// import { useEffect } from 'react'
// import { useNavigate } from 'react-router-dom'
// import { useDispatch } from 'react-redux'
// import { getCookie } from '../../private/cookies'
// import { setCredentials } from '../../libs/features/auth/authSlice'
// import { ROUTES } from '../../constants/routes'
// import { useGetUserProfileQuery } from '@/services/M1/user'

// export const useProfile = () => {
//   const navigate = useNavigate()
//   const dispatch = useDispatch()

//   const token = getCookie('accessToken')

//   const {
//     data: profileData,
//     isLoading,
//     isError,
//     error,
//   } = useGetUserProfileQuery(token ? { token } : ({} as any), {
//     skip: !token, // Skip query nếu không có token
//   })

//   useEffect(() => {
//     if (isError && error) {
//       console.error('Lỗi API:', error)
//       if ((error as any).status === 401) {
//         navigate(ROUTES.LOGIN) // Điều hướng khi 401
//       }
//     }
//   }, [isError, error, navigate])

//   useEffect(() => {
//     if (!token) {
//       console.warn('Không tìm thấy token, điều hướng về login.')
//       navigate(ROUTES.LOGIN)
//       return
//     }

//     if (profileData) {
//       dispatch(setCredentials(profileData))
//     }
//   }, [profileData, token, dispatch, navigate])

//   const fetchProfile = async () => {
//     if (!token) {
//       navigate(ROUTES.LOGIN)
//     }
//   }

//   return { profileData, isLoading, fetchProfile }
// }
