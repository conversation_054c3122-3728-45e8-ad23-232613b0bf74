import { useQrMutation, type QrResponse } from '@/services/M1/Qr/qr'
import { useEffect } from 'react'
import { useLoadingStore } from '@/utils/loadingStore'
import { getCookie } from '@/private/cookies'
import { data } from 'react-router-dom'
import {
  useChatBotAnalyzeMutation,
  type ChatBotResponse,
  type UserSupportRequestDto,
} from '@/services/Chatbot/supportChatbot'

// Hook gọi API chatbot (POST), trả về kết quả
const useGetChatBotHook = () => {
  const [analyzeChatbot, { isLoading, error }] = useChatBotAnalyzeMutation()

  const chat = async (data: UserSupportRequestDto): Promise<ChatBotResponse | null> => {
    try {
      const res = await analyzeChatbot(data).unwrap()
      return res || null
    } catch (error: any) {
      return null
    }
  }

  return { chat, isLoading, error }
}

// Xuất ra
export { useGetChatBotHook }
