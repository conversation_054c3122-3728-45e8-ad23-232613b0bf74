import React, { useState, useEffect } from 'react'
import {
  Table,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Tag,
  Space,
  Card,
  Statistic,
  Row,
  Col,
  Badge,
  Tooltip,
  Avatar,
  Descriptions,
  Divider,
  message,
  Popconfirm,
  Drawer,
  Collapse,
  DatePicker,
  Image,
  Timeline,
  Radio,
  Progress,
  Alert,
  Tabs,
  List,
} from 'antd'
import Button from '@/components/Button'
import {
  useGetInventory,
  useGetInventoryDetail,
  useUpdateVariants,
  useQuickUpdateStock,
  useGetInventoryHistory,
  useImportInventory,
  useExportInventory,
  useGetAlerts,
} from '@/hooks/inventory/useInventory'
import ModalButtonGroup from '@/components/ModalButtonGroup'
import {
  InboxOutlined,
  SearchOutlined,
  FilterOutlined,
  ExportOutlined,
  ImportOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  PlusOutlined,
  MinusOutlined,
  HistoryOutlined,
  ShopOutlined,
  BarcodeOutlined,
  CalendarOutlined,
  FileExcelOutlined,
  BellOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  InfoCircleOutlined,
  DollarOutlined, // ✅ Thêm import này
} from '@ant-design/icons'
import styles from './InventoryManagement.module.scss'

const { Option } = Select
const { TextArea } = Input
const { RangePicker } = DatePicker

// ❌ Xóa dòng này: const { Panel } = Collapse;
// ❌ Xóa dòng này: const { TabPane } = Tabs;

interface ProductVariant {
  id: string
  name: string
  sku: string
  price: number
  stock: number
  minStock: number
  batchNumber?: string
  expiryDate?: string
}

interface InventoryItem {
  id: string
  name: string
  mainSku: string
  image: string
  category: string
  variants: ProductVariant[]
  totalStock: number
  totalMinStock: number
  status: 'active' | 'inactive' | 'blocked'
  stockStatus: 'sufficient' | 'low' | 'out_of_stock'
  lastUpdated: string
  warehouse: string
  notes: string
}

interface StockHistory {
  id: string
  date: string
  type: 'import' | 'export' | 'adjustment'
  quantity: number
  reason: string
  user: string
}

const InventoryManagement: React.FC = () => {
  const [isMobile, setIsMobile] = useState(false)
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null)
  const [detailVisible, setDetailVisible] = useState(false)
  const [updateModalVisible, setUpdateModalVisible] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [searchText, setSearchText] = useState('')
  const [viewMode, setViewMode] = useState<'table' | 'card'>('table')
  const [filters, setFilters] = useState({
    category: '',
    stockStatus: '',
    warehouse: '',
    productStatus: '',
  })
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false)
  const [editingStock, setEditingStock] = useState<{ itemId: string; variantId: string } | null>(
    null
  )
  const [form] = Form.useForm()

  // Dùng hook lấy danh sách tồn kho
  const { data, isLoading: loading, refetch } = useGetInventory(filters)
  // Đảm bảo inventory luôn là mảng
  const [inventory, setInventory] = useState<InventoryItem[]>([])

  // Chuyển đổi dữ liệu từ API sang InventoryItem[]
  useEffect(() => {
    // Nếu không có data hoặc không có items, set rỗng
    if (!data || !Array.isArray(data.items)) {
      setInventory([])
      return
    }
    // Map từ API sang InventoryItem[]
    const mapped: InventoryItem[] = data.items.map((item: any) => {
      const product = item.product || {}
      const version = item.version || {}
      return {
        id: item.id,
        name: product.name || '',
        mainSku: version.sku_code || '',
        image: product.thumbnail_url || '',
        category: product.category_id || '',
        variants: [
          {
            id: version.id,
            name: version.name || '',
            sku: version.sku_code || '',
            price: parseFloat(version.price || '0'),
            stock: version.stock ?? item.stock_quantity ?? 0,
            minStock: item.threshold_min ?? 0,
            batchNumber: undefined,
            expiryDate: undefined,
          },
        ],
        totalStock: version.stock ?? item.stock_quantity ?? 0,
        totalMinStock: item.threshold_min ?? 0,
        status: 'active',
        stockStatus:
          (version.stock ?? item.stock_quantity ?? 0) === 0
            ? 'out_of_stock'
            : (version.stock ?? item.stock_quantity ?? 0) < (item.threshold_min ?? 0)
              ? 'low'
              : 'sufficient',
        lastUpdated: item.updated_at ? item.updated_at.replace('T', ' ').slice(0, 19) : '',
        warehouse: item.location_code || '',
        notes: item.note || '',
      }
    })
    setInventory(mapped)
  }, [data])
  // Dùng hook lấy lịch sử tồn kho
  const { data: stockHistory = [] } = useGetInventoryHistory(selectedItem?.id || '')

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    refetch()
  }, [])

  // Load inventory from API
  const loadInventory = () => {
    refetch()
  }

  // Stock status configuration
  const stockStatusConfig = {
    sufficient: { color: 'green', text: 'Đủ', icon: <CheckCircleOutlined /> },
    low: { color: 'orange', text: 'Sắp hết', icon: <WarningOutlined /> },
    out_of_stock: { color: 'red', text: 'Hết hàng', icon: <CloseCircleOutlined /> },
  }

  const productStatusConfig = {
    active: { color: 'green', text: 'Đang bán' },
    inactive: { color: 'orange', text: 'Tạm ẩn' },
    blocked: { color: 'red', text: 'Bị khóa' },
  }

  // Desktop columns (giữ nguyên từ dòng này đến handleBulkAction)
  const desktopColumns = [
    {
      title: 'Ảnh',
      dataIndex: 'image',
      key: 'image',
      width: 80,
      render: (image: string) => (
        <Image
          src={image}
          alt="Product"
          width={50}
          height={50}
          className={styles.productImage}
          fallback="/images/no-image.png"
        />
      ),
    },
    {
      title: 'Sản phẩm',
      key: 'product',
      render: (unused: any, record: InventoryItem) => (
        <div className={styles.productInfo}>
          <div className={styles.productName}>{record.name}</div>
          <div className={styles.productSku}>
            <BarcodeOutlined /> {record.mainSku}
          </div>
          <div className={styles.productCategory}>{record.category}</div>
        </div>
      ),
    },
    {
      title: 'Biến thể',
      key: 'variants',
      render: (unused: any, record: InventoryItem) => (
        <div className={styles.variantsList}>
          {record.variants.map((variant) => (
            <div key={variant.id} className={styles.variantItem}>
              <span className={styles.variantName}>{variant.name}</span>
              <span className={styles.variantPrice}>{variant.price.toLocaleString()}₫</span>
            </div>
          ))}
        </div>
      ),
    },
    {
      title: 'Tồn kho',
      key: 'stock',
      render: (unused: any, record: InventoryItem) => (
        <div className={styles.stockInfo}>
          <div className={styles.stockCurrent}>
            <span className={getStockColorClass(record.stockStatus)}>{record.totalStock}</span>
            <span className={styles.stockUnit}>sản phẩm</span>
          </div>
          <div className={styles.stockMin}>Tối thiểu: {record.totalMinStock}</div>
        </div>
      ),
    },
    {
      title: 'Trạng thái tồn',
      dataIndex: 'stockStatus',
      key: 'stockStatus',
      render: (status: string) => {
        const config = stockStatusConfig[status as keyof typeof stockStatusConfig]
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        )
      },
    },
    {
      title: 'Trạng thái SP',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const config = productStatusConfig[status as keyof typeof productStatusConfig]
        return <Tag color={config.color}>{config.text}</Tag>
      },
    },
    {
      title: 'Kho',
      dataIndex: 'warehouse',
      key: 'warehouse',
      render: (warehouse: string) => (
        <div className={styles.warehouseInfo}>
          <ShopOutlined /> {warehouse}
        </div>
      ),
    },
    {
      title: 'Cập nhật',
      dataIndex: 'lastUpdated',
      key: 'lastUpdated',
      render: (date: string) => (
        <div className={styles.dateInfo}>
          <CalendarOutlined /> {date}
        </div>
      ),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      render: (unused: any, record: InventoryItem) => (
        <Space size="small">
          <Button
            variant="default"
            icon={<EditOutlined />}
            onClick={() => openUpdateModal(record)}
            size="small"
          >
            Sửa
          </Button>
          <Button
            variant="default"
            icon={<EyeOutlined />}
            onClick={() => viewItemDetail(record)}
            size="small"
          >
            Xem
          </Button>
          <Button
            variant="default"
            icon={<HistoryOutlined />}
            onClick={() => viewStockHistory(record)}
            size="small"
          >
            Lịch sử
          </Button>
        </Space>
      ),
    },
  ]

  // Mobile columns (giữ nguyên toàn bộ mobile columns)
  const mobileColumns = [
    {
      title: 'Tồn kho',
      key: 'inventory',
      render: (unused: any, record: InventoryItem) => (
        <div className={styles.mobileInventoryCard}>
          <div className={styles.mobileInventoryHeader}>
            <div className={styles.mobileProductInfo}>
              <Image
                src={record.image}
                alt="Product"
                width={40}
                height={40}
                className={styles.productImageMobile}
                fallback="/images/no-image.png"
              />
              <div className={styles.mobileProductDetails}>
                <div className={styles.productNameMobile}>{record.name}</div>
                <div className={styles.productSkuMobile}>
                  <BarcodeOutlined /> {record.mainSku}
                </div>
                <div className={styles.productCategoryMobile}>{record.category}</div>
              </div>
            </div>
            <div className={styles.mobileStockStatus}>
              <Tag
                color={stockStatusConfig[record.stockStatus].color}
                icon={stockStatusConfig[record.stockStatus].icon}
              >
                {stockStatusConfig[record.stockStatus].text}
              </Tag>
            </div>
          </div>

          <div className={styles.mobileInventoryBody}>
            <div className={styles.mobileStockInfo}>
              <div className={styles.mobileStockCurrent}>
                <span className={getStockColorClass(record.stockStatus)}>{record.totalStock}</span>
                <span className={styles.mobileStockLabel}>Tồn kho</span>
              </div>
              <div className={styles.mobileStockMin}>
                <span>{record.totalMinStock}</span>
                <span className={styles.mobileStockLabel}>Tối thiểu</span>
              </div>
            </div>

            <div className={styles.mobileWarehouseInfo}>
              <ShopOutlined /> {record.warehouse}
            </div>
          </div>

          <div className={styles.mobileInventoryFooter}>
            <div className={styles.mobileUpdateInfo}>
              <CalendarOutlined /> {record.lastUpdated}
            </div>
            <div className={styles.mobileActions}>
              <Button
                variant="default"
                icon={<EditOutlined />}
                onClick={() => openUpdateModal(record)}
                size="small"
              >
                Sửa
              </Button>
              <Button
                variant="default"
                icon={<EyeOutlined />}
                onClick={() => viewItemDetail(record)}
                size="small"
              >
                Xem
              </Button>
              <Button
                variant="default"
                icon={<HistoryOutlined />}
                onClick={() => viewStockHistory(record)}
                size="small"
              >
                Lịch sử
              </Button>
            </div>
          </div>
        </div>
      ),
    },
  ]

  const getStockColorClass = (stockStatus: string) => {
    switch (stockStatus) {
      case 'sufficient':
        return styles.stockSufficient
      case 'low':
        return styles.stockLow
      case 'out_of_stock':
        return styles.stockOut
      default:
        return styles.stockSufficient
    }
  }

  const openUpdateModal = (item: InventoryItem) => {
    setSelectedItem(item)
    form.setFieldsValue({
      itemId: item.id,
      variants: item.variants.map((v) => ({
        id: v.id,
        stock: v.stock,
        minStock: v.minStock,
      })),
    })
    setUpdateModalVisible(true)
  }

  const viewItemDetail = (item: InventoryItem) => {
    setSelectedItem(item)
    setDetailVisible(true)
  }

  const viewStockHistory = (item: InventoryItem) => {
    setSelectedItem(item)
    message.info(`Xem lịch sử tồn kho ${item.name}`)
  }

  const handleQuickStockUpdate = (itemId: string, variantId: string, change: number) => {
    const updatedInventory = inventory.map((item) => {
      if (item.id === itemId) {
        const updatedVariants = item.variants.map((variant) => {
          if (variant.id === variantId) {
            const newStock = Math.max(0, variant.stock + change)
            return { ...variant, stock: newStock }
          }
          return variant
        })

        const newTotalStock = updatedVariants.reduce((sum, v) => sum + v.stock, 0)
        const newStockStatus =
          newTotalStock === 0
            ? 'out_of_stock'
            : newTotalStock < item.totalMinStock
              ? 'low'
              : 'sufficient'

        return {
          ...item,
          variants: updatedVariants,
          totalStock: newTotalStock,
          stockStatus: newStockStatus,
          lastUpdated: new Date().toISOString().replace('T', ' ').slice(0, 19),
        }
      }
      return item
    })

    setInventory(
      updatedInventory.map((item) => ({
        ...item,
        stockStatus: item.stockStatus as 'sufficient' | 'low' | 'out_of_stock',
      }))
    )
    message.success(`Cập nhật tồn kho ${change > 0 ? 'tăng' : 'giảm'} ${Math.abs(change)}`)
  }

  const handleUpdateStock = async (values: any) => {
    try {
      message.success('Cập nhật tồn kho thành công!')
      setUpdateModalVisible(false)
      form.resetFields()
      loadInventory()
    } catch (error) {
      message.error('Có lỗi xảy ra khi cập nhật!')
    }
  }

  const handleBulkAction = (action: string) => {
    if (selectedRowKeys.length === 0) {
      message.warning('Vui lòng chọn ít nhất một sản phẩm')
      return
    }

    switch (action) {
      case 'export':
        message.success(`Xuất Excel ${selectedRowKeys.length} sản phẩm`)
        break
      case 'update':
        message.info(`Cập nhật hàng loạt ${selectedRowKeys.length} sản phẩm`)
        break
      case 'alert':
        message.info(`Thiết lập cảnh báo ${selectedRowKeys.length} sản phẩm`)
        break
    }
    setSelectedRowKeys([])
  }

  const filteredInventory = inventory.filter((item: InventoryItem) => {
    const matchesSearch =
      (item.name && item.name.toLowerCase().includes(searchText.toLowerCase())) ||
      (item.mainSku && item.mainSku.toLowerCase().includes(searchText.toLowerCase()))
    const matchesCategory = !filters.category || item.category === filters.category
    const matchesStockStatus = !filters.stockStatus || item.stockStatus === filters.stockStatus
    const matchesWarehouse = !filters.warehouse || item.warehouse === filters.warehouse
    const matchesProductStatus = !filters.productStatus || item.status === filters.productStatus

    return (
      matchesSearch &&
      matchesCategory &&
      matchesStockStatus &&
      matchesWarehouse &&
      matchesProductStatus
    )
  })

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record: InventoryItem) => ({
      className:
        record.stockStatus === 'out_of_stock'
          ? styles.outOfStockRow
          : record.stockStatus === 'low'
            ? styles.lowStockRow
            : '',
    }),
  }

  const bulkActions = [
    { key: 'export', label: 'Xuất Excel', icon: <ExportOutlined /> },
    { key: 'update', label: 'Cập nhật hàng loạt', icon: <EditOutlined /> },
    { key: 'alert', label: 'Thiết lập cảnh báo', icon: <BellOutlined /> },
  ]

  // Statistics
  const totalProducts = inventory.length
  const lowStockProducts = inventory.filter(
    (item: InventoryItem) => item.stockStatus === 'low'
  ).length
  const outOfStockProducts = inventory.filter(
    (item: InventoryItem) => item.stockStatus === 'out_of_stock'
  ).length
  const totalStockValue = inventory.reduce((sum: number, item: InventoryItem) => {
    return (
      sum +
      (Array.isArray(item.variants)
        ? item.variants.reduce(
            (variantSum: number, variant: ProductVariant) =>
              variantSum + variant.stock * variant.price,
            0
          )
        : 0)
    )
  }, 0)

  // ✅ Sửa renderMobileStats - dùng items thay vì Panel
  const renderMobileStats = () => (
    <div className={styles.mobileStatsContainer}>
      <Collapse
        ghost
        items={[
          {
            key: '1',
            label: 'Thống kê tồn kho',
            children: (
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <div className={styles.mobileStatCard}>
                    <div className={styles.mobileStatValue}>{totalProducts}</div>
                    <div className={styles.mobileStatLabel}>Tổng SP</div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className={styles.mobileStatCard}>
                    <div className={styles.mobileStatValue} style={{ color: '#ff4d4f' }}>
                      {outOfStockProducts}
                    </div>
                    <div className={styles.mobileStatLabel}>Hết hàng</div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className={styles.mobileStatCard}>
                    <div className={styles.mobileStatValue} style={{ color: '#fa8c16' }}>
                      {lowStockProducts}
                    </div>
                    <div className={styles.mobileStatLabel}>Sắp hết</div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className={styles.mobileStatCard}>
                    <div className={styles.mobileStatValue} style={{ color: '#52c41a' }}>
                      {totalStockValue.toLocaleString()}₫
                    </div>
                    <div className={styles.mobileStatLabel}>Giá trị</div>
                  </div>
                </Col>
              </Row>
            ),
          },
        ]}
      />
    </div>
  )

  // ✅ Sửa renderItemDetail - dùng items thay vì TabPane
  const renderItemDetail = () => (
    <div className={styles.itemDetailContainer}>
      <div className={styles.itemDetailHeader}>
        <h3>Chi tiết tồn kho - {selectedItem?.name}</h3>
        <Space>
          <Button icon={<EditOutlined />} onClick={() => openUpdateModal(selectedItem!)}>
            Cập nhật
          </Button>
          <Button icon={<HistoryOutlined />}>Lịch sử</Button>
        </Space>
      </div>

      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: '1',
            label: 'Thông tin chung',
            children: (
              <Row gutter={[16, 16]}>
                <Col xs={24} lg={12}>
                  <Card title="Thông tin sản phẩm" className={styles.detailCard}>
                    <Descriptions column={1} size="small">
                      <Descriptions.Item label="Tên sản phẩm">
                        {selectedItem?.name}
                      </Descriptions.Item>
                      <Descriptions.Item label="Mã SKU">{selectedItem?.mainSku}</Descriptions.Item>
                      <Descriptions.Item label="Danh mục">
                        {selectedItem?.category}
                      </Descriptions.Item>
                      <Descriptions.Item label="Kho">{selectedItem?.warehouse}</Descriptions.Item>
                      <Descriptions.Item label="Trạng thái">
                        <Tag
                          color={
                            productStatusConfig[
                              selectedItem?.status as keyof typeof productStatusConfig
                            ]?.color
                          }
                        >
                          {
                            productStatusConfig[
                              selectedItem?.status as keyof typeof productStatusConfig
                            ]?.text
                          }
                        </Tag>
                      </Descriptions.Item>
                      <Descriptions.Item label="Ghi chú">
                        {selectedItem?.notes || 'Không có'}
                      </Descriptions.Item>
                    </Descriptions>
                  </Card>
                </Col>

                <Col xs={24} lg={12}>
                  <Card title="Tổng quan tồn kho" className={styles.detailCard}>
                    <div className={styles.stockOverview}>
                      <div className={styles.stockOverviewItem}>
                        <div className={styles.stockOverviewLabel}>Tổng tồn kho</div>
                        <div className={styles.stockOverviewValue}>
                          {selectedItem?.totalStock} sản phẩm
                        </div>
                      </div>
                      <div className={styles.stockOverviewItem}>
                        <div className={styles.stockOverviewLabel}>Ngưỡng tối thiểu</div>
                        <div className={styles.stockOverviewValue}>
                          {selectedItem?.totalMinStock} sản phẩm
                        </div>
                      </div>
                      <div className={styles.stockOverviewItem}>
                        <div className={styles.stockOverviewLabel}>Trạng thái</div>
                        <div className={styles.stockOverviewValue}>
                          <Tag
                            color={
                              stockStatusConfig[
                                selectedItem?.stockStatus as keyof typeof stockStatusConfig
                              ]?.color
                            }
                          >
                            {
                              stockStatusConfig[
                                selectedItem?.stockStatus as keyof typeof stockStatusConfig
                              ]?.text
                            }
                          </Tag>
                        </div>
                      </div>
                      <div className={styles.stockOverviewItem}>
                        <div className={styles.stockOverviewLabel}>Giá trị tồn kho</div>
                        <div className={styles.stockOverviewValue}>
                          {selectedItem?.variants
                            .reduce((sum, v) => sum + v.stock * v.price, 0)
                            .toLocaleString()}
                          ₫
                        </div>
                      </div>
                    </div>
                  </Card>
                </Col>
              </Row>
            ),
          },
          {
            key: '2',
            label: 'Biến thể',
            children: (
              <Card title="Danh sách biến thể" className={styles.detailCard}>
                <Table
                  dataSource={selectedItem?.variants}
                  rowKey="id"
                  pagination={false}
                  size="small"
                  columns={[
                    {
                      title: 'Biến thể',
                      dataIndex: 'name',
                      key: 'name',
                    },
                    {
                      title: 'SKU',
                      dataIndex: 'sku',
                      key: 'sku',
                      render: (sku: string) => <code className={styles.skuCode}>{sku}</code>,
                    },
                    {
                      title: 'Giá',
                      dataIndex: 'price',
                      key: 'price',
                      render: (price: number) => `${price.toLocaleString()}₫`,
                    },
                    {
                      title: 'Tồn kho',
                      dataIndex: 'stock',
                      key: 'stock',
                      render: (stock: number, record: ProductVariant) => (
                        <div className={styles.stockUpdateControls}>
                          <Button
                            variant="default"
                            icon={<MinusOutlined />}
                            size="small"
                            onClick={() => handleQuickStockUpdate(selectedItem!.id, record.id, -1)}
                          >
                            -
                          </Button>
                          <span className={styles.stockValue}>{stock}</span>
                          <Button
                            variant="default"
                            icon={<PlusOutlined />}
                            size="small"
                            onClick={() => handleQuickStockUpdate(selectedItem!.id, record.id, 1)}
                          >
                            +
                          </Button>
                        </div>
                      ),
                    },
                    {
                      title: 'Tối thiểu',
                      dataIndex: 'minStock',
                      key: 'minStock',
                    },
                    {
                      title: 'Lô SX',
                      dataIndex: 'batchNumber',
                      key: 'batchNumber',
                    },
                    {
                      title: 'Hạn sử dụng',
                      dataIndex: 'expiryDate',
                      key: 'expiryDate',
                    },
                  ]}
                />
              </Card>
            ),
          },
          {
            key: '3',
            label: 'Lịch sử',
            children: (
              <Card title="Lịch sử nhập xuất kho" className={styles.detailCard}>
                <Timeline>
                  {stockHistory.map((history: any) => (
                    <Timeline.Item
                      key={history.id}
                      color={
                        history.type === 'import'
                          ? 'green'
                          : history.type === 'export'
                            ? 'red'
                            : 'blue'
                      }
                      dot={
                        history.type === 'import' ? (
                          <PlusOutlined />
                        ) : history.type === 'export' ? (
                          <MinusOutlined />
                        ) : (
                          <EditOutlined />
                        )
                      }
                    >
                      <div className={styles.historyItem}>
                        <div className={styles.historyHeader}>
                          <span className={styles.historyDate}>{history.date}</span>
                          <span className={styles.historyUser}>bởi {history.user}</span>
                        </div>
                        <div className={styles.historyContent}>
                          <span className={styles.historyQuantity}>
                            {history.quantity > 0 ? '+' : ''}
                            {history.quantity}
                          </span>
                          <span className={styles.historyReason}>{history.reason}</span>
                        </div>
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            ),
          },
        ]}
      />
    </div>
  )

  return (
    <div className={styles.container}>
      {/* Header */}
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerTitle}>
          <h1>Quản lý tồn kho</h1>
          {!isMobile && <p>Theo dõi và quản lý tồn kho sản phẩm</p>}
        </div>
        <div className={styles.headerActions}>
          <Badge count={outOfStockProducts} showZero>
            <Button icon={<BellOutlined />} className={styles.alertButton}>
              Cảnh báo
            </Button>
          </Badge>
          <Button
            icon={<ImportOutlined />}
            className={styles.actionBtn} // ✅ Sửa từ actionButton
          >
            Nhập Excel
          </Button>
          <Button
            icon={<ExportOutlined />}
            className={styles.actionBtn} // ✅ Sửa từ actionButton
          >
            Xuất Excel
          </Button>
        </div>
      </div>

      {/* Search Bar - Mobile */}
      {isMobile && (
        <div className={styles.mobileSearchContainer}>
          <Input
            placeholder="Tìm sản phẩm, SKU..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className={styles.mobileSearchInput}
          />
          <Button
            variant="default"
            icon={<FilterOutlined />}
            onClick={() => setFilterDrawerVisible(true)}
          >
            Lọc
          </Button>
        </div>
      )}

      {/* Alert for low stock */}
      {(lowStockProducts > 0 || outOfStockProducts > 0) && (
        <Alert
          message={`Có ${outOfStockProducts} sản phẩm hết hàng và ${lowStockProducts} sản phẩm sắp hết hàng`}
          type="warning"
          showIcon
          className={styles.stockAlert}
          action={
            <Button size="small" danger>
              Xem chi tiết
            </Button>
          }
        />
      )}

      {/* Stats - Desktop */}
      {!isMobile && (
        <Row gutter={16} className={styles.statsRow}>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Tổng sản phẩm"
                value={totalProducts}
                prefix={<InboxOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Sản phẩm hết hàng"
                value={outOfStockProducts}
                prefix={<CloseCircleOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Sản phẩm sắp hết"
                value={lowStockProducts}
                prefix={<WarningOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className={styles.statCard}>
              <Statistic
                title="Giá trị tồn kho"
                value={totalStockValue}
                prefix={<DollarOutlined />}
                valueStyle={{ color: '#1890ff' }}
                formatter={(value) => `${value?.toLocaleString()}₫`}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* Stats - Mobile */}
      {isMobile && renderMobileStats()}

      {/* Filters & View Toggle */}
      <Card className={styles.filterCard}>
        <div className={styles.filterHeader}>
          <div className={styles.filterLeft}>
            {!isMobile && (
              <Input
                placeholder="Tìm sản phẩm, SKU..."
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                className={styles.searchInput}
                style={{ width: 280 }}
              />
            )}
          </div>
          <div className={styles.filterRight}>
            <Radio.Group
              value={viewMode}
              onChange={(e) => setViewMode(e.target.value)}
              buttonStyle="solid"
              className={styles.viewToggle}
            >
              <Radio.Button value="table">
                <UnorderedListOutlined /> Bảng
              </Radio.Button>
              <Radio.Button value="card">
                <AppstoreOutlined /> Thẻ
              </Radio.Button>
            </Radio.Group>
          </div>
        </div>

        {!isMobile && (
          <Row gutter={16} className={styles.filterRow}>
            <Col xs={24} sm={12} md={6}>
              <Select
                placeholder="Danh mục"
                value={filters.category}
                onChange={(value) => setFilters({ ...filters, category: value })}
                className={styles.filterSelect}
                allowClear
              >
                <Option value="Gạo">Gạo</Option>
                <Option value="Rau củ">Rau củ</Option>
                <Option value="Thịt">Thịt</Option>
                <Option value="Thủy sản">Thủy sản</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Select
                placeholder="Trạng thái tồn kho"
                value={filters.stockStatus}
                onChange={(value) => setFilters({ ...filters, stockStatus: value })}
                className={styles.filterSelect}
                allowClear
              >
                <Option value="sufficient">Đủ</Option>
                <Option value="low">Sắp hết</Option>
                <Option value="out_of_stock">Hết hàng</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Select
                placeholder="Kho"
                value={filters.warehouse}
                onChange={(value) => setFilters({ ...filters, warehouse: value })}
                className={styles.filterSelect}
                allowClear
              >
                <Option value="Kho chính">Kho chính</Option>
                <Option value="Kho lạnh">Kho lạnh</Option>
                <Option value="Kho phụ">Kho phụ</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Select
                placeholder="Trạng thái sản phẩm"
                value={filters.productStatus}
                onChange={(value) => setFilters({ ...filters, productStatus: value })}
                className={styles.filterSelect}
                allowClear
              >
                <Option value="active">Đang bán</Option>
                <Option value="inactive">Tạm ẩn</Option>
                <Option value="blocked">Bị khóa</Option>
              </Select>
            </Col>
          </Row>
        )}
      </Card>

      {/* Bulk Actions */}
      {selectedRowKeys.length > 0 && (
        <Card className={styles.bulkCard}>
          <div className={styles.bulkActions}>
            <span>Đã chọn {selectedRowKeys.length} sản phẩm</span>
            <Space>
              {bulkActions.map((action) => (
                <Button
                  key={action.key}
                  icon={action.icon}
                  onClick={() => handleBulkAction(action.key)}
                  size={isMobile ? 'small' : 'middle'}
                >
                  {action.label}
                </Button>
              ))}
            </Space>
          </div>
        </Card>
      )}

      {/* Inventory Table */}
      <Card className={styles.tableCard}>
        <Table
          columns={isMobile ? mobileColumns : desktopColumns}
          dataSource={filteredInventory}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            total: filteredInventory.length,
            pageSize: isMobile ? 5 : 10,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            showTotal: (total, range) =>
              isMobile
                ? `${range[0]}-${range[1]}/${total}`
                : `${range[0]}-${range[1]} của ${total} sản phẩm`,
            size: isMobile ? 'small' : 'default',
          }}
          className={styles.inventoryTable}
          scroll={isMobile ? { x: 'max-content' } : undefined}
          rowClassName={(record) => {
            if (record.stockStatus === 'out_of_stock') return styles.outOfStockRow
            if (record.stockStatus === 'low') return styles.lowStockRow
            return ''
          }}
        />
      </Card>

      {/* Filter Drawer - Mobile */}
      <Drawer
        title="Bộ lọc"
        placement="bottom"
        onClose={() => setFilterDrawerVisible(false)}
        open={filterDrawerVisible}
        height="auto"
        className={styles.filterDrawer}
      >
        <div className={styles.filtersContainer}>
          <div className={styles.mobileFilterItem}>
            <label>Danh mục:</label>
            <Select
              placeholder="Chọn danh mục"
              value={filters.category}
              onChange={(value) => setFilters({ ...filters, category: value })}
              className={styles.filterSelect}
              allowClear
            >
              <Option value="Gạo">Gạo</Option>
              <Option value="Rau củ">Rau củ</Option>
              <Option value="Thịt">Thịt</Option>
              <Option value="Thủy sản">Thủy sản</Option>
            </Select>
          </div>
          <div className={styles.mobileFilterItem}>
            <label>Trạng thái tồn kho:</label>
            <Select
              placeholder="Chọn trạng thái"
              value={filters.stockStatus}
              onChange={(value) => setFilters({ ...filters, stockStatus: value })}
              className={styles.filterSelect}
              allowClear
            >
              <Option value="sufficient">Đủ</Option>
              <Option value="low">Sắp hết</Option>
              <Option value="out_of_stock">Hết hàng</Option>
            </Select>
          </div>
          <div className={styles.mobileFilterItem}>
            <label>Kho:</label>
            <Select
              placeholder="Chọn kho"
              value={filters.warehouse}
              onChange={(value) => setFilters({ ...filters, warehouse: value })}
              className={styles.filterSelect}
              allowClear
            >
              <Option value="Kho chính">Kho chính</Option>
              <Option value="Kho lạnh">Kho lạnh</Option>
              <Option value="Kho phụ">Kho phụ</Option>
            </Select>
          </div>
        </div>
        <div className={styles.drawerActions}>
          <Button onClick={() => setFilterDrawerVisible(false)}>Đóng</Button>
          <Button
            type="primary"
            onClick={() => {
              setFilterDrawerVisible(false)
              message.success('Áp dụng bộ lọc')
            }}
          >
            Áp dụng
          </Button>
        </div>
      </Drawer>

      {/* Item Detail Modal */}
      <Modal
        title="Chi tiết tồn kho"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={isMobile ? '100%' : 1200}
        style={isMobile ? { top: 0, paddingBottom: 0 } : undefined}
        className={styles.itemDetailModal}
      >
        {selectedItem && renderItemDetail()}

        <ModalButtonGroup
          onCancel={() => setDetailVisible(false)}
          cancelText="Đóng"
          showConfirm={false}
        />
      </Modal>

      {/* Update Stock Modal */}
      <Modal
        title="Cập nhật tồn kho"
        open={updateModalVisible}
        onCancel={() => setUpdateModalVisible(false)}
        footer={null}
        width={isMobile ? '100%' : 800}
        style={isMobile ? { top: 0, paddingBottom: 0 } : undefined}
        className={styles.updateModal}
      >
        <Form
          form={form}
          onFinish={handleUpdateStock}
          layout="vertical"
          className={styles.updateForm}
        >
          <Alert
            message="Cập nhật tồn kho"
            description="Thay đổi số lượng tồn kho và ngưỡng cảnh báo cho từng biến thể sản phẩm"
            type="info"
            showIcon
            className={styles.updateAlert}
          />

          <Form.List name="variants">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card key={key} className={styles.variantUpdateCard}>
                    <Row gutter={16}>
                      <Col xs={24} sm={12}>
                        <Form.Item
                          {...restField}
                          name={[name, 'stock']}
                          label="Số lượng tồn kho"
                          rules={[{ required: true, message: 'Vui lòng nhập số lượng!' }]}
                        >
                          <InputNumber
                            min={0}
                            placeholder="Nhập số lượng"
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      <Col xs={24} sm={12}>
                        <Form.Item
                          {...restField}
                          name={[name, 'minStock']}
                          label="Ngưỡng cảnh báo"
                          rules={[{ required: true, message: 'Vui lòng nhập ngưỡng cảnh báo!' }]}
                        >
                          <InputNumber
                            min={0}
                            placeholder="Nhập ngưỡng cảnh báo"
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row gutter={16}>
                      <Col xs={24} sm={12}>
                        <Form.Item {...restField} name={[name, 'batchNumber']} label="Số lô">
                          <Input placeholder="Nhập số lô" />
                        </Form.Item>
                      </Col>
                      <Col xs={24} sm={12}>
                        <Form.Item {...restField} name={[name, 'expiryDate']} label="Hạn sử dụng">
                          <DatePicker placeholder="Chọn hạn sử dụng" style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                ))}
              </>
            )}
          </Form.List>

          <Form.Item>
            <ModalButtonGroup
              onCancel={() => setUpdateModalVisible(false)}
              onConfirm={() => form.submit()}
              cancelText="Hủy bỏ"
              confirmText="Cập nhật tồn kho"
              confirmVariant="primary"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default InventoryManagement
