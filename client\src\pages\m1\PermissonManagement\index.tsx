// Table hiển thị danh sách role sử dụng antd Table
import { Table } from 'antd'

interface RoleTableProps {
  loading: boolean
  error: any
  data: Array<{ id: string; role_code: string; name: string; description?: string }>
  columns: any[]
}

const RoleTable: React.FC<RoleTableProps> = ({ loading, error, data, columns }) => {
  if (loading) return <div>Đang tải danh sách role...</div>
  if (error) return <div style={{ color: 'red' }}>Lỗi tải roles</div>
  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      pagination={false}
      size="small"
      bordered
    />
  )
}
import React, { useState } from 'react'
import { Card, Tabs, Button, message, Form } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import RoleListModal from './RoleListModal'
import CreateRoleModal from './CreateRoleModal'
import { useGetAllRoleHook } from '@/hooks/user'
// import CreatePermissionModal from './CreatePermissionModal'; // Placeholder for future
// import PermissionList from './PermissionList'; // Placeholder for future

// Fetch roles from API

const roleColumns = [
  { title: 'Mã Role', dataIndex: 'role_code', key: 'role_code' },
  { title: 'Tên Role', dataIndex: 'name', key: 'name' },
  { title: 'Mô tả', dataIndex: 'description', key: 'description' },
]

const PermissionManagement = () => {
  const [activeTab, setActiveTab] = useState('role')
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false)
  const [isCreateRoleModalOpen, setIsCreateRoleModalOpen] = useState(false)
  const [roleForm] = Form.useForm()

  // Dummy permission data and modal state (for future)
  // const [isCreatePermissionModalOpen, setIsCreatePermissionModalOpen] = useState(false);

  // Fetch roles
  const {
    data: allRolesData,
    isLoading: rolesLoading,
    error: rolesError,
    refetch: refetchRoles,
  } = useGetAllRoleHook()
  const allRoles: Array<{ id: string; role_code: string; name: string; description?: string }> =
    allRolesData || []

  // TODO: Replace with real permission check
  //   const isAdmin = true // Only admin can access
  //   if (!isAdmin) {
  //     return <div>Bạn không có quyền truy cập trang này.</div>
  //   }

  return (
    <div style={{ padding: 24 }}>
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'role',
              label: 'Roles',
              children: (
                <>
                  <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 12 }}>
                    <Button
                      type="primary"
                      size="small"
                      className=""
                      icon={<PlusOutlined />}
                      onClick={() => setIsCreateRoleModalOpen(true)}
                    >
                      Thêm Role
                    </Button>
                  </div>
                  {/* Role List Table/Modal */}
                  <RoleListModal
                    open={isRoleModalOpen}
                    roles={allRoles}
                    columns={roleColumns}
                    onCancel={() => setIsRoleModalOpen(false)}
                  />
                  <CreateRoleModal
                    open={isCreateRoleModalOpen}
                    form={roleForm}
                    onCancel={() => setIsCreateRoleModalOpen(false)}
                    onSubmit={() => {
                      message.success('Tạo role thành công (demo)')
                      setIsCreateRoleModalOpen(false)
                      refetchRoles()
                    }}
                  />
                  {/* Hiển thị bảng hoặc danh sách role bằng Table antd */}
                  <div style={{ marginTop: 16 }}>
                    <Card bordered={false} bodyStyle={{ padding: 0 }}>
                      <RoleTable
                        loading={rolesLoading}
                        error={rolesError}
                        data={allRoles}
                        columns={roleColumns}
                      />
                    </Card>
                  </div>
                </>
              ),
            },
            {
              key: 'permission',
              label: 'Permissions',
              children: (
                <>
                  <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 12 }}>
                    <Button
                      type="primary"
                      size="small"
                      icon={<PlusOutlined />}
                      // onClick={() => setIsCreatePermissionModalOpen(true)}
                    >
                      Thêm Permission
                    </Button>
                  </div>
                  {/* Permission List/Modal sẽ đặt ở đây */}
                  <div style={{ marginTop: 16 }}>
                    <i>Chức năng quản lý permission sẽ bổ sung sau.</i>
                  </div>
                </>
              ),
            },
          ]}
        />
      </Card>
    </div>
  )
}

// Route config (ví dụ cho react-router v6)
// import { Navigate } from 'react-router-dom';
// const routes = [
//   {
//     path: '/permission-management',
//     element: <PermissionManagement />,
//     // Thêm điều kiện quyền ở đây nếu dùng react-router
//     // loader: () => isAdmin ? null : redirect('/'),
//   },
// ];

export default PermissionManagement
