import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { UserProfile } from '../entities/user_profile.entity';
import { OtpVerification } from '../entities/otp_verification.entity';
import { UserQR } from '../entities/user_qr.entity';
import { CloudinaryService } from 'src/cloudinary/cloudinary.service';
import { Role } from '../entities/role.entity';
import { UpdateUserByAdminDto } from '../dto/update-user-by-admin.dto';
import { ILike } from 'typeorm';
import { ClientProxy } from '@nestjs/microservices';
import { Inject } from '@nestjs/common';
import { LogActionType } from 'src/constants';
import { LogChange } from 'src/common/types/log-change.type';
import { omit } from 'lodash';

@Injectable()
export class UserManagementService {
  constructor(
    @InjectRepository(User) private readonly userRepo: Repository<User>,
    @InjectRepository(UserQR) private readonly qrRepo: Repository<UserQR>,
    @InjectRepository(Role) private readonly roleRepo: Repository<Role>,
    @InjectRepository(UserProfile) private readonly profileRepo: Repository<UserProfile>,
    @InjectRepository(OtpVerification) private readonly otpRepo: Repository<OtpVerification>,
    private readonly cloudinaryService: CloudinaryService,
    @Inject('LOG_SERVICE')
    private readonly logClient: ClientProxy,
  ) {}

  // CRUD user
  /**
   * Lấy danh sách người dùng với các điều kiện lọc
   * Hỗ trợ lọc theo roleCode, status, keyword (số điện thoại/email), phân trang
   */
  async getUserList(query: any) {
    const { roleCode, status, keyword, page = 1, limit = 20 } = query;

    // Tạo query builder để hỗ trợ tìm kiếm phức tạp
    const queryBuilder = this.userRepo
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect('user.profile', 'profile');

    // Thêm điều kiện lọc
    if (roleCode) {
      queryBuilder.andWhere('role.role_code = :roleCode', { roleCode });
    }
    
    if (status) {
      queryBuilder.andWhere('user.status = :status', { status });
    }
    
    if (keyword) {
      queryBuilder.andWhere(
        '(user.phone ILIKE :keyword OR user.email ILIKE :keyword OR profile.fullName ILIKE :keyword OR user.uidCode ILIKE :keyword)',
        { keyword: `%${keyword}%` }
      );
    }

    // Thêm phân trang và sắp xếp
    queryBuilder
      .orderBy('user.createdAt', 'DESC')
      .skip((+page - 1) * +limit)
      .take(+limit);

    const [items, total] = await queryBuilder.getManyAndCount();

    // Xoá password khỏi kết quả
    const sanitizedItems = items.map((user) => {
      const { password, profile, ...rest } = user;
      return {
        fullName: user.profile.fullName,
        ...rest,
      };
    });

    return {
      data: sanitizedItems,
      total,
      page: +page,
      limit: +limit,
    };
  }

  /**
   * Lấy chi tiết 1 người dùng theo ID
   */
  async getUserById(id: string) {
    const user = await this.userRepo.findOne({
      where: { id },
      relations: ['role', 'profile'],
    });

    if (!user) throw new NotFoundException('Không tìm thấy người dùng');

    return {
      id: user.id,
      uidCode: user.uidCode,
      phone: user.phone,
      email: user.email,
      status: user.status,
      role_id: user.role_id,
      fullName: user.profile.fullName,
      phoneNumber: user.profile.phoneNumber,
      citizenId: user.profile.citizenId,
      gender: user.profile.gender,
      dateOfBirth: user.profile.dateOfBirth,
      avatarUrl: user.profile.avatarUrl,
      address: user.profile.address,
      provinceCode: user.profile.provinceCode,
      communeCode: user.profile.communeCode,
      bio: user.profile.bio,
      facebookUrl: user.profile.facebookUrl,
      zaloNumber: user.profile.zaloNumber,
    };
  }

  /**
   * Admin cập nhật thông tin người dùng
   */
  async updateUserByAdmin(id: string, dto: UpdateUserByAdminDto) {
    const user = await this.userRepo.findOne({
      where: { id },
      relations: ['profile'],
    });

    if (!user) {
      throw new NotFoundException('Người dùng không tồn tại');
    }

    const draft: LogChange<any> = {
      before: omit(user, ['password']),
    };

    Object.assign(user, dto);
    await this.userRepo.save(user);

    draft.after = omit(user, ['password']);

    this.logClient.emit('log.update', {
      module: 'User',
      action: LogActionType.UPDATE,
      performedBy: '6509682d-1f89-4260-bd05-b0e90ff1feab',
      targetId: user.id,
      description: `Admin đã cập nhật tài khoản người dùng: ${user.profile?.fullName || user.email}`,
      timestamp: new Date().toISOString(),
      data: draft,
    });
  }

  /**
   * Admin xoá logic người dùng (chuyển trạng thái thành locked)
   */
  async softDeleteUser(id: string) {
    const user = await this.userRepo.findOneBy({ id });

    if (!user) {
      throw new NotFoundException('Người dùng không tồn tại');
    }

    user.status = 'locked';

    return await this.userRepo.save(user);
  }
}
