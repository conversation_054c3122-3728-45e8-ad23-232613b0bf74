import ProductCard from './ProductCard';
import styles from './ProductGrid.module.scss';

export default function ProductGrid() {
  // Dummy data - thay bằng API thực tế
  const products = [
    { id: 1, name: 'Product 1', price: 100, image: '/src/assets/images/products/product1.jpg' },
    // Thêm các sản phẩm khác
  ];

  return (
    <div className={styles.productGrid}>
      <h2>Discover unique hand-picked items</h2>
      <div className={styles.grid}>
        {products.map(product => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
    </div>
  );
}