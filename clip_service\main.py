from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from PIL import Image
import torch
import open_clip
import io

app = FastAPI()

# Load CLIP model (ViT-B/32, pretrained)
device = "cuda" if torch.cuda.is_available() else "cpu"
model, _, preprocess = open_clip.create_model_and_transforms('ViT-B-32', pretrained='openai')
model = model.to(device)
model.eval()

@app.post("/vectorize")
async def vectorize_image(file: UploadFile = File(...)):
    try:
        image_bytes = await file.read()
        image = Image.open(io.BytesIO(image_bytes)).convert("RGB")
        image_input = preprocess(image).unsqueeze(0).to(device)
        with torch.no_grad():
            image_features = model.encode_image(image_input)
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            vector = image_features.cpu().numpy().tolist()[0]
        return {"vector": vector}
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.get("/")
def root():
    return {"message": "CLIP Vectorization Service Ready"}
    
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8001)
