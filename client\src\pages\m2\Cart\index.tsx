import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styles from './Cart.module.scss';
import {
  useGetCartHook,
  useUpdateCartQuantityHook,
  useDeleteCartItemHook,
  useClear<PERSON>artHook,
  useCheckInventoryHook,
} from '@/hooks/cart/useCart';
import CartItemDetail from './CartItemDetail';
import { Popconfirm, Button } from 'antd';
import { FileTextOutlined } from '@ant-design/icons';
import { useToast } from '@/components/Toast';

// Interface cho Mã giảm giá
interface PromoCode {
  code: string;
  discount: number;
  type: 'percentage' | 'fixed';
  description: string;
}

const Cart: React.FC = () => {
  const [displayItems, setDisplayItems] = useState<any[]>([]);
  const [isInventorySufficient, setIsInventorySufficient] = useState(true);
  const [selectAll, setSelectAll] = useState(false);

  // States cho mã giảm giá và vận chuyển
  const [promoCode, setPromoCode] = useState('');
  const [appliedPromo, setAppliedPromo] = useState<PromoCode | null>(null);
  const [shippingFee] = useState(25000);
  const [selectedAddress, setSelectedAddress] = useState('default');
  const [availablePromoCodes] = useState<PromoCode[]>([
    { code: 'TAPGREEN10', discount: 10, type: 'percentage', description: 'Giảm 10% cho đơn hàng từ 200k' },
    { code: 'FREESHIP', discount: 25000, type: 'fixed', description: 'Miễn phí vận chuyển' },
    { code: 'NEWMEMBER', discount: 50000, type: 'fixed', description: 'Ưu đãi thành viên mới' }
  ]);

  // Khởi tạo các hooks
  const { data: cartData, isLoading, error, refetch } = useGetCartHook();
  const updateQuantityApi = useUpdateCartQuantityHook();
  const deleteItemApi = useDeleteCartItemHook();
  const clearCart = useClearCartHook();
  const checkInventory = useCheckInventoryHook();
  const navigate = useNavigate();
  const { showError } = useToast();

  // Effect để cập nhật danh sách sản phẩm hiển thị khi dữ liệu từ API thay đổi
  useEffect(() => {
    const itemsFromApi = (cartData && (Array.isArray(cartData) ? cartData : cartData.data)) || [];
    const sortedItems = [...itemsFromApi].sort((a, b) => {
      if (a.id && b.id) return a.id.localeCompare(b.id);
      return 0;
    });
    const initialItems = sortedItems.map(item => ({
      ...item,
      selected: item.is_checked !== undefined ? item.is_checked : true,
    }));
    setDisplayItems(initialItems);
    
    // Cập nhật selectAll state
    const allSelected = initialItems.length > 0 && initialItems.every(item => item.selected);
    setSelectAll(allSelected);
  }, [cartData]);

  // Effect để kiểm tra tồn kho mỗi khi giỏ hàng thay đổi
  useEffect(() => {
    const verifyInventory = async () => {
      if (displayItems.length > 0) {
        const result = await checkInventory();
        setIsInventorySufficient(result);
      } else {
        setIsInventorySufficient(true);
      }
    };
    verifyInventory();
  }, [displayItems, checkInventory]);

  // Effect để cập nhật selectAll state khi individual items thay đổi
  useEffect(() => {
    if (displayItems.length > 0) {
      const allSelected = displayItems.every(item => item.selected);
      setSelectAll(allSelected);
    } else {
      setSelectAll(false);
    }
  }, [displayItems]);

  // Các hàm xử lý giỏ hàng
  const updateQuantity = async (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    const success = await updateQuantityApi({ cart_item_id: itemId, quantity: newQuantity });
    if (success) refetch();
  };

  const removeItem = async (itemId: string) => {
    const success = await deleteItemApi({ cart_item_id: itemId });
    if (success) refetch();
  };

  const handleClearCart = async () => {
    const success = await clearCart();
    if (success) refetch();
  };

  const toggleItemSelection = (itemId: string) => {
    setDisplayItems(currentItems =>
      currentItems.map(item =>
        item.id === itemId ? { ...item, selected: !item.selected } : item
      )
    );
  };

  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    setDisplayItems(currentItems =>
      currentItems.map(item => ({ ...item, selected: newSelectAll }))
    );
  };

  const removeSelectedItems = async () => {
    const selectedItemIds = displayItems.filter(item => item.selected).map(item => item.id);
    if (selectedItemIds.length === 0) {
      showError('Bạn chưa chọn sản phẩm nào để xóa.');
      return;
    }
    
    // Xóa từng item đã chọn
    for (const itemId of selectedItemIds) {
      await deleteItemApi({ cart_item_id: itemId });
    }
    
    // Refresh lại giỏ hàng
    refetch();
    setSelectAll(false);
  };

  const applyPromoCode = () => {
    const promo = availablePromoCodes.find(p => p.code === promoCode.toUpperCase());
    if (promo) {
      setAppliedPromo(promo);
      setPromoCode('');
    }
  };

  const removePromoCode = () => setAppliedPromo(null);

  const calculateTotals = () => {
    const selectedItems = displayItems.filter(item => item.selected);
    const subtotal = selectedItems.reduce((total, item) => total + (parseFloat(item.unit_price) * item.quantity), 0);
    let discount = 0;
    if (appliedPromo) {
      discount = appliedPromo.type === 'percentage'
        ? subtotal * (appliedPromo.discount / 100)
        : appliedPromo.discount;
    }
    const shipping = selectedItems.length > 0 ? shippingFee : 0;
    const total = subtotal - discount + shipping;
    return { subtotal, discount, shipping, total, selectedCount: selectedItems.length, selectedItems };
  };

  const { subtotal, discount, shipping, total, selectedCount, selectedItems } = calculateTotals();

  const handleProceedToCheckout = () => {
    if (!isInventorySufficient) {
      showError('Một hoặc nhiều sản phẩm không đủ số lượng. Vui lòng kiểm tra lại!');
      return;
    }
    if (selectedCount === 0) {
      showError('Bạn chưa chọn sản phẩm nào để thanh toán.');
      return;
    }
    
    const orderItems = selectedItems.map(item => ({
      product_id: item.product_id,
      version_id: item.version_id,
      quantity: item.quantity,
      unit_price: parseFloat(item.unit_price),
      name: item.product?.name,
      thumbnail_url: item.product?.thumbnail_url,
    }));

    // Lưu vào localStorage để hỗ trợ F5 ở trang thanh toán
    localStorage.setItem('checkoutData', JSON.stringify({
      items: orderItems,
      totals: { subtotal, discount, shipping, total }
    }));

    navigate('/gio-hang/chi-tiet-don-hang', {
      state: {
        items: orderItems,
        totals: { subtotal, discount, shipping, total }
      }
    });
  };

  if (isLoading) {
    return <div className={styles.loading}>Đang tải giỏ hàng...</div>;
  }

  if (error) {
    return <div className={styles.error}>Lỗi tải giỏ hàng. Vui lòng thử lại.</div>;
  }

  return (
    <div className={styles.cartContainer}>
      <div className={styles.cartHeader}>
        <div className={styles.headerLeft}>
          <h1>Giỏ hàng của tôi</h1>
          <Link to="/gio-hang/don-hang-cua-toi" className={styles.myOrdersLink}>
            <FileTextOutlined /> Đơn hàng của tôi
          </Link>
        </div>
        <div className={styles.headerRight}>
          <Link to="/mua-sam" className={styles.backToShopping}>
            ← Tiếp tục mua sắm
          </Link>
        </div>
      </div>

      <div className={styles.notifications}>
        {displayItems.length > 0 && (
          <div className={`${styles.notification} ${styles.success}`}>
            <span>Bạn có {displayItems.length} sản phẩm trong giỏ</span>
          </div>
        )}
        {!isInventorySufficient && (
          <div className={`${styles.notification} ${styles.warning}`}>
            <span>⚠️ Một hoặc nhiều sản phẩm không đủ số lượng. Vui lòng kiểm tra lại giỏ hàng.</span>
          </div>
        )}
      </div>

      <div className={styles.cartContent}>
        <div className={styles.cartLeft}>
          {displayItems.length > 0 && (
            <div className={styles.cartActions}>
              <div className={styles.selectAllSection}>
                <label className={styles.selectAllLabel}>
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={toggleSelectAll}
                  />
                  <span>Chọn tất cả ({displayItems.length} sản phẩm)</span>
                </label>
              </div>
              <div className={styles.bulkActions}>
                <Popconfirm
                  title="Xóa sản phẩm đã chọn"
                  description={`Bạn có chắc chắn muốn xóa ${displayItems.filter(item => item.selected).length} sản phẩm đã chọn?`}
                  onConfirm={removeSelectedItems}
                  okText="Xóa"
                  cancelText="Hủy"
                  placement="topRight"
                >
                  <Button danger disabled={displayItems.filter(item => item.selected).length === 0}>
                    Xóa đã chọn ({displayItems.filter(item => item.selected).length})
                  </Button>
                </Popconfirm>
              </div>
            </div>
          )}
          
          {displayItems.map(item => (
            <CartItemDetail
              key={item.id}
              item={item}
              onUpdateQuantity={updateQuantity}
              onRemoveItem={removeItem}
              onSelectItem={toggleItemSelection}
            />
          ))}
          
          {displayItems.length > 0 && (
            <div className={styles.cartListFooter}>
              <Popconfirm
                title="Xóa toàn bộ giỏ hàng"
                description="Bạn có chắc chắn muốn xóa tất cả sản phẩm?"
                onConfirm={handleClearCart}
                okText="Xóa tất cả"
                cancelText="Hủy"
                placement="topRight"
              >
                <Button danger>Xóa tất cả sản phẩm</Button>
              </Popconfirm>
            </div>
          )}

          <div className={styles.promoSection}>
            <h3>🎁 Mã giảm giá</h3>
            <div className={styles.promoInput}>
              <input
                type="text"
                placeholder="Nhập mã giảm giá..."
                value={promoCode}
                onChange={(e) => setPromoCode(e.target.value)}
              />
              <button onClick={applyPromoCode}>Áp dụng</button>
            </div>
            
            {appliedPromo && (
              <div className={styles.appliedPromo}>
                <span>✅ {appliedPromo.code} - {appliedPromo.description}</span>
                <button onClick={removePromoCode}>✕</button>
              </div>
            )}
            
            <div className={styles.availablePromoCodes}>
              <h4>Mã khuyến mãi có sẵn:</h4>
              {availablePromoCodes.map((promo, index) => (
                <div key={index} className={styles.promoCode}>
                  <span className={styles.promoCodeText}>{promo.code}</span>
                  <span className={styles.promoDescription}>{promo.description}</span>
                  <button onClick={() => {
                    setPromoCode(promo.code);
                    applyPromoCode();
                  }}>Áp dụng</button>
                </div>
              ))}
            </div>
          </div>

          <div className={styles.shippingSection}>
            <h3>🚚 Phương thức vận chuyển</h3>
            <div className={styles.shippingOptions}>
              <label>
                <input
                  type="radio"
                  name="shipping"
                  value="standard"
                  checked={selectedAddress === 'standard'}
                  onChange={(e) => setSelectedAddress(e.target.value)}
                />
                <span>Giao hàng tiêu chuẩn</span>
                <span className={styles.shippingFee}>25.000đ</span>
              </label>
              <label>
                <input
                  type="radio"
                  name="shipping"
                  value="express"
                  checked={selectedAddress === 'express'}
                  onChange={(e) => setSelectedAddress(e.target.value)}
                />
                <span>Giao hàng nhanh</span>
                <span className={styles.shippingFee}>45.000đ</span>
              </label>
              <label>
                <input
                  type="radio"
                  name="shipping"
                  value="premium"
                  checked={selectedAddress === 'premium'}
                  onChange={(e) => setSelectedAddress(e.target.value)}
                />
                <span>Giao hàng cao cấp</span>
                <span className={styles.shippingFee}>65.000đ</span>
              </label>
            </div>
            
            <div className={styles.shippingInfo}>
              <p>📦 Dự kiến giao hàng: 2-5 ngày làm việc</p>
              <p>📍 Giao hàng toàn quốc</p>
              <p>💰 Miễn phí vận chuyển cho đơn hàng từ 500.000đ</p>
            </div>
          </div>
        </div>

        <div className={styles.cartRight}>
          <div className={styles.orderSummary}>
            <h3>Tóm tắt đơn hàng</h3>
            <div className={styles.summaryRow}>
              <span>Tổng tiền hàng ({selectedCount} sản phẩm)</span>
              <span>{subtotal.toLocaleString()}đ</span>
            </div>
            {discount > 0 && (
              <div className={`${styles.summaryRow} ${styles.discount}`}>
                <span>Giảm giá</span>
                <span>-{discount.toLocaleString()}đ</span>
              </div>
            )}
            <div className={styles.summaryRow}>
              <span>Phí vận chuyển</span>
              <span>{shipping.toLocaleString()}đ</span>
            </div>
            <div className={styles.summaryDivider}></div>
            <div className={`${styles.summaryRow} ${styles.total}`}>
              <span>Tổng cộng cần thanh toán</span>
              <span>{total.toLocaleString()}đ</span>
            </div>

            <button
              className={styles.checkoutButton}
              disabled={selectedCount === 0 || isLoading}
              onClick={handleProceedToCheckout}
            >
              Tiến hành đặt hàng ({selectedCount})
            </button>
            
            <div className={styles.continueShoppingActions}>
              <Link to="/mua-sam" className={styles.continueShoppingBtn}>Quay về mua tiếp</Link>
              <Link to="/promotions" className={styles.viewPromotionsBtn}>Xem ưu đãi khác</Link>
            </div>
          </div>
        </div>
      </div>
      {displayItems.length === 0 && !isLoading && (
        <div className={styles.emptyCart}>
          <div className={styles.emptyCartIcon}>🛒</div>
          <h2>Giỏ hàng của bạn đang trống</h2>
          <p>Hãy khám phá các sản phẩm tươi ngon từ các HTX địa phương</p>
          <Link to="/mua-sam" className={styles.startShoppingBtn}>Bắt đầu mua sắm</Link>
        </div>
      )}
    </div>
  );
};

export default Cart;
