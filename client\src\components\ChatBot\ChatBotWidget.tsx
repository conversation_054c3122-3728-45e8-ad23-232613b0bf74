import React, { useState } from 'react'
import Chatbot from 'react-chatbot-kit'
import 'react-chatbot-kit/build/main.css'
import { MessageOutlined } from '@ant-design/icons'
import './ChatBotWidget.css'
import { useGetChatBotHook } from '@/hooks/chatbot/chatbot'
import botAvatar from '@/assets/img/botAvatar.webp'
import { createChatBotMessage } from 'react-chatbot-kit'

let quickReplyCallback: ((text: string) => void) | null = null

const SuggestionButtons = (props: { suggestions?: string[]; lastMsg?: any }) => {
  // Ưu tiên suggestions từ props, sau đó đến lastMsg.widgetProps.suggestions
  const suggestions = props.suggestions || props.lastMsg?.widgetProps?.suggestions || []
  return (
    <div style={{ marginTop: 8, display: 'flex', gap: 8, flexWrap: 'wrap' }}>
      {suggestions.map((s: string, i: number) => (
        <button
          key={i}
          style={{
            background: '#e6f4ff',
            border: '1px solid #91d5ff',
            borderRadius: 16,
            padding: '4px 12px',
            cursor: 'pointer',
            fontSize: 14,
          }}
          onClick={() => quickReplyCallback && quickReplyCallback(s)}
        >
          {s}
        </button>
      ))}
    </div>
  )
}

const getConfig = (onQuickReply: (text: string) => void) => ({
  initialMessages: [createChatBotMessage('Xin chào! Tôi có thể giúp gì cho bạn?', {})],
  customComponents: {
    header: () => (
      <div className="d-flex align-items-center justify-content-right p-2">
        <img src={botAvatar} alt="Bot" style={{ width: 32, height: 32, borderRadius: '50%' }} />
        <p className="p-0 m-0 ms-2">
          <b>Hỗ trợ tự động - HTX TAP</b>
        </p>
      </div>
    ),
    botAvatar: () => (
      <img src={botAvatar} alt="Bot" style={{ width: 32, height: 32, borderRadius: '50%' }} />
    ),
    userAvatar: () => <></>,
  },
  customStyles: {},
  widgets: [
    {
      widgetName: 'suggestionButtons',
      widgetFunc: (props: any) => {
        quickReplyCallback = onQuickReply
        const lastMsg = props?.state?.messages?.[props.state.messages.length - 1]
        const suggestions =
          props.suggestions || (props.widgetProps && props.widgetProps.suggestions) || []
        return <SuggestionButtons suggestions={suggestions} lastMsg={lastMsg} />
      },
      props: {},
      mapStateToProps: [],
    },
  ],
})

class MessageParser {
  actionProvider: ActionProvider
  constructor(actionProvider: ActionProvider) {
    this.actionProvider = actionProvider
  }
  parse(message: string) {
    if (message) {
      this.actionProvider.handleDefault(message)
    }
  }
}

class ActionProvider {
  createChatBotMessage: (msg: string, options?: any) => any
  setState: (fn: (prev: any) => any) => void
  chat: (data: { text: string }) => Promise<any>
  constructor(
    createChatBotMessage: (msg: string, options?: any) => any,
    setStateFunc: (fn: (prev: any) => any) => void,
    chat: (data: { text: string }) => Promise<any>
  ) {
    this.createChatBotMessage = createChatBotMessage
    this.setState = setStateFunc
    this.chat = chat
  }
  async handleDefault(message: string) {
    const loadingMsg = this.createChatBotMessage('Đang xử lý...')
    this.setState((prev: any) => ({
      ...prev,
      messages: [...prev.messages, loadingMsg],
    }))
    try {
      const res = await this.chat({ text: message })
      const botMsg = this.createChatBotMessage(
        res?.reply || 'Xin lỗi, tôi chưa có câu trả lời phù hợp.',
        res?.suggestedReplies && res.suggestedReplies.length > 0
          ? {
              widget: 'suggestionButtons',
              widgetProps: {
                suggestions: res.suggestedReplies,
              },
            }
          : undefined
      )
      this.setState((prev: any) => ({
        ...prev,
        messages: prev.messages.slice(0, -1).concat(botMsg),
      }))
    } catch (e) {
      const botMsg = this.createChatBotMessage('Có lỗi khi kết nối chatbot.')
      this.setState((prev: any) => ({
        ...prev,
        messages: prev.messages.slice(0, -1).concat(botMsg),
      }))
    }
  }
}

const ChatBotWidget = () => {
  const [open, setOpen] = useState(false)
  const [greeted, setGreeted] = useState(false)
  const [key, setKey] = useState(0)

  const { chat } = useGetChatBotHook()

  const CustomActionProvider = function (createChatBotMessage: any, setStateFunc: any) {
    // @ts-ignore
    return new ActionProvider(createChatBotMessage, setStateFunc, chat)
  }

  const CustomMessageParser = function (actionProvider: any) {
    // @ts-ignore
    return new MessageParser(actionProvider)
  }

  const handleQuickReply = (text: string) => {
    if (quickReplyCallback && typeof quickReplyCallback === 'function') {
      const fn = quickReplyCallback
      quickReplyCallback = null // Ngăn lặp vô hạn
      fn(text)
    }
  }

  const handleToggle = () => {
    if (!open) {
      setKey((k) => k + 1)
      setGreeted(true)
    }
    setOpen((v) => !v)
  }

  return (
    <div className={`chatbot-popup-container${open ? ' open' : ''}`}>
      <div className="chatbot-toggle-btn" onClick={handleToggle}>
        <MessageOutlined style={{ fontSize: 28, color: '#fff' }} />
      </div>
      {open && (
        <div className="chatbot-popup-box">
          <Chatbot
            key={key}
            config={getConfig(handleQuickReply)}
            messageParser={CustomMessageParser}
            actionProvider={CustomActionProvider}
          />
        </div>
      )}
    </div>
  )
}

export default ChatBotWidget
