'use client'

import { ToastContainer, toast, Bounce } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import styles from '../Toast/Toast.module.scss'
import { createContext, useCallback, useContext, type ReactNode } from 'react'

type ToastContextType = {
  showSuccess: (message: string, description?: string) => void
  showError: (message: string, description?: string) => void
  showWarning: (message: string, description?: string) => void
  showInfo: (message: string, description?: string) => void
}

const ToastContext = createContext<ToastContextType>({
  showSuccess: () => {},
  showError: () => {},
  showWarning: () => {},
  showInfo: () => {},
})

export const ToastProvider = ({ children }: { children: ReactNode }) => {
  const showSuccess = useCallback((message: string, description?: string) => {
    toast.success(
      <div>
        <div>{message || 'Thành công!'}</div>
        {description && <p>{description}</p>}
      </div>
    )
  }, [])

  const showError = useCallback((message: string, description?: string) => {
    toast.error(
      <div>
        <div>{message || 'Thất bại!'}</div>
        {description && <p>{description}</p>}
      </div>
    )
  }, [])

  const showWarning = useCallback((message: string, description?: string) => {
    toast.warn(
      <div>
        <div>{message || 'Thông báo!'}</div>
        {description && <p>{description}</p>}
      </div>
    )
  }, [])

  const showInfo = useCallback((message: string, description?: string) => {
    toast.info(
      <div>
        <div>{message || 'Thông tin thêm!'}</div>
        {description && <p>{description}</p>}
      </div>
    )
  }, [])

  return (
    <div className={styles.toastContainer}>
      <ToastContext.Provider value={{ showSuccess, showError, showWarning, showInfo }}>
        {children}
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          
          transition={Bounce}
          progressClassName={styles.progressBar}
        />
      </ToastContext.Provider>
    </div>
  )
}

export const useToast = () => useContext(ToastContext)