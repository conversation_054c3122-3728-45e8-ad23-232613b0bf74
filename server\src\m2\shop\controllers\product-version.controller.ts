import { Body, Controller, Post, Put, Delete, Param, Get, UseGuards } from '@nestjs/common';
import { ProductVersionService } from '../services/product-version.service';
import { CreateProductVersionDto, UpdateProductVersionDto } from '../dto/product-version.dto';
import { ApiBearerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';

@ApiTags('Product Version')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
@Controller('products/:productId/versions')
export class ProductVersionController {
  constructor(private readonly versionService: ProductVersionService) {}

  @Post()
  @ApiOperation({ summary: 'Tạo version (SKU) mới cho sản phẩm' })
  async create(@Param('productId') productId: string, @Body() dto: CreateProductVersionDto) {
    return this.versionService.create(productId, dto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật version (SKU) cho sản phẩm' })
  async update(@Param('id') id: string, @Body() dto: UpdateProductVersionDto) {
    return this.versionService.update(id, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa version (SKU) khỏi sản phẩm' })
  async delete(@Param('id') id: string) {
    return this.versionService.delete(id);
  }

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách version (SKU) của sản phẩm' })
  async findByProduct(@Param('productId') productId: string) {
    return this.versionService.findByProduct(productId);
  }
}
