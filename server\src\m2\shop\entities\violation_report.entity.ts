import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../../m1/user/entities/user.entity';

@Entity('violation_reports')
export class ViolationReport {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  reporter_id: string;

  @Column({ type: 'varchar', length: 50 })
  target_type: string;

  @Column('uuid')
  target_id: string;

  @Column({ type: 'text' })
  reason: string;

  @Column({ type: 'jsonb', nullable: true })
  evidence?: any;

  @Column({ type: 'varchar', length: 50 })
  status_id: string;

  @Column('uuid', { nullable: true })
  resolved_by?: string;
  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'resolved_by' })
  resolvedBy?: User;

  @Column({ type: 'text', nullable: true })
  resolution_note?: string;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;
}
