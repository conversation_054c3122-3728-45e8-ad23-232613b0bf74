import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CartItem } from '../entities/cart_item.entity';
import { AddCartItemDto, UpdateCartItemDto } from '../dto/add-cart-item.dto';

interface CartInventoryCheckResult {
  cart_item_id: string;
  product_id: string;
  version_id?: string;
  quantity: number;
  available: number;
  enough: boolean;
}

@Injectable()
export class CartService {
  constructor(
    @InjectRepository(CartItem)
    private readonly cartItemRepository: Repository<CartItem>,
  ) {}

  getCart(userId: string) {
    return this.cartItemRepository.find({ where: { user_id: userId } });
  }

  async addItem(user_id: string, addCartItemDto: AddCartItemDto) {
    // Tìm item trùng product_id, version_id, selected_options
    // So sánh selected_options deep, version_id có thể undefined
    const allItems = await this.cartItemRepository.find({
      where: {
        user_id,
        product_id: addCartItemDto.product_id,
      },
    });
    const isSameOptions = (a: any, b: any) => {
      return JSON.stringify(a ?? null) === JSON.stringify(b ?? null);
    };
    const existing = allItems.find(
      (item) =>
        (item.version_id ?? undefined) === (addCartItemDto.version_id ?? undefined) &&
        isSameOptions(item.selected_options, addCartItemDto.selected_options),
    );

    // Tính tổng quantity nếu đã có item
    let totalQuantity = addCartItemDto.quantity;
    if (existing) {
      totalQuantity += existing.quantity;
    }

    // Kiểm tra tồn kho với fallback logic
    let available = 0;
    const invRepo = this.cartItemRepository.manager.getRepository('Inventory');
    
    if (addCartItemDto.version_id) {
      // Kiểm tra inventory cho version cụ thể
      const inv = await invRepo.findOne({ where: { version_id: addCartItemDto.version_id } });
      if (inv) {
        available = inv.stock_quantity - inv.reserved_quantity;
      } else {
        // Fallback: lấy stock từ product_version nếu không có inventory
        const versionRepo = this.cartItemRepository.manager.getRepository('ProductVersion');
        const version = await versionRepo.findOne({ where: { id: addCartItemDto.version_id } });
        available = version?.stock || 0;
      }
    } else {
      // Kiểm tra inventory cho sản phẩm (không có version)
      const inv = await invRepo.findOne({ where: { product_id: addCartItemDto.product_id, version_id: null } });
      if (inv) {
        available = inv.stock_quantity - inv.reserved_quantity;
      } else {
        // Fallback: lấy stock từ product nếu không có inventory
        const productRepo = this.cartItemRepository.manager.getRepository('Product');
        const product = await productRepo.findOne({ where: { id: addCartItemDto.product_id } });
        available = product?.stock || 0;
      }
    }

    // Kiểm tra đủ stock
    if (available < totalQuantity) {
      throw new BadRequestException('Không đủ tồn kho cho sản phẩm này');
    }

    if (existing) {
      existing.quantity = totalQuantity;
      return this.cartItemRepository.save(existing);
    } else {
      const item = this.cartItemRepository.create({
        ...addCartItemDto,
        user_id,
        unit_price: String(addCartItemDto.unit_price),
      });
      return this.cartItemRepository.save(item);
    }
  }

  async updateQuantity(user_id: string, cartItem: UpdateCartItemDto) {
    const item = await this.cartItemRepository.findOne({
      where: { id: cartItem.cart_item_id, user_id },
    });
    if (!item) throw new NotFoundException('Không tìm thấy item trong giỏ');
    
    // Kiểm tra tồn kho với fallback logic
    let available = 0;
    const invRepo = this.cartItemRepository.manager.getRepository('Inventory');
    
    if (item.version_id) {
      // Kiểm tra inventory cho version cụ thể
      const inv = await invRepo.findOne({ where: { version_id: item.version_id } });
      if (inv) {
        available = inv.stock_quantity - inv.reserved_quantity;
      } else {
        // Fallback: lấy stock từ product_version nếu không có inventory
        const versionRepo = this.cartItemRepository.manager.getRepository('ProductVersion');
        const version = await versionRepo.findOne({ where: { id: item.version_id } });
        available = version?.stock || 0;
      }
    } else {
      // Kiểm tra inventory cho sản phẩm (không có version)
      const inv = await invRepo.findOne({ where: { product_id: item.product_id, version_id: null } });
      if (inv) {
        available = inv.stock_quantity - inv.reserved_quantity;
      } else {
        // Fallback: lấy stock từ product nếu không có inventory
        const productRepo = this.cartItemRepository.manager.getRepository('Product');
        const product = await productRepo.findOne({ where: { id: item.product_id } });
        available = product?.stock || 0;
      }
    }

    // Kiểm tra đủ stock
    if (available < cartItem.quantity) {
      throw new BadRequestException('Không đủ tồn kho');
    }
    
    item.quantity = cartItem.quantity;
    return this.cartItemRepository.save(item);
  }

  async removeItem(user_id: string, cart_item_id: string) {
    const item = await this.cartItemRepository.findOne({ where: { id: cart_item_id, user_id } });
    if (!item) throw new NotFoundException('Không tìm thấy item trong giỏ');
    return this.cartItemRepository.remove(item);
  }

  async clearCart(user_id: string) {
    await this.cartItemRepository.delete({ user_id });
    return { success: true };
  }

  async checkInventory(user_id: string): Promise<CartInventoryCheckResult[]> {
    const items = await this.cartItemRepository.find({ where: { user_id } });
    const invRepo = this.cartItemRepository.manager.getRepository('Inventory');
    const result: CartInventoryCheckResult[] = [];
    
    for (const item of items) {
      let available = 0;
      
      if (item.version_id) {
        // Kiểm tra inventory cho version cụ thể
        const inv = await invRepo.findOne({ where: { version_id: item.version_id } });
        if (inv) {
          available = inv.stock_quantity - inv.reserved_quantity;
        } else {
          // Fallback: lấy stock từ product_version nếu không có inventory
          const versionRepo = this.cartItemRepository.manager.getRepository('ProductVersion');
          const version = await versionRepo.findOne({ where: { id: item.version_id } });
          available = version?.stock || 0;
        }
      } else {
        // Kiểm tra inventory cho sản phẩm (không có version)
        const inv = await invRepo.findOne({ where: { product_id: item.product_id, version_id: null } });
        if (inv) {
          available = inv.stock_quantity - inv.reserved_quantity;
        } else {
          // Fallback: lấy stock từ product nếu không có inventory
          const productRepo = this.cartItemRepository.manager.getRepository('Product');
          const product = await productRepo.findOne({ where: { id: item.product_id } });
          available = product?.stock || 0;
        }
      }
      
      result.push({
        cart_item_id: item.id,
        product_id: item.product_id,
        version_id: item.version_id,
        quantity: item.quantity,
        available,
        enough: item.quantity <= available,
      });
    }
    return result;
  }
}
