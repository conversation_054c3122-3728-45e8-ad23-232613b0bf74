import {
  useGetInventoryQuery,
  useGetInventoryDetailQuery,
  useUpdateVariantsMutation,
  useQuickUpdateStockMutation,
  useGetInventoryHistoryQuery,
  useImportInventoryMutation,
  useExportInventoryQuery,
  useGetAlertsQuery,
} from '@/services/M2/InventoryManagement/inventoryManagement'
import { useToast } from '@/components/Toast'
import { useLoadingStore } from '@/utils/loadingStore'

// Hook lấy danh sách tồn kho
export const useGetInventory = (params: any) => {
  const { data, error, isLoading, refetch } = useGetInventoryQuery(params)
  return { data, error, isLoading, refetch }
}

// Hook lấy chi tiết tồn kho
export const useGetInventoryDetail = (id: string) => {
  const { data, error, isLoading, refetch } = useGetInventoryDetailQuery(id)
  return { data, error, isLoading, refetch }
}

// Hook cập nhật tồn kho cho biến thể
export const useUpdateVariants = () => {
  const [updateVariants, { isLoading }] = useUpdateVariantsMutation()
  const { showSuccess, showError } = useToast()
  const { setLoading } = useLoadingStore()

  const update = async (id: string, variants: any[]) => {
    setLoading(true)
    try {
      await updateVariants({ id, variants }).unwrap()
      showSuccess('Cập nhật tồn kho thành công!')
    } catch (e: any) {
      showError('Cập nhật tồn kho thất bại!')
    } finally {
      setLoading(false)
    }
  }
  return { update, isLoading }
}

// Hook cập nhật nhanh tồn kho
export const useQuickUpdateStock = () => {
  const [quickUpdateStock, { isLoading }] = useQuickUpdateStockMutation()
  const { showSuccess, showError } = useToast()
  const { setLoading } = useLoadingStore()

  const update = async (id: string, variantId: string, change: number) => {
    setLoading(true)
    try {
      await quickUpdateStock({ id, variantId, change }).unwrap()
      showSuccess('Cập nhật tồn kho thành công!')
    } catch (e: any) {
      showError('Cập nhật tồn kho thất bại!')
    } finally {
      setLoading(false)
    }
  }
  return { update, isLoading }
}

// Hook lấy lịch sử nhập/xuất kho
export const useGetInventoryHistory = (id: string) => {
  const { data, error, isLoading, refetch } = useGetInventoryHistoryQuery(id, {
    skip: !id,
  })
  return { data, error, isLoading, refetch }
}

// Hook import tồn kho từ Excel
export const useImportInventory = () => {
  const [importInventory, { isLoading }] = useImportInventoryMutation()
  const { showSuccess, showError } = useToast()
  const { setLoading } = useLoadingStore()

  const importExcel = async (formData: FormData) => {
    setLoading(true)
    try {
      await importInventory(formData).unwrap()
      showSuccess('Import tồn kho thành công!')
    } catch (e: any) {
      showError('Import tồn kho thất bại!')
    } finally {
      setLoading(false)
    }
  }
  return { importExcel, isLoading }
}

// Hook export tồn kho ra Excel
export const useExportInventory = (params: any) => {
  const { data, error, isLoading, refetch } = useExportInventoryQuery(params)
  return { data, error, isLoading, refetch }
}

// Hook lấy danh sách cảnh báo hết hàng/sắp hết hàng
export const useGetAlerts = () => {
  const { data, error, isLoading, refetch } = useGetAlertsQuery()
  return { data, error, isLoading, refetch }
}
