import { OneToMany } from 'typeorm';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../../m1/user/entities/user.entity';
import { Product } from './products.entity';

@Entity('product_reviews')
export class ProductReview {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  product_id: string;
  @ManyToOne(() => Product, (product) => product.product_reviews, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column('uuid')
  user_id: string;
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column('uuid', { nullable: true })
  order_id?: string;

  @Column({ type: 'int' })
  rating: number;

  @Column({ type: 'text', nullable: true })
  content?: string;

  @Column({ type: 'jsonb', nullable: true })
  images?: any;

  @Column('uuid', { nullable: true })
  parent_id?: string;
  @ManyToOne(() => ProductReview, (review) => review.replies, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'parent_id' })
  parent?: ProductReview;

  @OneToMany(() => ProductReview, (review) => review.parent)
  replies: ProductReview[];

  @Column({ type: 'boolean', default: true })
  is_public: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;
}
