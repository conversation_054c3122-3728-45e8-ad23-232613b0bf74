import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

export const affiliateApi = createApi({
    reducerPath: 'affiliateApi',
    baseQuery: axiosBaseQuery({ baseUrl }),
    tagTypes: ['Affiliate'],
    endpoints: (build) => ({
        getAffiliateMe: build.query({
            query: () => ({
                url: '/users/affiliate/me',
                method: 'GET',
                authRequired: true,
            }),
        }),

        getAffiliateTree: build.query({
            query: () => ({
                url: '/users/affiliate/tree',
                method: 'GET',
                authRequired: true,
            }),
        }),

        getAffiliateStats: build.query({
            query: () => ({
                url: '/users/affiliate/stats',
                method: 'GET',
                authRequired: true,
            }),
        }),

        getAffiliateDirect: build.query({
            query: () => ({
                url: '/users/affiliate/direct',
                method: 'GET',
                authRequired: true,
            }),
        }),

        getAffiliateIndirect: build.query({
            query: (level) => ({
                url: `/users/affiliate/indirect?level=${level}`,
                method: 'GET',
                authRequired: true,
            }),
        }),
    }),
})

export const {
    useGetAffiliateMeQuery,
    useGetAffiliateTreeQuery,
    useGetAffiliateStatsQuery,
    useGetAffiliateDirectQuery,
    useGetAffiliateIndirectQuery,
} = affiliateApi
