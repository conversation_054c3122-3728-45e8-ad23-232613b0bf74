import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, Unique } from 'typeorm';

@Entity('product_vector_embedding')
@Unique(['product_id'])
export class ProductVectorEmbedding {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  product_id: string;

  @Column('float8', { array: true })
  vector: number[];

  @CreateDateColumn({ type: 'timestamp' })
  updated_at: Date;
}
