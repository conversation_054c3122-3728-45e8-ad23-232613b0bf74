import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Product } from './products.entity';
import { ProductVersion } from './product_version.entity';

@Entity('inventory')
export class Inventory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  product_id: string;
  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column('uuid', { nullable: true })
  version_id?: string;
  @ManyToOne(() => ProductVersion, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'version_id' })
  version?: ProductVersion;

  @Column({ type: 'varchar', length: 20, nullable: true })
  location_code?: string;

  @Column({ type: 'int' })
  stock_quantity: number;

  @Column({ type: 'int' })
  reserved_quantity: number;

  @Column({ type: 'int', nullable: true })
  threshold_min?: number;

  @Column('uuid', { nullable: true })
  last_updated_by?: string;

  @Column({ type: 'text', nullable: true })
  note?: string;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;
}
