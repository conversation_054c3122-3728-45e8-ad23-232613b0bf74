import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateProductCategoryDto {
  @ApiProperty({ example: 'Điện tử', description: 'Tên danh mục sản phẩm' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: 'dien-tu', description: 'Slug (không dấu, dùng cho URL)' })
  @IsString()
  @IsNotEmpty()
  slug: string;

  @ApiProperty({ example: 'dien-tu', description: 'Slug (không dấu, dùng cho URL)' })
  @IsString()
  @IsOptional()
  icon_url?: string;

  @ApiPropertyOptional({
    description: 'UUID của category cha (nếu có)',
    example: 'b1e2c3d4-5678-1234-9abc-1234567890ab',
    nullable: true,
  })
  @IsOptional()
  @IsUUID()
  parentId?: string;
}
