import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

import { User } from '../../user/entities/user.entity';
import { OtpVerification } from '../../user/entities/otp_verification.entity';
import { RegisterDto } from '../dto/register.dto';
import { LoginDto } from '../dto/login.dto';
import { AuthResponse, Login2FARequire } from '../dto/auth-response.dto';
import { MailService } from 'src/mail/mail.service';
import { Role } from '../../user/entities/role.entity';
import { RoleService } from '../../user/services/role.service';
import { UserProfile } from '../../user/entities/user_profile.entity';
import { UserAffiliateService } from '../../user/services/user_affiliate.service';
import { SocialLoginDto } from '../dto/social-login.dto';
import axios from 'axios';
import { UserProvider } from '../../user/entities/user-provider.entity';
import { User2FASettings } from '../../user/entities/user_2fa_settings.entity';
import { AuthSession } from '../../user/entities/auth_sessions.entity';
import { TokenService } from '../services/token.service';
import { TwofaService } from '../services/twofa.service';
import { Permission } from '../../user/entities/permissions.entity';
import { RoleGroup } from '../../user/entities/role_groups.entity';

@Injectable()
export class AuthService {
  /**
   * Gán quyền mặc định cho user mới
   */
  private async assignDefaultPermissions(userId: string) {
    const defaultPerms = await this.permissionRepo.find({
      where: [
        { code: 'PERM_USER_VIEW_SELF' },
        { code: 'PERM_USER_UPDATE_SELF' },
        { code: 'PERM_SESSION_LIST' },
      ],
    });
    // Nếu có entity UserPermission thì lưu vào đó, ví dụ:
    // for (const perm of defaultPerms) {
    //   await this.userPermRepo.save(this.userPermRepo.create({ userId, permissionId: perm.id }));
    // }
    // Nếu quyền nằm trong entity User thì cập nhật user (tuỳ thiết kế)
  }
  constructor(
    @InjectRepository(User) private readonly userRepo: Repository<User>,
    @InjectRepository(Role) private readonly roleRepo: Repository<Role>,
    @InjectRepository(UserProfile) private readonly profileRepo: Repository<UserProfile>,
    @InjectRepository(OtpVerification) private readonly otpRepo: Repository<OtpVerification>,
    @InjectRepository(UserProvider) private readonly providerRepo: Repository<UserProvider>,
    @InjectRepository(User2FASettings) private readonly user2FARepo: Repository<User2FASettings>,
    @InjectRepository(Permission) private readonly permissionRepo: Repository<Permission>,
    private readonly mailService: MailService,
    private affiliateService: UserAffiliateService,
    private readonly tokenService: TokenService,
    private readonly twoFaServie: TwofaService,
    private readonly roleService: RoleService,
  ) {}

  /**
   * 3️⃣ Đăng ký tài khoản người dùng mới (chỉ sau khi đã xác minh OTP)
   * @param dto Thông tin đăng ký
    @InjectRepository(Permission) private readonly permissionRepo: Repository<Permission>,
   * @returns AccessToken + RefreshToken
   */
  async register(dto: RegisterDto): Promise<AuthResponse> {
    const emailExist = await this.userRepo.findOneBy({ email: dto.email });
    const phoneExist = dto.phone ? await this.userRepo.findOneBy({ phone: dto.phone }) : null;

    if (emailExist) throw new ConflictException('Email đã tồn tại');
    if (phoneExist) throw new ConflictException('Số điện thoại đã tồn tại');

    const verifiedOtp = await this.otpRepo.findOne({
      where: { target: dto.email, isVerified: true },
      order: { createdAt: 'DESC' },
    });

    if (!verifiedOtp) {
      throw new UnauthorizedException('Bạn chưa xác minh email');
    }

    const hashedPassword = await bcrypt.hash(dto.password, 10);
    const uidCode = 'TAP_' + uuidv4().split('-')[0].toUpperCase();

    // ✅ Lấy role từ bảng `roles` qua roleId
    const role = await this.roleRepo.findOneBy({ id: dto.roleId });
    if (!role) {
      throw new BadRequestException('Role không hợp lệ');
    }

    // ✅ Lấy roleGroupCode mặc định: role_code + '.01'
    const roleGroupCode = role.role_code ? `${role.role_code}.01` : undefined;

    // ✅ Tìm referrer nếu có
    let referrer: User | null = null;
    if (dto.referralCode) {
      referrer = await this.userRepo.findOne({
        where: { uidCode: dto.referralCode },
      });
    }

    const user = this.userRepo.create({
      uidCode,
      phone: dto.phone,
      email: dto.email,
      password: hashedPassword,
      referralBy: dto.referralCode,
      role: role,
      status: 'active',
      roleGroupCode,
    });

    const saved = await this.userRepo.save(user);

    // ✅ Gán quyền mặc định cho user mới
    await this.assignDefaultPermissions(saved.id);

    // ✅ Ghi nhận hệ thống tuyến (F1 → F5)
    if (referrer) {
      await this.affiliateService.createAffiliateChain(saved.id, referrer.id);
    }

    // ✅ Tạo hồ sơ cá nhân (user_profiles)
    const profile = this.profileRepo.create({
      userId: saved.id,
      fullName: dto.fullName,
      phoneNumber: dto.phone,
      avatarUrl: '/static/mook-avatar-male.png', // avatar mặc định
    });
    await this.profileRepo.save(profile);

    await this.mailService.sendWelcomeEmail(saved.email, dto.fullName, saved.uidCode);

    return this.tokenService.generateTokens(saved);
  }

  /**
   * 4️⃣ Đăng nhập tài khoản bằng email và mật khẩu
   * @param dto Thông tin đăng nhập
   * @returns Token đăng nhập
   */
  async login(dto: LoginDto): Promise<AuthResponse | Login2FARequire> {
    const user = await this.userRepo.findOneBy({ email: dto.email });

    // Gộp tất cả các trường hợp sai về cùng một thông báo để tránh dò tài khoản
    if (!user || !user.password || !(await bcrypt.compare(dto.password, user.password))) {
      throw new UnauthorizedException('Sai tài khoản hoặc mật khẩu');
    }

    const setting = await this.user2FARepo.findOne({ where: { user_id: user.id } });

    // Nếu đã bật 2FA → yêu cầu xác minh mã
    if (setting?.is_otp_enabled || setting?.is_totp_enabled) {
      // gọi hàm để gửi otp tại đây
      await this.twoFaServie.sendOtp2FA({ target: user.email });
      return {
        requires2FA: true,
        userId: user.id,
        method: setting.is_totp_enabled ? 'totp' : 'otp',
        message: 'Tài khoản đã bật xác thực 2 yếu tố. Vui lòng nhập mã bảo mật.',
      };
    }

    return this.tokenService.generateTokens(user);
  }

  async loginWithProvider(dto: SocialLoginDto): Promise<AuthResponse> {
    const profile = await this.verifyWithProvider(dto.provider, dto.accessToken);
    const { providerId, email, fullName, picture } = profile;

    if (!email) {
      throw new BadRequestException('Không lấy được email từ tài khoản mạng xã hội.');
    }

    // 1️⃣ Nếu đã từng đăng nhập với provider này → login
    const existingProvider = await this.providerRepo.findOne({
      where: { provider: dto.provider, providerId },
      relations: ['user'],
    });
    if (existingProvider) {
      return this.tokenService.generateTokens(existingProvider.user);
    }

    // 2️⃣ Nếu email trùng account truyền thống → chặn tạo mới
    const userWithEmail = await this.userRepo.findOneBy({ email });
    if (userWithEmail) {
      throw new ConflictException(
        'Email đã tồn tại. Vui lòng đăng nhập bằng email và liên kết tài khoản mạng xã hội.',
      );
    }

    // 3️⃣ Lấy role mặc định (Người tiêu dùng R03) và roleGroup Khách lẻ R03.01 bằng RoleService
    const role = await this.roleService.getRoleByIdOrCode('R03');
    if (!role) throw new BadRequestException('Không tìm thấy vai trò R03');

    // Kiểm tra thuộc tính 'roleGroup' có tồn tại trong entity User
    const roleGroup = await this.roleService.getRoleGroupByCode('R03.01');
    if (!roleGroup) throw new BadRequestException('Không tìm thấy nhóm vai trò R03.01');

    // 4️⃣ Tạo user mới
    const newUserData: Partial<User> = {
      email,
      status: 'active',
      uidCode: 'TAP_' + uuidv4().split('-')[0].toUpperCase(),
      roleGroupCode: roleGroup ? roleGroup.code : undefined,
      role,
      ...(roleGroup ? { roleGroup } : {}),
    };
    const newUser = this.userRepo.create(newUserData);
    const savedUser = await this.userRepo.save(newUser);

    // 5️⃣ Lưu bảng user_providers
    const providerEntity = this.providerRepo.create({
      provider: dto.provider,
      providerId,
      email,
      user: savedUser,
    });
    await this.providerRepo.save(providerEntity);

    // 6️⃣ Tạo hồ sơ người dùng
    const profileEntity = this.profileRepo.create({
      userId: savedUser.id,
      fullName: fullName || '',
      avatarUrl: picture || '',
    });
    await this.profileRepo.save(profileEntity);

    // 7️⃣ Gửi mail chào mừng nếu có email
    if (email) {
      await this.mailService.sendWelcomeEmail(email, fullName || 'Bạn', newUser.uidCode);
    }

    return this.tokenService.generateTokens(savedUser);
  }

  private async verifyWithProvider(
    provider: 'google' | 'facebook' | 'zalo',
    accessToken: string,
  ): Promise<{
    provider: 'google' | 'facebook' | 'zalo';
    providerId: string;
    email: string;
    fullName: string;
    picture: string;
  }> {
    if (provider === 'google') {
      const response = await axios.get(
        `https://www.googleapis.com/oauth2/v3/userinfo?access_token=${accessToken}`,
      );

      const data = response.data;
      return {
        provider: 'google',
        providerId: data.sub,
        email: data.email,
        fullName: data.name,
        picture: data.picture,
      };
    }

    if (provider === 'facebook') {
      const response = await axios.get(
        `https://graph.facebook.com/me?fields=id,name,email,picture&access_token=${accessToken}`,
      );

      const data = response.data;

      return {
        provider: 'facebook',
        providerId: data.id,
        email: data.email,
        fullName: data.name,
        picture: data.picture,
      };
    }

    if (provider === 'zalo') {
      const response = await axios.get(
        `https://graph.zalo.me/v2.0/me?access_token=${accessToken}&fields=id,name,picture`,
      );

      const data = response.data;

      return {
        provider: 'zalo',
        providerId: data.id,
        email: `zalo_${data.id}@noemail.com`, // fake vì Zalo không có email
        fullName: data.name,
        picture: data.picture.data.url,
      };
    }

    throw new Error('Unsupported provider');
  }
}
