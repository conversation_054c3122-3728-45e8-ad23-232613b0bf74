#!/bin/bash
# AI Training Pipeline Script
# <PERSON><PERSON><PERSON> lầ<PERSON> l<PERSON> các bước pipeline AI cho module M2

set -e

# <PERSON><PERSON><PERSON> bảo script đ<PERSON><PERSON><PERSON> chạy từ thư mục server/
cd "$(dirname "$0")/../.."

# <PERSON>ể<PERSON> tra venv
if [ ! -d "venv" ]; then
  echo "[ERROR] Python venv chưa tồn tại. H<PERSON>y tạo venv trước khi chạy pipeline!"
  echo "Gợi ý: python -m venv venv && venv/Scripts/activate && pip install -r requirements.txt"
  exit 1
fi

PYTHON_EXEC="venv/Scripts/python.exe"

# 1. Export product data to JSON
npm run export:product-to-vector

# 2. Sinh embedding cho sản phẩm (Python)
$PYTHON_EXEC src/m2/ai_trainning/product_to_vector.py

# 3. Insert/update product vector vào DB
npm run insert:product-embeddings

# 4. Export user behavior log to JSON
npm run export:user-to-vector

# 5. Sinh embedding cho user (Python)
$PYTHON_EXEC src/m2/ai_trainning/user_to_vector.py

# 6. Insert/update user vector vào DB
npm run insert:user-embeddings

echo "AI training pipeline completed!"
