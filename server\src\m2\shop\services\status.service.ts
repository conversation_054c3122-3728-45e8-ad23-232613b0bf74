// src/m2/shop/services/status.service.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Status } from '../entities/status.entity';
import { Repository } from 'typeorm';

@Injectable()
export class StatusService {
  constructor(
    @InjectRepository(Status)
    private readonly statusRepo: Repository<Status>,
  ) {}

  /**
   * 🔍 Trả về danh sách trạng thái theo nhóm
   * @param type Loại trạng thái ('PRODUCT', 'ORDER', 'USER'…)
   * @returns Danh sách trạng thái theo type
   */
  async getStatusByType(type: string): Promise<Status[]> {
    return this.statusRepo.find({
      where: { type },
    });
  }

  /**
   * 🔍 Trả về danh sách trạng thái theo nhiều nhóm
   * @param types Mảng type
   */
  async getStatusByTypes(types: string[]): Promise<Status[]> {
    return this.statusRepo.find({
      where: types && types.length > 0 ? types.map((type) => ({ type })) : {},
    });
  }
}
