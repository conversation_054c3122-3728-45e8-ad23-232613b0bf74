import { useState, useEffect } from 'react'
import { Button, Form, FormGroup, Label, Input, FormFeedback } from 'reactstrap'
import { useNavigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Key, Eye, EyeOff } from 'lucide-react'
import loginIllustration from '../../../assets/images/logo.png'
import { useResetPasswordHook } from '@/hooks/auth/useResetPassword'
import { toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import { ROUTES } from '@/constants/routes'

interface Style {
  [key: string]: React.CSSProperties
}

const ChangePassword = () => {
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [passwordError, setPasswordError] = useState('')
  const [confirmPasswordError, setConfirmPasswordError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const resetPassword = useResetPasswordHook()

  // Retrieve email from navigation state
  const email = location.state?.email || ''

  useEffect(() => {
    window.scrollTo(0, 0)
    return () => {
      window.scrollTo(0, 0)
    }
  }, [])

  const validatePassword = (password: string): string | null => {
    // Kiểm tra độ dài
    if (password.length < 8 || password.length > 15) {
      return 'Mật khẩu phải từ 8 đến 15 ký tự'
    }
    // Kiểm tra chữ thường
    if (!/[a-z]/.test(password)) {
      return 'Mật khẩu phải chứa ít nhất một chữ thường'
    }
    // Kiểm tra chữ in hoa
    if (!/[A-Z]/.test(password)) {
      return 'Mật khẩu phải chứa ít nhất một chữ in hoa'
    }
    // Kiểm tra số
    if (!/\d/.test(password)) {
      return 'Mật khẩu phải chứa ít nhất một số'
    }
    // Kiểm tra ký tự đặc biệt
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return 'Mật khẩu phải chứa ít nhất một ký tự đặc biệt'
    }
    return null
  }

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setPasswordError('')
    setConfirmPasswordError('')
    setSuccessMessage('')
    setIsLoading(true)

    // Kiểm tra mật khẩu
    const passwordValidationError = validatePassword(newPassword)
    if (passwordValidationError) {
      toast.error(passwordValidationError, {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      })
      setPasswordError(passwordValidationError)
      setIsLoading(false)
      return
    }

    // Kiểm tra xác nhận mật khẩu
    if (newPassword !== confirmPassword) {
      toast.error('Mật khẩu xác nhận không khớp', {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      })
      setConfirmPasswordError('Mật khẩu xác nhận không khớp')
      setIsLoading(false)
      return
    }

    try {
      await resetPassword({ email, newPassword })
      setSuccessMessage('Mật khẩu đã được thay đổi thành công')
      setNewPassword('')
      setConfirmPassword('')
      setTimeout(() => {
        navigate(ROUTES.LOGIN) // Navigate to login page after 3 seconds
      }, 3000)
    } catch (err: any) {
      toast.error('Đổi mật khẩu thất bại. Vui lòng thử lại.', {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      })
      setPasswordError('Đổi mật khẩu thất bại. Vui lòng thử lại.')
    } finally {
      setIsLoading(false)
    }
  }

  const toggleShowPassword = () => {
    setShowPassword(!showPassword)
  }

  const toggleShowConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword)
  }

  return (
    <motion.div
      style={styles.container}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        style={styles.header}
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <img src={loginIllustration} alt="Logo TAP" style={styles.logo} />
      </motion.div>

      <div style={styles.formContainer}>
        <motion.h3
          style={styles.subTitle}
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4 }}
        >
          Đổi mật khẩu
        </motion.h3>

        <Form onSubmit={handleChangePassword}>
          {successMessage && <div style={styles.successMessage}>{successMessage}</div>}
          <FormGroup>
            <Label style={styles.label}>Mật khẩu mới</Label>
            <div style={styles.inputWrapper}>
              <Key style={styles.inputIcon} />
              <Input
                id="newPassword"
                type={showPassword ? 'text' : 'password'}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Nhập mật khẩu mới"
                invalid={!!passwordError}
                required
                style={
                  focusedField === 'newPassword'
                    ? { ...styles.input, ...styles.inputFocus }
                    : styles.input
                }
                onFocus={() => setFocusedField('newPassword')}
                onBlur={() => setFocusedField(null)}
              />
              <div style={styles.passwordToggle} onClick={toggleShowPassword}>
                {showPassword ? (
                  <EyeOff style={{ width: '1.25rem', height: '1.25rem' }} />
                ) : (
                  <Eye style={{ width: '1.25rem', height: '1.25rem' }} />
                )}
              </div>
            </div>
            {passwordError && <FormFeedback>{passwordError}</FormFeedback>}
          </FormGroup>
          <FormGroup>
            <Label style={styles.label}>Xác nhận mật khẩu mới</Label>
            <div style={styles.inputWrapper}>
              <Key style={styles.inputIcon} />
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Xác nhận mật khẩu mới"
                invalid={!!confirmPasswordError}
                required
                style={
                  focusedField === 'confirmPassword'
                    ? { ...styles.input, ...styles.inputFocus }
                    : styles.input
                }
                onFocus={() => setFocusedField('confirmPassword')}
                onBlur={() => setFocusedField(null)}
              />
              <div style={styles.passwordToggle} onClick={toggleShowConfirmPassword}>
                {showConfirmPassword ? (
                  <EyeOff style={{ width: '1.25rem', height: '1.25rem' }} />
                ) : (
                  <Eye style={{ width: '1.25rem', height: '1.25rem' }} />
                )}
              </div>
            </div>
            {confirmPasswordError && <FormFeedback>{confirmPasswordError}</FormFeedback>}
          </FormGroup>
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              color="primary"
              block
              type="submit"
              disabled={isLoading}
              style={
                isLoading
                  ? { ...styles.submitButton, ...styles.submitButtonDisabled }
                  : styles.submitButton
              }
              onMouseEnter={(e) =>
                !isLoading && Object.assign(e.currentTarget.style, styles.submitButtonHover)
              }
              onMouseLeave={(e) =>
                !isLoading && Object.assign(e.currentTarget.style, styles.submitButton)
              }
            >
              <Key
                style={{
                  width: '1.25rem',
                  height: '1.25rem',
                  marginRight: '0.5rem',
                  display: 'inline-block',
                }}
              />
              {isLoading ? 'Đang xử lý...' : 'Xác nhận đổi mật khẩu'}
            </Button>
          </motion.div>
          <div style={styles.registerContainer}>
            <div
              style={styles.link}
              onClick={() => navigate(ROUTES.LOGIN, { state: { email } })}
              onMouseEnter={(e) => Object.assign(e.currentTarget.style, styles.linkHover)}
              onMouseLeave={(e) => Object.assign(e.currentTarget.style, styles.link)}
            >
              Quay lại xác nhận mã OTP
            </div>
          </div>
        </Form>
      </div>
    </motion.div>
  )
}

export default ChangePassword

const styles: Style = {
  container: {
    minHeight: '100vh',
    width: '100%',
    background: '#FFFFFF',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 0,
    margin: 0,
    overflow: 'hidden',
    position: 'absolute',
    top: 0,
    left: 0,
  },
  header: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: '10px 0',
  },
  logo: {
    width: '150px',
    height: '150px',
  },
  formContainer: {
    backgroundColor: '#FFFFFF',
    padding: '2.5rem',
    borderRadius: '15px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
    width: '100%',
    maxWidth: '500px',
    margin: 0,
  },
  subTitle: {
    fontSize: '1.4rem',
    color: '#7A7A7A',
    textAlign: 'center',
    marginBottom: '1.5rem',
    userSelect: 'none',
  },
  label: {
    color: '#2E2E2E',
    fontSize: '1.1rem',
    marginBottom: '0.5rem',
  },
  inputWrapper: {
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    marginBottom: '1rem',
  },
  input: {
    border: '1px solid #E0E0E0',
    borderRadius: '8px',
    color: '#2E2E2E',
    width: '100%',
    padding: '0.75rem 3rem 0.75rem 2.75rem', // Increased padding-left and padding-right
    fontSize: '1.1rem',
    transition: 'all 0.2s ease',
  },
  inputFocus: {
    borderColor: '#34A853',
    boxShadow: '0 0 0 0.2rem rgba(52, 168, 83, 0.4)',
  },
  inputIcon: {
    position: 'absolute',
    left: '0.75rem',
    color: '#7A7A7A',
  },
  passwordToggle: {
    position: 'absolute',
    right: '0.75rem',
    cursor: 'pointer',
    color: '#7A7A7A',
  },
  link: {
    color: '#2D9CDB',
    textDecoration: 'none',
    fontSize: '1.1rem',
  },
  linkHover: {
    color: '#34A853',
    textDecoration: 'underline',
  },
  submitButton: {
    backgroundColor: '#2D9CDB',
    borderColor: '#2D9CDB',
    color: '#FFFFFF',
    width: '100%',
    padding: '0.75rem',
    borderRadius: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all 0.3s ease',
    fontSize: '1.1rem',
  },
  submitButtonHover: {
    backgroundColor: '#34A853',
    borderColor: '#34A853',
    transform: 'scale(1.05)',
  },
  submitButtonDisabled: {
    backgroundColor: '#B2DFDB',
    borderColor: '#B2DFDB',
    cursor: 'not-allowed',
  },
  registerContainer: {
    textAlign: 'center',
    marginTop: '1.5rem',
    fontSize: '1.1rem',
  },
  successMessage: {
    color: '#34A853',
    textAlign: 'center',
    marginBottom: '1.5rem',
    fontSize: '1.1rem',
  },
}
