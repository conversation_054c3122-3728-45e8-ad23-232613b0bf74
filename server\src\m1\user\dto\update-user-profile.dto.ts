// src/user/dto/update-user-profile.dto.ts
import { ApiProperty } from '@nestjs/swagger';

export class UpdateUserProfileDto {
  @ApiProperty({ required: false })
  fullName?: string;

  @ApiProperty({ required: false })
  phone?: string;

  @ApiProperty({ required: false })
  email?: string;

  @ApiProperty({ required: false })
  citizenId?: string;

  @ApiProperty({ required: false })
  address?: string;

  @ApiProperty({ required: false })
  gender?: 'Nam' | 'Nữ';

  @ApiProperty({ required: false })
  dateOfBirth?: string; // YYYY-MM-DD
}
