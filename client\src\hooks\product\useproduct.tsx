// 🆕 Hook tìm kiếm sản phẩm bằng hình ảnh (AI object detection)
import { useGetPublicProductsQuery } from '@/services/M2/ProductManagement/ProductPublic/productPublic'

import {
  useCreateProductAdminMutation,
  useUploadProductImagesMutation,
  useGetProductDetailQuery,
  useUpdateProductMutation,
  useDeleteProductMutation,
  useDisableProductMutation,
  useGetStatusesByTypesQuery, // <-- import hook mới
  useGetFavoritesQuery, // <-- import hook mới
  useToggleFavoriteMutation, // <-- import hook mới
  useSearchProductByImageMutation,
  useGetProductsQuery,
  useGetProductInventoryQuery,
  useGetProductStatsQuery,
  type CreateProductRequest,
  type UpdateProductRequest,
  type ProductImage,
  type UploadImagesResponse,
  type GetStatusesByTypesParams,
  type GetProductsParams,
  type GetProductsResponse,
  type GetProductInventoryParams,
} from '@/services/M2/ProductManagement/ProductAdmin/productAdmin'

import {
  useGetCategoriesQuery,
  type GetCategoriesParams,
} from '@/services/M2/ProductManagement/categoryManagement'
import { useToast } from '../../components/Toast'
import { useEffect } from 'react'
import { useLoadingStore } from '@/utils/loadingStore'
import logo from '@/assets/images/logo.png'

// 🆕 Hook tìm kiếm sản phẩm bằng hình ảnh (AI object detection)
const useSearchProductByImageHook = () => {
  const { showSuccess, showError } = useToast()
  const [searchByImage, { isLoading }] = useSearchProductByImageMutation()
  const { setLoading } = useLoadingStore()

  const search = async (file: File): Promise<any> => {
    setLoading(true)
    try {
      const res = await searchByImage(file).unwrap()
      // showSuccess('Tìm kiếm sản phẩm bằng ảnh thành công')
      return res
    } catch (e: any) {
      // showError('Tìm kiếm sản phẩm bằng ảnh thất bại')
      return null
    } finally {
      setLoading(false)
    }
  }
  return { search, isLoading }
}
// ✅ Hook lấy categories
const useGetCategoriesHook = (params: GetCategoriesParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetCategoriesQuery(params, {
    refetchOnMountOrArgChange: true,
    skip: false,
    refetchOnReconnect: true,
  })

  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

const useGetStatusesByTypeHook = (params: GetStatusesByTypesParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetStatusesByTypesQuery(params, {
    refetchOnMountOrArgChange: true,
    skip: false,
    refetchOnReconnect: true,
  })

  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// ✅ Hook tạo product đơn giản
const useCreateProductHook = () => {
  const { showSuccess, showError } = useToast()
  const [createFunc] = useCreateProductAdminMutation()

  const create = async (data: CreateProductRequest): Promise<boolean> => {
    try {
      const res = await createFunc(data)
      if (!res?.error) {
        showSuccess('Tạo sản phẩm thành công')
        return true
      } else {
        if ((res.error as any)?.status === 500) {
          showError('Lỗi server - Vui lòng liên hệ admin')
        } else {
          const messageDes =
            (res.error as { data?: { message?: string } })?.data?.message || 'Unknown error'
          showError('Tạo sản phẩm thất bại: ' + messageDes)
        }
        return false
      }
    } catch (e) {
      showError('Lỗi kết nối - Kiểm tra server backend')
      return false
    }
  }

  return create
}

// ✅ Hook upload ảnh
const useUploadProductImagesHook = () => {
  const { showSuccess, showError } = useToast()
  const [uploadFunc] = useUploadProductImagesMutation()

  // Trả về đúng response gốc (UploadedImage[]), không map sang ProductImage
  const uploadImages = async (files: File[]): Promise<any[] | null> => {
    try {
      const formData = new FormData()
      files.forEach((file) => {
        formData.append('files', file)
      })

      const res = await uploadFunc(formData)
      if (!res?.error) {
        let uploaded: any[] = []

        // Xử lý nhiều case response structure
        if (res.data?.data && Array.isArray(res.data.data)) {
          uploaded = res.data.data
        } else if (Array.isArray(res.data)) {
          uploaded = res.data
        } else if (res.data && typeof res.data === 'object') {
          const possibleArrays = Object.values(res.data).filter((val) => Array.isArray(val))
          if (possibleArrays.length > 0) {
            uploaded = possibleArrays[0] as any[]
          }
        }

        if (uploaded.length === 0) {
          showError('Không tìm thấy ảnh trong phản hồi')
          return null
        }

        return uploaded
      } else {
        showError('Upload ảnh thất bại')
        return null
      }
    } catch (e) {
      showError('Lỗi kết nối - Kiểm tra server backend')
      return null
    }
  }

  return uploadImages
}

// ✅ Hook tạo product với ảnh - Update theo structure mới
const useCreateProductWithImagesHook = () => {
  const { showSuccess, showError } = useToast()
  const [createFunc] = useCreateProductAdminMutation()
  const uploadImages = useUploadProductImagesHook()

  const createWithImages = async (
    productData: {
      name: string
      description?: string
      price: number
      unit: string
      category_id: string
      product_attribute?: any[]
      product_versions?: any[]
      product_images?: any[]
    },
    imageFiles?: File[]
  ): Promise<boolean> => {
    try {
      let product_images: ProductImage[] = []
      // Ưu tiên dùng productData.product_images nếu có
      if (
        productData.product_images &&
        Array.isArray(productData.product_images) &&
        productData.product_images.length > 0
      ) {
        product_images = productData.product_images
      } else if (imageFiles && imageFiles.length > 0) {
        const uploadedImages = await uploadImages(imageFiles)
        if (uploadedImages) {
          product_images = uploadedImages
        }
      }
      const cleanedProductVersions = (productData.product_versions || []).map((version) => ({
        name: version.name || '',
        price: Number(version.price) || 0,
        stock: Number(version.stock) || 0,
        options: version.options || {},
        sku_code: version.sku_code || '',
      }))

      const finalProductData: CreateProductRequest = {
        name: productData.name,
        description: productData.description || '',
        price: productData.price,
        unit: productData.unit,
        category_id: productData.category_id,
        product_images: product_images,
        product_attribute: productData.product_attribute || [],
        product_versions: cleanedProductVersions,
      }

      const res = await createFunc(finalProductData)
      if (!res?.error) {
        showSuccess('Tạo sản phẩm thành công')
        return true
      } else {
        showError('Tạo sản phẩm thất bại')
        return false
      }
    } catch (e) {
      showError('Lỗi kết nối')
      return false
    }
  }

  return createWithImages
}

// ✅ Hook lấy danh sách sản phẩm (có phân trang, tìm kiếm, lọc, sắp xếp)
const useGetProductsHook = (params: GetProductsParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetProductsQuery(params, {
    refetchOnMountOrArgChange: true,
    skip: false,
    refetchOnReconnect: true,
  })

  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

const useGetPublicProductsHook = (params: GetProductsParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetPublicProductsQuery(params, {
    refetchOnMountOrArgChange: true,
    skip: false,
    refetchOnReconnect: true,
  })

  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// ✅ Hook cập nhật sản phẩm (CŨ - GIỮ NGUYÊN)
const useUpdateProductHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useUpdateProductMutation()

  const update = async (id: string, data: any): Promise<boolean> => {
    try {
      const res = await updateFunc({ id, data })
      if (!res?.error) {
        showSuccess('Cập nhật sản phẩm thành công')
        return true
      } else {
        if ((res.error as any)?.status === 500) {
          showError('Lỗi server - Vui lòng liên hệ admin')
        } else {
          const messageDes =
            (res.error as { data?: { message?: string } })?.data?.message || 'Unknown error'
          showError('Cập nhật sản phẩm thất bại: ' + messageDes)
        }
        return false
      }
    } catch (e) {
      showError('Lỗi kết nối - Kiểm tra server backend')
      return false
    }
  }

  return update
}

// 🆕 Hook cập nhật sản phẩm với xử lý ảnh - ĐÃ SỬA LỖI
const useUpdateProductWithImagesHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useUpdateProductMutation()

  return async (id: string, productData: any): Promise<boolean> => {
    try {
      const res = await updateFunc({ id, data: productData })
      if (!res?.error) {
        showSuccess('Cập nhật sản phẩm thành công')
        return true
      } else {
        if ((res.error as any)?.status === 500) {
          showError('Lỗi server - Vui lòng liên hệ admin')
        } else {
          const messageDes =
            (res.error as { data?: { message?: string } })?.data?.message || 'Unknown error'
          showError('Cập nhật sản phẩm thất bại: ' + messageDes)
        }
        return false
      }
    } catch (e) {
      showError('Lỗi kết nối - Kiểm tra server backend')
      return false
    }
  }
}
// ✅ Hook xóa sản phẩm
const useDeleteProductHook = () => {
  const { showSuccess, showError } = useToast()
  const [deleteFunc] = useDeleteProductMutation()

  const remove = async (id: string): Promise<boolean> => {
    try {
      const res = await deleteFunc(id)
      if (!res?.error) {
        showSuccess('Xóa sản phẩm thành công')
        return true
      } else {
        if ((res.error as any)?.status === 500) {
          showError('Lỗi server - Vui lòng liên hệ admin')
        } else {
          const messageDes =
            (res.error as { data?: { message?: string } })?.data?.message || 'Unknown error'
          showError('Xóa sản phẩm thất bại: ' + messageDes)
        }
        return false
      }
    } catch (e) {
      showError('Lỗi kết nối - Kiểm tra server backend')
      return false
    }
  }

  return remove
}

// ✅ Hook tắt (ẩn) sản phẩm
const useDisableProductHook = () => {
  const { showSuccess, showError } = useToast()
  const [disableFunc] = useDisableProductMutation()

  const disable = async (id: string): Promise<boolean> => {
    try {
      const res = await disableFunc(id)
      if (!res?.error) {
        showSuccess('Đã ẩn sản phẩm thành công')
        return true
      } else {
        if ((res.error as any)?.status === 500) {
          showError('Lỗi server - Vui lòng liên hệ admin')
        } else {
          const messageDes =
            (res.error as { data?: { message?: string } })?.data?.message || 'Unknown error'
          showError('Ẩn sản phẩm thất bại: ' + messageDes)
        }
        return false
      }
    } catch (e) {
      showError('Lỗi kết nối - Kiểm tra server backend')
      return false
    }
  }

  return disable
}

// 🆕 Hook lấy danh sách status theo nhóm
const useGetStatusListHook = (params: { type: 'PRODUCT' | 'ORDER' | 'PAYMENT' | 'IMAGE' }) => {
  const { data, error, isLoading, refetch, isFetching } = useGetStatusesByTypesQuery(params, {
    refetchOnMountOrArgChange: true,
    skip: !params?.type,
    refetchOnReconnect: true,
  })

  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// ✅ Hook lấy chi tiết sản phẩm (kèm version, QR code)
const useGetProductDetailHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetProductDetailQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true,
    refetchOnReconnect: true,
  })

  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// ⭐️ Hook GET: Lấy danh sách sản phẩm yêu thích
const useGetFavoritesHook = () => {
  // Only use default options to avoid repeated refetches
  const { data, error, isLoading, refetch, isFetching } = useGetFavoritesQuery(undefined, {
    skip: false,
  })

  const { setLoading } = useLoadingStore()
  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return { data, isLoading, error, refetch }
}

// ⭐️ Hook PUT: Thích hoặc bỏ thích sản phẩm
const useToggleFavoriteHook = () => {
  const { showSuccess, showError } = useToast()
  const [toggleFunc] = useToggleFavoriteMutation()
  const toggle = async (
    product_id: string,
    isCurrentlyFavorite?: boolean
  ): Promise<boolean | null> => {
    try {
      const res = await toggleFunc({ product_id })
      if (!res?.error) {
        const liked = res.data?.liked
        if (liked === true) {
          showSuccess('Đã thêm vào danh sách yêu thích')
        } else if (liked === false) {
          showSuccess('Đã xóa khỏi danh sách yêu thích')
        } else {
          showSuccess('Cập nhật yêu thích thành công')
        }
        return liked === undefined ? null : liked
      } else {
        const messageDes =
          (res.error as { data?: { message?: string } })?.data?.message || 'Unknown error'
        showError('Cập nhật yêu thích thất bại: ' + messageDes)
        return null
      }
    } catch (e) {
      showError('Lỗi kết nối - Kiểm tra server backend')
      return null
    }
  }
  return toggle
}

export const useGetProductInventoryHook = (params: { productId: string; versionId?: string }) => {
  return useGetProductInventoryQuery(params, {
    skip: !params.productId,
  })
}

export const useGetProductStatsHook = (productId: string) => {
  return useGetProductStatsQuery(productId)
}

// ✅ Export hooks - CẬP NHẬT THÊM HOOK MỚI
export {
  useGetCategoriesHook,
  useCreateProductHook,
  useUploadProductImagesHook,
  useCreateProductWithImagesHook,
  useGetProductsHook,
  useUpdateProductHook, // ✅ Hook cũ - giữ nguyên
  useUpdateProductWithImagesHook, // 🆕 Hook mới - ĐÃ SỬA LỖI
  useGetProductDetailHook,
  useDeleteProductHook,
  useDisableProductHook,
  useGetStatusesByTypeHook,
  useGetFavoritesHook, // ⭐️ mới
  useToggleFavoriteHook, // ⭐️ mới
  useSearchProductByImageHook, // 🆕 export hook mới
  useGetPublicProductsHook,
}
