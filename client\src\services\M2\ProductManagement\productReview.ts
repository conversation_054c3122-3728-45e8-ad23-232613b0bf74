import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@/libs/axios/axiosBase';

const baseUrl = 'http://localhost:8080';

// Types
export interface ProductReview {
  _id?: string;
  product_id: string;
  rating: number;
  content: string;
  parent_id?: string;
  order_id?: string;
  images?: Record<string, any>;
}

export interface ProductReviewUpdate {
  rating: number;
  content: string;
  images?: Record<string, any>;
}

export interface ProductReviewResponse {
  data?: any;
  message?: string;
  statusCode?: number;
}

export const productReviewApi = createApi({
  reducerPath: 'productReviewApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['ProductReview'],
  endpoints: (build) => ({
    // Lấy danh sách sản phẩm đã hoàn thành cần đánh giá
    getToReview: build.query<ProductReviewResponse, void>({
      query: () => ({
        url: '/product-reviews/to-review',
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['ProductReview'],
    }),
    // Lấy danh sách đánh giá của user để chỉnh sửa
    getMyReviews: build.query<ProductReviewResponse, void>({
      query: () => ({
        url: '/product-reviews/my',
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['ProductReview'],
    }),
    // Tạo đánh giá hoặc rep lại đánh giá
    createReview: build.mutation<ProductReviewResponse, ProductReview>({
      query: (data) => ({
        url: '/product-reviews',
        method: 'POST',
        data,
        authRequired: true,
      }),
      invalidatesTags: ['ProductReview'],
    }),
    // Chỉnh sửa đánh giá
    updateReview: build.mutation<ProductReviewResponse, { id: string; data: ProductReviewUpdate }>({
      query: ({ id, data }) => ({
        url: `/product-reviews/${id}`,
        method: 'PUT',
        data,
        authRequired: true,
      }),
      invalidatesTags: ['ProductReview'],
    }),
    // Xóa đánh giá
    deleteReview: build.mutation<ProductReviewResponse, string>({
      query: (id) => ({
        url: `/product-reviews/${id}`,
        method: 'DELETE',
        authRequired: true,
      }),
      invalidatesTags: ['ProductReview'],
    }),
  }),
});

export const {
  useGetToReviewQuery,
  useGetMyReviewsQuery,
  useCreateReviewMutation,
  useUpdateReviewMutation,
  useDeleteReviewMutation,
} = productReviewApi;
