/* =================================================================
   CATEGORY PAGE STYLES
   Đồng bộ với Commerce page để đảm bảo consistency
================================================================= */

/* -----------------------------------------------------------------
   1. GLOBAL STYLES & VARIABLES
----------------------------------------------------------------- */
.categoryPage {
  min-height: 100vh;
  background: #f8f9fa;
  position: relative;
}

.mainContent {
  padding-top: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  font-size: 1.1rem;
  color: #666;
}

/* -----------------------------------------------------------------
   2. CATEGORY HEADER SECTION
----------------------------------------------------------------- */
.categoryHeader {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 40px 0;
  margin-bottom: 40px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
}

.breadcrumbLink {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  text-decoration: none;
  transition: color 0.3s;
  
  &:hover {
    color: white;
  }
}

.breadcrumbSeparator {
  margin: 0 8px;
  color: rgba(255, 255, 255, 0.6);
}

.breadcrumbCurrent {
  color: white;
  font-weight: 500;
}

.categoryInfo {
  display: flex;
  align-items: center;
  gap: 20px;
}

.categoryIcon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
  }
}

.categoryDetails {
  flex: 1;
}

.categoryTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
}

.categoryDescription {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
}

/* -----------------------------------------------------------------
   3. PRODUCTS SECTION
----------------------------------------------------------------- */
.productsSection {
  padding: 0 0 60px 0;
}

.sectionHeader {
  text-align: center;
  margin-bottom: 40px;
  
  h2 {
    font-size: 2.2rem;
    color: #333;
    margin: 0 0 8px 0;
    font-weight: 700;
  }
  
  p {
    font-size: 1.1rem;
    color: #666;
    margin: 0;
  }
}

.productsGrid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

/* Đảm bảo 5 sản phẩm mỗi hàng trên desktop */
@media (min-width: 1025px) {
  .productsGrid {
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
  }
}

.productCard {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-width: 0;
  display: flex;
  flex-direction: column;
  height: 300px;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.productImage {
  position: relative;
  height: 160px;
  overflow: hidden;
  background-color: #f0f0f0;
  flex-shrink: 0;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  &:hover img {
    transform: scale(1.05);
  }
}

.favoriteButton {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 2;
  
  &:hover {
    background: white;
    color: #e74c3c;
    transform: scale(1.1);
  }
  
  &.favorited {
    background: #e74c3c;
    color: white;
  }
}

.cartButton {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: #4CAF50;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 2;
  
  &:hover {
    background: #45a049;
    transform: scale(1.1);
  }
}

.productInfo {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.productName {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.productRating {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
}

.starIcon {
  font-size: 12px;
}

.ratingNumber {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.productPrice {
  margin-top: auto;
}

.currentPrice {
  font-size: 16px;
  font-weight: 700;
  color: #4CAF50;
}

/* -----------------------------------------------------------------
   4. PAGINATION STYLES
----------------------------------------------------------------- */
.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  margin-bottom: 20px;
  padding: 20px 0;
}

.paginationContainer :global(.ant-pagination) {
  display: flex;
  align-items: center;
  gap: 8px;
}

.paginationContainer :global(.ant-pagination-item) {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  min-width: 32px;
  height: 32px;
  line-height: 30px;
  text-align: center;
  background: white;
  transition: all 0.3s;
}

.paginationContainer :global(.ant-pagination-item:hover) {
  border-color: #4CAF50;
  color: #4CAF50;
}

.paginationContainer :global(.ant-pagination-item-active) {
  background: #4CAF50;
  border-color: #4CAF50;
  color: white;
}

/* -----------------------------------------------------------------
   5. EMPTY STATES
----------------------------------------------------------------- */
.notFound,
.noProducts {
  text-align: center;
  padding: 80px 20px;
  
  h2, h3 {
    color: #333;
    margin-bottom: 16px;
  }
  
  p {
    color: #666;
    margin-bottom: 24px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
  }
}

.noProductsIcon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.backButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  
  &:hover {
    background: #45a049;
    transform: translateY(-2px);
  }
}

/* =================================================================
   RESPONSIVE DESIGN
================================================================= */

/* -----------------------------------------------------------------
   TABLET RESPONSIVE (769px - 1024px)
----------------------------------------------------------------- */
@media (max-width: 1024px) and (min-width: 769px) {
  .productsGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }

  .categoryTitle {
    font-size: 2rem;
  }

  .categoryDescription {
    font-size: 1rem;
  }

  .categoryIcon {
    width: 60px;
    height: 60px;

    img {
      width: 40px;
      height: 40px;
    }
  }
}

/* -----------------------------------------------------------------
   MOBILE RESPONSIVE (0px - 768px)
----------------------------------------------------------------- */
@media (max-width: 768px) {
  .categoryHeader {
    padding: 30px 0;
    margin-bottom: 30px;
  }

  .categoryInfo {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .categoryTitle {
    font-size: 1.8rem;
  }

  .categoryDescription {
    font-size: 0.9rem;
  }

  .categoryIcon {
    width: 60px;
    height: 60px;
    margin: 0 auto;

    img {
      width: 40px;
      height: 40px;
    }
  }

  .productsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 0 16px;
  }

  .productCard {
    height: auto;
    min-height: 240px;
  }

  .sectionHeader {
    h2 {
      font-size: 1.8rem;
    }

    p {
      font-size: 1rem;
    }
  }

  .paginationContainer {
    margin-top: 20px;
    padding: 15px 0;
  }

  .paginationContainer :global(.ant-pagination) {
    flex-wrap: wrap;
    justify-content: center;
  }

  .paginationContainer :global(.ant-pagination-total-text) {
    order: -1;
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
    margin-right: 0;
  }
}

/* -----------------------------------------------------------------
   SMALL MOBILE (0px - 375px)
----------------------------------------------------------------- */
@media (max-width: 375px) {
  .categoryHeader {
    padding: 20px 0;
  }

  .categoryTitle {
    font-size: 1.5rem;
  }

  .categoryDescription {
    font-size: 0.85rem;
  }

  .productsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .productCard {
    min-height: 220px;
  }

  .productName {
    font-size: 12px;
  }

  .currentPrice {
    font-size: 14px;
  }
}

/* -----------------------------------------------------------------
   ERROR STATE
----------------------------------------------------------------- */
.error {
  text-align: center;
  padding: 60px 20px;
  color: #666;

  .errorIcon {
    font-size: 3rem;
    margin-bottom: 16px;
  }

  h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 12px;
  }

  p {
    font-size: 1rem;
    margin-bottom: 24px;
    line-height: 1.5;
  }
}

.retryButton {
  padding: 12px 24px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #45a049;
    transform: translateY(-1px);
  }
}

/* =================================================================
   FILTER STYLES
================================================================= */

/* -----------------------------------------------------------------
   FILTER CONTROLS
----------------------------------------------------------------- */
.filterControls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 1px solid #e0e0e0;
}

.filterLeft {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filterToggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #45a049;
    transform: translateY(-1px);
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.productCount {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.filterRight {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sortSelect {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s;

  &:focus {
    outline: none;
    border-color: #4CAF50;
  }
}

/* -----------------------------------------------------------------
   FILTER SIDEBAR
----------------------------------------------------------------- */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 320px;
  height: 100vh;
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
}

.sidebarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;

  h2 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin: 0;
  }
}

.sidebarClose {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;

  &:hover {
    background: #e0e0e0;
  }

  svg {
    width: 20px;
    height: 20px;
    color: #666;
  }
}

.filterSection {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.filterTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.filterItem {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;

  input[type="checkbox"],
  input[type="radio"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: #4CAF50;
  }

  label {
    font-size: 0.9rem;
    color: #666;
    cursor: pointer;
    flex: 1;
    transition: color 0.3s;

    &:hover {
      color: #333;
    }
  }
}

.clearFiltersButton {
  margin: 20px;
  padding: 12px 24px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  width: calc(100% - 40px);

  &:hover {
    background: #d32f2f;
    transform: translateY(-1px);
  }
}

/* -----------------------------------------------------------------
   FILTER RESPONSIVE STYLES
----------------------------------------------------------------- */
@media (max-width: 768px) {
  .filterControls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .filterLeft,
  .filterRight {
    justify-content: center;
  }

  .sidebar {
    width: 100%;
    max-width: 320px;
  }

  .filterToggle {
    width: 100%;
    justify-content: center;
  }

  .sortSelect {
    width: 100%;
  }
}

@media (max-width: 375px) {
  .sidebar {
    width: 100vw;
    max-width: none;
  }

  .filterSection {
    padding: 16px;
  }

  .sidebarHeader {
    padding: 16px;
  }
}
