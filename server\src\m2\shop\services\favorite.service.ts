import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from '../entities/products.entity';
import { FavoriteProduct } from '../entities/favorite.entity';

@Injectable()
export class FavoriteService {
  constructor(
    @InjectRepository(FavoriteProduct)
    private readonly favoriteRepo: Repository<FavoriteProduct>,
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,
  ) {}

  // Lấy danh sách sản phẩm yêu thích của user
  async getFavorites(user_id: string) {
    const favorites = await this.favoriteRepo.find({ where: { user_id } });
    const productIds = favorites.map((f) => f.product_id);
    if (!productIds.length) return [];
    return this.productRepo.findByIds(productIds);
  }

  // Thích hoặc bỏ thích sản phẩm
  async toggleFavorite(user_id: string, product_id: string) {
    try {
      // Kiểm tra product_id hợp lệ và tồn tại
      if (!product_id || typeof product_id !== 'string') {
        throw new BadRequestException('product_id không hợp lệ');
      }
      console.log('Toggling favorite for product:', product_id, 'for user:', user_id);
      const product = await this.productRepo.findOne({ where: { id: product_id } });
      if (!product) {
        throw new NotFoundException('Không tìm thấy sản phẩm');
      }
      const existed = await this.favoriteRepo.findOne({ where: { user_id, product_id } });
      if (existed) {
        await this.favoriteRepo.delete({ user_id, product_id });
        return { liked: false };
      } else {
        await this.favoriteRepo.save(this.favoriteRepo.create({ user_id, product_id }));
        return { liked: true };
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error?.message || 'Lỗi thao tác yêu thích sản phẩm');
    }
  }
}
