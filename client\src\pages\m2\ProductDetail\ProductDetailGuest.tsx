import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Button, Card, message, Rate, Divider, Tag } from 'antd'
import { FaHeart, FaRegHeart, FaShoppingCart, FaQrcode } from 'react-icons/fa'
import { IoChevronBack, IoChevronForward } from 'react-icons/io5'
import { LoginOutlined, UserAddOutlined } from '@ant-design/icons'
import { useGetProductDetailHook } from '@/hooks/product/useproduct'
import { ROUTES } from '@/constants/routes'
import styles from './ProductDetail.module.scss'
import logo from '@/assets/images/logo.png'

const ProductDetailGuest: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [selectedVersion, setSelectedVersion] = useState<any>(null)
  const [quantity, setQuantity] = useState(1)

  const { data: product, isLoading, error } = useGetProductDetailHook(id || '')

  useEffect(() => {
    if (product?.product_versions && product.product_versions.length > 0) {
      setSelectedVersion(product.product_versions[0])
    }
  }, [product])

  const handleImageNavigation = (direction: 'prev' | 'next') => {
    if (!product?.product_images || product.product_images.length === 0) return
    
    const totalImages = product.product_images.length
    if (direction === 'prev') {
      setCurrentImageIndex((prev) => (prev - 1 + totalImages) % totalImages)
    } else {
      setCurrentImageIndex((prev) => (prev + 1) % totalImages)
    }
  }

  const handleVersionSelect = (version: any) => {
    setSelectedVersion(version)
    const versionImageIndex = (product?.product_images ?? []).findIndex((img: any) => 
      (img?.image_url ?? '') === (version?.image_url ?? '')
    )
    if (versionImageIndex !== -1) {
      setCurrentImageIndex(versionImageIndex)
    }
  }

  const handleLoginRequired = (action: string) => {
    message.info(`Vui lòng đăng nhập để ${action}`)
    navigate(ROUTES.LOGIN, { state: { from: `/san-pham/${id}` } })
  }

  const handleLogin = () => {
    navigate(ROUTES.LOGIN, { state: { from: `/san-pham/${id}` } })
  }

  const handleRegister = () => {
    navigate(ROUTES.REGISTER, { state: { from: `/san-pham/${id}` } })
  }

  const formatPrice = (price: string) => {
    return new Intl.NumberFormat('vi-VN').format(parseInt(price))
  }

  const renderStars = (rating: number) => {
    return <Rate disabled defaultValue={rating} style={{ fontSize: 14 }} />
  }

  if (isLoading) {
    return (
      <div className={styles.productDetailContainer}>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          Đang tải thông tin sản phẩm...
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className={styles.productDetailContainer}>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          Không tìm thấy sản phẩm
        </div>
      </div>
    )
  }

  const currentImage = product.product_images?.[currentImageIndex]?.image_url || product.thumbnail_url || logo

  return (
    <div className={styles.productDetailContainer}>
      <div className={styles.productContent}>
        {/* Product Images */}
        <div className={styles.productImages}>
          <div className={styles.mainImage}>
            <img src={currentImage} alt={product.name} />
            {product.product_images && product.product_images.length > 1 && (
              <>
                <button 
                  className={styles.navButton} 
                  onClick={() => handleImageNavigation('prev')}
                >
                  <IoChevronBack />
                </button>
                <button 
                  className={styles.navButton} 
                  onClick={() => handleImageNavigation('next')}
                >
                  <IoChevronForward />
                </button>
              </>
            )}
          </div>
          
          {product.product_images && product.product_images.length > 1 && (
            <div className={styles.thumbnailImages}>
              {product.product_images.map((image: any, index: number) => (
                <img
                  key={index}
                  src={image.image_url}
                  alt={`${product.name} ${index + 1}`}
                  className={currentImageIndex === index ? styles.active : ''}
                  onClick={() => setCurrentImageIndex(index)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className={styles.productInfo}>
          <h1 className={styles.productName}>{product.name}</h1>
          
          <div className={styles.productRating}>
            {renderStars(4.5)} {/* Mock rating */}
            <span className={styles.ratingText}>4.5 (123 đánh giá)</span>
          </div>

          <div className={styles.productPrice}>
            <span className={styles.currentPrice}>
              {formatPrice(selectedVersion?.price || product.price)}đ
            </span>
            {product.product_versions && product.product_versions.length > 1 && (
              <div className={styles.versionSelector}>
                <h4>Phân loại:</h4>
                <div className={styles.versionOptions}>
                  {product.product_versions.map((version: any) => (
                    <button
                      key={version.id}
                      className={`${styles.versionOption} ${
                        selectedVersion?.id === version.id ? styles.selected : ''
                      }`}
                      onClick={() => handleVersionSelect(version)}
                    >
                      {version.name}
                    </button>
                  ))}
                </div>
              </div>
            )}

            <div className={styles.quantitySelector}>
              <h4>Số lượng:</h4>
              <div className={styles.quantityControls}>
                <button 
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  disabled={quantity <= 1}
                >
                  -
                </button>
                <span>{quantity}</span>
                <button onClick={() => setQuantity(quantity + 1)}>+</button>
              </div>
            </div>

            <div className={styles.stockInfo}>
              <Tag color="green">Còn hàng</Tag>
              <span>Kho: {selectedVersion?.stock || product.stock} {product.unit}</span>
            </div>
          </div>

          <div className={styles.productActions}>
            <Button 
              type="primary" 
              size="large" 
              icon={<FaShoppingCart />}
              onClick={() => handleLoginRequired('thêm vào giỏ hàng')}
              className={styles.addToCartBtn}
            >
              Thêm vào giỏ
            </Button>
            
            <Button 
              size="large"
              onClick={() => handleLoginRequired('mua ngay')}
              className={styles.buyNowBtn}
            >
              Mua ngay
            </Button>

            <Button 
              type="text" 
              icon={<FaRegHeart />}
              onClick={() => handleLoginRequired('thêm vào yêu thích')}
              className={styles.favoriteBtn}
            />
          </div>

          <Divider />

          <Card className={styles.loginPrompt}>
            <div style={{ textAlign: 'center' }}>
              <h3>Đăng nhập để mua sắm</h3>
              <p>Đăng nhập để thêm vào giỏ hàng, yêu thích và đặt hàng</p>
              <div style={{ display: 'flex', gap: 12, justifyContent: 'center', marginTop: 16 }}>
                <Button
                  type="primary"
                  icon={<LoginOutlined />}
                  onClick={handleLogin}
                >
                  Đăng nhập
                </Button>
                <Button
                  type="primary"
                  ghost
                  icon={<UserAddOutlined />}
                  onClick={handleRegister}
                >
                  Đăng ký
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Product Description */}
      <div className={styles.productDescription}>
        <h2>Mô tả sản phẩm</h2>
        <div dangerouslySetInnerHTML={{ __html: String(product.description || '') }} />
      </div>
    </div>
  )
}

export default ProductDetailGuest 