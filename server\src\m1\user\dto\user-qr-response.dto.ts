import { ApiProperty } from '@nestjs/swagger';

export class UserQRResponseDto {
  @ApiProperty() id: string;

  @ApiProperty() qrCode: string;

  @ApiProperty() qrType: string;

  @ApiProperty({ required: false }) description?: string;

  @ApiProperty({ required: false }) linkUrl?: string;

  @ApiProperty() isActive: boolean;

  @ApiProperty() createdAt: Date;

  @ApiProperty() updatedAt: Date;

  // Rút gọn thông tin user liên kết
  @ApiProperty({
    type: 'object',
    properties: {
      uidCode: { type: 'string' },
      fullName: { type: 'string' },
      email: { type: 'string' },
      phoneNumber: { type: 'string' },
      role: { type: 'string' },
    },
  })
  user: {
    uidCode: string;
    fullName?: string;
    email?: string;
    phoneNumber?: string;
    role?: string;
  };
}
