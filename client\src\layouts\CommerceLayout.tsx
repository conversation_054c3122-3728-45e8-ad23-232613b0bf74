import classNames from 'classnames/bind'

import NavbarHomepage from '@/layouts/HomePageLayout/Navbar'
import FooterHomepage from '@/layouts/HomePageLayout/Footer'
import type { ReactNode } from 'react'
import styles from './CommerceLayout.module.scss'
import { Outlet } from 'react-router-dom'

const cx = classNames.bind(styles)

// type CommerceLayoutProps = {
//     children: ReactNode
// }

const CommerceLayout = () => {
    return (
        <div className={cx('wrapper')}>
            <NavbarHomepage />
            <div className={cx('content')}>
                <Outlet />
            </div>
            <FooterHomepage />
        </div>
    )
}
export default CommerceLayout

