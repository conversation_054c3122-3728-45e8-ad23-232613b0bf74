import React from 'react'
import { Card, Typography, Alert, Space, Button, List } from 'antd'
import { 
  EnvironmentOutlined, 
  InfoCircleOutlined,
  WifiOutlined,
  MobileOutlined,
  HomeOutlined,
  SettingOutlined
} from '@ant-design/icons'

const { Text, Title } = Typography

interface LocationTroubleshootProps {
  onClose: () => void
  errorCode?: number
}

const LocationTroubleshoot: React.FC<LocationTroubleshootProps> = ({ onClose, errorCode }) => {
  const getTroubleshootSteps = () => {
    const steps = [
      {
        icon: <MobileOutlined />,
        title: 'Kiểm tra GPS',
        description: 'Đ<PERSON>m bảo GPS được bật trên thiết bị của bạn',
        actions: [
          'Mở Cài đặt > Vị trí',
          '<PERSON>ật "Vị trí" hoặc "GPS"',
          'Chọn "Độ chính xác cao" nếu có'
        ]
      },
      {
        icon: <HomeOutlined />,
        title: '<PERSON> chuyển ra ngoài',
        description: 'GPS hoạt động tốt hơn ở ngoài trời',
        actions: [
          'Thử ở nơi có tín hiệu GPS tốt',
          '<PERSON><PERSON><PERSON><PERSON> các tòa nhà cao tầng',
          'Đợi vài giây để GPS định vị'
        ]
      },
      {
        icon: <WifiOutlined />,
        title: 'Kiểm tra kết nối mạng',
        description: 'Cần kết nối internet để xác định vị trí',
        actions: [
          'Kiểm tra kết nối WiFi hoặc 4G/5G',
          'Thử tắt/bật lại WiFi',
          'Kiểm tra tín hiệu mạng'
        ]
      },
      {
        icon: <SettingOutlined />,
        title: 'Kiểm tra quyền trình duyệt',
        description: 'Đảm bảo trình duyệt cho phép truy cập vị trí',
        actions: [
          'Nhấn vào biểu tượng khóa 🔒 ở thanh địa chỉ',
          'Chọn "Cho phép" trong phần "Vị trí"',
          'Hoặc vào Cài đặt trình duyệt > Vị trí'
        ]
      }
    ]

    return steps
  }

  const getErrorMessage = () => {
    switch (errorCode) {
      case 1:
        return 'Quyền truy cập vị trí bị từ chối'
      case 2:
        return 'Không thể xác định vị trí hiện tại'
      case 3:
        return 'Hết thời gian chờ lấy vị trí'
      default:
        return 'Có lỗi xảy ra khi lấy vị trí'
    }
  }

  return (
    <Card 
      title={
        <Space>
          <EnvironmentOutlined />
          <span>Khắc phục lỗi lấy vị trí</span>
        </Space>
      }
      extra={<Button type="text" onClick={onClose}>✕</Button>}
      style={{ marginBottom: 16 }}
    >
      <Alert
        message={getErrorMessage()}
        description="Hãy thử các giải pháp bên dưới để khắc phục vấn đề."
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <List
        dataSource={getTroubleshootSteps()}
        renderItem={(step, index) => (
          <List.Item>
            <div style={{ width: '100%' }}>
              <Space align="start" style={{ width: '100%', marginBottom: 8 }}>
                {step.icon}
                <div style={{ flex: 1 }}>
                  <Text strong>{step.title}</Text>
                  <br />
                  <Text type="secondary">{step.description}</Text>
                </div>
              </Space>
              
              <List
                size="small"
                dataSource={step.actions}
                renderItem={(action) => (
                  <List.Item style={{ padding: '4px 0' }}>
                    <Text>• {action}</Text>
                  </List.Item>
                )}
                style={{ marginLeft: 24, marginTop: 8 }}
              />
            </div>
          </List.Item>
        )}
      />

      <Alert
        message="Lưu ý"
        description="Sau khi thực hiện các bước trên, hãy thử lại chức năng lấy vị trí. Nếu vẫn gặp lỗi, có thể do thiết bị không hỗ trợ GPS hoặc đang ở khu vực có tín hiệu yếu."
        type="info"
        showIcon
        style={{ marginTop: 16 }}
      />
    </Card>
  )
}

export default LocationTroubleshoot 