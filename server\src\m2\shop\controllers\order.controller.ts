import { Body, Controller, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { OrderService } from '../services/order.service';
import { UserBehaviorLogService } from '../../../m_log/log/user-behavior-log.service';
import { AuthGuard } from '@nestjs/passport';
import { CreateOrderFullDto } from '../dto/create-order-full.dto';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { OrderListItemDto } from '../dto/order-list-item.dto';

@ApiTags('Orders')
@Controller('orders')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
export class OrderController {
  constructor(
    private readonly orderService: OrderService,
    private readonly userBehaviorLogService: UserBehaviorLogService,
  ) {}

  @Get('me')
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách đơn hàng của user hiện tại' })
  @ApiResponse({ status: 200, type: [OrderListItemDto] })
  async getMyOrders(@Req() req: any): Promise<OrderListItemDto[]> {
    const userId = req.user?.userId;
    this.userBehaviorLogService.emitUserBehavior({
      user_id: userId,
      event_type: 'search',
      target_type: 'order',
      product_id: null,
      event_data: {},
      description: 'User searched order list',
    });
    return this.orderService.getOrderListForUser(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết đơn hàng theo id' })
  @ApiResponse({ status: 200 })
  async getOrder(@Param('id') id: string, @Req() req: any) {
    const userId = req.user?.userId;
    this.userBehaviorLogService.emitUserBehavior({
      user_id: userId,
      event_type: 'view',
      target_type: 'order',
      target_id: id,
      product_id: null,
      event_data: {},
      description: 'User viewed order detail',
    });
    return this.orderService.findOneForUser(id, userId);
  }

  @Post()
  @ApiOperation({ summary: 'Tạo đơn hàng mới (user)' })
  @ApiResponse({ status: 201 })
  async createOrder(@Body() dto: CreateOrderFullDto, @Req() req: any) {
    const userId = req.user?.userId;
    const order = await this.orderService.createOrder(userId, dto);
    this.userBehaviorLogService.emitUserBehavior({
      user_id: userId,
      event_type: 'create',
      target_type: 'order',
      target_id: order?.id,
      product_id: null,
      event_data: { dto },
      description: 'User created an order',
    });
    return order;
  }

  /**
   * API yêu cầu hủy đơn hàng (chỉ cho phép khi trạng thái là PENDING, CONFIRMED, PACKING)
   */
  @Post(':id/request-cancel')
  @ApiOperation({ summary: 'Yêu cầu hủy đơn hàng (user)' })
  @ApiResponse({ status: 200 })
  async requestCancelOrder(
    @Req() req: any,
    @Param('id') orderId: string,
    @Body('reason') reason?: string,
  ) {
    const userId = req.user?.userId;
    const result = await this.orderService.requestCancelOrder(orderId, userId, reason);
    this.userBehaviorLogService.emitUserBehavior({
      user_id: userId,
      event_type: 'request_cancel',
      target_type: 'order',
      target_id: orderId,
      description: 'User requested order cancel',
    });
    return result;
  }

  /**
   * API xác nhận đã nhận được hàng (chỉ cho phép khi trạng thái là DELIVERED)
   */
  @Post(':id/confirm-received')
  @ApiOperation({ summary: 'Xác nhận đã nhận hàng (user)' })
  @ApiResponse({ status: 200 })
  async confirmReceivedOrder(@Req() req: any, @Param('id') orderId: string) {
    const userId = req.user?.userId;
    const result = await this.orderService.confirmReceivedOrder(orderId, userId);
    this.userBehaviorLogService.emitUserBehavior({
      user_id: userId,
      event_type: 'confirm_received',
      target_type: 'order',
      target_id: orderId,
      description: 'User confirmed received order',
    });
    return result;
  }
}
