.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: 12px;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    margin-bottom: 16px;
  }

  &Title {
    h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      color: #262626;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 22px;
      }
    }

    p {
      margin: 4px 0 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

.filterCard {
  margin-bottom: 24px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-body) {
    padding: 20px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }
}

.filterHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

.filterTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #262626;
  font-weight: 500;
  font-size: 16px;

  @media (max-width: 768px) {
    font-size: 14px;
  }
}

.filterActions {
  display: flex;
  align-items: center;
  gap: 12px;

  @media (max-width: 768px) {
    width: 100%;
  }
}

.dateRangePicker {
  border-radius: 8px;

  :global(.ant-picker) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: 36px;
    transition: all 0.2s ease;

    &:focus,
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  @media (max-width: 768px) {
    width: 100%;
  }
}

.quickFilters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  @media (max-width: 768px) {
    gap: 6px;
  }
}

.quickFilterButton {
  border-radius: 8px;
  height: 32px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }

  @media (max-width: 768px) {
    height: 28px;
    font-size: 12px;
    padding: 0 8px;
  }
}

// Stats Cards
.statsRow {
  margin-bottom: 24px;

  @media (max-width: 768px) {
    display: none;
  }
}

// Mobile Stats
.mobileStatsContainer {
  margin-bottom: 16px;

  @media (min-width: 769px) {
    display: none;
  }

  :global(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header) {
    padding: 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    margin-bottom: 8px;
  }

  :global(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box) {
    padding: 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
  }
}

.statCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  height: 100%;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  &.clickable {
    cursor: pointer;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
      border-color: #1890ff;
    }
  }

  :global(.ant-card-body) {
    padding: 20px;
    height: 100%;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }
}

.statContent {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  height: 100%;

  @media (max-width: 768px) {
    gap: 12px;
  }
}

.statIcon {
  font-size: 28px;
  flex-shrink: 0;

  @media (max-width: 768px) {
    font-size: 24px;
  }
}

.statInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.statTitle {
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;

  @media (max-width: 768px) {
    font-size: 12px;
  }
}

.statValue {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 8px;

  @media (max-width: 768px) {
    font-size: 18px;
  }
}

.statSuffix {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 400;
  margin-left: 4px;
}

.statChange {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;

  @media (max-width: 768px) {
    font-size: 11px;
  }
}

.upArrow {
  color: #52c41a;
}

.downArrow {
  color: #ff4d4f;
}

.positive {
  color: #52c41a;
  font-weight: 500;
}

.negative {
  color: #ff4d4f;
  font-weight: 500;
}

.neutral {
  color: #8c8c8c;
  font-weight: 500;
}

.compareText {
  color: #8c8c8c;
  font-size: 11px;
}

// Revenue Card
.revenueCard {
  margin-bottom: 24px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-body) {
    padding: 20px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }
}

.revenueItem {
  text-align: center;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
  
  @media (max-width: 768px) {
    padding: 12px;
    margin-bottom: 12px;
  }
}

.revenueLabel {
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;

  @media (max-width: 768px) {
    font-size: 12px;
  }
}

.revenueValue {
  font-size: 20px;
  font-weight: 600;

  @media (max-width: 768px) {
    font-size: 16px;
  }
}

// Charts
.chartsRow {
  margin-bottom: 24px;

  @media (max-width: 768px) {
    margin-bottom: 16px;
  }
}

.chartCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 100%;

  :global(.ant-card-head) {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    min-height: auto;
  }

  :global(.ant-card-head-title) {
    color: #262626;
    font-weight: 600;
    font-size: 16px;

    @media (max-width: 768px) {
      font-size: 14px;
    }
  }

  :global(.ant-card-body) {
    padding: 20px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }
}

// AOV Card
.aovCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  height: 100%;

  :global(.ant-card-body) {
    padding: 24px;
    height: 100%;

    @media (max-width: 768px) {
      padding: 20px;
    }
  }
}

.aovContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;

  @media (max-width: 768px) {
    gap: 12px;
  }
}

.aovHeader {
  display: flex;
  align-items: center;
  gap: 12px;
}

.aovIcon {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.9);

  @media (max-width: 768px) {
    font-size: 20px;
  }
}

.aovTitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;

  @media (max-width: 768px) {
    font-size: 12px;
  }
}

.aovValue {
  font-size: 28px;
  font-weight: 600;
  color: white;
  margin: 8px 0;

  @media (max-width: 768px) {
    font-size: 22px;
  }
}

.aovDescription {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 8px;

  @media (max-width: 768px) {
    font-size: 11px;
  }
}

.aovTarget {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin-top: 8px;

  @media (max-width: 768px) {
    font-size: 11px;
  }
}

// Payment Method List
.paymentMethodList {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;

  @media (max-width: 768px) {
    margin-top: 12px;
    gap: 6px;
  }
}

.paymentMethodItem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;

  @media (max-width: 768px) {
    padding: 6px 8px;
    gap: 6px;
  }
}

.paymentMethodColor {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;

  @media (max-width: 768px) {
    width: 10px;
    height: 10px;
  }
}

.paymentMethodName {
  color: #262626;
  font-weight: 500;
  font-size: 13px;
  flex: 1;

  @media (max-width: 768px) {
    font-size: 12px;
  }
}

.paymentMethodValue {
  color: #595959;
  font-size: 12px;

  @media (max-width: 768px) {
    font-size: 11px;
  }
}

.paymentMethodPercentage {
  color: #1890ff;
  font-weight: 600;
  font-size: 12px;
  min-width: 40px;
  text-align: right;

  @media (max-width: 768px) {
    font-size: 11px;
    min-width: 35px;
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .container {
    padding: 8px;
  }

  .header {
    margin-bottom: 12px;

    &Title h1 {
      font-size: 20px;
    }
  }

  .filterCard :global(.ant-card-body) {
    padding: 12px;
  }

  .quickFilters {
    gap: 4px;
  }

  .quickFilterButton {
    height: 24px;
    font-size: 11px;
    padding: 0 6px;
  }

  .statsRow {
    margin-bottom: 12px;
  }

  .revenueCard :global(.ant-card-body) {
    padding: 12px;
  }

  .chartCard :global(.ant-card-body) {
    padding: 12px;
  }

  .aovCard :global(.ant-card-body) {
    padding: 16px;
  }

  .statCard :global(.ant-card-body) {
    padding: 12px;
  }

  .statContent {
    gap: 8px;
  }

  .statIcon {
    font-size: 20px;
  }

  .statValue {
    font-size: 16px;
  }
}

// Chart customizations
:global(.recharts-wrapper) {
  .recharts-cartesian-axis-tick-value {
    font-size: 12px;
    fill: #8c8c8c;
  }

  .recharts-tooltip-wrapper {
    .recharts-default-tooltip {
      background-color: rgba(255, 255, 255, 0.95);
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  .recharts-legend-wrapper {
    .recharts-default-legend {
      .recharts-legend-item {
        margin-right: 10px;
      }
    }
  }
}
