.container {
  // padding: 24px;
  // background-color: #f5f5f5;
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: 12px;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
  }

  &Title {
    h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      color: #262626;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 22px;
      }
    }

    p {
      margin: 4px 0 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  &Actions {
    display: flex;
    gap: 12px;

    @media (max-width: 768px) {
      width: 100%;
      justify-content: space-between;
      gap: 8px;
    }
  }
}

.actionBtn {
  border-radius: 8px;
  height: 36px;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  color: #595959;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    background-color: #f8f9ff;
  }

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  @media (max-width: 768px) {
    height: 32px;
    padding: 0 8px;
    font-size: 12px;
  }
}

.addBtn {
  border-radius: 8px;
  height: 36px;
  background-color: #1890ff;
  border-color: #1890ff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transition: all 0.2s ease;

  &:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    transform: translateY(-1px);
  }

  &:focus {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  @media (max-width: 768px) {
    height: 32px;
    padding: 0 12px;
    font-size: 14px;
    flex: 1;
  }
}

// Mobile Search Container
.mobileSearchContainer {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;

  @media (min-width: 769px) {
    display: none;
  }
}

.mobileSearchInput {
  flex: 1;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  height: 36px;
  transition: all 0.2s ease;

  &:focus,
  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;
  }
}

.mobileFilterButton {
  border-radius: 8px;
  height: 36px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  color: #595959;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    background-color: #f8f9ff;
  }
}

// Mobile Stats
.mobileStatsContainer {
  margin-bottom: 16px;

  @media (min-width: 769px) {
    display: none;
  }

  :global(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header) {
    padding: 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    margin-bottom: 8px;
  }

  :global(
    .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box
  ) {
    padding: 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
  }
}

.mobileStatCard {
  text-align: center;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.mobileStatValue {
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.mobileStatLabel {
  font-size: 12px;
  color: #8c8c8c;
}

.statsRow {
  margin-bottom: 24px;

  @media (max-width: 768px) {
    display: none;
  }
}

.statCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  :global(.ant-card-body) {
    padding: 20px;
  }

  :global(.ant-statistic-title) {
    color: #8c8c8c;
    font-weight: 500;
    margin-bottom: 8px;
  }

  :global(.ant-statistic-content) {
    font-size: 24px;
    font-weight: 600;
  }
}

.filterCard {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-body) {
    padding: 20px;
  }

  @media (max-width: 768px) {
    display: none;
  }
}

.filtersContainer {
  :global(.ant-row) {
    gap: 8px;
  }
}

.searchInput {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  height: 36px;
  transition: all 0.2s ease;

  &:focus,
  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;
  }
}

.filterSelect {
  width: 100%;
  border-radius: 8px;

  :global(.ant-select-selector) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: 36px;
    transition: all 0.2s ease;

    &:focus,
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

.filterDrawer {
  :global(.ant-drawer-body) {
    padding: 16px;
  }
}

.drawerActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;

  button {
    border-radius: 8px;
    height: 36px;
  }
}

.bulkCard {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #1890ff;
  background-color: #f8f9ff;

  :global(.ant-card-body) {
    padding: 16px 20px;

    @media (max-width: 768px) {
      padding: 12px 16px;
    }
  }
}

.bulkActions {
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  span {
    color: #1890ff;
    font-weight: 500;
    font-size: 14px;

    @media (max-width: 768px) {
      font-size: 12px;
    }
  }
}

.tableCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-body) {
    padding: 0;
  }
}

.productTable {
  :global(.ant-table) {
    border-radius: 12px;
    overflow: hidden;
  }

  :global(.ant-table-thead > tr > th) {
    background-color: #fafafa;
    border-bottom: 1px solid #f5f5f5;
    color: #262626;
    font-weight: 600;
    padding: 16px;

    &:first-child {
      border-top-left-radius: 12px;
    }

    &:last-child {
      border-top-right-radius: 12px;
    }

    @media (max-width: 768px) {
      padding: 12px 8px;
      font-size: 12px;
    }
  }

  :global(.ant-table-tbody > tr > td) {
    padding: 16px;
    border-bottom: 1px solid #f5f5f5;

    @media (max-width: 768px) {
      padding: 12px 8px;
    }
  }

  :global(.ant-table-tbody > tr:hover > td) {
    background-color: #f8f9ff;
  }

  :global(.ant-table-pagination) {
    padding: 16px 24px;
    border-top: 1px solid #f5f5f5;

    @media (max-width: 768px) {
      padding: 12px 16px;
    }
  }

  @media (max-width: 768px) {
    :global(.ant-table-pagination-options) {
      display: none;
    }
  }
}

// Mobile Product Card
.mobileProductCard {
  width: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background-color: #ffffff;
  padding: 12px;
  margin-bottom: 8px;
}

.mobileProductHeader {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.productImageMobile {
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.mobileProductInfo {
  flex: 1;
  min-width: 0;
}

.mobileProductMeta {
  display: flex;
  gap: 8px;
  align-items: center;
  margin: 4px 0;
}

.skuMobile {
  background-color: #f5f5f5;
  color: #595959;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-family: 'Courier New', monospace;
}

.categoryMobile {
  color: #8c8c8c;
  font-size: 12px;
}

.mobilePriceContainer {
  display: flex;
  gap: 8px;
  align-items: center;
}

.originalPriceMobile {
  color: #8c8c8c;
  text-decoration: line-through;
  font-size: 12px;
}

.salePriceMobile {
  color: #ff4d4f;
  font-weight: 600;
  font-size: 14px;
}

.priceMobile {
  color: #262626;
  font-weight: 600;
  font-size: 14px;
}

.mobileProductFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f5f5f5;
}

.mobileStatus {
  display: flex;
  gap: 8px;
  align-items: center;
}

.mobileActions {
  display: flex;
  gap: 4px;
}

.mobileActionButton {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 12px;

  &:hover {
    background-color: #f8f9ff;
    border-color: #1890ff;
    color: #1890ff;
  }

  &.danger:hover {
    background-color: #fff2f0;
    border-color: #ff4d4f;
    color: #ff4d4f;
  }
}

.productImage {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.productName {
  font-weight: 500;
  color: #262626;
  line-height: 1.4;

  @media (max-width: 768px) {
    font-size: 14px;
  }
}

.sku {
  background-color: #f5f5f5;
  color: #595959;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
}

.priceContainer {
  .originalPrice {
    color: #8c8c8c;
    text-decoration: line-through;
    font-size: 12px;
    display: block;
    margin-bottom: 2px;
  }

  .salePrice {
    color: #ff4d4f;
    font-weight: 600;
    font-size: 14px;
  }
}

.outOfStock {
  color: #ff4d4f;
  font-weight: 600;
}

.inStock {
  color: #52c41a;
  font-weight: 500;
}

.actionButton {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9ff;
    border-color: #1890ff;
    color: #1890ff;
  }

  &.danger:hover {
    background-color: #fff2f0;
    border-color: #ff4d4f;
    color: #ff4d4f;
  }
}

.productModal {
  :global(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :global(.ant-modal-header) {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 20px 24px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }

  :global(.ant-modal-title) {
    font-size: 18px;
    font-weight: 600;
    color: #262626;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }

  :global(.ant-modal-body) {
    padding: 24px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }

  @media (max-width: 768px) {
    :global(.ant-modal) {
      margin: 0;
      max-width: 100%;
      height: 100vh;
      border-radius: 0;
    }

    :global(.ant-modal-content) {
      height: 100vh;
      border-radius: 0;
      display: flex;
      flex-direction: column;
    }

    :global(.ant-modal-body) {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
    }
  }
}

.productForm {
  :global(.ant-form-item-label > label) {
    color: #262626;
    font-weight: 500;
  }

  :global(.ant-form-item) {
    margin-bottom: 16px;

    @media (max-width: 768px) {
      margin-bottom: 12px;
    }
  }

  :global(.ant-input),
  :global(.ant-select-selector),
  :global(.ant-input-number) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: 36px;
    transition: all 0.2s ease;

    &:focus,
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    @media (max-width: 768px) {
      height: 40px;
    }
  }

  :global(.ant-input-number) {
    width: 100%;
  }
}

.modalActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;

  @media (max-width: 768px) {
    margin-top: 16px;
    padding-top: 16px;
    justify-content: space-between;
  }

  button {
    border-radius: 8px;
    height: 36px;
    padding: 0 16px;
    font-weight: 500;

    @media (max-width: 768px) {
      height: 40px;
      flex: 1;
    }
  }
}

// ==== PRODUCT DETAIL MODAL STYLES - REDESIGNED ====

.productDetailModal {
  :global(.ant-modal-content) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  }

  :global(.ant-modal-header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 20px 24px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }

  :global(.ant-modal-title) {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }

  :global(.ant-modal-close) {
    color: #ffffff;

    &:hover {
      color: #ffffff;
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
    }
  }

  :global(.ant-modal-body) {
    padding: 0;
    background-color: #f8f9fa;

    @media (max-width: 768px) {
      padding: 0;
    }
  }

  :global(.ant-modal-footer) {
    background-color: #ffffff;
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;

    @media (max-width: 768px) {
      padding: 12px 16px;
    }
  }

  @media (max-width: 768px) {
    :global(.ant-modal) {
      margin: 0;
      max-width: 100%;
      height: 100vh;
      border-radius: 0;
    }

    :global(.ant-modal-content) {
      height: 100vh;
      border-radius: 0;
      display: flex;
      flex-direction: column;
    }

    :global(.ant-modal-body) {
      flex: 1;
      overflow-y: auto;
    }
  }
}

.modalTitleContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.modalTitleIcon {
  font-size: 18px;
  color: #ffffff;
}

.modalFooterActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;

  @media (max-width: 768px) {
    gap: 8px;
    justify-content: space-between;

    button {
      flex: 1;
    }
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background-color: #ffffff;
}

.loadingSpinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f0f0f0;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.emptyContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background-color: #ffffff;
}

.productDetailContent {
  min-height: 500px;
}

.productTabs {
  :global(.ant-tabs-nav) {
    background-color: #ffffff;
    margin: 0;
    padding: 0 24px;
    border-bottom: 1px solid #f0f0f0;

    @media (max-width: 768px) {
      padding: 0 16px;
    }
  }

  :global(.ant-tabs-tab) {
    padding: 16px 8px;
    font-weight: 500;
    color: #595959;

    @media (max-width: 768px) {
      padding: 12px 4px;
      font-size: 12px;
    }

    &:hover {
      color: #1890ff;
    }
  }

  :global(.ant-tabs-tab-active) {
    color: #1890ff !important;
    font-weight: 600;
  }

  :global(.ant-tabs-ink-bar) {
    background-color: #1890ff;
    height: 3px;
  }

  :global(.ant-tabs-content-holder) {
    background-color: #ffffff;
  }
}

.tabContent {
  padding: 24px;
  min-height: 400px;

  @media (max-width: 768px) {
    padding: 16px;
  }
}

// Overview Tab Styles
.productImageSection {
  .mainImageContainer {
    margin-bottom: 16px;
  }

  .mainProductImage {
    width: 100%;
    max-width: 300px;
    height: 300px;
    object-fit: cover;
    border-radius: 12px;
    border: 1px solid #f0f0f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    @media (max-width: 768px) {
      max-width: 250px;
      height: 250px;
    }
  }

  .thumbnailContainer {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;

    @media (max-width: 768px) {
      gap: 4px;
    }
  }

  .thumbnailImage {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: #1890ff;
      transform: scale(1.05);
    }

    @media (max-width: 768px) {
      width: 50px;
      height: 50px;
    }
  }
}

.productInfo {
  .productHeader {
    margin-bottom: 24px;
  }

  .productTitle {
    margin-bottom: 8px !important;
    color: #262626;
    font-size: 24px;
    font-weight: 600;

    @media (max-width: 768px) {
      font-size: 20px;
    }
  }

  .productMeta {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-bottom: 16px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  }

  .statusBadge {
    :global(.ant-badge-status-text) {
      font-weight: 500;
      font-size: 14px;
    }
  }

  .productSku {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    background-color: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
  }
}

.productDescriptions {
  margin-bottom: 24px;

  :global(.ant-descriptions-item-label) {
    font-weight: 600;
    color: #262626;
    background-color: #fafafa;
  }

  :global(.ant-descriptions-item-content) {
    font-weight: 500;
  }
}

.priceText {
  font-size: 18px;
  color: #1890ff;
  font-weight: 600;
}

.productDescription {
  margin-top: 24px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  h5 {
    color: #262626;
    margin-bottom: 12px !important;
  }
}

.descriptionText {
  color: #595959;
  line-height: 1.6;
  margin-bottom: 0 !important;
}

// Attributes Tab Styles
.attributesList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attributeCard {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  }

  :global(.ant-card-body) {
    padding: 16px;
  }
}

// Versions Tab Styles
.versionsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.versionCard {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  }

  :global(.ant-card-body) {
    padding: 20px;
  }
}

.versionSku {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #8c8c8c;
}

.versionPrice {
  color: #1890ff;
  font-size: 16px;
}

// Images Tab Styles
.imageGallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }
}

.galleryItem {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
    transform: translateY(-2px);
  }
}

.galleryImage {
  width: 100%;
  height: 150px;
  object-fit: cover;

  @media (max-width: 768px) {
    height: 120px;
  }
}

.imageInfo {
  padding: 12px;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.imageId {
  font-size: 11px;
  font-family: 'Courier New', monospace;
  color: #8c8c8c;
}

// Mobile product card styles
.mobileProductCard {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  width: 100%;
  overflow: hidden;
  display: block !important;

  &:last-child {
    border-bottom: none;
  }

  // Override table cell behavior
  * {
    word-break: normal !important;
    white-space: normal !important;
    overflow-wrap: normal !important;
  }
}

.mobileProductHeader {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px;
}

.productImageMobile {
  border-radius: 6px;
  object-fit: cover;
}

.mobileProductInfo {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.mobileProductName {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #333 !important;
  margin-bottom: 4px !important;
  line-height: 1.3 !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  white-space: normal !important;
  display: block !important;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed !important;
  direction: ltr !important;
  unicode-bidi: normal !important;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

.mobileProductSku {
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Courier New', monospace;
  margin-bottom: 4px;
}

.mobileProductPrice {
  font-size: 14px;
  font-weight: 600;
  color: #ff4d4f;
}

.mobileProductDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.mobileProductCategory {
  font-size: 12px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.mobileActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.mobileActionButton {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;

  &:hover {
    transform: scale(1.05);
  }
}

// Additional mobile optimizations
@media (max-width: 480px) {
  .container {
    padding: 8px;
  }

  .header {
    margin-bottom: 12px;

    &Title h1 {
      font-size: 20px;
    }
  }

  .mobileSearchContainer {
    margin-bottom: 12px;
  }

  .mobileStatsContainer {
    margin-bottom: 12px;
  }

  .mobileStatCard {
    padding: 8px;
  }

  .mobileStatValue {
    font-size: 16px;
  }

  .mobileStatLabel {
    font-size: 11px;
  }

  .bulkCard :global(.ant-card-body) {
    padding: 8px 12px;
  }

  .bulkActions {
    span {
      font-size: 11px;
    }
  }

  .mobileProductCard {
    padding: 8px;
  }

  .mobileProductHeader {
    gap: 8px;
  }

  .productImageMobile {
    width: 32px;
    height: 32px;
  }

  .mobileActionButton {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }
  .productModal {
    :global(.ant-modal-header) {
      padding: 12px;
    }

    :global(.ant-modal-body) {
      padding: 12px;
    }
  }

  .modalActions {
    margin-top: 12px;
    padding-top: 12px;
    gap: 8px;
  }

  .tabContent {
    padding: 12px;
  }

  .productTitle {
    font-size: 18px !important;
  }

  .mainProductImage {
    max-width: 200px !important;
    height: 200px !important;
  }

  .imageGallery {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
  }

  .galleryImage {
    height: 100px !important;
  }
}

// Landscape orientation for mobile
@media (max-width: 768px) and (orientation: landscape) {
  .productDetailModal {
    :global(.ant-modal) {
      height: 100vh;
    }
  }
}
