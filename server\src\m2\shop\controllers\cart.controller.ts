import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  UseGuards,
  NotFoundException,
  BadRequestException,
  Req,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { CartService } from '../services/cart.service';
import { UserBehaviorLogService } from '../../../m_log/log/user-behavior-log.service';
import { AddCartItemDto, UpdateCartItemDto } from '../dto/add-cart-item.dto';
import { CartItemResponseDto } from '../dto/cart-item-response.dto';
import { CartInventoryCheckResultDto } from '../dto/cart-inventory-check-result.dto';

@ApiTags('Cart')
@Controller('cart')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
export class CartController {
  constructor(
    private readonly cartService: CartService,
    private readonly userBehaviorLogService: UserBehaviorLogService,
  ) {}

  @Get('me')
  @ApiOperation({ summary: 'Lấy giỏ hàng của user hiện tại' })
  @ApiResponse({ status: 200, type: [CartItemResponseDto] })
  async getCart(@Req() req: any) {
    const userId = req.user?.userId;
    console.log(userId);
    return this.cartService.getCart(userId);
  }

  @Post('add')
  @ApiOperation({ summary: 'Thêm sản phẩm vào giỏ hàng' })
  @ApiResponse({ status: 201, type: CartItemResponseDto })
  async addItem(@Body() addCartItemDto: AddCartItemDto, @Req() req: any) {
    try {
      const userId = req.user?.userId;
      const result = await this.cartService.addItem(userId, addCartItemDto);
      this.userBehaviorLogService.emitUserBehavior({
        user_id: userId,
        event_type: 'add_to_cart',
        target_type: 'product',
        target_id: addCartItemDto.product_id,
        product_id: addCartItemDto.product_id,
        event_data: { addCartItemDto },
        description: 'User added product to cart',
      });
      return result;
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('update-quantity')
  @ApiOperation({ summary: 'Cập nhật số lượng sản phẩm trong giỏ hàng' })
  @ApiResponse({ status: 200, type: CartItemResponseDto })
  async updateQuantity(@Body() eidtCartItem: UpdateCartItemDto, @Req() req: any) {
    try {
      const userId = req.user?.userId;
      const result = await this.cartService.updateQuantity(userId, eidtCartItem);
      this.userBehaviorLogService.emitUserBehavior({
        user_id: userId,
        event_type: 'update_cart_quantity',
        target_type: 'cart_item',
        target_id: eidtCartItem.cart_item_id,
        description: 'User updated cart item quantity',
      });
      return result;
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Delete('item')
  @ApiOperation({ summary: 'Xóa 1 item khỏi giỏ hàng' })
  @ApiResponse({ status: 200, type: CartItemResponseDto })
  async removeItem(@Query('cart_item_id') cartItemId: string, @Req() req: any) {
    try {
      const userId = req.user?.userId;
      const result = await this.cartService.removeItem(userId, cartItemId);
      this.userBehaviorLogService.emitUserBehavior({
        user_id: userId,
        event_type: 'remove_cart_item',
        target_type: 'cart_item',
        target_id: cartItemId,
        description: 'User removed item from cart',
      });
      return result;
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Post('clear')
  @ApiOperation({ summary: 'Xóa toàn bộ giỏ hàng của user hiện tại' })
  @ApiResponse({ status: 200, schema: { example: { success: true } } })
  async clearCart(@Req() req: any) {
    const userId = req.user?.userId;
    const result = await this.cartService.clearCart(userId);
    this.userBehaviorLogService.emitUserBehavior({
      user_id: userId,
      event_type: 'clear_cart',
      target_type: 'cart',
      description: 'User cleared cart',
    });
    return result;
  }

  @Post('check-inventory')
  @ApiOperation({ summary: 'Kiểm tra tồn kho các item trong giỏ hàng' })
  @ApiResponse({ status: 200, type: [CartInventoryCheckResultDto] })
  async checkInventory(@Req() req: any): Promise<CartInventoryCheckResultDto[]> {
    const userId = req.user?.userId;
    const result = await this.cartService.checkInventory(userId);
    this.userBehaviorLogService.emitUserBehavior({
      user_id: userId,
      event_type: 'check_cart_inventory',
      target_type: 'cart',
      description: 'User checked cart inventory',
    });
    return result as CartInventoryCheckResultDto[];
  }
}
