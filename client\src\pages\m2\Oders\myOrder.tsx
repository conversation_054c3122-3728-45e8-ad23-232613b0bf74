import React, { useState, useEffect } from 'react';
import {
  Tag, Spin, Alert, Empty, Modal, Descriptions, Divider, 
  Popconfirm, Tabs, Input, Rate, Form, Upload, message, Select, Tooltip
} from 'antd';
import Button from '@/components/Button';
import ModalButtonGroup from '@/components/ModalButtonGroup';
import type { TabsProps } from 'antd';
import { SearchOutlined, CameraOutlined, VideoCameraOutlined, UploadOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import {
  useGetMyOrdersHook,
  useGetOrderByIdHook,
  useRequestCancelOrderHook,
  useConfirmReceivedOrderHook,
} from '@/hooks/orders/useOrder';
import {
  useGetToReviewHook,
  useGetMyReviewsHook,
  useCreateReviewHook,
  useUpdateReviewHook,
  useDeleteReviewHook,
} from '@/hooks/product/useProductReview';
import styles from './myOrder.module.scss';
import logo from '@/assets/images/logo.png';

// Định nghĩa kiểu dữ liệu chi tiết
interface ProductItem {
  id: string;
  product: { id: string; name: string; thumbnail_url: string };
  version?: { name: string };
  quantity: number;
  unit_price: string;
  total_price: string;
}
interface OrderDataType {
  id: string;
  created_at: string;
  total_price: string;
  status_label: string;
  status_label_color?: string;
  items?: ProductItem[];
  shop_name?: string;
  [key: string]: any;
}
const getStatusTagColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'chờ xác nhận': return 'gold';
    case 'đang xử lý': return 'processing';
    case 'vận chuyển': return 'blue';
    case 'đang giao': return 'blue';
    case 'hoàn thành': return 'success';
    case 'đã giao': return 'success';
    case 'đã giao hàng': return 'success';
    case 'đã hủy': return 'error';
    case 'yêu cầu hủy': return 'warning';
    default: return 'default';
  }
};

const MyOrders: React.FC = () => {
  const [activeTab, setActiveTab] = useState('Tất cả');
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [reviewOrderId, setReviewOrderId] = useState<string | null>(null);
  const [expandedOrders, setExpandedOrders] = useState<Set<string>>(new Set());

  // State đánh giá
  const [isReviewModalVisible, setIsReviewModalVisible] = useState(false);
  const [selectedProductForReview, setSelectedProductForReview] = useState<ProductItem | null>(null);
  const [selectedOrderForReview, setSelectedOrderForReview] = useState<OrderDataType | null>(null);
  const [reviewForm] = Form.useForm();
  const [fileList, setFileList] = useState<any[]>([]);

  // State cho modal xem đánh giá
  const [isReviewListModalVisible, setIsReviewListModalVisible] = useState(false);
  const [reviewListOrderId, setReviewListOrderId] = useState<string | null>(null);
  const { data: myReviewsData, isLoading: isLoadingMyReviews, refetch: refetchMyReviewsList } = useGetMyReviewsHook();

  // State cho modal sửa đánh giá
  const [isEditReviewModalVisible, setIsEditReviewModalVisible] = useState(false);
  const [selectedReviewForEdit, setSelectedReviewForEdit] = useState<any>(null);
  const [editReviewForm] = Form.useForm();
  const [editFileList, setEditFileList] = useState<any[]>([]);

  const { data: ordersData, isLoading: isLoadingList, error: listError, refetch: refetchList } = useGetMyOrdersHook();
  const { data: detailData, isLoading: isLoadingDetail } = useGetOrderByIdHook(selectedOrderId || '');
  const { data: reviewOrderData, isLoading: isLoadingReviewOrder } = useGetOrderByIdHook(reviewOrderId || '');
  const cancelOrder = useRequestCancelOrderHook();
  const confirmOrder = useConfirmReceivedOrderHook();
  // Đánh giá hook
  const { refetch: refetchToReview } = useGetToReviewHook();
  const { refetch: refetchMyReviews } = useGetMyReviewsHook();
  const createReview = useCreateReviewHook();
  const updateReview = useUpdateReviewHook();
  const deleteReview = useDeleteReviewHook();

  // Load dữ liệu đánh giá khi component mount
  useEffect(() => {
    refetchMyReviewsList();
  }, [refetchMyReviewsList]);

  // Chuẩn hóa dữ liệu ordersData
  const allOrders = Array.isArray(ordersData) ? ordersData.map(order => ({
    ...order,
    items: Array.isArray(order.items) ? order.items : []
  })) : [];

  // Hàm phân loại trạng thái cho các tab
  // Phân chia các trạng thái thực tế từ backend thành 5 nhóm chính:
  // 1. Chờ xác nhận: PENDING (Chờ xác nhận), CONFIRMED (Đã xác nhận), PACKING (Đang đóng gói)
  // 2. Vận chuyển: DELIVERING (Đang giao hàng)
  // 3. Đã giao hàng: DELIVERED (Đã giao hàng) - chờ xác nhận nhận hàng
  // 4. Hoàn thành: COMPLETED (Đã hoàn thành) - đã xác nhận nhận hàng
  // 5. Đã hủy: CANCELLED (Đã hủy), RETURNED (Đã hoàn hàng), CANCEL_REQUESTED (Yêu cầu hủy đơn)
  const getStatusCategory = (statusLabel: string): string => {
    const status = statusLabel?.toLowerCase() || '';
    
    // Chờ xác nhận: PENDING, CONFIRMED, PACKING
    if (status.includes('chờ xác nhận') || status.includes('đã xác nhận') || status.includes('đang đóng gói')) {
      return 'Chờ xác nhận';
    }
    
    // Vận chuyển: DELIVERING (chỉ đang giao hàng, không bao gồm đã giao hàng)
    if (status.includes('đang giao hàng') || status.includes('vận chuyển')) {
      return 'Vận chuyển';
    }
    
    // Đã giao hàng: DELIVERED (đã giao hàng nhưng chưa xác nhận nhận)
    if (status.includes('đã giao hàng') || status.includes('delivered')) {
      return 'Đã giao hàng';
    }
    
    // Hoàn thành: chỉ COMPLETED (Đã hoàn thành), không bao gồm DELIVERED (Đã giao hàng)
    if (status.includes('đã hoàn thành') || status.includes('completed')) {
      return 'Hoàn thành';
    }
    
    // Đã hủy: CANCELLED, RETURNED, CANCEL_REQUESTED
    if (status.includes('đã hủy') || status.includes('đã hoàn hàng') || status.includes('yêu cầu hủy đơn')) {
      return 'Đã hủy';
    }
    
    // Mặc định trả về trạng thái gốc nếu không khớp
    return statusLabel || 'Không xác định';
  };

  const filteredOrders = allOrders
    .filter((order: OrderDataType) => {
      if (activeTab === 'Tất cả') return true;
      
      const statusCategory = getStatusCategory(order.status_label);
      return statusCategory === activeTab;
    })
    .filter((order: OrderDataType) =>
      !searchTerm ||
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (Array.isArray(order.items) && order.items.some((item: ProductItem) => item.product?.name.toLowerCase().includes(searchTerm.toLowerCase())))
    );

  // Xem chi tiết đơn
  const handleViewDetails = (orderId: string) => {
    setSelectedOrderId(orderId);
    setIsModalVisible(true);
  };
  const handleCloseModal = () => setIsModalVisible(false);
  const handleTabChange = (key: string) => setActiveTab(key);
  
  // Toggle hiển thị chi tiết sản phẩm
  const toggleOrderDetails = (orderId: string) => {
    const newExpanded = new Set(expandedOrders);
    if (newExpanded.has(orderId)) {
      newExpanded.delete(orderId);
    } else {
      newExpanded.add(orderId);
    }
    setExpandedOrders(newExpanded);
  };
  const handleCancelOrder = async (orderId: string) => {
    if (await cancelOrder(orderId)) refetchList();
  };
  const handleConfirmOrder = async (orderId: string) => {
    if (await confirmOrder(orderId)) refetchList();
  };

  // Mở form đánh giá
  const handleOpenReviewModal = (order: OrderDataType) => {
    setReviewOrderId(order.id);
    setSelectedOrderForReview(order); // Đảm bảo luôn set lại
    setIsReviewModalVisible(true);
  };

  // Cập nhật selectedProductForReview khi reviewOrderData thay đổi
  useEffect(() => {
    if (isReviewModalVisible && reviewOrderData) {
      let items = (reviewOrderData as any)?.orderItems || (reviewOrderData as any)?.data?.orderItems || [];
      items = items.map((item: any) => ({
        id: item.id,
        product: item.product,
        version: item.version,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
      })).filter(Boolean);
      if (!items || !Array.isArray(items) || items.length === 0) {
        message.error('Đơn hàng không có sản phẩm để đánh giá.');
        setIsReviewModalVisible(false);
        setReviewOrderId(null);
        return;
      }
      setSelectedProductForReview(items[0]);
      reviewForm.resetFields();
    }
  }, [reviewOrderData, isReviewModalVisible, reviewForm]);

  const handleCloseReviewModal = () => {
    setIsReviewModalVisible(false);
    setSelectedProductForReview(null);
    setSelectedOrderForReview(null);
    setReviewOrderId(null);
    setFileList([]);
    reviewForm.resetFields();
  };

  // Xử lý gửi đánh giá
  const handleSubmitReview = async (values: any) => {
    if (!selectedOrderForReview || !selectedProductForReview) {
      message.error('Không có dữ liệu sản phẩm để đánh giá!');
      return;
    }
    // Validate: phải có rating và comment
    if (!values.rating || !values.comment || String(values.comment).trim() === '') {
      message.error('Vui lòng nhập đầy đủ đánh giá và nhận xét!');
      return;
    }
    const reviewData = {
      product_id: selectedProductForReview.product?.id || '',
      rating: values.rating,
      content: values.comment,
      parent_id: undefined,
      order_id: selectedOrderForReview.id,
      images: fileList.length > 0 ? fileList.reduce((acc: any, file: any, idx: number) => {
        acc[`image_${idx}`] = file.url || file.thumbUrl || file.response?.url || '';
        return acc;
      }, {}) : {}
    };
    const success = await createReview(reviewData);
    if (success) {
      handleCloseReviewModal();
      refetchToReview();
      refetchMyReviews();
      refetchMyReviewsList(); // Refresh danh sách đánh giá để cập nhật trạng thái nút
      message.success('Đánh giá sản phẩm thành công!');
    }
  };

  // Upload ảnh đánh giá
  const handleUploadChange = (info: any) => {
    let newFileList = [...info.fileList];
    newFileList = newFileList.slice(-5);
    setFileList(newFileList);
  };
  const uploadProps = {
    name: 'file',
    multiple: true,
    fileList: fileList,
    onChange: handleUploadChange,
    beforeUpload: (file: any) => {
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('Chỉ được upload file hình ảnh!');
        return Upload.LIST_IGNORE;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('Hình ảnh phải nhỏ hơn 2MB!');
        return Upload.LIST_IGNORE;
      }
      return true;
    },
    listType: 'picture-card' as const
  };
  // Tính số lượng đơn hàng cho mỗi tab
  const getOrderCountByCategory = (category: string): number => {
    if (category === 'Tất cả') return allOrders.length;
    return allOrders.filter(order => getStatusCategory(order.status_label) === category).length;
  };

  // Kiểm tra đơn hàng đã được đánh giá chưa
  const hasOrderBeenReviewed = (orderId: string): boolean => {
    // Kiểm tra cả myReviewsData.data và myReviewsData trực tiếp
    const reviewsArray = myReviewsData?.data || myReviewsData;
    
    if (!reviewsArray || !Array.isArray(reviewsArray)) {
      return false;
    }
    
    return reviewsArray.some((review: any) => {
      const reviewOrderId = String(review.order_id || '').trim();
      const currentOrderId = String(orderId || '').trim();
      return reviewOrderId === currentOrderId;
    });
  };

  const tabItems: TabsProps['items'] = [
    { 
      key: 'Tất cả', 
      label: `Tất cả (${getOrderCountByCategory('Tất cả')})` 
    },
    { 
      key: 'Chờ xác nhận', 
      label: `Chờ xác nhận (${getOrderCountByCategory('Chờ xác nhận')})` 
    },
    { 
      key: 'Vận chuyển', 
      label: `Vận chuyển (${getOrderCountByCategory('Vận chuyển')})` 
    },
    { 
      key: 'Đã giao hàng', 
      label: `Đã giao hàng (${getOrderCountByCategory('Đã giao hàng')})` 
    },
    { 
      key: 'Hoàn thành', 
      label: `Hoàn thành (${getOrderCountByCategory('Hoàn thành')})` 
    },
    { 
      key: 'Đã hủy', 
      label: `Đã hủy (${getOrderCountByCategory('Đã hủy')})` 
    },
  ];
  if (isLoadingList) return <div className={styles.centeredContainer}><Spin size="large" /></div>;
  if (listError) return <div className={styles.container}><Alert message="Lỗi tải dữ liệu" type="error" showIcon /></div>;
  const orderDetail: any = detailData;

  // Hàm mở modal xem đánh giá
  const handleOpenReviewListModal = (orderId: string) => {
    setReviewListOrderId(orderId);
    setIsReviewListModalVisible(true);
    refetchMyReviewsList(); // Refresh dữ liệu đánh giá
  };
  const handleCloseReviewListModal = () => {
    setIsReviewListModalVisible(false);
    setReviewListOrderId(null);
  };

  // Hàm mở modal sửa đánh giá
  const handleOpenEditReviewModal = (review: any) => {
    setSelectedReviewForEdit(review);
    setEditFileList(review.images ? Object.values(review.images).map((url: any, idx: number) => ({
      uid: `-${idx}`,
      name: `image-${idx}`,
      status: 'done',
      url: url,
      thumbUrl: url,
    })) : []);
    editReviewForm.setFieldsValue({
      rating: review.rating,
      comment: review.content,
    });
    setIsEditReviewModalVisible(true);
  };

  // Hàm đóng modal sửa đánh giá
  const handleCloseEditReviewModal = () => {
    setIsEditReviewModalVisible(false);
    setSelectedReviewForEdit(null);
    setEditFileList([]);
    editReviewForm.resetFields();
  };

  // Hàm xử lý sửa đánh giá
  const handleSubmitEditReview = async (values: any) => {
    if (!selectedReviewForEdit) {
      message.error('Không có dữ liệu đánh giá để sửa!');
      return;
    }

    if (!values.rating || !values.comment || String(values.comment).trim() === '') {
      message.error('Vui lòng nhập đầy đủ đánh giá và nhận xét!');
      return;
    }

    const reviewData = {
      rating: values.rating,
      content: values.comment,
      images: editFileList.length > 0 ? editFileList.reduce((acc: any, file: any, idx: number) => {
        acc[`image_${idx}`] = file.url || file.thumbUrl || file.response?.url || '';
        return acc;
      }, {}) : {}
    };

    const success = await updateReview(selectedReviewForEdit.id, reviewData);
    if (success) {
      handleCloseEditReviewModal();
      refetchMyReviewsList();
      refetchMyReviews();
      message.success('Cập nhật đánh giá thành công!');
    }
  };

  // Hàm xử lý xóa đánh giá
  const handleDeleteReview = async (reviewId: string) => {
    const success = await deleteReview(reviewId);
    if (success) {
      refetchMyReviewsList();
      refetchMyReviews();
      message.success('Xóa đánh giá thành công!');
    }
  };

  // Upload props cho modal sửa đánh giá
  const editUploadProps = {
    name: 'file',
    multiple: true,
    fileList: editFileList,
    onChange: (info: any) => {
      let newFileList = [...info.fileList];
      newFileList = newFileList.slice(-5);
      setEditFileList(newFileList);
    },
    beforeUpload: (file: any) => {
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('Chỉ được upload file hình ảnh!');
        return Upload.LIST_IGNORE;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('Hình ảnh phải nhỏ hơn 2MB!');
        return Upload.LIST_IGNORE;
      }
      return true;
    },
    listType: 'picture-card' as const
  };

  return (
          <div className={styles.myOrdersPage}>
        <div className={styles.tabsContainer}>
          <Tabs defaultActiveKey="Tất cả" items={tabItems} onChange={handleTabChange} />
        </div>
      <div className={styles.searchBar}>
        <Input
          placeholder="Bạn có thể tìm kiếm theo ID đơn hàng hoặc Tên Sản phẩm"
          prefix={<SearchOutlined />}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      <div className={styles.orderList}>
        {filteredOrders.length > 0 ? (
          filteredOrders.map((order: OrderDataType) => (
            <div key={order.id} className={styles.orderCard}>
              <div className={styles.cardHeader}>
                <div className={styles.headerLeft}>
                  <span className={styles.shopName}>{order.shop_name || 'TapGreen'}</span>
                  <span className={styles.orderId}>#{order.id.substring(0, 8).toUpperCase()}</span>
                  <span className={styles.orderDate}>
                    {new Date(order.created_at).toLocaleDateString('vi-VN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </div>
                <Tooltip title={`Trạng thái chi tiết: ${order.status_label}`}>
                  <span className={styles.statusText}>{order.status_label.toUpperCase()}</span>
                </Tooltip>
              </div>
              <div className={styles.cardBody}>
                {/* Tóm tắt sản phẩm */}
                <div className={styles.productSummary}>
                  <div className={styles.summaryHeader}>
                    <span className={styles.summaryTitle}>Sản phẩm trong đơn ({order.items?.length || 0} sản phẩm)</span>
                  </div>
                  <div className={styles.productList}>
                    {order.items?.map((item, index) => (
                      <div key={item.id} className={styles.summaryItem}>
                        <span className={styles.itemNumber}>{index + 1}.</span>
                        <span className={styles.itemName}>{item.product?.name}</span>
                        <span className={styles.itemQuantity}>x{item.quantity}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Chi tiết từng sản phẩm - chỉ hiển thị khi expanded */}
                {expandedOrders.has(order.id) && order.items?.map(item => (
                  <div key={item.id} className={styles.productItem} onClick={() => handleViewDetails(order.id)}>
                    <img src={item.product?.thumbnail_url || logo} alt={item.product?.name} className={styles.productImage}/>
                    <div className={styles.productInfo}>
                      <span className={styles.productName}>{item.product?.name}</span>
                      <span className={styles.productVariation}>Phân loại: {item.version?.name || 'Mặc định'}</span>
                      <div className={styles.productDetails}>
                        <span className={styles.productQuantity}>Số lượng: {item.quantity}</span>
                        <span className={styles.unitPrice}>Đơn giá: {(Number(item.unit_price)).toLocaleString('vi-VN')}đ</span>
                      </div>
                    </div>
                    <div className={styles.priceInfo}>
                      <span className={styles.currentPrice}>{(Number(item.total_price)).toLocaleString('vi-VN')}đ</span>
                    </div>
                  </div>
                ))}
              </div>
              <div className={styles.cardFooter}>
                <div className={styles.totalAmount}>
                  <span>Thành tiền:</span>
                  <strong>{(Number(order.total_price || 0)).toLocaleString('vi-VN', { style: 'currency', currency: 'VND' })}</strong>
                </div>
                <div className={styles.actionButtons}>
                  {/* Nút hủy đơn hàng - chỉ hiển thị khi đơn hàng chưa được xử lý */}
                  {(getStatusCategory(order.status_label) === 'Chờ xác nhận') &&
                    <Popconfirm title="Bạn chắc chắn muốn hủy đơn hàng?" onConfirm={() => handleCancelOrder(order.id)} okText="Đồng ý" cancelText="Không">
                      <Button>Hủy Đơn Hàng</Button>
                    </Popconfirm>
                  }
                  
                  {/* Nút xác nhận đã nhận hàng - chỉ hiển thị khi đã giao hàng (DELIVERED) */}
                  {/* Không hiển thị khi đang giao hàng (DELIVERING) - chỉ khi đã giao xong */}
                  {(order.status_label.toLowerCase().includes('đã giao hàng') || order.status_label.toLowerCase().includes('delivered')) &&
                    <Button variant="primary" onClick={() => handleConfirmOrder(order.id)}>Đã Nhận Được Hàng</Button>
                  }
                  
                  {/* Nút đánh giá - chỉ hiển thị khi đã hoàn thành (COMPLETED) */}
                  {/* Không hiển thị khi đã giao hàng (DELIVERED) - chỉ khi đã xác nhận nhận hàng */}
                  {(getStatusCategory(order.status_label) === 'Hoàn thành') && (
                    hasOrderBeenReviewed(order.id) ? (
                      <Button variant="primary" onClick={() => handleOpenReviewListModal(order.id)}>
                        Xem Đánh Giá
                      </Button>
                    ) : (
                      <Button variant="primary" onClick={() => handleOpenReviewModal(order)}>
                        Đánh Giá
                      </Button>
                    )
                  )}
                  
                  <Button variant="default" onClick={() => handleViewDetails(order.id)}>Xem Chi Tiết</Button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <Empty description="Không có đơn hàng nào trong mục này." className={styles.emptyOrders}/>
        )}
      </div>

      {/* Modal chi tiết */}
      <Modal
        title={`Chi tiết Đơn hàng #${selectedOrderId?.substring(0, 8).toUpperCase()}`}
        open={isModalVisible}
        onCancel={handleCloseModal}
        width={700}
        footer={null}
      >
        {isLoadingDetail ? <div className={styles.centeredContainer}><Spin /></div> :
          orderDetail ? (
            <div>
              <Descriptions bordered column={1} size="small">
                <Descriptions.Item label="Trạng thái"><Tag color={orderDetail.status?.color || getStatusTagColor(orderDetail.status?.label)}>{orderDetail.status?.label}</Tag></Descriptions.Item>
                <Descriptions.Item label="Người nhận">{orderDetail.shipping_name}</Descriptions.Item>
                <Descriptions.Item label="Số điện thoại">{orderDetail.shipping_phone}</Descriptions.Item>
                <Descriptions.Item label="Địa chỉ giao hàng">{orderDetail.shipping_address}</Descriptions.Item>
                <Descriptions.Item label="Ghi chú">{orderDetail.note || 'Không có'}</Descriptions.Item>
              </Descriptions>
              <Divider>Sản phẩm đã mua</Divider>
              {orderDetail.orderItems?.map((item: any) => (
                  <div key={item.id} className={`${styles.productItem} ${styles.modalProductItem}`}>
                      <img src={item.product?.thumbnail_url || logo} alt={item.product?.name} className={styles.productImage} />
                      <div className={styles.productInfo}>
                          <span className={styles.productName}>{item.product?.name}</span>
                          <span className={styles.productVariation}>Phân loại: {item.version?.name || 'Mặc định'}</span>
                          <span className={styles.productQuantity}>x {item.quantity}</span>
                      </div>
                      <div className={styles.priceInfo}>
                        <span className={styles.currentPrice}>{(Number(item.total_price)).toLocaleString('vi-VN')}đ</span>
                      </div>
                  </div>
              ))}
              <Divider />
               <div className={`${styles.totalAmount} ${styles.modalTotalAmount}`}>
                  <span>Tổng cộng:</span>
                  <strong>{(Number(orderDetail.total_price || 0)).toLocaleString('vi-VN', { style: 'currency', currency: 'VND' })}</strong>
              </div>
            </div>
         ) : <Alert message="Không thể tải chi tiết đơn hàng." type="warning"/>
        }
        
        <ModalButtonGroup
          onCancel={handleCloseModal}
          onConfirm={selectedOrderId ? () => handleOpenReviewListModal(selectedOrderId) : undefined}
          cancelText="Đóng"
          confirmText="Xem đánh giá"
          showConfirm={!!selectedOrderId}
          confirmVariant="primary"
        />
      </Modal>

      {/* Modal đánh giá sản phẩm */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ fontSize: '18px', fontWeight: 600 }}>⭐ Đánh giá sản phẩm</span>
          </div>
        }
        open={isReviewModalVisible}
        onCancel={handleCloseReviewModal}
        width={700}
        footer={null}
        bodyStyle={{ padding: '24px' }}
      >
        {isLoadingReviewOrder ? (
          <div className={styles.centeredContainer}><Spin /></div>
        ) : (
          (() => {
            let productList = (reviewOrderData as any)?.orderItems || (reviewOrderData as any)?.data?.orderItems || [];
            productList = productList.map((item: any) => ({
              id: item.id,
              product: item.product,
              version: item.version,
              quantity: item.quantity,
              unit_price: item.unit_price,
              total_price: item.total_price,
            })).filter(Boolean);
            if (!productList || !Array.isArray(productList) || productList.length === 0) {
              return <Alert type="warning" message="Không tìm thấy thông tin sản phẩm để đánh giá. Đơn hàng này không có sản phẩm hoặc dữ liệu lỗi." showIcon />;
            }
            return (
              <>
                {productList.length > 1 && (
                  <div style={{ marginBottom: 16 }}>
                    <span>Chọn sản phẩm để đánh giá: </span>
                    <Select
                      style={{ minWidth: 220 }}
                      value={selectedProductForReview?.id || productList[0].id}
                      onChange={id => {
                        const found = productList.find((item: ProductItem) => item.id === id);
                        setSelectedProductForReview(found || null);
                      }}
                    >
                      {productList.map((item: ProductItem) => (
                        <Select.Option key={item.id} value={item.id}>
                          {item.product?.name} {item.version?.name ? `- ${item.version.name}` : ''}
                        </Select.Option>
                      ))}
                    </Select>
                  </div>
                )}
                {selectedProductForReview && (
                  <Form form={reviewForm} onFinish={handleSubmitReview} layout="vertical" initialValues={{ rating: 5 }}>
                    <div className={styles.reviewProductItem}>
                      <img src={selectedProductForReview.product?.thumbnail_url || logo} alt={selectedProductForReview.product?.name} className={styles.reviewProductImage} />
                      <div className={styles.reviewProductInfo}>
                        <span className={styles.reviewProductName}>{selectedProductForReview.product?.name}</span>
                        <span className={styles.reviewProductVariation}>
                          Phân loại: {selectedProductForReview.version?.name || 'Mặc định'}
                        </span>
                        <span className={styles.reviewProductQuantity}>Số lượng: {selectedProductForReview.quantity}</span>
                      </div>
                    </div>
                    <Divider />
                    <Form.Item label="Đánh giá chất lượng sản phẩm" name="rating" rules={[{ required: true, message: 'Vui lòng đánh giá sản phẩm!' }]}> 
                      <Rate allowHalf /> 
                    </Form.Item>
                    <Form.Item label="Nhận xét của bạn" name="comment" rules={[{ required: true, message: 'Vui lòng nhập nhận xét!' }]}> 
                      <Input.TextArea rows={4} maxLength={500} showCount placeholder="Chia sẻ cảm nhận về sản phẩm..." /> 
                    </Form.Item>
                    <Form.Item label="Hình ảnh đánh giá (tùy chọn)">
                      <Upload {...uploadProps} listType={'picture-card' as const}>
                        <Button icon={<UploadOutlined />}>Chọn hình ảnh</Button>
                      </Upload>
                      <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                        Tối đa 5 ảnh, mỗi ảnh nhỏ hơn 2MB
                      </div>
                    </Form.Item>
                    <div className={styles.uploadButtons}>
                      <Button icon={<CameraOutlined />} className={styles.uploadButton} onClick={() => message.info('Chức năng chụp ảnh sẽ được cập nhật')}>Chụp ảnh</Button>
                      <Button icon={<VideoCameraOutlined />} className={styles.uploadButton} onClick={() => message.info('Chức năng quay video sẽ được cập nhật')}>Quay video</Button>
                    </div>
                  </Form>
                )}
              </>
            );
          })()
        )}
        
        <ModalButtonGroup
          onCancel={handleCloseReviewModal}
          onConfirm={() => reviewForm.submit()}
          cancelText="HỦY"
          confirmText="GỬI ĐÁNH GIÁ"
          confirmVariant="primary"
        />
      </Modal>

      {/* Modal xem danh sách đánh giá */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ fontSize: '18px', fontWeight: 600 }}>Đánh giá của bạn</span>
            <Tag color="blue" style={{ marginLeft: 'auto' }}>
              Đơn hàng #{reviewListOrderId?.substring(0, 8).toUpperCase()}
            </Tag>
          </div>
        }
        open={isReviewListModalVisible}
        onCancel={handleCloseReviewListModal}
        width={800}
        footer={null}
        bodyStyle={{ padding: '24px' }}
      >
        {isLoadingMyReviews ? (
          <div className={styles.centeredContainer}><Spin size="large" /></div>
        ) : (
          (() => {
            const reviewsArray = myReviewsData?.data || myReviewsData;
            const reviews = Array.isArray(reviewsArray)
              ? reviewsArray.filter((rv: any) => String(rv.order_id) === String(reviewListOrderId))
              : [];
            if (!reviews.length) {
              return (
                <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>📝</div>
                  <div style={{ fontSize: '16px', color: '#666', marginBottom: '8px' }}>Bạn chưa có đánh giá nào</div>
                  <div style={{ fontSize: '14px', color: '#999' }}>Hãy đánh giá sản phẩm để chia sẻ trải nghiệm của bạn</div>
                </div>
              );
            }
            return (
              <div style={{ maxHeight: '500px', overflowY: 'auto' }}>
                {reviews.map((rv: any, index: number) => (
                  <div 
                    key={rv.id} 
                    style={{ 
                      border: '1px solid #f0f0f0', 
                      borderRadius: '12px', 
                      marginBottom: '20px', 
                      padding: '20px',
                      backgroundColor: '#fafafa',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
                    }}
                  >
                    {/* Header với thông tin sản phẩm */}
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'flex-start', 
                      marginBottom: '16px',
                      gap: '12px'
                    }}>
                      <img 
                        src={rv.product_image || rv.product?.thumbnail_url || logo} 
                        alt={rv.product_name || rv.product?.name}
                        style={{ 
                          width: '60px', 
                          height: '60px', 
                          objectFit: 'cover', 
                          borderRadius: '8px',
                          border: '1px solid #e8e8e8'
                        }} 
                      />
                      <div style={{ flex: 1 }}>
                        <div style={{ 
                          fontSize: '16px', 
                          fontWeight: 600, 
                          color: '#262626',
                          marginBottom: '4px'
                        }}>
                          {rv.product_name || rv.product?.name}
                        </div>
                        <div style={{ 
                          fontSize: '14px', 
                          color: '#8c8c8c',
                          marginBottom: '8px'
                        }}>
                          Đánh giá #{index + 1}
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <Rate disabled value={rv.rating} style={{ fontSize: '16px' }} />
                          <span style={{ 
                            fontSize: '14px', 
                            color: '#faad14', 
                            fontWeight: 500 
                          }}>
                            {rv.rating}/5
                          </span>
                        </div>
                      </div>
                      
                      {/* Action buttons */}
                      <div style={{ display: 'flex', gap: '8px' }}>
                        <Tooltip title="Sửa đánh giá">
                          <Button 
                            variant="primary" 
                            size="small"
                            icon={<EditOutlined />}
                            onClick={() => handleOpenEditReviewModal(rv)}
                          >
                            Sửa
                          </Button>
                        </Tooltip>
                        <Tooltip title="Xóa đánh giá">
                          <Popconfirm
                            title="Xóa đánh giá"
                            description="Bạn chắc chắn muốn xóa đánh giá này?"
                            onConfirm={() => handleDeleteReview(rv.id)}
                            okText="Đồng ý"
                            cancelText="Không"
                            placement="left"
                          >
                            <Button 
                              variant="danger" 
                              size="small"
                              icon={<DeleteOutlined />}
                            >
                              Xóa
                            </Button>
                          </Popconfirm>
                        </Tooltip>
                      </div>
                    </div>

                    {/* Nội dung đánh giá */}
                    <div style={{ 
                      backgroundColor: 'white', 
                      padding: '16px', 
                      borderRadius: '8px',
                      border: '1px solid #e8e8e8',
                      marginBottom: '16px'
                    }}>
                      <div style={{ 
                        fontSize: '15px', 
                        lineHeight: '1.6', 
                        color: '#262626',
                        whiteSpace: 'pre-wrap'
                      }}>
                        {rv.content}
                      </div>
                    </div>

                    {/* Hình ảnh đánh giá */}
                    {rv.images && Object.values(rv.images).length > 0 && (
                      <div>
                        <div style={{ 
                          fontSize: '14px', 
                          fontWeight: 500, 
                          color: '#595959',
                          marginBottom: '8px'
                        }}>
                          Hình ảnh đánh giá ({Object.values(rv.images).length} ảnh)
                        </div>
                        <div style={{ 
                          display: 'flex', 
                          gap: '8px', 
                          flexWrap: 'wrap'
                        }}>
                          {Object.values(rv.images).map((img: any, idx: number) => (
                            <div 
                              key={idx} 
                              style={{ 
                                position: 'relative',
                                cursor: 'pointer'
                              }}
                              onClick={() => {
                                // Có thể thêm lightbox để xem ảnh to
                                window.open(img, '_blank');
                              }}
                            >
                              <img 
                                src={img || logo} 
                                alt={`review-img-${idx}`} 
                                style={{ 
                                  width: '80px', 
                                  height: '80px', 
                                  objectFit: 'cover', 
                                  borderRadius: '8px',
                                  border: '1px solid #e8e8e8',
                                  transition: 'transform 0.2s'
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.transform = 'scale(1.05)';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.transform = 'scale(1)';
                                }}
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            );
          })()
        )}
      </Modal>

      {/* Modal sửa đánh giá */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ fontSize: '18px', fontWeight: 600 }}>✏️ Sửa đánh giá sản phẩm</span>
          </div>
        }
        open={isEditReviewModalVisible}
        onCancel={handleCloseEditReviewModal}
        width={700}
        footer={null}
        bodyStyle={{ padding: '24px' }}
      >
        {selectedReviewForEdit && (
          <Form form={editReviewForm} onFinish={handleSubmitEditReview} layout="vertical">
            <div className={styles.reviewProductItem}>
              <img 
                src={selectedReviewForEdit.product_image || selectedReviewForEdit.product?.thumbnail_url || logo} 
                alt={selectedReviewForEdit.product_name || selectedReviewForEdit.product?.name} 
                className={styles.reviewProductImage} 
              />
              <div className={styles.reviewProductInfo}>
                <span className={styles.reviewProductName}>
                  {selectedReviewForEdit.product_name || selectedReviewForEdit.product?.name}
                </span>
              </div>
            </div>
            <Divider />
            <Form.Item label="Đánh giá chất lượng sản phẩm" name="rating" rules={[{ required: true, message: 'Vui lòng đánh giá sản phẩm!' }]}> 
              <Rate allowHalf /> 
            </Form.Item>
            <Form.Item label="Nhận xét của bạn" name="comment" rules={[{ required: true, message: 'Vui lòng nhập nhận xét!' }]}> 
              <Input.TextArea rows={4} maxLength={500} showCount placeholder="Chia sẻ cảm nhận về sản phẩm..." /> 
            </Form.Item>
            <Form.Item label="Hình ảnh đánh giá (tùy chọn)">
              <Upload {...editUploadProps} listType={'picture-card' as const}>
                <Button icon={<UploadOutlined />}>Chọn hình ảnh</Button>
              </Upload>
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                Tối đa 5 ảnh, mỗi ảnh nhỏ hơn 2MB
              </div>
            </Form.Item>
            <div className={styles.uploadButtons}>
              <Button icon={<CameraOutlined />} className={styles.uploadButton} onClick={() => message.info('Chức năng chụp ảnh sẽ được cập nhật')}>Chụp ảnh</Button>
              <Button icon={<VideoCameraOutlined />} className={styles.uploadButton} onClick={() => message.info('Chức năng quay video sẽ được cập nhật')}>Quay video</Button>
            </div>
          </Form>
        )}
        
        <ModalButtonGroup
          onCancel={handleCloseEditReviewModal}
          onConfirm={() => editReviewForm.submit()}
          cancelText="HỦY"
          confirmText="CẬP NHẬT"
          confirmVariant="primary"
        />
      </Modal>
    </div>
  );
};

export default MyOrders;