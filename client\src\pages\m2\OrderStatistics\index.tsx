import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Statistic, 
  Row, 
  Col, 
  DatePicker, 
  Select, 
  Button,
  Progress,
  Tag,
  Space,
  Tooltip,
  Collapse,
  message
} from 'antd';
import {
  CalendarOutlined,
  ShoppingCartOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  DollarOutlined,
//   TrendingUpOutlined,
//   TrendingDownOutlined,
  EyeOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  PayCircleOutlined,
  Bar<PERSON>hartOutlined,
  PieChartOutlined
} from '@ant-design/icons';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  ComposedChart,
  Area,
  AreaChart
} from 'recharts';
import dayjs from 'dayjs';
import styles from './OrderStatistics.module.scss';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Panel } = Collapse;

interface StatisticsData {
  totalOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  actualRevenue: number;
  averageOrderValue: number;
  previousPeriodComparison: {
    totalOrders: number;
    completedOrders: number;
    cancelledOrders: number;
    totalRevenue: number;
  };
}

interface TrendData {
  date: string;
  orders: number;
  revenue: number;
  completedOrders: number;
  cancelledOrders: number;
}

interface PaymentMethodData {
  method: string;
  value: number;
  percentage: number;
  color: string;
}

const OrderStatistics: React.FC = () => {
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, 'day'),
    dayjs()
  ]);
  const [quickFilter, setQuickFilter] = useState('7days');
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<StatisticsData>({
    totalOrders: 0,
    completedOrders: 0,
    cancelledOrders: 0,
    totalRevenue: 0,
    actualRevenue: 0,
    averageOrderValue: 0,
    previousPeriodComparison: {
      totalOrders: 0,
      completedOrders: 0,
      cancelledOrders: 0,
      totalRevenue: 0
    }
  });
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [paymentMethodData, setPaymentMethodData] = useState<PaymentMethodData[]>([]);
  const [isMobile, setIsMobile] = useState(false);

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Mock data
  const mockStatistics: StatisticsData = {
    totalOrders: 1247,
    completedOrders: 1089,
    cancelledOrders: 158,
    totalRevenue: 28560000,
    actualRevenue: 26890000,
    averageOrderValue: 225000,
    previousPeriodComparison: {
      totalOrders: 1156,
      completedOrders: 1034,
      cancelledOrders: 122,
      totalRevenue: 25420000
    }
  };

  const mockTrendData: TrendData[] = [
    { date: '2024-03-09', orders: 45, revenue: 980000, completedOrders: 38, cancelledOrders: 7 },
    { date: '2024-03-10', orders: 52, revenue: 1150000, completedOrders: 47, cancelledOrders: 5 },
    { date: '2024-03-11', orders: 38, revenue: 890000, completedOrders: 32, cancelledOrders: 6 },
    { date: '2024-03-12', orders: 61, revenue: 1340000, completedOrders: 54, cancelledOrders: 7 },
    { date: '2024-03-13', orders: 48, revenue: 1020000, completedOrders: 42, cancelledOrders: 6 },
    { date: '2024-03-14', orders: 55, revenue: 1180000, completedOrders: 49, cancelledOrders: 6 },
    { date: '2024-03-15', orders: 67, revenue: 1450000, completedOrders: 58, cancelledOrders: 9 }
  ];

  const mockPaymentMethodData: PaymentMethodData[] = [
    { method: 'COD', value: 689, percentage: 55.3, color: '#ff7300' },
    { method: 'Ví TAP', value: 374, percentage: 30.0, color: '#8884d8' },
    { method: 'Chuyển khoản', value: 159, percentage: 12.8, color: '#00d9ff' },
    { method: 'Khác', value: 25, percentage: 2.0, color: '#ffc658' }
  ];

  useEffect(() => {
    loadStatistics();
  }, [dateRange]);

  const loadStatistics = async () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setStatistics(mockStatistics);
      setTrendData(mockTrendData);
      setPaymentMethodData(mockPaymentMethodData);
      setLoading(false);
    }, 1000);
  };

  const handleQuickFilter = (value: string) => {
    setQuickFilter(value);
    const now = dayjs();
    
    switch (value) {
      case 'today':
        setDateRange([now.startOf('day'), now.endOf('day')]);
        break;
      case 'yesterday':
        setDateRange([now.subtract(1, 'day').startOf('day'), now.subtract(1, 'day').endOf('day')]);
        break;
      case '7days':
        setDateRange([now.subtract(7, 'day'), now]);
        break;
      case 'thisMonth':
        setDateRange([now.startOf('month'), now.endOf('month')]);
        break;
      case 'lastMonth':
        setDateRange([now.subtract(1, 'month').startOf('month'), now.subtract(1, 'month').endOf('month')]);
        break;
      default:
        break;
    }
  };

  const calculatePercentageChange = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  const viewCancelledOrders = () => {
    message.info('Chuyển đến danh sách đơn hàng đã hủy');
  };

  const renderStatCard = (
    title: string,
    value: number,
    icon: React.ReactNode,
    color: string,
    suffix?: string,
    previousValue?: number,
    formatter?: (value: number) => string,
    onClick?: () => void
  ) => {
    const change = previousValue ? calculatePercentageChange(value, previousValue) : 0;
    const isPositive = change > 0;
    const isNegative = change < 0;

    return (
      <Card 
        className={`${styles.statCard} ${onClick ? styles.clickable : ''}`}
        onClick={onClick}
      >
        <div className={styles.statContent}>
          <div className={styles.statIcon} style={{ color }}>
            {icon}
          </div>
          <div className={styles.statInfo}>
            <div className={styles.statTitle}>{title}</div>
            <div className={styles.statValue} style={{ color }}>
              {formatter ? formatter(value) : value.toLocaleString()}
              {suffix && <span className={styles.statSuffix}>{suffix}</span>}
            </div>
            {previousValue && (
              <div className={styles.statChange}>
                {isPositive && <ArrowUpOutlined className={styles.upArrow} />}
                {isNegative && <ArrowDownOutlined className={styles.downArrow} />}
                <span className={isPositive ? styles.positive : isNegative ? styles.negative : styles.neutral}>
                  {Math.abs(change).toFixed(1)}%
                </span>
                <span className={styles.compareText}>so với kỳ trước</span>
              </div>
            )}
          </div>
        </div>
      </Card>
    );
  };

  const renderMobileStats = () => (
    <div className={styles.mobileStatsContainer}>
      <Collapse ghost>
        <Panel header="Thống kê tổng quan" key="1">
          <Row gutter={[8, 8]}>
            <Col span={12}>
              {renderStatCard(
                'Tổng đơn hàng',
                statistics.totalOrders,
                <ShoppingCartOutlined />,
                '#1890ff',
                '',
                statistics.previousPeriodComparison.totalOrders
              )}
            </Col>
            <Col span={12}>
              {renderStatCard(
                'Đã hoàn tất',
                statistics.completedOrders,
                <CheckCircleOutlined />,
                '#52c41a',
                '',
                statistics.previousPeriodComparison.completedOrders
              )}
            </Col>
            <Col span={12}>
              {renderStatCard(
                'Đã hủy',
                statistics.cancelledOrders,
                <CloseCircleOutlined />,
                '#ff4d4f',
                '',
                statistics.previousPeriodComparison.cancelledOrders,
                undefined,
                viewCancelledOrders
              )}
            </Col>
            <Col span={12}>
              {renderStatCard(
                'Doanh thu',
                statistics.totalRevenue,
                <DollarOutlined />,
                '#fa8c16',
                '',
                statistics.previousPeriodComparison.totalRevenue,
                formatCurrency
              )}
            </Col>
          </Row>
        </Panel>
      </Collapse>
    </div>
  );

  const renderTrendChart = () => (
    <Card title="Xu hướng đơn hàng & doanh thu" className={styles.chartCard}>
      <ResponsiveContainer width="100%" height={isMobile ? 250 : 350}>
        <ComposedChart data={trendData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="date" 
            tickFormatter={(value) => dayjs(value).format('DD/MM')}
          />
          <YAxis yAxisId="left" orientation="left" />
          <YAxis yAxisId="right" orientation="right" />
          <RechartsTooltip
            formatter={(value: any, name: string) => [
              name === 'revenue' ? formatCurrency(value) : value,
              name === 'orders' ? 'Đơn hàng' : 
              name === 'revenue' ? 'Doanh thu' :
              name === 'completedOrders' ? 'Hoàn tất' : 'Hủy'
            ]}
            labelFormatter={(label) => dayjs(label).format('DD/MM/YYYY')}
          />
          <Bar yAxisId="left" dataKey="orders" fill="#1890ff" name="orders" />
          <Line 
            yAxisId="right" 
            type="monotone" 
            dataKey="revenue" 
            stroke="#fa8c16" 
            strokeWidth={2}
            name="revenue"
          />
        </ComposedChart>
      </ResponsiveContainer>
    </Card>
  );

  const renderOrderStatusChart = () => (
    <Card title="Phân tích trạng thái đơn hàng" className={styles.chartCard}>
      <ResponsiveContainer width="100%" height={isMobile ? 250 : 300}>
        <BarChart data={trendData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="date" 
            tickFormatter={(value) => dayjs(value).format('DD/MM')}
          />
          <YAxis />
          <RechartsTooltip
            labelFormatter={(label) => dayjs(label).format('DD/MM/YYYY')}
          />
          <Bar dataKey="completedOrders" stackId="a" fill="#52c41a" name="Hoàn tất" />
          <Bar dataKey="cancelledOrders" stackId="a" fill="#ff4d4f" name="Hủy" />
        </BarChart>
      </ResponsiveContainer>
    </Card>
  );

  const renderPaymentMethodChart = () => (
    <Card title="Phương thức thanh toán" className={styles.chartCard}>
      <ResponsiveContainer width="100%" height={isMobile ? 250 : 300}>
        <PieChart>
          <Pie
            data={paymentMethodData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ method, percentage }) => `${method} (${percentage}%)`}
            outerRadius={isMobile ? 60 : 80}
            fill="#8884d8"
            dataKey="value"
          >
            {paymentMethodData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <RechartsTooltip
            formatter={(value: any) => [`${value} đơn`, 'Số lượng']}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
      <div className={styles.paymentMethodList}>
        {paymentMethodData.map((item, index) => (
          <div key={index} className={styles.paymentMethodItem}>
            <div className={styles.paymentMethodColor} style={{ backgroundColor: item.color }} />
            <span className={styles.paymentMethodName}>{item.method}</span>
            <span className={styles.paymentMethodValue}>{item.value} đơn</span>
            <span className={styles.paymentMethodPercentage}>{item.percentage}%</span>
          </div>
        ))}
      </div>
    </Card>
  );

  const renderAOVCard = () => (
    <Card className={styles.aovCard}>
      <div className={styles.aovContent}>
        <div className={styles.aovHeader}>
          <PayCircleOutlined className={styles.aovIcon} />
          <span className={styles.aovTitle}>Giá trị trung bình mỗi đơn (AOV)</span>
        </div>
        <div className={styles.aovValue}>
          {formatCurrency(statistics.averageOrderValue)}
        </div>
        <div className={styles.aovDescription}>
          Chỉ số quan trọng đánh giá chất lượng đơn hàng
        </div>
        <Progress
          percent={75}
          strokeColor="#52c41a"
          trailColor="#f0f0f0"
          showInfo={false}
        />
        <div className={styles.aovTarget}>
          Mục tiêu: {formatCurrency(300000)}
        </div>
      </div>
    </Card>
  );

  return (
    <div className={styles.container}>
      {/* Header */}
      <div className={styles.header}>
        <div className={styles.headerTitle}>
          <h1>Thống kê đơn hàng</h1>
          {!isMobile && <p>Phân tích chi tiết hiệu quả kinh doanh</p>}
        </div>
      </div>

      {/* Date Range Filter */}
      <Card className={styles.filterCard}>
        <div className={styles.filterHeader}>
          <div className={styles.filterTitle}>
            <CalendarOutlined />
            <span>Khoảng thời gian thống kê</span>
          </div>
          <div className={styles.filterActions}>
            <RangePicker
              value={dateRange}
              onChange={(dates) => {
                if (dates) {
                  setDateRange([dates[0]!, dates[1]!]);
                  setQuickFilter('custom');
                }
              }}
              className={styles.dateRangePicker}
              format="DD/MM/YYYY"
            />
          </div>
        </div>
        <div className={styles.quickFilters}>
          <Space wrap>
            <Button
              type={quickFilter === 'today' ? 'primary' : 'default'}
              onClick={() => handleQuickFilter('today')}
              className={styles.quickFilterButton}
            >
              Hôm nay
            </Button>
            <Button
              type={quickFilter === 'yesterday' ? 'primary' : 'default'}
              onClick={() => handleQuickFilter('yesterday')}
              className={styles.quickFilterButton}
            >
              Hôm qua
            </Button>
            <Button
              type={quickFilter === '7days' ? 'primary' : 'default'}
              onClick={() => handleQuickFilter('7days')}
              className={styles.quickFilterButton}
            >
              7 ngày gần nhất
            </Button>
            <Button
              type={quickFilter === 'thisMonth' ? 'primary' : 'default'}
              onClick={() => handleQuickFilter('thisMonth')}
              className={styles.quickFilterButton}
            >
              Tháng này
            </Button>
            <Button
              type={quickFilter === 'lastMonth' ? 'primary' : 'default'}
              onClick={() => handleQuickFilter('lastMonth')}
              className={styles.quickFilterButton}
            >
              Tháng trước
            </Button>
          </Space>
        </div>
      </Card>

      {/* Stats Cards - Desktop */}
      {!isMobile && (
        <Row gutter={[16, 16]} className={styles.statsRow}>
          <Col xs={24} sm={12} lg={6}>
            {renderStatCard(
              'Tổng số đơn hàng',
              statistics.totalOrders,
              <ShoppingCartOutlined />,
              '#1890ff',
              '',
              statistics.previousPeriodComparison.totalOrders
            )}
          </Col>
          <Col xs={24} sm={12} lg={6}>
            {renderStatCard(
              'Đơn hoàn tất',
              statistics.completedOrders,
              <CheckCircleOutlined />,
              '#52c41a',
              '',
              statistics.previousPeriodComparison.completedOrders
            )}
          </Col>
          <Col xs={24} sm={12} lg={6}>
            {renderStatCard(
              'Đơn bị hủy',
              statistics.cancelledOrders,
              <CloseCircleOutlined />,
              '#ff4d4f',
              '',
              statistics.previousPeriodComparison.cancelledOrders,
              undefined,
              viewCancelledOrders
            )}
          </Col>
          <Col xs={24} sm={12} lg={6}>
            {renderStatCard(
              'Tổng doanh thu',
              statistics.totalRevenue,
              <DollarOutlined />,
              '#fa8c16',
              '',
              statistics.previousPeriodComparison.totalRevenue,
              formatCurrency
            )}
          </Col>
        </Row>
      )}

      {/* Stats Cards - Mobile */}
      {isMobile && renderMobileStats()}

      {/* Revenue Details */}
      <Card className={styles.revenueCard}>
        <Row gutter={16}>
          <Col xs={24} sm={12}>
            <div className={styles.revenueItem}>
              <div className={styles.revenueLabel}>Doanh thu thực nhận</div>
              <div className={styles.revenueValue} style={{ color: '#52c41a' }}>
                {formatCurrency(statistics.actualRevenue)}
              </div>
            </div>
          </Col>
          <Col xs={24} sm={12}>
            <div className={styles.revenueItem}>
              <div className={styles.revenueLabel}>Doanh thu trước hoàn tiền</div>
              <div className={styles.revenueValue} style={{ color: '#fa8c16' }}>
                {formatCurrency(statistics.totalRevenue)}
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* Charts */}
      <Row gutter={[16, 16]} className={styles.chartsRow}>
        <Col xs={24} lg={16}>
          {renderTrendChart()}
        </Col>
        <Col xs={24} lg={8}>
          {renderAOVCard()}
        </Col>
      </Row>

      <Row gutter={[16, 16]} className={styles.chartsRow}>
        <Col xs={24} lg={12}>
          {renderOrderStatusChart()}
        </Col>
        <Col xs={24} lg={12}>
          {renderPaymentMethodChart()}
        </Col>
      </Row>
    </div>
  );
};

export default OrderStatistics;
