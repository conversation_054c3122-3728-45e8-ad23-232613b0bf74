import { Permission } from 'src/m1/user/entities/permissions.entity';
import { Role } from 'src/m1/user/entities/role.entity';
import { RoleGroupPermission } from 'src/m1/user/entities/role_group_permissions.entity';
import { RoleGroup } from 'src/m1/user/entities/role_groups.entity';
import { DataSource } from 'typeorm';

export async function seedExtendedRBAC(dataSource: DataSource) {
  const permissionRepo = dataSource.getRepository(Permission);
  const roleGroupRepo = dataSource.getRepository(RoleGroup);
  const rgpRepo = dataSource.getRepository(RoleGroupPermission);
  const roleRepo = dataSource.getRepository(Role);

  // Seed extended permissions based on new requirements
  const extendedPermissions = [
    // M4 - Production/Agriculture permissions
    { code: 'PERM_M4_PRODUCTION_LOG', name: '<PERSON><PERSON> <PERSON>h<PERSON><PERSON> ký sản xuất' },
    { code: 'PERM_M4_CROP_CALENDAR', name: 'Quản lý lịch mùa vụ' },
    { code: 'PERM_M4_AI_DIAGNOSIS', name: '<PERSON><PERSON> dụng AI chẩn đoán sâu bệnh' },
    { code: 'PERM_M4_TRACEABILITY', name: 'Truy xuất nguồn gốc sản phẩm' },
    { code: 'PERM_M4_CERTIFICATION_VIEW', name: 'Xem chứng nhận (OCOP, VietGAP)' },
    { code: 'PERM_M4_CERTIFICATION_MANAGE', name: 'Quản lý cấp chứng nhận' },
    { code: 'PERM_M4_ADVISOR_ACCESS', name: 'Truy cập cổng cố vấn nông nghiệp' },

    // M2 - E-commerce permissions
    { code: 'PERM_M2_SELLER_CENTER', name: 'Truy cập Seller Center' },
    { code: 'PERM_M2_LIVESTREAM', name: 'Thực hiện livestream bán hàng' },
    { code: 'PERM_M2_GROUP_BUYING', name: 'Tổ chức mua chung nhóm' },
    { code: 'PERM_M2_CIRCULAR_ECONOMY', name: 'Tham gia kinh tế tuần hoàn' },
    { code: 'PERM_M2_STORE_VERIFICATION', name: 'Thẩm định gian hàng' },

    // M3 - Digital Wallet permissions
    { code: 'PERM_M3_WALLET_BASIC', name: 'Sử dụng ví điện tử cơ bản' },
    { code: 'PERM_M3_MICROCREDIT', name: 'Tiện ích tài chính vi mô' },
    { code: 'PERM_M3_PAYMENT_PROCESSING', name: 'Xử lý thanh toán' },
    { code: 'PERM_M3_TRANSACTION_TRACE', name: 'Truy vết giao dịch (B2G)' },

    // M5 - Employment permissions
    { code: 'PERM_M5_JOB_SEARCH', name: 'Tìm kiếm việc làm' },
    { code: 'PERM_M5_SEASONAL_HIRING', name: 'Thuê nhân công thời vụ' },
    { code: 'PERM_M5_EMPLOYER_VERIFICATION', name: 'Thẩm định nhà tuyển dụng' },

    // M6 - Academy permissions
    { code: 'PERM_M6_COURSE_VIEW', name: 'Xem khóa học' },
    { code: 'PERM_M6_COURSE_CREATE', name: 'Tạo khóa học' },
    { code: 'PERM_M6_INSTRUCTOR_PORTAL', name: 'Cổng quản lý giảng viên' },
    { code: 'PERM_M6_TRAINING_MANAGE', name: 'Quản lý đào tạo chuyên ngành' },
    { code: 'PERM_M6_WORKSHOP_ORGANIZE', name: 'Tổ chức workshop chuyên đề' },

    // M7 - Credit/Trust Score permissions
    { code: 'PERM_M7_TRUST_SCORE_VIEW', name: 'Xem điểm tín nhiệm' },
    { code: 'PERM_M7_LOCAL_ADMIN', name: 'Quản trị địa phương' },
    { code: 'PERM_M7_AUDIT_APP', name: 'Ứng dụng thẩm định chuyên dụng' },
    { code: 'PERM_M7_AMBASSADOR_DASHBOARD', name: 'Dashboard đại sứ TAP' },
    { code: 'PERM_M7_COMMUNITY_KPI', name: 'Xem KPI cộng đồng' },

    // M8 - Family/Genealogy permissions
    { code: 'PERM_M8_FAMILY_CIRCLE', name: 'Vòng tròn an toàn gia đình' },
    { code: 'PERM_M8_SOS_RESPONSE', name: 'Phản hồi tín hiệu SOS' },
    { code: 'PERM_M8_GUARDIAN_ANGEL', name: 'Thiên thần hộ mệnh' },

    // M9 - Credit/Loan permissions
    { code: 'PERM_M9_LOAN_APPLICATION', name: 'Nộp hồ sơ vay vốn' },
    { code: 'PERM_M9_CREDIT_ASSESSMENT', name: 'Đánh giá tín dụng' },

    // M10 - Document Library permissions
    { code: 'PERM_M10_DOCUMENT_VIEW', name: 'Xem tài liệu' },
    { code: 'PERM_M10_DOCUMENT_SELL', name: 'Bán tài liệu chuyên sâu' },

    // M13 - Logistics permissions
    { code: 'PERM_M13_DELIVERY_ORDER', name: 'Tạo lệnh giao hàng' },
    { code: 'PERM_M13_SHIPMENT_TRACK', name: 'Theo dõi hành trình' },
    { code: 'PERM_M13_SHIPPING_MANAGE', name: 'Quản lý phí ship' },

    // M16 - Accounting permissions
    { code: 'PERM_M16_INVOICE_MANAGE', name: 'Quản lý hóa đơn' },
    { code: 'PERM_M16_TAX_DECLARATION', name: 'Ủy quyền kê khai thuế' },
    { code: 'PERM_M16_FINANCIAL_REPORT', name: 'Báo cáo tài chính' },
    { code: 'PERM_M16_TAX_AUDIT', name: 'Thanh tra thuế (B2G)' },

    // M17 - Social Network permissions
    { code: 'PERM_M17_SOCIAL_BASIC', name: 'Sử dụng mạng xã hội cơ bản' },
    { code: 'PERM_M17_COMMUNITY_LEADER', name: 'Quản trị nhóm cộng đồng' },
    { code: 'PERM_M17_MODERATOR', name: 'Kiểm duyệt nội dung' },
    { code: 'PERM_M17_EVENT_ORGANIZE', name: 'Tổ chức sự kiện nhóm' },
    { code: 'PERM_M17_TRANSACTION_POST', name: 'Bài đăng giao dịch' },

    // M18 - Healthcare permissions
    { code: 'PERM_M18_DIGITAL_CLINIC', name: 'Phòng mạch số' },
    { code: 'PERM_M18_MEDICAL_CONSULTATION', name: 'Tư vấn y tế' },
    { code: 'PERM_M18_APPOINTMENT_BOOKING', name: 'Đặt lịch khám' },

    // M20 - Tourism permissions
    { code: 'PERM_M20_TOUR_BOOKING', name: 'Đặt tour du lịch' },
    { code: 'PERM_M20_TOURISM_MANAGE', name: 'Quản lý ngành du lịch' },

    // M12, M14, M19 - Vertical industry permissions
    { code: 'PERM_M12_BEAUTY_SERVICE', name: 'Dịch vụ làm đẹp' },
    { code: 'PERM_M14_REAL_ESTATE', name: 'Bất động sản' },
    { code: 'PERM_M19_CONSTRUCTION', name: 'Xây dựng' },

    // M22 - Strategic Operations permissions
    { code: 'PERM_M22_STRATEGIC_DASHBOARD', name: 'Trung tâm điều hành chiến lược' },
    { code: 'PERM_M22_GLOBAL_KPI', name: 'KPI toàn cục' },

    // M27 - AI Training permissions
    { code: 'PERM_M27_AI_FOUNDRY', name: 'TAP AI Foundry - Gán nhãn dữ liệu' },
    { code: 'PERM_M27_DATA_LABELING', name: 'Gán nhãn dữ liệu chuyên môn' },

    // M29 - Consensus permissions
    { code: 'PERM_M29_VOTING', name: 'Tham gia trưng cầu ý dân' },
    { code: 'PERM_M29_COMMUNITY_PROJECT', name: 'Quản lý dự án cộng đồng' },

    // M98 - AI Advisor permissions
    { code: 'PERM_M98_AI_ADVISOR', name: 'AI cố vấn chiến lược' },
    { code: 'PERM_M98_RISK_ANALYSIS', name: 'Phân tích rủi ro AI' },

    // B2G - Government interface permissions
    { code: 'PERM_B2G_DATA_ACCESS', name: 'Truy cập dữ liệu theo phân quyền' },
    { code: 'PERM_B2G_AUDIT_LOG', name: 'Ghi log kiểm toán' },
    { code: 'PERM_B2G_GIS_MONITOR', name: 'Giám sát GIS' },
    { code: 'PERM_B2G_INVESTIGATION', name: 'Điều tra chính thức' },
  ];

  for (const p of extendedPermissions) {
    const exists = await permissionRepo.findOneBy({ code: p.code });
    if (!exists) {
      await permissionRepo.save(permissionRepo.create(p));
    }
  }

  // Seed all roles (existing + extended) based on the requirements
  const allRoles = [
    // Existing roles from original seed
    { role_code: 'R01', name: 'Nông dân', description: 'Người trực tiếp sản xuất' },
    { role_code: 'R02', name: 'Người bán hàng', description: 'Hộ kinh doanh, doanh nghiệp' },
    { role_code: 'R03', name: 'Người tiêu dùng', description: 'Người mua hàng hoặc dịch vụ' },
    { role_code: 'R04', name: 'Chuyên gia', description: 'Bác sĩ, giảng viên, chuyên gia' },
    { role_code: 'R05', name: 'Quản lý khu vực', description: 'Admin xã, huyện, tỉnh' },
    { role_code: 'R06', name: 'Tài xế', description: 'Giao hàng và gọi xe' },
    { role_code: 'R07', name: 'Nhà đầu tư', description: 'Góp vốn và cổ đông' },
    { role_code: 'R08', name: 'Ban điều hành', description: 'Chủ tịch, kế toán, giám đốc' },

    // New extended roles
    {
      role_code: 'R09',
      name: 'Người phụ trách ngành',
      description: 'Quản lý ngành dọc chuyên biệt',
    },
    {
      role_code: 'R10',
      name: 'Cán bộ nông nghiệp',
      description: 'Cán bộ cơ quan nhà nước - Nông nghiệp',
    },
    {
      role_code: 'R11',
      name: 'Cán bộ thuế & tài chính',
      description: 'Cán bộ cơ quan nhà nước - Thuế',
    },
    {
      role_code: 'R12',
      name: 'Cán bộ liên ngành',
      description: 'Cán bộ cơ quan nhà nước - Liên ngành',
    },
    { role_code: 'R13', name: 'Đại sứ TAP', description: 'Cầu nối cộng đồng tại địa phương' },
    {
      role_code: 'R14',
      name: 'Chuyên viên thẩm định',
      description: 'Kiểm soát chất lượng & thẩm định',
    },
    {
      role_code: 'R15',
      name: 'Trưởng nhóm cộng đồng',
      description: 'Quản lý cộng đồng mạng xã hội',
    },
    { role_code: 'R16', name: 'Cộng tác viên AI', description: 'Huấn luyện và gán nhãn AI' },
  ];

  for (const r of allRoles) {
    const exists = await roleRepo.findOneBy({ role_code: r.role_code });
    if (!exists) {
      await roleRepo.save(roleRepo.create(r));
    }
  }

  // Seed all roleGroups (existing + extended) with detailed module scope
  const allRoleGroupsRaw = [
    // R01 - Nông dân sản xuất (updated with detailed modules)
    {
      code: 'R01.01',
      name: 'Nông dân trồng trọt',
      role_code: 'R01',
      moduleScope: ['M4', 'M2', 'M5', 'M6', 'M9'],
    },
    {
      code: 'R01.02',
      name: 'Nông dân chăn nuôi',
      role_code: 'R01',
      moduleScope: ['M4', 'M2', 'M5', 'M6', 'M9'],
    },
    {
      code: 'R01.03',
      name: 'Nuôi trồng thủy sản',
      role_code: 'R01',
      moduleScope: ['M4', 'M2', 'M5', 'M6', 'M9'],
    },

    // R02 - Người bán hàng (updated with detailed modules)
    {
      code: 'R02.01',
      name: 'Hộ kinh doanh',
      role_code: 'R02',
      moduleScope: ['M2', 'M13', 'M16', 'M17', 'M7'],
    },
    {
      code: 'R02.02',
      name: 'Doanh nghiệp',
      role_code: 'R02',
      moduleScope: ['M2', 'M13', 'M16', 'M17', 'M7'],
    },

    // R03 - Người tiêu dùng (updated with detailed modules)
    {
      code: 'R03.01',
      name: 'Khách lẻ',
      role_code: 'R03',
      moduleScope: ['M2', 'M3', 'M4', 'M6', 'M8'],
    },
    {
      code: 'R03.02',
      name: 'Người mua dịch vụ',
      role_code: 'R03',
      moduleScope: ['M2', 'M3', 'M4', 'M6', 'M8', 'M12', 'M14', 'M18', 'M20'],
    },

    // R04 - Chuyên gia (updated with specialized workspaces)
    {
      code: 'R04.01',
      name: 'Bác sĩ cộng đồng',
      role_code: 'R04',
      moduleScope: ['M18', 'M6', 'M10', 'M17', 'M7'],
    },
    {
      code: 'R04.02',
      name: 'Chuyên gia nông nghiệp',
      role_code: 'R04',
      moduleScope: ['M4', 'M6', 'M10', 'M17', 'M7'],
    },
    {
      code: 'R04.03',
      name: 'Giảng viên đào tạo',
      role_code: 'R04',
      moduleScope: ['M6', 'M10', 'M17', 'M7'],
    },

    // R05 - Quản lý khu vực (updated with local admin tools)
    {
      code: 'R05.01',
      name: 'Quản lý người dùng',
      role_code: 'R05',
      moduleScope: ['M7', 'M29', 'M4', 'M5', 'M17'],
    },
    {
      code: 'R05.02',
      name: 'Admin nội dung',
      role_code: 'R05',
      moduleScope: ['M7', 'M29', 'M4', 'M5', 'M17'],
    },
    {
      code: 'R05.03',
      name: 'Admin kỹ thuật',
      role_code: 'R05',
      moduleScope: ['M7', 'M29', 'M4', 'M5', 'M17'],
    },

    // R06 - Tài xế (from original seed)
    { code: 'R06.01', name: 'Tài xế giao hàng', role_code: 'R06', moduleScope: ['M13'] },
    { code: 'R06.02', name: 'Tài xế gọi xe', role_code: 'R06', moduleScope: ['M13'] },

    // R07 - Nhà đầu tư (from original seed)
    { code: 'R07.01', name: 'Cá nhân góp vốn', role_code: 'R07', moduleScope: ['M5'] },
    { code: 'R07.02', name: 'Nhà đầu tư tổ chức', role_code: 'R07', moduleScope: ['M5'] },

    // R08 - Ban điều hành cấp cao (updated with strategic tools)
    {
      code: 'R08.01',
      name: 'Chủ tịch HĐQT',
      role_code: 'R08',
      moduleScope: ['M22', 'M7', 'M0', 'M98'],
    },
    {
      code: 'R08.02',
      name: 'Giám đốc tài chính',
      role_code: 'R08',
      moduleScope: ['M22', 'M7', 'M16', 'M98'],
    },
    { code: 'R08.03', name: 'Kế toán', role_code: 'R08', moduleScope: ['M16'] },
    { code: 'R08.04', name: 'Nhân sự', role_code: 'R08', moduleScope: ['M1'] },
    { code: 'R08.05', name: 'Ban kiểm soát', role_code: 'R08', moduleScope: ['M1'] },

    // R09 - Người phụ trách ngành (new)
    {
      code: 'R09.01',
      name: 'Giám đốc Ngành Du lịch',
      role_code: 'R09',
      moduleScope: ['M20', 'M7', 'M22', 'M6', 'M16'],
    },
    {
      code: 'R09.02',
      name: 'Giám đốc Ngành Bất động sản',
      role_code: 'R09',
      moduleScope: ['M14', 'M7', 'M22', 'M6', 'M16'],
    },
    {
      code: 'R09.03',
      name: 'Giám đốc Ngành Y tế',
      role_code: 'R09',
      moduleScope: ['M18', 'M7', 'M22', 'M6', 'M16'],
    },
    {
      code: 'R09.04',
      name: 'Giám đốc Ngành Xây dựng',
      role_code: 'R09',
      moduleScope: ['M19', 'M7', 'M22', 'M6', 'M16'],
    },
    {
      code: 'R09.05',
      name: 'Giám đốc Ngành Làm đẹp',
      role_code: 'R09',
      moduleScope: ['M12', 'M7', 'M22', 'M6', 'M16'],
    },

    // R10-R12 - Cán bộ cơ quan nhà nước (new)
    { code: 'R10.01', name: 'Cán bộ nông nghiệp', role_code: 'R10', moduleScope: ['M4'] },
    { code: 'R11.01', name: 'Cán bộ thuế & tài chính', role_code: 'R11', moduleScope: ['M16'] },
    {
      code: 'R12.01',
      name: 'Cán bộ liên ngành',
      role_code: 'R12',
      moduleScope: ['M3', 'M2', 'M13'],
    },

    // R13 - Đại sứ TAP (new)
    {
      code: 'R13.01',
      name: 'Đại sứ số',
      role_code: 'R13',
      moduleScope: ['M7', 'M1', 'M8', 'M6', 'M3', 'M2', 'M4', 'M18'],
    },

    // R14 - Chuyên viên thẩm định (new)
    {
      code: 'R14.01',
      name: 'Thẩm định viên chất lượng',
      role_code: 'R14',
      moduleScope: ['M7', 'M2', 'M4', 'M5', 'M12', 'M15'],
    },

    // R15 - Trưởng nhóm cộng đồng (new)
    {
      code: 'R15.01',
      name: 'Trưởng nhóm cộng đồng',
      role_code: 'R15',
      moduleScope: ['M17', 'M6', 'M2', 'M29'],
    },

    // R16 - Cộng tác viên AI (new)
    {
      code: 'R16.01',
      name: 'Cộng tác viên huấn luyện AI',
      role_code: 'R16',
      moduleScope: ['M27', 'M3', 'M6', 'M7'],
    },
  ];

  const createdRoleGroups: { code: string; name: string; roleId: string; moduleScope: string[] }[] =
    [];
  for (const rg of allRoleGroupsRaw) {
    const role = await roleRepo.findOneBy({ role_code: rg.role_code });
    if (!role) throw new Error(`Không tìm thấy role với code ${rg.role_code}`);
    createdRoleGroups.push({
      code: rg.code,
      name: rg.name,
      roleId: role.id,
      moduleScope: rg.moduleScope,
    });
  }

  for (const rg of createdRoleGroups) {
    const exists = await roleGroupRepo.findOneBy({ code: rg.code });
    if (!exists) {
      await roleGroupRepo.save(roleGroupRepo.create(rg));
    }
  }

  // Assign specific permissions to role groups based on their functions
  const roleGroupPermissions = {
    // R01 - Nông dân sản xuất
    'R01.01': [
      'PERM_M4_PRODUCTION_LOG',
      'PERM_M4_CROP_CALENDAR',
      'PERM_M4_AI_DIAGNOSIS',
      'PERM_M2_CIRCULAR_ECONOMY',
      'PERM_M5_SEASONAL_HIRING',
      'PERM_M6_COURSE_VIEW',
      'PERM_M9_LOAN_APPLICATION',
    ],
    'R01.02': [
      'PERM_M4_PRODUCTION_LOG',
      'PERM_M4_AI_DIAGNOSIS',
      'PERM_M2_CIRCULAR_ECONOMY',
      'PERM_M5_SEASONAL_HIRING',
      'PERM_M6_COURSE_VIEW',
      'PERM_M9_LOAN_APPLICATION',
    ],
    'R01.03': [
      'PERM_M4_PRODUCTION_LOG',
      'PERM_M4_AI_DIAGNOSIS',
      'PERM_M2_CIRCULAR_ECONOMY',
      'PERM_M5_SEASONAL_HIRING',
      'PERM_M6_COURSE_VIEW',
      'PERM_M9_LOAN_APPLICATION',
    ],

    // R02 - Người bán hàng
    'R02.01': [
      'PERM_M2_SELLER_CENTER',
      'PERM_M2_LIVESTREAM',
      'PERM_M13_DELIVERY_ORDER',
      'PERM_M16_INVOICE_MANAGE',
      'PERM_M16_TAX_DECLARATION',
      'PERM_M17_TRANSACTION_POST',
      'PERM_M7_TRUST_SCORE_VIEW',
    ],
    'R02.02': [
      'PERM_M2_SELLER_CENTER',
      'PERM_M2_LIVESTREAM',
      'PERM_M13_DELIVERY_ORDER',
      'PERM_M16_INVOICE_MANAGE',
      'PERM_M16_TAX_DECLARATION',
      'PERM_M17_TRANSACTION_POST',
      'PERM_M7_TRUST_SCORE_VIEW',
    ],

    // R03 - Người tiêu dùng
    'R03.01': [
      'PERM_M3_WALLET_BASIC',
      'PERM_M4_TRACEABILITY',
      'PERM_M6_COURSE_VIEW',
      'PERM_M8_FAMILY_CIRCLE',
    ],
    'R03.02': [
      'PERM_M3_WALLET_BASIC',
      'PERM_M4_TRACEABILITY',
      'PERM_M6_COURSE_VIEW',
      'PERM_M8_FAMILY_CIRCLE',
      'PERM_M12_BEAUTY_SERVICE',
      'PERM_M14_REAL_ESTATE',
      'PERM_M18_APPOINTMENT_BOOKING',
      'PERM_M20_TOUR_BOOKING',
    ],

    // R04 - Chuyên gia
    'R04.01': [
      'PERM_M18_DIGITAL_CLINIC',
      'PERM_M18_MEDICAL_CONSULTATION',
      'PERM_M6_COURSE_CREATE',
      'PERM_M10_DOCUMENT_SELL',
      'PERM_M17_SOCIAL_BASIC',
      'PERM_M7_TRUST_SCORE_VIEW',
    ],
    'R04.02': [
      'PERM_M4_ADVISOR_ACCESS',
      'PERM_M6_COURSE_CREATE',
      'PERM_M10_DOCUMENT_SELL',
      'PERM_M17_SOCIAL_BASIC',
      'PERM_M7_TRUST_SCORE_VIEW',
    ],
    'R04.03': [
      'PERM_M6_INSTRUCTOR_PORTAL',
      'PERM_M6_COURSE_CREATE',
      'PERM_M10_DOCUMENT_SELL',
      'PERM_M17_SOCIAL_BASIC',
      'PERM_M7_TRUST_SCORE_VIEW',
    ],

    // R05 - Quản lý khu vực
    'R05.01': [
      'PERM_M7_LOCAL_ADMIN',
      'PERM_M29_COMMUNITY_PROJECT',
      'PERM_M4_CERTIFICATION_MANAGE',
      'PERM_M5_EMPLOYER_VERIFICATION',
      'PERM_M17_MODERATOR',
    ],
    'R05.02': [
      'PERM_M7_LOCAL_ADMIN',
      'PERM_M29_COMMUNITY_PROJECT',
      'PERM_M4_CERTIFICATION_MANAGE',
      'PERM_M5_EMPLOYER_VERIFICATION',
      'PERM_M17_MODERATOR',
    ],
    'R05.03': [
      'PERM_M7_LOCAL_ADMIN',
      'PERM_M29_COMMUNITY_PROJECT',
      'PERM_M4_CERTIFICATION_MANAGE',
      'PERM_M5_EMPLOYER_VERIFICATION',
      'PERM_M17_MODERATOR',
    ],

    // R06 - Tài xế
    'R06.01': ['PERM_M13_DELIVERY_ORDER', 'PERM_M13_SHIPMENT_TRACK'],
    'R06.02': ['PERM_M13_DELIVERY_ORDER', 'PERM_M13_SHIPMENT_TRACK'],

    // R07 - Nhà đầu tư
    'R07.01': ['PERM_M16_FINANCIAL_REPORT'],
    'R07.02': ['PERM_M16_FINANCIAL_REPORT'],

    // R08 - Ban điều hành cấp cao
    'R08.01': [
      'PERM_M22_STRATEGIC_DASHBOARD',
      'PERM_M22_GLOBAL_KPI',
      'PERM_M98_AI_ADVISOR',
      'PERM_ADMIN_FULL_ACCESS',
    ],
    'R08.02': ['PERM_M22_STRATEGIC_DASHBOARD', 'PERM_M16_FINANCIAL_REPORT', 'PERM_M98_AI_ADVISOR'],
    'R08.03': ['PERM_M16_INVOICE_MANAGE', 'PERM_M16_FINANCIAL_REPORT'],
    'R08.04': ['PERM_USER_LIST', 'PERM_USER_VIEW_DETAIL', 'PERM_USER_UPDATE'],
    'R08.05': ['PERM_M16_FINANCIAL_REPORT', 'PERM_M22_GLOBAL_KPI'],

    // R09 - Người phụ trách ngành
    'R09.01': [
      'PERM_M20_TOURISM_MANAGE',
      'PERM_M7_COMMUNITY_KPI',
      'PERM_M22_STRATEGIC_DASHBOARD',
      'PERM_M6_TRAINING_MANAGE',
      'PERM_M16_FINANCIAL_REPORT',
    ],
    'R09.02': [
      'PERM_M14_REAL_ESTATE',
      'PERM_M7_COMMUNITY_KPI',
      'PERM_M22_STRATEGIC_DASHBOARD',
      'PERM_M6_TRAINING_MANAGE',
      'PERM_M16_FINANCIAL_REPORT',
    ],
    'R09.03': [
      'PERM_M18_DIGITAL_CLINIC',
      'PERM_M7_COMMUNITY_KPI',
      'PERM_M22_STRATEGIC_DASHBOARD',
      'PERM_M6_TRAINING_MANAGE',
      'PERM_M16_FINANCIAL_REPORT',
    ],
    'R09.04': [
      'PERM_M19_CONSTRUCTION',
      'PERM_M7_COMMUNITY_KPI',
      'PERM_M22_STRATEGIC_DASHBOARD',
      'PERM_M6_TRAINING_MANAGE',
      'PERM_M16_FINANCIAL_REPORT',
    ],
    'R09.05': [
      'PERM_M12_BEAUTY_SERVICE',
      'PERM_M7_COMMUNITY_KPI',
      'PERM_M22_STRATEGIC_DASHBOARD',
      'PERM_M6_TRAINING_MANAGE',
      'PERM_M16_FINANCIAL_REPORT',
    ],

    // R10-R12 - Cán bộ cơ quan nhà nước
    'R10.01': [
      'PERM_B2G_DATA_ACCESS',
      'PERM_B2G_AUDIT_LOG',
      'PERM_B2G_GIS_MONITOR',
      'PERM_M4_CERTIFICATION_VIEW',
    ],
    'R11.01': ['PERM_B2G_DATA_ACCESS', 'PERM_B2G_AUDIT_LOG', 'PERM_M16_TAX_AUDIT'],
    'R12.01': [
      'PERM_B2G_DATA_ACCESS',
      'PERM_B2G_AUDIT_LOG',
      'PERM_B2G_INVESTIGATION',
      'PERM_M3_TRANSACTION_TRACE',
    ],

    // R13 - Đại sứ TAP
    'R13.01': [
      'PERM_M7_AMBASSADOR_DASHBOARD',
      'PERM_M8_GUARDIAN_ANGEL',
      'PERM_M8_SOS_RESPONSE',
      'PERM_M6_COURSE_VIEW',
      'PERM_M3_WALLET_BASIC',
    ],

    // R14 - Chuyên viên thẩm định
    'R14.01': [
      'PERM_M7_AUDIT_APP',
      'PERM_M2_STORE_VERIFICATION',
      'PERM_M4_CERTIFICATION_MANAGE',
      'PERM_M5_EMPLOYER_VERIFICATION',
    ],

    // R15 - Trưởng nhóm cộng đồng
    'R15.01': [
      'PERM_M17_COMMUNITY_LEADER',
      'PERM_M17_EVENT_ORGANIZE',
      'PERM_M6_WORKSHOP_ORGANIZE',
      'PERM_M2_GROUP_BUYING',
      'PERM_M29_VOTING',
    ],

    // R16 - Cộng tác viên AI
    'R16.01': [
      'PERM_M27_AI_FOUNDRY',
      'PERM_M27_DATA_LABELING',
      'PERM_M3_WALLET_BASIC',
      'PERM_M6_COURSE_VIEW',
      'PERM_M7_TRUST_SCORE_VIEW',
    ],
  };

  // Apply permissions to role groups
  for (const [roleGroupCode, permCodes] of Object.entries(roleGroupPermissions)) {
    const roleGroup = await roleGroupRepo.findOneBy({ code: roleGroupCode });
    if (!roleGroup) continue;

    for (const permCode of permCodes) {
      const permission = await permissionRepo.findOneBy({ code: permCode });
      if (!permission) continue;

      const exists = await rgpRepo.findOne({
        where: { roleGroup: { id: roleGroup.id }, permission: { id: permission.id } },
        relations: ['roleGroup', 'permission'],
      });

      if (!exists) {
        await rgpRepo.save(rgpRepo.create({ roleGroup, permission }));
      }
    }
  }

  // Grant full permissions to admin role groups (R05.01 and R08.01)
  const adminRoleGroups = ['R05.01', 'R08.01'];
  const allPermissions = await permissionRepo.find();

  for (const adminCode of adminRoleGroups) {
    const adminRoleGroup = await roleGroupRepo.findOneBy({ code: adminCode });
    if (!adminRoleGroup) {
      console.warn(`Không tìm thấy admin role group: ${adminCode}`);
      continue;
    }

    for (const permission of allPermissions) {
      const exists = await rgpRepo.findOne({
        where: { roleGroup: { id: adminRoleGroup.id }, permission: { id: permission.id } },
        relations: ['roleGroup', 'permission'],
      });

      if (!exists) {
        await rgpRepo.save(rgpRepo.create({ roleGroup: adminRoleGroup, permission }));
      }
    }
  }

  // Assign basic permissions to all other role groups
  const basicPermCodes = [
    'PERM_USER_VIEW_SELF',
    'PERM_USER_UPDATE_SELF',
    'PERM_USER_2FA_TOGGLE',
    'PERM_SESSION_LIST',
  ];
  const allExistingRoleGroups = await roleGroupRepo.find();

  for (const rg of allExistingRoleGroups) {
    // Skip admin groups that already have full access
    if (rg.code === 'R05.01' || rg.code === 'R08.01') continue;

    for (const code of basicPermCodes) {
      const perm = await permissionRepo.findOneBy({ code });
      if (perm) {
        const exists = await rgpRepo.findOne({
          where: { roleGroup: { id: rg.id }, permission: { id: perm.id } },
          relations: ['roleGroup', 'permission'],
        });
        if (!exists) {
          await rgpRepo.save(rgpRepo.create({ roleGroup: rg, permission: perm }));
        }
      }
    }
  }

  console.log('✅ Extended RBAC seeding completed successfully!');
}
