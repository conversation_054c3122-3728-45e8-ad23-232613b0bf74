import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../libs/axios/axiosBase'

// Types hiện tại
export type SignInProps = {
  email: string
  password: string
  authRequired?: boolean
}

export type SignUpProps = {
  fullName: string
  email: string
  password: string
  referralCode?: string
  phone: string
  roleId: string
}

export type GetUserProps = {
  token: string
}

export type SendOtpProps = {
  target: string
}

export type VerifyOtpProps = {
  target: string
  otp: string
}

export type ResetPasswordRequest = {
  email: string
  newPassword: string
}

// Type mới cho social login
export type SocialLoginProps = {
  provider: string
  accessToken: string
}

// Định nghĩa type cho tham số query
export type GetUserProfileParams = {
  token: string
}

export type Request2FAOTPProps = {
  target: string
}

export type Verify2FAOTPProps = {
  target: string
  otp: string
}

export type Verify2FALoginProps = {
  userId: string
  otpCode: string
  totpCode: string
}

export type Disable2FAProps = {
  target: string // email
  otp: string // mã OTP
}

export type ChangePasswordProps = {
  currentPassword: string
  newPassword: string
}

// Type mới cho sessions
export type SessionData = {
  id: string
  ip_address: string
  user_agent: string
  isTrusted: boolean
  createdAt: string
  location: string | null
  fingerprint: string | null
}

export type SessionsResponse = {
  data: SessionData[]
  message: string
  statusCode: number
}

export interface RoleType {
  id: string
  role_code: string
  name: string
  description: string
}

export interface AuthUser {
  id: string | null
  email: string | null
  createdAt: string | null
  roleGroupCode: string | null
  role: RoleType | null
  sub: string | null
  permissions: string[]
  iat: number
  exp: number
}

const baseUrl = 'http://localhost:8080'

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  endpoints: (build) => ({
    login: build.mutation({
      query: (userData: SignInProps) => ({
        url: '/auth/login',
        method: 'POST',
        data: userData,
        authRequired: false,
      }),
    }),
    register: build.mutation({
      query: (userData: SignUpProps) => ({
        url: '/auth/register',
        method: 'POST',
        data: userData,
        authRequired: false,
      }),
    }),
    getAuthUser: build.mutation<AuthUser, GetUserProps>({
      query: (data) => ({
        url: '/auth/me',
        method: 'POST',
        data: { token: data.token },
        authRequired: false,
      }),
    }),
    sendOtp: build.mutation({
      query: (data: SendOtpProps) => ({
        url: '/auth/send-otp',
        method: 'POST',
        data: data,
        authRequired: false,
      }),
    }),
    verifyOtp: build.mutation({
      query: (data: VerifyOtpProps) => ({
        url: '/auth/verify-otp',
        method: 'POST',
        data: data,
        authRequired: false,
      }),
    }),

    sendOtpResetPassword: build.mutation({
      query: (data: SendOtpProps) => ({
        url: '/auth/forgot-password/send-otp',
        method: 'POST',
        data: data,
        authRequired: false,
      }),
    }),

    verifyOtpPassword: build.mutation({
      query: (data: VerifyOtpProps) => ({
        url: '/auth/forgot-password/verify-otp',
        method: 'POST',
        data: data,
        authRequired: false,
      }),
    }),

    resetPassword: build.mutation({
      query: (data: ResetPasswordRequest) => ({
        url: '/auth/forgot-password/reset',
        method: 'POST',
        data: data,
        authRequired: false,
      }),
    }),

    // API mới cho social login
    socialLogin: build.mutation({
      query: (data: SocialLoginProps) => ({
        url: '/auth/login/social',
        method: 'POST',
        data: data,
        authRequired: false,
      }),
    }),

    request2FAOTP: build.mutation<any, Request2FAOTPProps>({
      query: (data) => ({
        url: '/auth/2fa/otp/request',
        method: 'POST',
        data: data,
        authRequired: true,
      }),
    }),

    verify2FAOTP: build.mutation<any, Verify2FAOTPProps>({
      query: (data) => ({
        url: '/auth/2fa/otp/verify',
        method: 'POST',
        data: data,
        authRequired: true,
      }),
    }),

    verify2FALogin: build.mutation<any, Verify2FALoginProps>({
      query: (data) => ({
        url: '/auth/verify-2fa',
        method: 'POST',
        data: data,
        authRequired: false,
      }),
    }),

    // API mới cho disable 2FA
    disable2FA: build.mutation<any, Disable2FAProps>({
      query: (data) => ({
        url: '/auth/2fa/otp/confirm-disable',
        method: 'POST',
        data: {
          target: data.target, // email
          otp: data.otp, // OTP code
        },
        authRequired: true,
      }),
    }),

    changePassword: build.mutation<any, ChangePasswordProps>({
      query: (data) => ({
        url: '/users/change-password',
        method: 'POST',
        data: {
          currentPassword: data.currentPassword,
          newPassword: data.newPassword,
        },
        authRequired: true,
      }),
    }),

    // API mới cho sessions
    getSessions: build.query<SessionsResponse, null>({
      query: () => ({
        url: '/auth/sessions',
        method: 'GET',
        authRequired: true,
      }),
    }),
  }),
})

export const {
  useLoginMutation,
  useRegisterMutation,
  useGetAuthUserMutation,
  useSendOtpMutation,
  useVerifyOtpMutation,
  useSendOtpResetPasswordMutation,
  useVerifyOtpPasswordMutation,
  useResetPasswordMutation,
  useSocialLoginMutation,
  useRequest2FAOTPMutation,
  useVerify2FAOTPMutation,
  useVerify2FALoginMutation,
  useDisable2FAMutation,
  useChangePasswordMutation,
  useGetSessionsQuery,
} = authApi
