@import '../../styles/variables.scss';

.modalButtonGroup {
  display: flex;
  gap: $modal-button-gap;
  justify-content: flex-end;
  align-items: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  flex-wrap: wrap;
  
  > * {
    min-width: 80px;
    width: auto;
  }
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    
    > * {
      width: 100%;
      min-width: auto;
    }
  }
} 