import { Test, TestingModule } from '@nestjs/testing';
import { SessionService } from '../services/session.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuthSession } from '../../user/entities/auth_sessions.entity';
import { MailService } from 'src/mail/mail.service';
import { User } from '../../user/entities/user.entity';
import { Request } from 'express';

describe('SessionService', () => {
  let service: SessionService;
  let sessionRepo: Repository<AuthSession>;
  let mailService: MailService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SessionService,
        {
          provide: getRepositoryToken(AuthSession),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
          },
        },
        {
          provide: MailService,
          useValue: {
            waringLoginNewDevice: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<SessionService>(SessionService);
    sessionRepo = module.get(getRepositoryToken(AuthSession));
    mailService = module.get(MailService);
  });

  it('should check unrecognized device and save session', async () => {
    const user = { id: 'u1', email: '<EMAIL>', profile: { fullName: 'Tester' } } as User;
    const request = {
      headers: {
        'user-agent': 'Chrome',
        'x-forwarded-for': '***********',
      },
      socket: { remoteAddress: '***********' },
    } as unknown as Request;

    (sessionRepo.findOne as jest.Mock).mockResolvedValue(null);
    (sessionRepo.save as jest.Mock).mockResolvedValue({});

    await service.checkUnrecognizedDevice(user, request);

    expect(sessionRepo.save).toHaveBeenCalledWith(
      expect.objectContaining({ user_id: 'u1', user_agent: 'Chrome', ip_address: '***********' }),
    );
    expect(mailService.waringLoginNewDevice).toHaveBeenCalledWith(
      '<EMAIL>',
      'Tester',
      '***********',
      'Chrome',
    );
  });

  it('should return list of sessions for user', async () => {
    const fakeSessions = [
      {
        id: 's1',
        ip_address: '***********',
        user_agent: 'Chrome',
        is_recognized: true,
        created_at: new Date(),
        location: 'VN',
        device_fingerprint: 'abc123',
      },
    ];
    (sessionRepo.find as jest.Mock).mockResolvedValue(fakeSessions);

    const result = await service.getSessions('u1');

    expect(result.length).toBe(1);
    expect(result[0].ip_address).toBe('***********');
  });
});
