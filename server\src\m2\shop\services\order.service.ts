import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Order, OrderSource } from '../entities/order.entity';
import { Payment } from '../entities/payment.entity';
import { OrderItem } from '../entities/order_item.entity';
import { Status } from '../entities/status.entity';
import { CreateOrderFullDto, CreateOrderItemDto } from '../dto/create-order-full.dto';
import { CartItem } from '../entities/cart_item.entity';
import { PaginationRequest } from '../dto/pagination-request.dto';
import { LogService } from 'src/m_log/log/log.service';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { LogActionType } from 'src/constants';

@Injectable()
export class OrderService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(OrderItem)
    private readonly orderItemRepository: Repository<OrderItem>,
    @InjectRepository(Status)
    private readonly statusRepository: Repository<Status>,
    private readonly dataSource: DataSource,
    private readonly logService: LogService,
    @Inject('LOG_SERVICE')
    private readonly logClient: ClientProxy,
  ) {}

  /**
   * Lấy chi tiết đơn hàng cho user
   */
  async findOneForUser(id: string, user_id: string) {
    try {
      return await this.orderRepository.findOne({
        where: { id, user_id },
        relations: ['orderItems', 'orderItems.product', 'orderItems.version', 'status', 'payment'],
      });
    } catch (error) {
      throw new BadRequestException('Lỗi lấy chi tiết đơn hàng user: ' + (error?.message || error));
    }
  }

  /**
   * Lấy danh sách tất cả đơn hàng cho admin (pagination, search, filter by status)
   */
  async getAllOrdersForAdmin(dto: PaginationRequest) {
    try {
      const { page = 1, size = 20, search, status_id } = dto;
      const qb = this.orderRepository.createQueryBuilder('order');
      if (search) {
        // Nếu search là số, chỉ LIKE trên phone, name. Nếu là chuỗi, LIKE trên name, phone, id (nếu hợp lệ)
        const isNumeric = /^[0-9]+$/.test(search);
        if (isNumeric) {
          qb.andWhere(
            '("order".shipping_phone LIKE :search OR "order".shipping_name LIKE :search)',
            {
              search: `%${search}%`,
            },
          );
        } else {
          if (/^[0-9a-fA-F-]{8,}$/.test(search)) {
            qb.andWhere(
              '("order".id::text LIKE :search OR "order".shipping_name LIKE :search OR "order".shipping_phone LIKE :search)',
              { search: `%${search}%` },
            );
          } else {
            qb.andWhere(
              '("order".shipping_name LIKE :search OR "order".shipping_phone LIKE :search)',
              {
                search: `%${search}%`,
              },
            );
          }
        }
      }
      if (status_id) {
        qb.andWhere('order.status_id = :status_id', { status_id });
      }
      qb.orderBy('order.created_at', 'DESC')
        .skip((page - 1) * size)
        .take(size);
      const [data, total] = await qb.getManyAndCount();
      return {
        data,
        total,
        page,
        size,
        totalPages: Math.ceil(total / size),
      };
    } catch (error) {
      console.error('Error fetching orders for admin:', error);
      throw new BadRequestException(
        'Lỗi lấy danh sách đơn hàng admin: ' + (error?.message || error),
      );
    }
  }

  /**

  /**
   * Xác nhận đã nhận hàng dành cho user (chỉ khi trạng thái là DELIVERED)
   */
  async confirmReceivedOrder(order_id: string, user_id: string) {
    try {
      const order = await this.orderRepository.findOne({
        where: { id: order_id, user_id },
        relations: ['status'],
      });
      if (!order) throw new NotFoundException('Không tìm thấy đơn hàng');
      if (order.status?.code !== 'DELIVERED') {
        throw new BadRequestException('Chỉ xác nhận được khi đơn hàng đã giao hàng');
      }
      // Lấy id status COMPLETED
      const completedStatus = await this.statusRepository.findOne({ where: { code: 'COMPLETED' } });
      if (!completedStatus) throw new NotFoundException('Không tìm thấy trạng thái COMPLETED');

      await this.orderRepository.update(order_id, { status_id: completedStatus.id });

      this.logClient.emit('log.update', {
        module: 'order',
        action: LogActionType.UPDATE,
        performedBy: user_id,
        targetId: order_id,
        description: 'Xác nhận đã nhận hàng',
        timestamp: new Date().toISOString(),
        data: { order_id },
      });
      return order;
    } catch (error) {
      throw new BadRequestException('Lỗi xác nhận đã nhận hàng: ' + (error?.message || error));
    }
  }

  /**
   * Lấy danh sách tất cả đơn hàng của 1 shop (pagination, search, filter by status)
   */
  async getAllOrdersForShop(shop_id: string, dto: PaginationRequest) {
    try {
      const { page = 1, size = 20, search, status_id } = dto;
      // shop_id ở đây chính là userId của chủ shop
      const qb = this.orderRepository
        .createQueryBuilder('order')
        .innerJoin('order.orderItems', 'item')
        .innerJoin('item.product', 'product')
        .where('product.user_id = :user_id', { user_id: shop_id });
      if (search) {
        const isNumeric = /^[0-9]+$/.test(search);
        if (isNumeric) {
          qb.andWhere(
            '("order".shipping_phone LIKE :search OR "order".shipping_name LIKE :search)',
            {
              search: `%${search}%`,
            },
          );
        } else {
          if (/^[0-9a-fA-F-]{8,}$/.test(search)) {
            qb.andWhere(
              '("order".id::text LIKE :search OR "order".shipping_name LIKE :search OR "order".shipping_phone LIKE :search)',
              { search: `%${search}%` },
            );
          } else {
            qb.andWhere(
              '("order".shipping_name LIKE :search OR "order".shipping_phone LIKE :search)',
              {
                search: `%${search}%`,
              },
            );
          }
        }
      }
      if (status_id) {
        qb.andWhere('order.status_id = :status_id', { status_id });
      }
      qb.orderBy('order.created_at', 'DESC')
        .skip((page - 1) * size)
        .take(size);
      const [data, total] = await qb.getManyAndCount();
      return {
        data,
        total,
        page,
        size,
        totalPages: Math.ceil(total / size),
      };
    } catch (error) {
      console.error('Error fetching orders for shop:', error);
      throw new BadRequestException(
        'Lỗi lấy danh sách đơn hàng shop: ' + (error?.message || error),
      );
    }
  }

  /**
   * Xem chi tiết đơn hàng cho admin
   */
  async getOrderDetailForAdmin(order_id: string) {
    try {
      const order = await this.orderRepository.findOne({
        where: { id: order_id },
        relations: [
          'order_items',
          'order_items.product',
          'order_items.variant',
          'status',
          'payment',
        ],
      });
      if (!order) throw new NotFoundException('Không tìm thấy đơn hàng');
      return order;
    } catch (error) {
      console.error('Error fetching order detail for admin:', error);
      throw new BadRequestException(
        'Lỗi lấy chi tiết đơn hàng admin: ' + (error?.message || error),
      );
    }
  }

  /**
   * Xem chi tiết đơn hàng cho shop
   */
  async getOrderDetailForShop(shop_id: string, order_id: string) {
    try {
      const order = await this.orderRepository
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.orderItems', 'item')
        .leftJoinAndSelect('item.product', 'product')
        .leftJoinAndSelect('item.version', 'version')
        .leftJoinAndSelect('order.status', 'status')
        .leftJoinAndSelect('order.payment', 'payment')
        .where('order.id = :order_id', { order_id })
        .andWhere('product.user_id = :shop_id', { shop_id })
        .getOne();
      if (!order) throw new NotFoundException('Không tìm thấy đơn hàng');
      return order;
    } catch (error) {
      console.error('Error fetching order detail for shop:', error);
      throw new BadRequestException('Lỗi lấy chi tiết đơn hàng shop: ' + (error?.message || error));
    }
  }

  /**
   * Cập nhật trạng thái đơn hàng (admin/shop)
   */
  async updateOrderStatus(order_id: string, status_id: string, user_id: string) {
    try {
      return await this.dataSource.transaction(async (manager) => {
        const order = await manager.getRepository(Order).findOne({ where: { id: order_id } });
        if (!order) throw new NotFoundException('Không tìm thấy đơn hàng');
        const status = await manager.getRepository(Status).findOne({ where: { id: status_id } });
        if (!status) throw new NotFoundException('Không tìm thấy trạng thái');
        order.status_id = status_id;
        await manager.getRepository(Order).save(order);
        this.logClient.emit('log.update', {
          module: 'order',
          action: LogActionType.UPDATE,
          performedBy: user_id,
          targetId: order_id,
          description: 'Cập nhật trạng thái đơn hàng',
          timestamp: new Date().toISOString(),
          data: { order_id, status_id },
        });
        return order;
      });
    } catch (error) {
      console.error('Error updating order status:', error);
      throw new BadRequestException(
        'Lỗi cập nhật trạng thái đơn hàng: ' + (error?.message || error),
      );
    }
  }

  /**
   * Theo dõi đơn hàng dành cho người mua (lấy lịch sử trạng thái, tracking, v.v.)
   */
  async getOrderTrackingForUser(order_id: string, user_id: string) {
    try {
      // TODO: join với bảng log trạng thái, tracking, v.v.
      const order = await this.orderRepository.findOne({
        where: { id: order_id, user_id },
        relations: ['status'],
      });
      if (!order) throw new NotFoundException('Không tìm thấy đơn hàng');
      // TODO: lấy thêm lịch sử trạng thái nếu có
      return order;
    } catch (error) {
      throw new BadRequestException('Lỗi lấy tracking đơn hàng: ' + (error?.message || error));
    }
  }

  /**
   * Yêu cầu hủy đơn dành cho người mua (chừa đoạn logic kiểm tra điều kiện được phép hủy)
   */
  async requestCancelOrder(order_id: string, user_id: string, reason?: string) {
    try {
      // Lấy đơn hàng của user (không join status)
      const order = await this.orderRepository.findOne({
        where: { id: order_id, user_id },
      });
      if (!order)
        throw new NotFoundException('Không tìm thấy đơn hàng hoặc bạn không có quyền hủy đơn này');
      // Lấy status hiện tại để kiểm tra code
      const currentStatus = await this.statusRepository.findOne({ where: { id: order.status_id } });
      if (!currentStatus)
        throw new NotFoundException('Không tìm thấy trạng thái hiện tại của đơn hàng');
      if (!['PENDING', 'CONFIRMED', 'PACKING'].includes(currentStatus.code)) {
        throw new BadRequestException(
          'Chỉ có thể yêu cầu hủy khi đơn hàng đang ở trạng thái chờ xác nhận, đã xác nhận hoặc đang đóng gói',
        );
      }
      // Lấy id status CANCEL_REQUESTED
      const cancelRequestedStatus = await this.statusRepository.findOne({
        where: { code: 'CANCEL_REQUESTED' },
      });
      if (!cancelRequestedStatus)
        throw new NotFoundException('Không tìm thấy trạng thái CANCEL_REQUESTED');
      order.status_id = cancelRequestedStatus.id;
      order['cancel_request'] = true;
      order['cancel_reason'] = reason;
      order['cancel_requested_at'] = new Date();
      await this.orderRepository.save(order);
      // Lấy lại order mới nhất với relations
      const savedOrder = await this.orderRepository.findOne({
        where: { id: order_id },
        relations: ['status'],
      });
      this.logClient.emit('log.update', {
        module: 'order',
        action: LogActionType.UPDATE,
        performedBy: user_id,
        targetId: order_id,
        description: 'Yêu cầu hủy đơn hàng',
        timestamp: new Date().toISOString(),
        data: { order_id, reason },
      });
      return savedOrder;
    } catch (error) {
      console.error('Error requesting cancel order:', error);
      throw new BadRequestException('Lỗi yêu cầu hủy đơn: ' + (error?.message || error));
    }
  }

  /**
   * Admin hủy đơn hàng
   */
  async cancelOrderByAdmin(order_id: string, admin_id: string, reason?: string) {
    try {
      return await this.dataSource.transaction(async (manager) => {
        const order = await manager.getRepository(Order).findOne({ 
          where: { id: order_id },
          relations: ['orderItems']
        });
        if (!order) throw new NotFoundException('Không tìm thấy đơn hàng');
        // Lấy id status CANCELLED
        const cancelledStatus = await manager
          .getRepository(Status)
          .findOne({ where: { code: 'CANCELLED' } });
        if (!cancelledStatus) throw new NotFoundException('Không tìm thấy trạng thái CANCELLED');
        order.status_id = cancelledStatus.id;
        order['cancel_by_admin'] = admin_id;
        order['cancel_reason'] = reason;
        order['cancel_at'] = new Date();
        await manager.getRepository(Order).save(order);
        
        // ✅ HOÀN TRẢ STOCK KHI ADMIN HỦY ĐƠN HÀNG
        if (order.orderItems && order.orderItems.length > 0) {
          await this._restoreStockAfterCancel(order.orderItems, manager);
        }
        
        this.logClient.emit('log.update', {
          module: 'order',
          action: LogActionType.UPDATE,
          performedBy: admin_id,
          targetId: order_id,
          description: 'Admin hủy đơn hàng',
          timestamp: new Date().toISOString(),
          data: { order_id, reason },
        });
        return order;
      });
    } catch (error) {
      throw new BadRequestException('Lỗi admin hủy đơn: ' + (error?.message || error));
    }
  }

  /**
   * Lấy danh sách đơn hàng của user hiện tại
   */

  async getOrderListForUser(user_id: string) {
    try {
      const orders = await this.orderRepository.find({
        where: { user_id },
        order: { created_at: 'DESC' },
        relations: ['orderItems', 'orderItems.product', 'orderItems.version', 'status', 'payment'],
      });
      return orders.map((order) => ({
        id: order.id,
        user_id: order.user_id || '',
        guest_phone: order.guest_phone || '',
        guest_email: order.guest_email || '',
        status_id: order.status_id,
        status_label: order.status?.label || '',
        status_label_color: order.status?.color || '',
        source: order.source,
        total_price: order.total_price,
        shipping_name: order.shipping_name,
        shipping_phone: order.shipping_phone,
        shipping_address: order.shipping_address,
        note: order.note || '',
        payment_id: order.payment_id ?? null,
        payment_status: order.payment?.status ?? null,
        created_at: order.created_at,
        updated_at: order.updated_at,
        orderItemsCount: order.orderItems?.length || 0,
        items: order.orderItems?.map(item => ({
          id: item.id,
          product: item.product,
          version: item.version,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.total_price,
        })) || [],
      }));
    } catch (error) {
      throw new BadRequestException(
        'Lỗi lấy danh sách đơn hàng user: ' + (error?.message || error),
      );
    }
  }

  /**
   * Tạo đơn hàng mới từ user (giữ nguyên logic transactional)
   */
  async createOrder(user_id: string, dto: any) {
    try {
      return await this.dataSource.transaction(async (manager) => {
        // Lấy status mặc định (pending)
        const status = await manager
          .getRepository(Status)
          .findOne({ where: { code: 'PENDING', type: 'ORDER' } });
        if (!status) throw new NotFoundException('Không tìm thấy trạng thái đơn hàng mặc định');

        let items: any[] = [];
        if (dto.source === 'cart') {
          // Lấy cart_items đã chọn của user
          items = await manager.getRepository(CartItem).find({
            where: { user_id, is_checked: true },
          });
          if (!items.length) throw new NotFoundException('Không có sản phẩm nào trong giỏ hàng');
        } else if (dto.source === 'buy_now') {
          items = dto.items;
        } else {
          throw new BadRequestException('Nguồn tạo đơn hàng không hợp lệ');
        }

        // Tính tổng tiền
        let total_price = 0;
        for (const item of items) {
          total_price += Number(item.unit_price) * Number(item.quantity);
        }

        const order = manager.getRepository(Order).create({
          user_id,
          shipping_address: dto.shipping_address,
          shipping_name: dto.receiver_name,
          shipping_phone: dto.receiver_phone,
          note: dto.note,
          guest_phone: dto.guest_phone,
          guest_email: dto.guest_email,
          ref_code: dto.ref_code,
          is_guest: dto.is_guest ?? false,
          status_id: status.id,
          source: dto.source === 'cart' ? OrderSource.WEB : OrderSource.WEB,
          total_price: total_price.toString(),
        });
        const savedOrder = await manager.getRepository(Order).save(order);

        // Lưu order_items
        for (const item of items) {
          const orderItem = manager.getRepository(OrderItem).create({
            order_id: savedOrder.id,
            product_id: item.product_id,
            variant_id: item.version_id ?? item.variant_id,
            quantity: item.quantity,
            unit_price: item.unit_price.toString(),
            total_price: (Number(item.unit_price) * Number(item.quantity)).toString(),
            selected_options: item.selected_options,
            product_name: '',
          });
          await manager.getRepository(OrderItem).save(orderItem);
        }

        // ✅ CẬP NHẬT STOCK KHI TẠO ĐƠN HÀNG
        await this._updateStockAfterOrder(items, manager);

        // Nếu là từ giỏ hàng thì xoá các cart_item đã mua
        if (dto.source === 'cart') {
          await manager.getRepository(CartItem).delete({ user_id, is_checked: true });
        }

        return savedOrder;
      });
    } catch (error) {
      throw new BadRequestException('Tạo đơn hàng thất bại: ' + (error?.message || error));
    }
  }

  /**
   * Lấy danh sách các yêu cầu hủy đơn cho admin (phân trang, search)
   */
  async getCancelRequestsForAdmin(dto: PaginationRequest) {
    try {
      const { page = 1, size = 20, search, status_id } = dto;
      const qb = this.orderRepository
        .createQueryBuilder('order')
        .innerJoin('order.status', 'status')
        .where('status.code = :cancelCode', { cancelCode: 'CANCEL_REQUESTED' });
      if (search) {
        const isNumeric = /^[0-9]+$/.test(search);
        if (isNumeric) {
          qb.andWhere(
            '("order".shipping_phone LIKE :search OR "order".shipping_name LIKE :search)',
            { search: `%${search}%` },
          );
        } else {
          if (/^[0-9a-fA-F-]{8,}$/.test(search)) {
            qb.andWhere(
              '("order".id::text LIKE :search OR "order".shipping_name LIKE :search OR "order".shipping_phone LIKE :search)',
              { search: `%${search}%` },
            );
          } else {
            qb.andWhere(
              '("order".shipping_name LIKE :search OR "order".shipping_phone LIKE :search)',
              { search: `%${search}%` },
            );
          }
        }
      }
      if (status_id) {
        qb.andWhere('order.status_id = :status_id', { status_id });
      }
      qb.orderBy('order.created_at', 'DESC')
        .skip((page - 1) * size)
        .take(size);
      const [data, total] = await qb.getManyAndCount();
      return {
        data,
        total,
        page,
        size,
        totalPages: Math.ceil(total / size),
      };
    } catch (error) {
      throw new BadRequestException(
        'Lỗi lấy danh sách yêu cầu hủy đơn admin: ' + (error?.message || error),
      );
    }
  }

  /**
   * Lấy danh sách các yêu cầu hủy đơn cho shop/seller (phân trang, search)
   */
  async getCancelRequestsForShop(shop_id: string, dto: PaginationRequest) {
    try {
      const { page = 1, size = 20, search, status_id } = dto;
      const qb = this.orderRepository
        .createQueryBuilder('order')
        .innerJoin('order.status', 'status')
        .innerJoin('order.orderItems', 'item')
        .innerJoin('item.product', 'product')
        .where('status.code = :cancelCode', { cancelCode: 'CANCEL_REQUESTED' })
        .andWhere('product.user_id = :user_id', { user_id: shop_id });
      if (search) {
        const isNumeric = /^[0-9]+$/.test(search);
        if (isNumeric) {
          qb.andWhere(
            '("order".shipping_phone LIKE :search OR "order".shipping_name LIKE :search)',
            { search: `%${search}%` },
          );
        } else {
          if (/^[0-9a-fA-F-]{8,}$/.test(search)) {
            qb.andWhere(
              '("order".id::text LIKE :search OR "order".shipping_name LIKE :search OR "order".shipping_phone LIKE :search)',
              { search: `%${search}%` },
            );
          } else {
            qb.andWhere(
              '("order".shipping_name LIKE :search OR "order".shipping_phone LIKE :search)',
              { search: `%${search}%` },
            );
          }
        }
      }
      if (status_id) {
        qb.andWhere('order.status_id = :status_id', { status_id });
      }
      qb.orderBy('order.created_at', 'DESC')
        .skip((page - 1) * size)
        .take(size);
      const [data, total] = await qb.getManyAndCount();
      return {
        data,
        total,
        page,
        size,
        totalPages: Math.ceil(total / size),
      };
    } catch (error) {
      console.error('Error fetching cancel requests for shop:', error);
      throw new BadRequestException(
        'Lỗi lấy danh sách yêu cầu hủy đơn shop: ' + (error?.message || error),
      );
    }
  }

  /**
   * Duyệt yêu cầu hủy đơn (admin hoặc seller)
   */
  async acceptCancelRequest(order_id: string, user_id: string, note?: string) {
    try {
      return await this.dataSource.transaction(async (manager) => {
        const order = await manager.getRepository(Order).findOne({ 
          where: { id: order_id },
          relations: ['orderItems']
        });
        if (!order) throw new NotFoundException('Không tìm thấy đơn hàng');
        
        // Lấy id status CANCELLED
        const cancelledStatus = await manager
          .getRepository(Status)
          .findOne({ where: { code: 'CANCELLED' } });
        if (!cancelledStatus) throw new NotFoundException('Không tìm thấy trạng thái CANCELLED');
        
        order.status_id = cancelledStatus.id;
        order['cancel_accepted_by'] = user_id;
        order['cancel_accepted_at'] = new Date();
        order['cancel_accept_note'] = note;
        await manager.getRepository(Order).save(order);
        
        // ✅ HOÀN TRẢ STOCK KHI HỦY ĐƠN HÀNG
        if (order.orderItems && order.orderItems.length > 0) {
          await this._restoreStockAfterCancel(order.orderItems, manager);
        }
        
        this.logClient.emit('log.update', {
          module: 'order',
          action: LogActionType.UPDATE,
          performedBy: user_id,
          targetId: order_id,
          description: 'Duyệt yêu cầu hủy đơn',
          timestamp: new Date().toISOString(),
          data: { order_id, note },
        });
        return order;
      });
    } catch (error) {
      throw new BadRequestException('Lỗi duyệt yêu cầu hủy đơn: ' + (error?.message || error));
    }
  }

  /**
   * ✅ Cập nhật stock sau khi tạo đơn hàng
   */
  private async _updateStockAfterOrder(items: any[], manager: any) {
    const invRepo = manager.getRepository('Inventory');
    
    for (const item of items) {
      if (item.version_id) {
        // Cập nhật inventory cho version cụ thể
        const inv = await invRepo.findOne({ where: { version_id: item.version_id } });
        if (inv) {
          inv.stock_quantity = Math.max(0, inv.stock_quantity - item.quantity);
          await invRepo.save(inv);
        } else {
          // Fallback: cập nhật stock trong product_version
          const versionRepo = manager.getRepository('ProductVersion');
          const version = await versionRepo.findOne({ where: { id: item.version_id } });
          if (version) {
            version.stock = Math.max(0, version.stock - item.quantity);
            await versionRepo.save(version);
          }
        }
      } else {
        // Cập nhật inventory cho sản phẩm (không có version)
        const inv = await invRepo.findOne({ where: { product_id: item.product_id, version_id: null } });
        if (inv) {
          inv.stock_quantity = Math.max(0, inv.stock_quantity - item.quantity);
          await invRepo.save(inv);
        } else {
          // Fallback: cập nhật stock trong product
          const productRepo = manager.getRepository('Product');
          const product = await productRepo.findOne({ where: { id: item.product_id } });
          if (product) {
            product.stock = Math.max(0, product.stock - item.quantity);
            await productRepo.save(product);
          }
        }
      }
    }
  }

  /**
   * ✅ HOÀN TRẢ STOCK KHI HỦY ĐƠN HÀNG
   */
  private async _restoreStockAfterCancel(orderItems: any[], manager: any) {
    const invRepo = manager.getRepository('Inventory');

    for (const item of orderItems) {
      if (item.version_id) {
        // Cập nhật inventory cho version cụ thể
        const inv = await invRepo.findOne({ where: { version_id: item.version_id } });
        if (inv) {
          inv.stock_quantity = Math.max(0, inv.stock_quantity + item.quantity);
          await invRepo.save(inv);
        } else {
          // Fallback: cập nhật stock trong product_version
          const versionRepo = manager.getRepository('ProductVersion');
          const version = await versionRepo.findOne({ where: { id: item.version_id } });
          if (version) {
            version.stock = Math.max(0, version.stock + item.quantity);
            await versionRepo.save(version);
          }
        }
      } else {
        // Cập nhật inventory cho sản phẩm (không có version)
        const inv = await invRepo.findOne({ where: { product_id: item.product_id, version_id: null } });
        if (inv) {
          inv.stock_quantity = Math.max(0, inv.stock_quantity + item.quantity);
          await invRepo.save(inv);
        } else {
          // Fallback: cập nhật stock trong product
          const productRepo = manager.getRepository('Product');
          const product = await productRepo.findOne({ where: { id: item.product_id } });
          if (product) {
            product.stock = Math.max(0, product.stock + item.quantity);
            await productRepo.save(product);
          }
        }
      }
    }
  }
}