import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Product } from './products.entity';

@Entity('ai_image_vectors')
export class AiImageVector {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  product_id: string;
  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column({ type: 'varchar', length: 100 })
  image_id: string;

  // Nếu chưa dùng pgvector, có thể lưu tạm dưới dạng text hoặc jsonb
  @Column({ type: 'text', nullable: true })
  vector?: string;

  @Column({ type: 'varchar', length: 50 })
  model: string;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;
}
