@tailwind components;
@tailwind utilities;

@layer components {
  .all-\[unset\] {
    all: unset;
  }
}

:root {
  --base-border: rgba(233, 233, 233, 1);
  --base-gray-background: rgba(245, 246, 248, 1);
  --base-soft-background: rgba(255, 249, 243, 1);
  --body-texts-text-1-font-family: "Poppins", Helvetica;
  --body-texts-text-1-font-size: 12px;
  --body-texts-text-1-font-style: normal;
  --body-texts-text-1-font-weight: 400;
  --body-texts-text-1-letter-spacing: 0px;
  --body-texts-text-1-line-height: 16px;
  --body-texts-text-2-font-family: "Poppins", Helvetica;
  --body-texts-text-2-font-size: 12px;
  --body-texts-text-2-font-style: normal;
  --body-texts-text-2-font-weight: 500;
  --body-texts-text-2-letter-spacing: 0px;
  --body-texts-text-2-line-height: 16px;
  --body-texts-text-4-font-family: "Poppins", Helvetica;
  --body-texts-text-4-font-size: 16px;
  --body-texts-text-4-font-style: normal;
  --body-texts-text-4-font-weight: 500;
  --body-texts-text-4-letter-spacing: 0px;
  --body-texts-text-4-line-height: 20px;
  --body-texts-text-5-font-family: "Poppins", Helvetica;
  --body-texts-text-5-font-size: 14px;
  --body-texts-text-5-font-style: normal;
  --body-texts-text-5-font-weight: 500;
  --body-texts-text-5-letter-spacing: 0px;
  --body-texts-text-5-line-height: 18px;
  --body-texts-text-6-font-family: "Poppins", Helvetica;
  --body-texts-text-6-font-size: 14px;
  --body-texts-text-6-font-style: normal;
  --body-texts-text-6-font-weight: 400;
  --body-texts-text-6-letter-spacing: 0px;
  --body-texts-text-6-line-height: 24px;
  --color-text-paragraph: rgba(41, 41, 41, 1);
  --color-text-subtext: rgba(154, 154, 154, 1);
  --color-text-text: rgba(62, 62, 62, 1);
  --duotone: rgba(126, 134, 158, 0.25);
  --fillicon: rgba(255, 255, 255, 1);
  --heading-title-text-2-font-family: "Poppins", Helvetica;
  --heading-title-text-2-font-size: 16px;
  --heading-title-text-2-font-style: normal;
  --heading-title-text-2-font-weight: 500;
  --heading-title-text-2-letter-spacing: 0px;
  --heading-title-text-2-line-height: 20px;
  --heading-title-text-3-font-family: "Poppins", Helvetica;
  --heading-title-text-3-font-size: 18px;
  --heading-title-text-3-font-style: normal;
  --heading-title-text-3-font-weight: 500;
  --heading-title-text-3-letter-spacing: 0px;
  --heading-title-text-3-line-height: 26px;
  --lineicon: rgba(204, 210, 227, 1);
  --main-light: rgba(214, 214, 214, 1);
  --main-medium: rgba(95, 95, 95, 1);
  --main-middle-black: rgba(11, 11, 11, 1);
  --main-primary: rgba(235, 109, 32, 1);
  --main-thick-black: rgba(4, 4, 4, 1);
  --main-white: rgba(255, 255, 255, 1);
  --mainthird: rgba(235, 132, 38, 1);
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: transparent;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --card: transparent;
    --card-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}
