FROM python:3.10-slim

RUN apt-get update && apt-get install -y build-essential gcc libgl1-mesa-glx libglib2.0-0 && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt


# Chỉ copy các file cần thiết, tr<PERSON>h copy toàn bộ nếu không cần
COPY main.py ./
COPY tapODBest.pt ./
COPY yolov5 ./yolov5

EXPOSE 8003

CMD ["python", "main.py"]