.container {
  min-height: 100vh;
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
}

.header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 10px 0;
}

.logo {
  width: 150px;
  height: 150px;
}

.title {
  font-size: 2.2rem;
  font-weight: bold;
  color: #2e2e2e;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  user-select: none;
}

.formContainer {
  background-color: #ffffff;
  padding: 2.5rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 500px;
  margin: 0;
}

.subTitle {
  font-size: 1.4rem;
  color: #7a7a7a;
  text-align: center;
  margin-bottom: 1.5rem;
  user-select: none;
}

.socialButtons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.googleButtonWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.socialButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  color: #2e2e2e;
  padding: 0.75rem 2rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  width: 180px;

  &:hover {
    background-color: #f5f5f5;
    border-color: #34a853;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.divider {
  text-align: center;
  color: #7a7a7a;
  margin-bottom: 1.5rem;
  position: relative;
}

.dividerLine {
  border-bottom: 1px solid #e0e0e0;
  position: absolute;
  top: 50%;
  width: 45%;
  transform: translateY(-50%);

  &.left {
    left: 0;
  }

  &.right {
    right: 0;
  }
}

.dividerText {
  background-color: #ffffff;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
  font-size: 1.1rem;
}

.label {
  color: #2e2e2e;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #ffffff;
  transition: all 0.2s ease;
  
  // CSS Variables cho responsive
  --icon-size: 1.25rem;
  --icon-padding: 0.75rem;
  --input-padding-left: 2.5rem;
  --input-padding-right: 2.5rem;

  &:focus-within {
    border-color: #34a853;
    box-shadow: 0 0 0 0.2rem rgba(52, 168, 83, 0.4);
  }
}

.input {
  border: none !important;
  border-radius: 0 !important;
  color: #2e2e2e;
  width: 100%;
  font-size: 1.1rem;
  background: transparent !important;
  box-shadow: none !important;
  padding-left: var(--input-padding-left) !important;
  padding-right: var(--input-padding-right) !important;
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;

  &:focus {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }

  &::placeholder {
    color: #999;
  }
}

.inputIcon {
  position: absolute;
  left: var(--icon-padding);
  color: #7a7a7a;
  z-index: 2;
  flex-shrink: 0;
  width: var(--icon-size);
  height: var(--icon-size);
  pointer-events: none;
}

.passwordToggle {
  position: absolute;
  right: var(--icon-padding);
  cursor: pointer;
  color: #7a7a7a;
  z-index: 2;
  flex-shrink: 0;
  width: var(--icon-size);
  height: var(--icon-size);
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #2e2e2e;
  }
}

.checkboxContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.checkbox {
  color: #2e2e2e;
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  cursor: pointer;

  input {
    margin-right: 0.5rem;
    accent-color: #34a853;
  }
}

.link {
  color: #2d9cdb;
  text-decoration: none;
  font-size: 1.1rem;
  cursor: pointer;

  &:hover {
    color: #34a853;
    text-decoration: underline;
  }
}

.submitButton {
  background-color: #2d9cdb;
  border-color: #2d9cdb;
  color: #ffffff;
  width: 100%;
  padding: 0.75rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 1.1rem;
  border: none;
  cursor: pointer;

  &:hover:not(:disabled) {
    background-color: #34a853;
    border-color: #34a853;
    transform: scale(1.02);
  }

  &:disabled {
    background-color: #b2dfdb;
    border-color: #b2dfdb;
    cursor: not-allowed;
    transform: none;
  }
}

.registerContainer {
  text-align: center;
  margin-top: 1.5rem;
  font-size: 1.1rem;
}

.successMessage {
  color: #34a853;
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  padding: 1rem;
  background-color: rgba(52, 168, 83, 0.1);
  border: 1px solid rgba(52, 168, 83, 0.3);
  border-radius: 8px;
}

// Animation classes
.fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.slideUp {
  animation: slideUp 0.4s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// Tablet responsive
@media (max-width: 992px) {
  .formContainer {
    max-width: 450px;
    padding: 2rem;
  }

  .socialButton {
    width: 160px;
    padding: 0.7rem 1.5rem;
  }
}

// Mobile responsive
@media (max-width: 768px) {
  .container {
    padding: 15px;
    align-items: flex-start;
    padding-top: 30px;
  }

  .formContainer {
    padding: 1.5rem;
    margin: 1rem 0;
    max-width: 100%;
  }

  .inputWrapper {
    --icon-size: 1.1rem;
    --icon-padding: 0.6rem;
    --input-padding-left: 2.2rem;
    --input-padding-right: 2.2rem;
    margin-bottom: 0.8rem;
  }

  .input {
    font-size: 16px !important; // Prevent zoom on iOS
    padding-top: 0.6rem !important;
    padding-bottom: 0.6rem !important;
  }

  .socialButtons {
    flex-direction: column;
    gap: 0.5rem;
  }

  .socialButton {
    width: 100%;
    padding: 0.6rem 1.5rem;
    font-size: 1rem;
  }

  .subTitle {
    font-size: 1.2rem;
  }

  .label {
    font-size: 1rem;
  }

  .submitButton {
    padding: 0.6rem;
    font-size: 1rem;
  }

  .logo {
    width: 120px;
    height: 120px;
  }

  .checkboxContainer {
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.2rem;
  }

  .checkbox {
    font-size: 1rem;
  }

  .link {
    font-size: 1rem;
  }
}

// Small mobile
@media (max-width: 480px) {
  .container {
    padding: 10px;
    padding-top: 20px;
  }

  .formContainer {
    padding: 1rem;
    margin: 0.5rem 0;
  }

  .inputWrapper {
    --icon-size: 1rem;
    --icon-padding: 0.5rem;
    --input-padding-left: 2rem;
    --input-padding-right: 2rem;
    margin-bottom: 0.7rem;
  }

  .input {
    font-size: 16px !important;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .socialButton {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .subTitle {
    font-size: 1.1rem;
    margin-bottom: 1.2rem;
  }

  .label {
    font-size: 0.9rem;
  }

  .submitButton {
    padding: 0.5rem;
    font-size: 0.9rem;
  }

  .checkboxContainer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.8rem;
    margin-bottom: 1rem;
  }

  .checkbox {
    font-size: 0.9rem;
  }

  .link {
    font-size: 0.9rem;
  }

  .registerContainer {
    font-size: 0.9rem;
    margin-top: 1.2rem;
  }
}

// Extra small screens
@media (max-width: 360px) {
  .formContainer {
    padding: 0.8rem;
  }

  .inputWrapper {
    --icon-size: 0.9rem;
    --icon-padding: 0.4rem;
    --input-padding-left: 1.8rem;
    --input-padding-right: 1.8rem;
  }

  .input {
    font-size: 16px !important;
    padding-top: 0.4rem !important;
    padding-bottom: 0.4rem !important;
  }

  .logo {
    width: 100px;
    height: 100px;
  }

  .subTitle {
    font-size: 1rem;
  }
}
// 2FA Styles
.twoFAHeader {
  text-align: center;
  margin-bottom: 24px;

  .twoFAIcon {
    width: 48px;
    height: 48px;
    color: #1890ff;
    margin: 0 auto 16px;
    display: block;
  }

  .twoFATitle {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  .twoFADesc {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
  }
}

.errorAlert {
  margin-bottom: 16px;
  border-radius: 6px;
}
