import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Order } from '../entities/order.entity';
import { OrderItem } from '../entities/order_item.entity';
import { Product } from '../entities/products.entity';
import {
  OrderStatisticsDto,
  OrderTrendDto,
  PaymentMethodStatsDto,
  RevenueDetailsDto,
  CancelledOrderListDto,
  OrderStatisticsQueryDto,
} from '../dto/order-statistics.dto';

@Injectable()
export class OrderStatisticsService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepo: Repository<Order>,
    @InjectRepository(OrderItem)
    private readonly orderItemRepo: Repository<OrderItem>,
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,
  ) {}

  async getStatistics(
    query: OrderStatisticsQueryDto & { shopId?: string; adminRegion?: string },
  ): Promise<OrderStatisticsDto> {
    // Filter by date range
    const where: any = {};
    if (query.from && query.to) {
      where.created_at = {
        $gte: new Date(query.from),
        $lte: new Date(query.to),
      };
    }
    // If shopId provided, filter orders by products.user_id
    let shopUserId = query.shopId;
    let orders: Order[];
    if (shopUserId) {
      // Get all product ids of this shop
      const productIds = (await this.productRepo.find({ where: { user_id: shopUserId } })).map(
        (p) => p.id,
      );
      // Get all order items with these product ids
      const orderItemIds = (
        await this.orderItemRepo.find({ where: { product_id: In(productIds) } })
      ).map((oi) => oi.order_id);
      // Get all orders with those ids
      orders = await this.orderRepo.findByIds(orderItemIds);
    } else {
      orders = await this.orderRepo.find({ where });
    }
    // Calculate statistics
    const totalOrders = orders.length;
    const completedOrders = orders.filter((o) => o.status?.code === 'completed').length;
    const cancelledOrders = orders.filter((o) => o.status?.code === 'cancelled').length;
    const totalRevenue = orders.reduce((sum, o) => sum + parseFloat(o.total_price || '0'), 0);
    const actualRevenue = totalRevenue; // TODO: subtract refunds if needed
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    // TODO: previousPeriodComparison logic
    const previousPeriodComparison = 0;
    return {
      totalOrders,
      completedOrders,
      cancelledOrders,
      totalRevenue,
      actualRevenue,
      averageOrderValue,
      previousPeriodComparison,
    };
  }

  async getTrend(query: OrderStatisticsQueryDto): Promise<OrderTrendDto[]> {
    // TODO: Implement actual logic
    return [
      {
        date: '2025-07-01',
        orders: 10,
        revenue: 1200000,
        completedOrders: 8,
        cancelledOrders: 2,
      },
    ];
  }

  async getPaymentMethods(query: OrderStatisticsQueryDto): Promise<PaymentMethodStatsDto[]> {
    // TODO: Implement actual logic
    return [
      { method: 'COD', value: 50, percentage: 50, color: '#1890ff' },
      { method: 'Bank', value: 30, percentage: 30, color: '#52c41a' },
      { method: 'E-wallet', value: 20, percentage: 20, color: '#faad14' },
    ];
  }

  async getRevenueDetails(query: OrderStatisticsQueryDto): Promise<RevenueDetailsDto> {
    // TODO: Implement actual logic
    return { actualRevenue: 9500000, totalRevenue: ******** };
  }

  async getCancelledOrders(query: OrderStatisticsQueryDto): Promise<CancelledOrderListDto[]> {
    // TODO: Implement actual logic
    return [{ id: 'order1', cancelledAt: '2025-07-10', amount: 120000, customer: 'Nguyen Van A' }];
  }

  /**
   * Lấy thống kê cho SuperAdmin - bao gồm thông tin khu vực và thuế
   */
  async getStatisticsForSuperAdmin(query: OrderStatisticsQueryDto): Promise<
    OrderStatisticsDto & {
      regionBreakdown: any[];
      taxSummary: any;
    }
  > {
    const baseStats = await this.getStatistics(query);

    // TODO: Implement region breakdown logic
    const regionBreakdown = [
      {
        region_code: '01', // Hà Nội
        region_name: 'Hà Nội',
        total_orders: 150,
        total_revenue: 5000000,
        total_shops: 25,
        avg_order_value: 33333,
      },
      {
        region_code: '79', // TP.HCM
        region_name: 'TP. Hồ Chí Minh',
        total_orders: 200,
        total_revenue: 8000000,
        total_shops: 35,
        avg_order_value: 40000,
      },
    ];

    // TODO: Implement tax calculation logic based on role-based tax policy
    const taxSummary = {
      total_taxable_revenue: 13000000, // Tổng doanh thu chịu thuế
      total_tax_amount: 1300000, // Tổng số thuế phải nộp (10%)
      tax_by_role: {
        R01: {
          // Thành viên HTX - sản xuất nông nghiệp
          revenue: 3000000,
          tax_rate: 0, // Không chịu thuế trực tiếp
          tax_amount: 0,
          note: 'HTX đại diện kê khai',
        },
        R02: {
          // Người bán hàng TMĐT
          revenue: 5000000,
          tax_rate: 0.1, // 10% GTGT
          tax_amount: 500000,
          note: 'TNCN/GTGT theo ngưỡng',
        },
        R04: {
          // Chuyên gia - Bác sĩ - Giảng viên
          revenue: 2000000,
          tax_rate: 0.1,
          tax_amount: 200000,
          note: 'TNCN/GTGT nếu tự doanh',
        },
        R06: {
          // Tài xế, người giao hàng
          revenue: 1000000,
          tax_rate: 0.05, // 5% TNCN
          tax_amount: 50000,
          note: 'TNCN nếu thu nhập cao',
        },
        R07: {
          // Thành viên góp vốn
          revenue: 2000000,
          tax_rate: 0.05, // 5% thu nhập từ đầu tư
          tax_amount: 100000,
          note: 'Thuế thu nhập từ đầu tư vốn',
        },
      },
      htx_cooperative_tax: {
        total_htx_revenue: 8000000, // Doanh thu qua HTX
        htx_tax_rate: 0.15, // 15% thuế TNDN cho HTX
        htx_tax_amount: 1200000,
        member_share_after_tax: 6800000, // Chia lại cho thành viên sau thuế
      },
    };

    return {
      ...baseStats,
      regionBreakdown,
      taxSummary,
    };
  }

  /**
   * Lấy thống kê cho Admin - theo khu vực và thông tin người bán
   */
  async getStatisticsForAdmin(query: OrderStatisticsQueryDto & { adminRegion?: string }): Promise<
    OrderStatisticsDto & {
      sellerBreakdown: any[];
      regionTaxSummary: any;
    }
  > {
    const baseStats = await this.getStatistics({ ...query, adminRegion: query.adminRegion });

    // TODO: Filter by admin's region
    const sellerBreakdown = [
      {
        seller_id: 'user-1',
        seller_name: 'Nguyễn Văn A',
        seller_role: 'R01', // HTX member
        phone: '0123456789',
        location: 'Hà Nội',
        total_orders: 50,
        total_revenue: 2000000,
        tax_liability: 0, // HTX đại diện
        htx_coverage: true,
      },
      {
        seller_id: 'user-2',
        seller_name: 'Trần Thị B',
        seller_role: 'R02', // TMĐT seller
        phone: '0987654321',
        location: 'Hà Nội',
        total_orders: 30,
        total_revenue: 1500000,
        tax_liability: 150000, // 10% GTGT
        htx_coverage: false,
      },
    ];

    // TODO: Calculate tax for this admin's region
    const regionTaxSummary = {
      region_code: query.adminRegion || '01',
      region_name: 'Hà Nội', // TODO: Get from region mapping
      total_regional_revenue: 3500000,
      total_regional_tax: 150000,
      htx_members_revenue: 2000000,
      individual_sellers_revenue: 1500000,
      tax_optimization_potential: 75000, // Tiết kiệm thuế nếu join HTX
    };

    return {
      ...baseStats,
      sellerBreakdown,
      regionTaxSummary,
    };
  }

  /**
   * Lấy thống kê cho Shop - chỉ của shop đó với tính toán thuế
   */
  async getStatisticsForShop(query: OrderStatisticsQueryDto & { shopId: string }): Promise<
    OrderStatisticsDto & {
      shopTaxInfo: any;
      recommendations: any;
    }
  > {
    const baseStats = await this.getStatistics({ ...query, shopId: query.shopId });

    // TODO: Get shop's role from user table
    const shopRole: string = 'R02'; // Example: TMĐT seller

    // TODO: Calculate tax based on shop's role and revenue
    const shopTaxInfo = {
      shop_id: query.shopId,
      current_role: shopRole,
      total_revenue: baseStats.totalRevenue,
      current_tax_rate: shopRole === 'R01' ? 0 : 0.1, // HTX member vs individual
      current_tax_amount: shopRole === 'R01' ? 0 : baseStats.totalRevenue * 0.1,
      htx_member: shopRole === 'R01',
      quarterly_revenue: baseStats.totalRevenue * 3, // Estimate quarterly
      annual_revenue_estimate: baseStats.totalRevenue * 12,
      vat_threshold_status: baseStats.totalRevenue * 12 > ********* ? 'above' : 'below', // 200M VND/year
      personal_income_threshold: baseStats.totalRevenue * 12 > ********0 ? 'above' : 'below', // 100M VND/year
    };

    const recommendations = {
      should_join_htx: !shopTaxInfo.htx_member && shopTaxInfo.current_tax_amount > 500000,
      potential_tax_savings: shopTaxInfo.htx_member ? 0 : shopTaxInfo.current_tax_amount * 0.5,
      next_threshold_warning:
        shopTaxInfo.vat_threshold_status === 'below'
          ? `Còn ${********* - shopTaxInfo.annual_revenue_estimate} VND để đạt ngưỡng VAT`
          : null,
      suggested_actions: [
        shopTaxInfo.htx_member ? null : 'Cân nhắc gia nhập HTX để tối ưu thuế',
        shopTaxInfo.vat_threshold_status === 'above' ? 'Cần kê khai VAT định kỳ' : null,
        'Lưu trữ đầy đủ chứng từ để kê khai thuế',
      ].filter(Boolean),
    };

    return {
      ...baseStats,
      shopTaxInfo,
      recommendations,
    };
  }
}
