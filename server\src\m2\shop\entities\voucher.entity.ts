import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { User } from '../../../m1/user/entities/user.entity';

export enum VoucherType {
  PERCENT = 'percent',
  FIXED = 'fixed',
  REFERRAL = 'referral',
}

@Unique(['code'])
@Entity('vouchers')
export class Voucher {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @Column({ type: 'varchar', length: 50 })
  code: string;

  @Index()
  @Column({ type: 'enum', enum: VoucherType })
  type: VoucherType;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  value: string;

  @Column({ type: 'decimal', precision: 12, scale: 2, nullable: true })
  min_order?: string;

  @Column({ type: 'decimal', precision: 12, scale: 2, nullable: true })
  max_discount?: string;

  @Column({ type: 'int' })
  quantity: number;

  @Column({ type: 'int', default: 0 })
  used_count: number;

  @Index()
  @Column({ type: 'timestamp' })
  start_date: Date;

  @Index()
  @Column({ type: 'timestamp' })
  end_date: Date;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @Index()
  @Column('uuid', { nullable: true })
  created_by?: string;
  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'created_by' })
  createdBy?: User;

  @Column({ type: 'text', nullable: true })
  note?: string;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;
}
