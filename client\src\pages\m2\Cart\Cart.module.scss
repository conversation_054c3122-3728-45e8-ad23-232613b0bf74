// ===================================
// 🎨 CSS VARIABLES & GLOBAL STYLES
// ===================================
:root {
  --primary-color: #4CAF50;
  --secondary-color: #2E7D32;
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --error-color: #F44336;
  --text-primary: #333;
  --text-secondary: #666;
  --text-muted: #999;
  --border-color: #e0e0e0;
  --bg-light: #f5f5f5;
  --bg-white: #ffffff;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

// ===================================
// 🖥️ DESKTOP STYLES (DEFAULT)
// ===================================
.cartContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.cartHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  gap: 20px;
  
  .headerLeft {
    display: flex;
    flex-direction: column;
    gap: 12px;
    flex: 1;
    
    h1 {
      color: var(--text-primary);
      font-size: 28px;
      font-weight: 600;
      margin: 0;
    }
    
    .myOrdersLink {
      color: var(--text-secondary);
      text-decoration: none;
      font-weight: 500;
      font-size: 14px;
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      transition: var(--transition);
      display: inline-flex;
      align-items: center;
      gap: 6px;
      width: fit-content;
      
      svg {
        font-size: 16px;
      }
      
      &:hover {
        background-color: var(--bg-light);
        color: var(--primary-color);
        border-color: var(--primary-color);
      }
    }
  }
  
  .headerRight {
    display: flex;
    align-items: center;
    
    .backToShopping {
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      padding: 12px 20px;
      border: 2px solid var(--primary-color);
      border-radius: var(--border-radius);
      transition: var(--transition);
      white-space: nowrap;
      
      &:hover {
        background-color: var(--primary-color);
        color: white;
      }
    }
  }
}

.notifications {
  margin-bottom: 24px;
  
  .notification {
    padding: 12px 16px;
    border-radius: var(--border-radius);
    margin-bottom: 8px;
    
    &.success {
      background-color: #e8f5e8;
      border: 1px solid var(--success-color);
      color: var(--success-color);
    }
    
    &.warning {
      background-color: #fff3e0;
      border: 1px solid var(--warning-color);
      color: #e65100;
    }
  }
}

.cartContent {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 24px;
  align-items: start;
}

.cartLeft {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.cartActions {
  background: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 16px 20px;
  box-shadow: var(--shadow);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  
  .selectAllSection {
    .selectAllLabel {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      font-weight: 500;
      color: var(--text-primary);
      
      input[type="checkbox"] {
        width: 18px;
        height: 18px;
        accent-color: var(--primary-color);
      }
      
      span {
        font-size: 14px;
      }
    }
  }
  
  .bulkActions {
    display: flex;
    gap: 8px;
    
    button {
      font-size: 14px;
      padding: 8px 16px;
      height: auto;
    }
  }
}

.vendorGroup {
  background: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.vendorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: var(--bg-light);
  border-bottom: 1px solid var(--border-color);
  
  .selectAll {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    
    input[type="checkbox"] {
      width: 18px;
      height: 18px;
      accent-color: var(--primary-color);
    }
    
    span {
      font-weight: 500;
      color: var(--text-primary);
    }
  }
  
  .vendorName {
    font-weight: 600;
    color: var(--text-primary);
  }
}

.cartItem {
  display: grid;
  grid-template-columns: 40px 80px 1fr 120px 120px 100px 40px;
  gap: 16px;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  transition: var(--transition);
  animation: fadeIn 0.3s ease-out;
  
  &:hover {
    background-color: #fafafa;
    
    .itemImage img {
      transform: scale(1.05);
    }
  }
  
  &.outOfStock {
    opacity: 0.6;
    background-color: #f5f5f5;
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.itemSelect {
  input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
  }
}

.itemImage {
  position: relative;
  
  img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease;
  }
  
  .outOfStockBadge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--error-color);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
  }
}

.itemInfo {
  .itemName {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    display: block;
    margin-bottom: 4px;
    
    &:hover {
      color: var(--primary-color);
    }
  }
  
  .itemVariant {
    color: var(--text-secondary);
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .stockWarning {
    color: var(--warning-color);
    font-size: 12px;
    font-weight: 500;
  }
}

.itemPrice {
  .currentPrice {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 16px;
  }
  
  .originalPrice {
    color: var(--text-muted);
    text-decoration: line-through;
    font-size: 14px;
  }
}

.quantityControl {
  display: flex;
  align-items: center;
  gap: 8px;
  
  button {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    transition: var(--transition);
    
    &:hover:not(:disabled) {
      background-color: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  span {
    min-width: 32px;
    text-align: center;
    font-weight: 500;
  }
}

.itemTotal {
  font-weight: 600;
  color: var(--text-primary);
  text-align: right;
}

.removeItem {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: var(--transition);
  
  svg {
    font-size: 18px;
  }
  
  &:hover {
    background-color: #ffebee;
    color: var(--error-color);
  }
}

.cartRight {
  position: sticky;
  top: 20px;
}

.orderSummary {
  background: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 24px;
  box-shadow: var(--shadow);
  
  h3 {
    margin: 0 0 20px 0;
    color: var(--text-primary);
    font-size: 18px;
  }
  
  .summaryRow {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    
    &.discount {
      color: var(--success-color);
      font-weight: 500;
    }
    
    &.total {
      font-weight: 600;
      font-size: 18px;
      color: var(--text-primary);
    }
  }
  
  .summaryDivider {
    height: 1px;
    background-color: var(--border-color);
    margin: 16px 0;
  }
  
  .checkoutButton {
    width: 100%;
    padding: 16px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 16px;
    
    &:hover:not(:disabled) {
      background-color: var(--secondary-color);
    }
    
    &:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
  }
  
  .continueShoppingActions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 16px;
    
    .continueShoppingBtn,
    .viewPromotionsBtn {
      padding: 12px;
      text-align: center;
      text-decoration: none;
      border-radius: var(--border-radius);
      font-weight: 500;
      transition: var(--transition);
    }
    
    .continueShoppingBtn {
      background-color: #f5f5f5;
      color: var(--text-primary);
      border: 1px solid var(--border-color);
      
      &:hover {
        background-color: #e0e0e0;
      }
    }
    
    .viewPromotionsBtn {
      background-color: #fff3e0;
      color: var(--warning-color);
      border: 1px solid var(--warning-color);
      
      &:hover {
        background-color: var(--warning-color);
        color: white;
      }
    }
  }
}

// ===================================
// 🎁 PROMO & SHIPPING SECTIONS
// ===================================
.promoSection {
  background: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--shadow);
  
  h3 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 18px;
  }
  
  .promoInput {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    
    input {
      flex: 1;
      padding: 12px;
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      font-size: 14px;
      
      &:focus {
        outline: none;
        border-color: var(--primary-color);
      }
    }
    
    button {
      padding: 12px 16px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--border-radius);
      cursor: pointer;
      font-weight: 500;
      transition: var(--transition);
      
      &:hover {
        background-color: var(--secondary-color);
      }
    }
  }
  
  .appliedPromo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #e8f5e8;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    margin-bottom: 16px;
    
    span {
      color: var(--success-color);
      font-weight: 500;
    }
    
    button {
      background: none;
      border: none;
      color: var(--text-muted);
      cursor: pointer;
      font-size: 18px;
      padding: 4px;
      
      &:hover {
        color: var(--error-color);
      }
    }
  }
  
  .availablePromoCodes {
    h4 {
      margin: 0 0 12px 0;
      color: var(--text-secondary);
      font-size: 14px;
    }
    
    .promoCode {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background-color: #f5f5f5;
      border-radius: var(--border-radius);
      margin-bottom: 8px;
      
      .promoCodeText {
        background-color: var(--primary-color);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
      }
      
      .promoDescription {
        flex: 1;
        font-size: 12px;
        color: var(--text-secondary);
      }
      
      button {
        background: none;
        border: 1px solid var(--primary-color);
        color: var(--primary-color);
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: var(--transition);
        
        &:hover {
          background-color: var(--primary-color);
          color: white;
        }
      }
    }
  }
}

.shippingSection {
  background: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--shadow);
  
  h3 {
    margin: 0 0 16px 0;
    color: var(--text-primary);
    font-size: 18px;
  }
  
  .shippingOptions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
    
    label {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 12px;
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      transition: var(--transition);
      
      &:hover {
        background-color: #f5f5f5;
      }
      
      input[type="radio"] {
        accent-color: var(--primary-color);
      }
      
      span:first-of-type {
        flex: 1;
        font-weight: 500;
      }
      
      .shippingFee {
        color: var(--primary-color);
        font-weight: 600;
      }
    }
  }
  
  .shippingInfo {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    
    p {
      margin: 4px 0;
      font-size: 14px;
      color: var(--text-secondary);
      
      &:first-child {
        margin-top: 0;
      }
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// ===================================
// 🛒 EMPTY CART SECTION
// ===================================
.emptyCart {
  text-align: center;
  padding: 60px 20px;
  
  .emptyCartIcon {
    font-size: 64px;
    margin-bottom: 16px;
  }
  
  h2 {
    color: var(--text-primary);
    margin-bottom: 8px;
  }
  
  p {
    color: var(--text-secondary);
    margin-bottom: 24px;
  }
  
  .startShoppingBtn {
    display: inline-block;
    padding: 12px 24px;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    
    &:hover {
      background-color: var(--secondary-color);
    }
  }
}

// ===================================
// 🎨 ANIMATIONS & EFFECTS
// ===================================
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Loading và Error states
.loading,
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: 18px;
  color: var(--text-secondary);
  text-align: center;
  padding: 40px 20px;
}

.error {
  color: var(--error-color);
  flex-direction: column;
  gap: 16px;
  
  button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    
    &:hover {
      background-color: var(--secondary-color);
    }
  }
}

// Focus states for accessibility
input:focus,
button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

// ===================================
// 📱 TABLET STYLES (768px - 1024px)
// ===================================
@media (max-width: 1024px) and (min-width: 769px) {
  .cartContent {
    grid-template-columns: 1fr 300px;
    gap: 20px;
  }
  
  .cartItem {
    grid-template-columns: 35px 70px 1fr 100px 100px 80px 35px;
    gap: 12px;
  }
  
  .orderSummary {
    padding: 20px;
  }
}

// ===================================
// 📱 MOBILE STYLES (≤ 768px)
// ===================================
@media (max-width: 768px) {
  // === CONTAINER & HEADER ===
  .cartContainer {
    padding: 16px;
  }
  
  .cartHeader {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    
    .headerLeft {
      width: 100%;
      
      h1 {
        font-size: 24px;
      }
      
      .myOrdersLink {
        width: 100%;
        justify-content: center;
        padding: 10px 12px;
      }
    }
    
    .headerRight {
      width: 100%;
      
      .backToShopping {
        width: 100%;
        text-align: center;
        padding: 12px;
      }
    }
  }
  
  // === CONTENT LAYOUT ===
  .cartContent {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .cartRight {
    position: static;
    order: 2;
  }
  
  // === VENDOR HEADER ===
  .vendorHeader {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
    padding: 12px 16px;
    
    .selectAll {
      order: 2;
    }
  }
  
  // === CART ITEMS ===
  .cartItem {
    grid-template-columns: 30px 60px 1fr 80px;
    gap: 12px;
    padding: 16px;
    position: relative;
    
    .itemPrice,
    .itemTotal {
      display: none;
    }
    
    .quantityControl {
      grid-column: 1 / -1;
      justify-content: center;
      margin-top: 12px;
      
      button {
        width: 40px;
        height: 40px;
        font-size: 18px;
      }
    }
    
    .removeItem {
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 20px;
      padding: 6px;
    }
  }
  
  // === CART ACTIONS ===
  .cartActions {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
    
    .selectAllSection {
      width: 100%;
      
      .selectAllLabel {
        justify-content: center;
        font-size: 16px;
        
        input[type="checkbox"] {
          width: 20px;
          height: 20px;
        }
      }
    }
    
    .bulkActions {
      width: 100%;
      
      button {
        width: 100%;
        padding: 12px;
        font-size: 16px;
      }
    }
  }
  
  // === PROMO & SHIPPING ===
  .promoSection,
  .shippingSection {
    padding: 16px;
    
    .promoInput {
      flex-direction: column;
      
      button {
        width: 100%;
        padding: 14px;
      }
    }
  }
  
  .shippingOptions label {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    
    .shippingFee {
      align-self: flex-end;
    }
  }
  
  .shippingInfo {
    padding: 10px;
    
    p {
      font-size: 13px;
    }
  }
  
  // === ORDER SUMMARY ===
  .orderSummary {
    padding: 16px;
    
    .checkoutButton {
      padding: 18px;
      font-size: 18px;
    }
  }
  
  // === EMPTY CART ===
  .emptyCart {
    padding: 40px 16px;
    
    .emptyCartIcon {
      font-size: 48px;
    }
  }
}

// ===================================
// 📱 SMALL MOBILE STYLES (≤ 480px)
// ===================================
@media (max-width: 480px) {
  // === EXTRA SMALL ADJUSTMENTS ===
  .cartItem {
    grid-template-columns: 30px 50px 1fr;
    
    .itemImage img {
      width: 50px;
      height: 50px;
    }
    
    .quantityControl {
      margin-top: 8px;
      
      button {
        width: 36px;
        height: 36px;
        font-size: 16px;
      }
    }
  }
  
  .itemInfo {
    .itemName {
      font-size: 14px;
    }
    
    .itemVariant {
      font-size: 12px;
    }
  }
  
  .notifications .notification {
    padding: 10px 12px;
    font-size: 14px;
  }
  
  .promoSection .availablePromoCodes .promoCode {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    
    .promoCodeText {
      align-self: flex-start;
    }
    
    button {
      align-self: flex-end;
    }
  }
}

// ===================================
// 🖨️ PRINT STYLES
// ===================================
@media print {
  .cartContainer {
    max-width: none;
    padding: 0;
  }
  
  .cartHeader .backToShopping,
  .removeItem,
  .quantityControl,
  .checkoutButton,
  .continueShoppingActions {
    display: none;
  }
  
  .cartContent {
    grid-template-columns: 1fr;
  }
  
  .orderSummary {
    box-shadow: none;
    border: 1px solid #000;
  }
}

.addToCartBtn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 8px 16px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  margin-top: 8px;
  transition: var(--transition);
  display: inline-block;

  &:hover:not(:disabled) {
    background-color: var(--secondary-color);
  }
  &:disabled {
    background-color: #ccc;
    color: #fff;
    cursor: not-allowed;
  }
}
