import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../../m1/user/entities/user.entity';

@Entity('ai_suggestions')
export class AiSuggestion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  user_id: string;
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'varchar', length: 50 })
  suggestion_type: string;

  @Column('uuid', { nullable: true })
  target_id?: string;

  @Column({ type: 'jsonb', nullable: true })
  content?: any;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  score?: string;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;
}
