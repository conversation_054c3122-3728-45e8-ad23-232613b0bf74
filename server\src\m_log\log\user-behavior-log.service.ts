import { Injectable, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserBehavior } from '../../m2/shop/entities/user_behavior.entity';
import { UserBehaviorLog } from './user-behavior-log.type';

@Injectable()
export class UserBehaviorLogService {
  constructor(
    @Inject('LOG_SERVICE')
    private readonly logClient: ClientProxy,
    @InjectRepository(UserBehavior)
    private readonly userBehaviorRepo: Repository<UserBehavior>,
  ) {}
  async saveUserBehavior(payload: Partial<UserBehavior>) {
    const entity = this.userBehaviorRepo.create(payload);
    return this.userBehaviorRepo.save(entity);
  }

  emitUserBehavior(log: User<PERSON>ehaviorLog) {
    this.logClient.emit('user_behavior', {
      ...log,
      timestamp: log.timestamp || new Date().toISOString(),
    });
  }
}
