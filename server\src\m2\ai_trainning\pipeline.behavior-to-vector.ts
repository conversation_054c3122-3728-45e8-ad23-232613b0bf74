// Đọc log hành vi người dùng và sinh vector huấn luyện cho AI
// Chạy: npx ts-node src/m2/ai_trainning/pipeline.behavior-to-vector.ts

import { AppDataSource } from '../../data-source';
import { UserBehavior } from '../shop/entities/user_behavior.entity';
import * as fs from 'fs';

async function main() {
  // 1. Kết nối DB
  await AppDataSource.initialize();
  const repo = AppDataSource.getRepository(UserBehavior);

  // 2. Đ<PERSON><PERSON> toàn bộ log hành vi
  const logs = await repo.find();

  // 3. Tiền xử lý dữ liệu (ví dụ: chỉ lấy 1 số trường)
  const data = logs.map((log) => ({
    user_id: log.user_id,
    event_type: log.event_type,
    product_id: log.product_id,
    event_data: log.event_data,
    created_at: log.created_at,
  }));

  // 4. Lưu ra file json để chuyển sang Python train
  fs.writeFileSync('user_behavior_logs.json', JSON.stringify(data, null, 2));

  // 5. (Tuỳ chọn) Gọi API Python để sinh vector hoặc chuyển file cho script Python xử lý
}

main().then(() => process.exit(0));
