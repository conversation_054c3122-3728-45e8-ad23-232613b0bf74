.chatbot-popup-container {
  position: fixed;
  top: 160px;
  right: 32px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.chatbot-toggle-btn {
  background: #1890ff;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  margin-bottom: 8px;
  transition: background 0.2s;
}

.chatbot-toggle-btn:hover {
  background: #40a9ff;
}

.chatbot-popup-box {
  width: 100%;
  max-width: 90vw;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.18);
  overflow: hidden;
  animation: chatbot-fade-in 0.2s;
}

@keyframes chatbot-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 600px) {
  .chatbot-popup-container {
    right: 8px;
    top: 80px;
  }
  .chatbot-popup-box {
    width: 98vw;
    min-width: 0;
  }
}
