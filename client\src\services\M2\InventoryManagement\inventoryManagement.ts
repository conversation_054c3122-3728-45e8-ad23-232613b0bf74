import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

export const inventoryManagementApi = createApi({
  reducerPath: 'inventoryManagementApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['Inventory', 'StockHistory', 'Alerts'],

  endpoints: (build) => ({
    // Lấy danh sách tồn kho
    getInventory: build.query<any, any>({
      query: (params) => ({
        url: '/inventory',
        method: 'GET',
        params,
        authRequired: true,
      }),
      providesTags: ['Inventory'],
    }),
    // Lấy chi tiết tồn kho
    getInventoryDetail: build.query<any, string>({
      query: (id) => ({
        url: `/inventory/${id}`,
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['Inventory'],
    }),
    // Cập nhật tồn kho cho biến thể
    updateVariants: build.mutation<any, { id: string; variants: any[] }>({
      query: ({ id, variants }) => ({
        url: `/inventory/${id}/variants`,
        method: 'PUT',
        data: variants,
        authRequired: true,
      }),
      invalidatesTags: ['Inventory'],
    }),
    // Cập nhật nhanh tồn kho
    quickUpdateStock: build.mutation<any, { id: string; variantId: string; change: number }>({
      query: ({ id, variantId, change }) => ({
        url: `/inventory/${id}/variant/${variantId}/stock`,
        method: 'PATCH',
        data: { change },
        authRequired: true,
      }),
      invalidatesTags: ['Inventory'],
    }),
    // Lấy lịch sử nhập/xuất kho
    getInventoryHistory: build.query<any, string>({
      query: (id) => ({
        url: `/inventory/${id}/history`,
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['StockHistory'],
    }),
    // Import tồn kho từ Excel
    importInventory: build.mutation<any, FormData>({
      query: (formData) => ({
        url: '/inventory/import',
        method: 'POST',
        data: formData,
        authRequired: true,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }),
      invalidatesTags: ['Inventory'],
    }),
    // Export tồn kho ra Excel
    exportInventory: build.query<any, any>({
      query: (params) => ({
        url: '/inventory/export',
        method: 'GET',
        params,
        authRequired: true,
      }),
      providesTags: ['Inventory'],
    }),
    // Lấy danh sách cảnh báo hết hàng/sắp hết hàng
    getAlerts: build.query<any, void>({
      query: () => ({
        url: '/inventory/alerts',
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['Alerts'],
    }),
  }),
})

export const {
  useGetInventoryQuery,
  useGetInventoryDetailQuery,
  useUpdateVariantsMutation,
  useQuickUpdateStockMutation,
  useGetInventoryHistoryQuery,
  useImportInventoryMutation,
  useExportInventoryQuery,
  useGetAlertsQuery,
} = inventoryManagementApi
