import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../../m1/user/entities/user.entity';

export enum AiTrainingDataType {
  TEXT = 'text',
  IMAGE = 'image',
}

@Entity('ai_training_data')
export class AiTrainingData {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'enum', enum: AiTrainingDataType })
  data_type: AiTrainingDataType;

  @Column({ type: 'varchar', length: 100, nullable: true })
  source?: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  label?: string;

  @Column('uuid', { nullable: true })
  created_by?: string;
  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'created_by' })
  createdBy?: User;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;
}
