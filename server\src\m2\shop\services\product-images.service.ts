import { Injectable, BadRequestException, Inject, Logger, Optional } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ClientProxy } from '@nestjs/microservices';
import { ProductImage } from '../entities/product_image.entity';
import { Product } from '../entities/products.entity';
import { Status } from '../entities/status.entity';
import { CloudinaryService } from 'src/cloudinary/cloudinary.service';

@Injectable()
export class ProductImagesService {
  constructor(
    @InjectRepository(ProductImage)
    private readonly imageRepo: Repository<ProductImage>,
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,
    @InjectRepository(Status)
    private readonly statusRepo: Repository<Status>,
    private readonly cloudinaryService: CloudinaryService,
    @Optional() @Inject('RABBITMQ_SERVICE') private readonly rabbitClient?: ClientProxy,
  ) {}

  // Upload nhiều ảnh lên cloud, tr<PERSON> về mảng {url, id}, đồng thời gửi message vào RabbitMQ để vector hóa
  async uploadImages(files: Express.Multer.File[], productId?: string) {
    try {
      if (!files || files.length === 0) throw new BadRequestException('Không có file');
      const uploadResults = await this.cloudinaryService.uploadFiles(files);

      // Lấy status_id cho IMAGE/UPLOADING
      const uploadingStatus = await this.statusRepo.findOne({
        where: { type: 'IMAGE', code: 'UPLOADING' },
      });
      if (!uploadingStatus) throw new BadRequestException('Không tìm thấy status IMAGE/UPLOADING');

      const savedImages: Array<{
        image_url: string;
        image_id: string;
        id: string;
        status_id?: string;
      }> = [];
      for (const uploaded of uploadResults) {
        const image = this.imageRepo.create({
          image_url: uploaded.secure_url,
          image_id: uploaded.public_id,
          status_id: uploadingStatus.id,
        });
        const saved = await this.imageRepo.save(image);
        savedImages.push({
          image_url: saved.image_url,
          image_id: saved.image_id,
          id: saved.id,
          status_id: saved.status_id,
        });
      }
      return savedImages;
    } catch (error) {
      throw new BadRequestException('Lỗi upload ảnh: ' + (error?.message || error));
    }
  }

  // Xóa 1 ảnh trên cloud theo id (public_id)
  async deleteImage(imageId: string) {
    try {
      if (!imageId) throw new BadRequestException('Thiếu imageId');
      const result = await this.cloudinaryService.deleteFile(imageId);
      return { deleted: result };
    } catch (error) {
      throw new BadRequestException('Lỗi xóa ảnh: ' + (error?.message || error));
    }
  }
}
