import React, { useState, useCallback } from 'react'
import {
  Table,
  Card,
  Input,
  Select,
  Pagination,
  Spin,
  Row,
  Col,
  Button,
  Tag,
  Avatar,
  Space,
  Popconfirm,
  Form,
} from 'antd'
import {
  ReloadOutlined,
  SearchOutlined,
  CloseOutlined,
  EyeOutlined,
  EditOutlined,
  TeamOutlined,
  DeleteOutlined,
  UserOutlined,
} from '@ant-design/icons'
import UserStatistics from './UserStatistics'
import UserDetailModal from './UserDetailModal'
import ModalButtonGroup from '@/components/ModalButtonGroup'
import { getUserColumns } from './userTableColumns'
import styles from './UserManagement.module.scss'
import {
  useGetUserManagementHook,
  useGetUserManagementByIdHook,
  useUpdateUserManagementHook,
  useDeleteUserManagementHook,
  useAssignRoleToUserHook,
  useGetAllRolesHook,
  useCreateRoleHook,
} from '@/hooks/system/useUserManagement'
import { useGetAll<PERSON>oleHook, useGetAllRolesForAdminHook } from '@/hooks/user/index'
import { useToast } from '../../../components/Toast'
import { useGetAllRoleGroupsHook, useAssignRoleGroupToUserHook } from '@/hooks/user/index'
import { getAvatarUrl } from '@/utils/avatar'
import AssignRoleGroupModal from './AssignRoleGroupModal'

const { Option } = Select

// Regex pattern cho role code
const ROLE_CODE_PATTERN = /^[A-Z0-9_]+$/

// Type definitions
interface User {
  id: string
  fullName?: string
  full_name?: string
  name?: string
  uidCode?: string
  uid_code?: string
  email?: string
  phone?: string
  phoneNumber?: string
  avatarUrl?: string
  role?: any
  role_id?: string
  status?: 'active' | 'locked' | 'pending'
  createdAt?: string
  updatedAt?: string
}

interface Role {
  id: string
  name?: string
  role_code?: string
}

interface AllRole {
  id: string
  role_code: string
  name: string
  description?: string
}

interface FormValues {
  fullName?: string
  email?: string
  phone?: string
  roleId?: string
  status?: 'active' | 'locked' | 'pending'
}

interface RoleFormValues {
  role_code: string
  name: string
  description: string
}

const UserManagement = () => {
  const [searchText, setSearchText] = useState('')
  const [selectedRole, setSelectedRole] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<'active' | 'locked' | 'pending' | ''>('')
  const [page, setPage] = useState(1)
  const [limit, setLimit] = useState(10)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [modalType, setModalType] = useState('create')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [selectedUserId, setSelectedUserId] = useState('')
  const [isMobile, setIsMobile] = useState(false)

  // ...existing code...

  const [isAssignRoleGroupModalVisible, setIsAssignRoleGroupModalVisible] = useState(false)
  const [selectedRoleGroupCode, setSelectedRoleGroupCode] = useState('')

  const [form] = Form.useForm<FormValues>()
  const [roleForm] = Form.useForm<RoleFormValues>()

  // Toast hook
  const { showSuccess, showError, showWarning } = useToast()

  // Use API hooks
  const { data, isLoading, error, refetch } = useGetUserManagementHook({
    roleCode: selectedRole,
    status: selectedStatus || undefined,
    keyword: searchText,
    page,
    limit,
  })

  // Get user detail for modal
  const { data: userDetail, isLoading: userDetailLoading } =
    useGetUserManagementByIdHook(selectedUserId)

  // API CŨ - dùng cho tất cả chức năng user management
  const { data: rolesData } = useGetAllRolesForAdminHook()
  const roles: Role[] = rolesData || []

  // New: Get all roles for filter
  const {
    data: allRolesData,
    isLoading: rolesLoading,
    error: rolesError,
    refetch: refetchRoles,
  } = useGetAllRolesForAdminHook()
  const allRoles: Array<{ id: string; role_code: string; name: string; description?: string }> =
    allRolesData || []

  // Mutation hooks
  const updateUser = useUpdateUserManagementHook()
  const deleteUser = useDeleteUserManagementHook()
  const assignRole = useAssignRoleToUserHook()
  const createRole = useCreateRoleHook()
  const assignRoleGroup = useAssignRoleGroupToUserHook()
  // const { data: productStatuses } = useGetStatusesByTypeHook({ type: '', parent: null })

  const handleAssignRoleGroup = async () => {
    if (!selectedUserId || !selectedRoleGroupCode) return
    const success = await assignRoleGroup(selectedUserId, selectedRoleGroupCode)
    if (success) {
      setIsAssignRoleGroupModalVisible(false)
      showSuccess('Bổ nhiệm nhóm vai trò thành công')
      refetch()
    }
  }
  // Data from API
  const users: User[] = data?.data || []
  const total = data?.total || 0

  // Helper functions - ĐƯA LÊN TRƯỚC VỊ TRÍ SỬ DỤNG
  const getRoleId = (role: any): string => {
    if (!role) return ''
    if (typeof role === 'string') return role
    if (typeof role === 'object') {
      if ('id' in role && role.id) return role.id
      if ('roleId' in role && role.roleId) return role.roleId
      if ('role_id' in role && role.role_id) return role.role_id
      if ('_id' in role && role._id) return role._id
    }
    return ''
  }

  // Truy vấn roleGroups theo roleId của user đang chọn (sau khi users đã có)
  const user = users.find((u) => u.id === selectedUserId)
  const roleId = user?.role_id || getRoleId(user?.role)
  const { data: roleGroupsData, isLoading: roleGroupsLoading } = useGetAllRoleGroupsHook(roleId)
  const roleGroups: { code: string; name: string }[] = roleGroupsData || []

  // Helper functions - GIỮ NGUYÊN, dùng API cũ
  const getRoleNameById = (roleId: string): string => {
    const role = roles.find((r: Role) => r.id === roleId)
    return role?.name || role?.role_code || 'Unknown'
  }

  const getRoleName = (role: any): string => {
    if (typeof role === 'object' && role !== null) {
      return role.name || role.role_code || 'Unknown'
    }
    return role || 'Unknown'
  }

  const getFullName = (user: User): string => {
    return user?.fullName || user?.full_name || user?.name || user?.uidCode || 'Chưa có tên'
  }

  // Statistics - GIỮ NGUYÊN
  const stats = {
    total: total,
    active: users.filter((u: User) => u.status === 'active').length,
    locked: users.filter((u: User) => u.status === 'locked').length,
    pending: users.filter((u: User) => u.status === 'pending').length,
    admins: users.filter((u: User) => {
      const roleName = getRoleName(u.role)
      return roleName === 'Quản trị hệ thống'
    }).length,
  }

  // Mobile columns - simplified for mobile view
  const mobileColumns = [
    {
      title: 'Người dùng',
      key: 'user',
      render: (_: any, record: User, index: number) => (
        <div className={styles.mobileUserCard}>
          <div className={styles.mobileUserHeader}>
            <Avatar
              size={40}
              src={getAvatarUrl(record.avatarUrl)}
              icon={<UserOutlined />}
              className={styles.userAvatarMobile}
            />
            <div className={styles.mobileUserInfo}>
              <div className={styles.mobileUserName}>
                <span
                  style={{
                    display: 'inline-block',
                    whiteSpace: 'nowrap',
                    wordBreak: 'keep-all',
                    writingMode: 'horizontal-tb',
                    textOrientation: 'mixed',
                    direction: 'ltr',
                    unicodeBidi: 'normal',
                    fontFamily: 'Arial, sans-serif',
                    fontSize: '14px',
                    fontWeight: '600',
                    color: '#333',
                  }}
                >
                  {getFullName(record) || 'Test User Name'}
                </span>
              </div>
              <div className={styles.mobileUserCode}>{record.uidCode || record.uid_code}</div>
              <div className={styles.mobileUserContact}>
                <div>{record.email}</div>
                <div>{record.phone}</div>
              </div>
            </div>
          </div>
          <div className={styles.mobileUserDetails}>
            <Tag color={getRoleColor(getRoleName(record.role))}>{getRoleName(record.role)}</Tag>
            <Tag color={getStatusColor(record.status)}>{getStatusText(record.status)}</Tag>
          </div>
        </div>
      ),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      render: (_: any, record: User) => (
        <div className={styles.mobileActions}>
          <Button
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            size="small"
            className={styles.mobileActionButton}
          >
            Xem
          </Button>
          <Button
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
            className={styles.mobileActionButton}
          >
            Sửa
          </Button>
        </div>
      ),
    },
  ]

  // Helper functions for mobile view
  const getRoleColor = (roleName: string) => {
    if (roleName === 'Quản trị hệ thống') return 'red'
    if (roleName === 'Admin tỉnh') return 'orange'
    if (roleName === 'Admin huyện') return 'blue'
    if (roleName === 'Trưởng ấp') return 'green'
    if (roleName === 'Nông dân') return 'purple'
    return 'default'
  }

  const getStatusColor = (status: string | undefined) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'locked':
        return 'error'
      case 'pending':
        return 'warning'
      default:
        return 'default'
    }
  }

  const getStatusText = (status: string | undefined) => {
    switch (status) {
      case 'active':
        return 'Hoạt động'
      case 'locked':
        return 'Không hoạt động'
      case 'pending':
        return 'Chờ duyệt'
      default:
        return 'Không xác định'
    }
  }

  // Các handler phải được khai báo trước khi truyền vào getUserColumns

  // Table columns

  // Handlers - GIỮ NGUYÊN TẤT CẢ
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchText(value)
    setPage(1)
  }, [])

  const handleClearSearch = useCallback(() => {
    setSearchText('')
    setPage(1)
  }, [])

  const handleRoleFilter = (value: string) => {
    setSelectedRole(value)
    setPage(1)
  }

  const handleStatusFilter = (value: 'active' | 'locked' | 'pending') => {
    setSelectedStatus(value)
    setPage(1)
    if (value) {
      const statusText =
        value === 'active'
          ? 'Hoạt động'
          : value === 'locked'
            ? 'Không hoạt động'
            : value === 'pending'
              ? 'Đang xử lý'
              : value
      showSuccess(`Đã lọc theo trạng thái: ${statusText}`)
    }
  }

  const handleView = (user: User) => {
    setModalType('view')
    setSelectedUser(user)
    setSelectedUserId(user.id)
    setIsModalVisible(true)
  }

  const handleEdit = (user: User) => {
    setModalType('edit')
    setSelectedUser(user)
    setSelectedUserId(user.id)
    setIsModalVisible(true)
  }

  const handleDelete = async (userId: string) => {
    try {
      const success = await deleteUser(userId)
      if (success) {
        refetch()
      }
    } catch (error) {
      showError('Có lỗi xảy ra khi xóa người dùng')
    }
  }

  const columns = getUserColumns({
    page,
    limit,
    getFullName,
    getRoleName,
    getRoleColor,
    getStatusColor,
    getStatusText,
    handleView,
    handleEdit,
    setSelectedUserId,
    setIsAssignRoleGroupModalVisible,
    handleDelete,
  })

  // Handlers CHO 2 NÚT MỚI

  // Modal handlers - GIỮ NGUYÊN, dùng API cũ
  const handleModalOk = async () => {
    try {
      const values = await form.validateFields()

      if (modalType === 'edit') {
        // Kiểm tra xem có thay đổi role không
        const currentRoleId = selectedUser?.role_id || getRoleId(selectedUser?.role) || ''
        const newRoleId = values.roleId

        if (currentRoleId !== newRoleId && newRoleId) {
          // Nếu có thay đổi role, gọi API assign role
          const assignSuccess = await assignRole({
            userId: selectedUserId,
            roleId: newRoleId,
          })

          if (!assignSuccess) {
            return // Dừng lại nếu assign role thất bại
          }

          // Hiển thị thông báo gán quyền thành công
          const newRoleName = getRoleNameById(newRoleId)
        }

        // Sau đó update thông tin user (trừ roleId vì đã assign rồi)
        const { roleId, ...updateData } = values
        const success = await updateUser({
          id: selectedUserId,
          ...updateData,
        })

        if (success) {
          setIsModalVisible(false)
          setSelectedUserId('')
          refetch()
        }
      }
    } catch (error) {
      console.error('Form validation failed:', error)
      showError('Vui lòng kiểm tra lại thông tin nhập vào')
    }
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    setSelectedUserId('')
    form.resetFields()
  }

  const handleRefresh = () => {
    refetch()
    showSuccess('Đã làm mới dữ liệu')
  }

  const handleTableChange = (pagination: any) => {
    setPage(pagination.current)
    setLimit(pagination.pageSize)
  }

  // Handle API error
  if (error) {
    console.error('API Error:', error)
    showError('Có lỗi xảy ra khi tải dữ liệu')
  }

  // Check mobile screen size
  React.useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768)
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Set form data when userDetail is loaded - GIỮ NGUYÊN, dùng API cũ
  React.useEffect(() => {
    if (userDetail && (modalType === 'view' || modalType === 'edit')) {
      // API trả về role_id
      const roleId = userDetail.role_id || ''

      const formData = {
        fullName: getFullName(userDetail),
        email: userDetail.email || '',
        phone: userDetail.phone || userDetail.phoneNumber || '',
        roleId: roleId,
        status: userDetail.status || 'active',
      }

      form.setFieldsValue(formData)
    }
  }, [userDetail, modalType, form])

  return (
    <Card>
      <div className={styles.container}>
        {/* Header - CHỈ THAY ĐỔI PHẦN NÚT */}
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <h1 className={styles.title}>Quản lý người dùng</h1>
            <p className={styles.subtitle}>
              Quản lý thông tin và quyền hạn người dùng trong hệ thống
            </p>
          </div>
          {/* Đã chuyển quản lý role sang PermissionManagement */}
        </div>

        {/* Mobile Search */}
        {isMobile && (
          <div className={styles.mobileSearchContainer}>
            <Input
              placeholder="Tìm kiếm theo tên, ID, email..."
              prefix={<SearchOutlined />}
              suffix={
                searchText ? (
                  <CloseOutlined onClick={handleClearSearch} style={{ cursor: 'pointer' }} />
                ) : null
              }
              value={searchText}
              onChange={handleSearch}
              className={styles.mobileSearchInput}
              allowClear
            />
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              className={styles.mobileFilterButton}
            >
              Làm mới
            </Button>
          </div>
        )}

        {/* Statistics */}
        <UserStatistics stats={stats} />

        {/* Filters */}
        {!isMobile && (
          <Card className={styles.filterCard}>
            <Row gutter={16}>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div style={{ marginBottom: 4, fontWeight: 500 }}>Tìm kiếm</div>
                <Input
                  placeholder="Tìm kiếm theo tên, ID, email..."
                  prefix={<SearchOutlined />}
                  suffix={
                    searchText ? (
                      <CloseOutlined onClick={handleClearSearch} style={{ cursor: 'pointer' }} />
                    ) : null
                  }
                  value={searchText}
                  onChange={handleSearch}
                  className={styles.searchInput}
                  allowClear
                />
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div style={{ marginBottom: 4, fontWeight: 500 }}>Lọc theo vai trò</div>
                <Select
                  placeholder="Chọn vai trò"
                  allowClear
                  onChange={handleRoleFilter}
                  className={styles.filterSelect}
                  loading={rolesLoading}
                  value={selectedRole || undefined}
                >
                  {allRoles.map((role) => (
                    <Option key={role.role_code} value={role.role_code}>
                      {role.name || role.role_code}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div style={{ marginBottom: 4, fontWeight: 500 }}>Lọc theo trạng thái</div>
                <Select
                  placeholder="Chọn trạng thái"
                  allowClear
                  onChange={handleStatusFilter}
                  className={styles.filterSelect}
                >
                  <Option value="active">Hoạt động</Option>
                  <Option value="pending">Đang xử lý</Option>
                  <Option value="locked">Không hoạt động</Option>
                </Select>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6}>
                <div style={{ marginBottom: 4, fontWeight: 500, visibility: 'hidden' }}>
                  refresh
                </div>
                <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
                  Làm mới
                </Button>
              </Col>
            </Row>
          </Card>
        )}

        {/* Table - GIỮ NGUYÊN */}
        <Card className={styles.tableCard}>
          {searchText && !isLoading && users.length === 0 && (
            <div style={{ textAlign: 'center', padding: '20px', color: '#8c8c8c' }}>
              Không tìm thấy người dùng nào phù hợp với từ khóa "{searchText}"
            </div>
          )}
          {isMobile ? (
            // Mobile: Use custom card layout instead of table
            <div>
              {isLoading ? (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                  <Spin size="large" />
                </div>
              ) : (
                <div className={styles.mobileUsersList}>
                  {users.map((user, index) => (
                    <div key={user.id} className={styles.mobileUserCardCustom}>
                      <div className={styles.mobileUserHeaderCustom}>
                        <Avatar
                          size={40}
                          src={getAvatarUrl(user.avatarUrl)}
                          icon={<UserOutlined />}
                          className={styles.userAvatarMobile}
                        />
                        <div className={styles.mobileUserInfoCustom}>
                          <div className={styles.mobileUserNameCustom}>
                            {getFullName(user) || 'Test User Name'}
                          </div>
                          <div className={styles.mobileUserCodeCustom}>
                            {user.uidCode || user.uid_code}
                          </div>
                          <div className={styles.mobileUserContactCustom}>
                            <div>{user.email}</div>
                            <div>{user.phone}</div>
                          </div>
                        </div>
                      </div>
                      <div className={styles.mobileUserDetailsCustom}>
                        <Tag color={getRoleColor(getRoleName(user.role))}>
                          {getRoleName(user.role)}
                        </Tag>
                        <Tag color={getStatusColor(user.status)}>{getStatusText(user.status)}</Tag>
                      </div>
                      <div className={styles.mobileActionsCustom}>
                        <Button
                          icon={<EyeOutlined />}
                          onClick={() => handleView(user)}
                          size="small"
                          className={styles.mobileActionButton}
                        >
                          Xem
                        </Button>
                        <Button
                          icon={<EditOutlined />}
                          onClick={() => handleEdit(user)}
                          size="small"
                          className={styles.mobileActionButton}
                        >
                          Sửa
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              {/* Mobile Pagination */}
              {!isLoading && (
                <div className={styles.mobilePagination}>
                  <Pagination
                    current={page}
                    pageSize={limit}
                    total={total}
                    showSizeChanger={false}
                    showQuickJumper={false}
                    showTotal={(total, range) => `${range[0]}-${range[1]}/${total}`}
                    size="small"
                    onChange={(page, pageSize) => {
                      setPage(page)
                      setLimit(pageSize || limit)
                    }}
                  />
                </div>
              )}
            </div>
          ) : (
            // Desktop: Use table
            <div className={styles.desktopTableWrapper}>
              <Table
                columns={columns}
                dataSource={users}
                rowKey="id"
                loading={isLoading}
                pagination={{
                  current: page,
                  pageSize: limit,
                  total: total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `${range[0]}-${range[1]} của ${total} người dùng`,
                  size: 'default',
                }}
                scroll={{ x: 'max-content' }}
                className={styles.userTable}
                onChange={handleTableChange}
              />
            </div>
          )}
        </Card>

        <UserDetailModal
          open={isModalVisible}
          loading={userDetailLoading}
          modalType={modalType}
          userDetail={userDetail}
          form={form}
          roles={roles}
          onCancel={handleModalCancel}
          onOk={handleModalOk}
          getFullName={getFullName}
          getRoleName={getRoleName}
          getRoleColor={getRoleColor}
          getStatusColor={getStatusColor}
          getStatusText={getStatusText}
        />

        <AssignRoleGroupModal
          open={isAssignRoleGroupModalVisible}
          loading={roleGroupsLoading}
          roleGroups={roleGroups}
          selectedRoleGroupCode={selectedRoleGroupCode}
          onChange={setSelectedRoleGroupCode}
          onCancel={() => setIsAssignRoleGroupModalVisible(false)}
          onOk={handleAssignRoleGroup}
        />
      </div>
    </Card>
  )
}

export default UserManagement
