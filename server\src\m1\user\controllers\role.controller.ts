import { Controller, Get, Post, Body, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { CreateRoleDto } from '../../auth/dto/create-role.dto';
import { RoleService } from '../services/role.service';
import { AllowAnonymous } from 'src/common/decorators/allow-anonymous.decorator';

@ApiTags('Role')
@Controller('roles')
export class RoleController {
  /**
   * Bổ nhiệm vai trò chi tiết: cập nhật userGroupCode cho user, kiểm tra hợp lệ
   */
  @Post('assign-group')
  @ApiOperation({ summary: 'Bổ nhiệm nhóm vai trò cho user (cập nhật userGroupCode)' })
  async assignRoleGroup(@Body() dto: { userId: string; roleGroupCode: string }) {
    return this.roleService.assignRoleGroupToUser(dto.userId, dto.roleGroupCode);
  }

  /**
   * <PERSON><PERSON>y danh sách role group theo roleId (lọc ở server)
   */
  @Get('groups')
  @ApiOperation({ summary: 'L<PERSON>y danh sách nhóm vai trò theo roleId (lọc ở server)' })
  async getAllRoleGroups(@Query('roleId') roleId?: string) {
    return this.roleService.getAllRoleGroups(roleId);
  }
  /**
   * Lấy toàn bộ vai trò và quyền dành cho admin
   */
  @Get('admin')
  @ApiOperation({ summary: 'Lấy toàn bộ vai trò và quyền dành cho admin' })
  getAllRolesForAdmin() {
    return this.roleService.getAllRolesForAdmin();
  }
  /**
   * Quản lý phạm vi vai trò (roleScope)
   */
  @Get('scopes')
  @ApiOperation({ summary: 'Lấy danh sách phạm vi vai trò (roleScope)' })
  getRoleScopes() {
    return this.roleService.getAllRoleScopes();
  }

  /**
   * Quản lý quyền của vai trò (rolePermission)
   */
  @Get('permissions')
  @ApiOperation({ summary: 'Lấy danh sách quyền của vai trò (rolePermission)' })
  getRolePermissions() {
    return this.roleService.getAllRolePermissions();
  }

  /**
   * Bổ nhiệm vai trò cho user
   */
  @Post('assign')
  @ApiOperation({ summary: 'Bổ nhiệm vai trò cho user' })
  assignRole(@Body() dto: { userId: string; roleId: string }) {
    return this.roleService.assignRoleToUser(dto.userId, dto.roleId);
  }
  constructor(private readonly roleService: RoleService) {}

  /**
   * 🧩 Tạo mới một vai trò (dành cho quản trị viên)
   */
  @Post()
  @ApiOperation({ summary: 'Tạo vai trò mới' })
  createRole(@Body() dto: CreateRoleDto) {
    return this.roleService.createRole(dto);
  }

  /**
   * 📋 Lấy toàn bộ danh sách vai trò
   */
  @Get()
  @AllowAnonymous()
  @ApiOperation({ summary: 'Lấy toàn bộ danh sách vai trò' })
  getAllRoles() {
    return this.roleService.getAllRolesExcept();
  }
}
