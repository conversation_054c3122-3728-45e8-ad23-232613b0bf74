import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColum<PERSON>,
} from 'typeorm';
import { IsEnum, IsOptional, IsObject } from 'class-validator';

export enum UserBehaviorEventType {
  SEARCH = 'search',
  CLICK = 'click',
  VIEW = 'view',
  ADD_TO_CART = 'add_to_cart',
  UPDATE_CART_QUANTITY = 'update_cart_quantity',
  REMOVE_CART_ITEM = 'remove_cart_item',
  CLEAR_CART = 'clear_cart',
  CHECK_CART_INVENTORY = 'check_cart_inventory',
  CREATE = 'create',
  UPDATE = 'update',
  REQUEST_CANCEL = 'request_cancel',
  CONFIRM_RECEIVED = 'confirm_received',
  AI_ONBOARDING = 'ai_onboarding',
}
import { User } from '../../../m1/user/entities/user.entity';

@Entity('user_behaviors')
export class UserBehavior {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  user_id: string;
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'enum', enum: UserBehaviorEventType })
  @IsEnum(UserBehaviorEventType)
  event_type: UserBehaviorEventType;

  @Column('uuid', { nullable: true })
  product_id?: string;

  @Column({ type: 'jsonb', nullable: true })
  event_data?: any;

  @Column({ type: 'jsonb', nullable: true })
  device_info?: any;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;
}
