import type { ReactElement, ReactNode } from 'react'

export type Role = 'R01' | 'R02' | 'R03' | 'R04' | 'R05'

export type PublicRoute = {
  path: string
  component: React.ComponentType
  layout?: (props: { children: ReactElement }) => ReactNode | null
}

export interface PrivateRoute {
  path: string
  component: React.FC
  layout?: (props: { children: ReactElement }) => ReactNode | null
  isAuthenticated?: boolean
  requiredPermissions?: string[]
  hasPermission?: boolean
}

export type PrivateRouteProps = {
  isAuthenticated: boolean
  hasPermission?: boolean
  requiredPermissions?: string[]
  children: React.ReactNode
}
