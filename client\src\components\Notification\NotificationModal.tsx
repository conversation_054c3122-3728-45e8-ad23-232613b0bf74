import React, { useState } from 'react';
import { Modal, List, Badge, Empty } from 'antd';
import { BellOutlined } from '@ant-design/icons';
import styles from './NotificationModal.module.scss';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: Date;
  read: boolean;
}

interface NotificationModalProps {
  visible: boolean;
  onClose: () => void;
}

const NotificationModal: React.FC<NotificationModalProps> = ({ visible, onClose }) => {
  // Mock data - trong thực tế sẽ lấy từ API
  const [notifications] = useState<Notification[]>([
    {
      id: '1',
      title: 'Đơn hàng mới',
      message: 'Bạn có đơn hàng mới #12345 đã được đặt',
      type: 'info',
      timestamp: new Date(),
      read: false,
    },
    {
      id: '2',
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON> mãi',
      message: '<PERSON><PERSON><PERSON><PERSON> giá 20% cho tất cả sản phẩm nông nghiệp',
      type: 'success',
      timestamp: new Date(Date.now() - 3600000),
      read: true,
    },
  ]);

  const unreadCount = notifications.filter(n => !n.read).length;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <span className={styles.iconSuccess}>✓</span>;
      case 'warning':
        return <span className={styles.iconWarning}>⚠</span>;
      case 'error':
        return <span className={styles.iconError}>✗</span>;
      default:
        return <span className={styles.iconInfo}>ℹ</span>;
    }
  };

  return (
    <Modal
      title={
        <div className={styles.modalTitle}>
          <BellOutlined />
          <span>Thông báo</span>
          {unreadCount > 0 && (
            <Badge count={unreadCount} className={styles.badge} />
          )}
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={400}
      className={styles.notificationModal}
    >
      {notifications.length > 0 ? (
        <List
          className={styles.notificationList}
          itemLayout="horizontal"
          dataSource={notifications}
          renderItem={(item) => (
            <List.Item className={`${styles.notificationItem} ${!item.read ? styles.unread : ''}`}>
              <List.Item.Meta
                avatar={getNotificationIcon(item.type)}
                title={item.title}
                description={
                  <div className={styles.notificationContent}>
                    <p>{item.message}</p>
                    <span className={styles.timestamp}>
                      {item.timestamp.toLocaleString('vi-VN')}
                    </span>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      ) : (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="Không có thông báo nào"
          className={styles.emptyState}
        />
      )}
    </Modal>
  );
};

export default NotificationModal; 