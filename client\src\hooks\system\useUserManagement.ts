import {
  useGetUserManagementQuery,
  useGetUserManagementByIdQuery,
  useUpdateUserManagementMutation,
  useDeleteUserManagementMutation,
  useAssignRoleToUserMutation,
  useGetAllRolesQuery,
  useCreateRoleMutation,
  type GetUserManagementParams,
  type UpdateUserManagementRequest,
  type AssignRoleRequest,
  type CreateRoleRequest,
} from '@/services/M1/UserManagement/userManagement'
import { useToast } from '../../components/Toast'
import { useEffect } from 'react'
import { useLoadingStore } from '@/utils/loadingStore'

// Hooks cũ...
const useGetUserManagementHook = (params: GetUserManagementParams) => {
  const { data, error, isLoading, refetch, isFetching } = useGetUserManagementQuery(params, {
    refetchOnMountOrArgChange: true,
    skip: false,
    refetchOnReconnect: true,
  })

  const { setLoading } = useLoadingStore()
  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

const useGetUserManagementByIdHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetUserManagementByIdQuery(id, {
    refetchOnMountOrArgChange: true,
    skip: !id,
    refetchOnReconnect: true,
  })

  const { setLoading } = useLoadingStore()
  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

const useUpdateUserManagementHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useUpdateUserManagementMutation()

  const update = async (data: UpdateUserManagementRequest): Promise<boolean> => {
    try {
      const res = await updateFunc(data)
      if (!res?.error) {
        showSuccess('Cập nhật người dùng thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Cập nhật người dùng thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Cập nhật người dùng thất bại' + e)
      return false
    }
  }
  return update
}

const useDeleteUserManagementHook = () => {
  const { showSuccess, showError } = useToast()
  const [deleteFunc] = useDeleteUserManagementMutation()

  const deleteUser = async (id: string): Promise<boolean> => {
    try {
      const res = await deleteFunc(id)
      if (!res?.error) {
        showSuccess('Xóa người dùng thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Xóa người dùng thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Xóa người dùng thất bại' + e)
      return false
    }
  }
  return deleteUser
}

const useAssignRoleToUserHook = () => {
  const { showSuccess, showError } = useToast()
  const [assignFunc] = useAssignRoleToUserMutation()

  const assignRole = async (data: AssignRoleRequest): Promise<boolean> => {
    try {
      const res = await assignFunc(data)
      if (!res?.error) {
        showSuccess('Gán quyền cho người dùng thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Gán quyền thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Gán quyền thất bại' + e)
      return false
    }
  }
  return assignRole
}

// Hooks mới cho role management
const useGetAllRolesHook = () => {
  const { data, error, isLoading, refetch, isFetching } = useGetAllRolesQuery(undefined, {
    refetchOnMountOrArgChange: true,
    skip: false,
    refetchOnReconnect: true,
  })

  const { setLoading } = useLoadingStore()
  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

const useCreateRoleHook = () => {
  const { showSuccess, showError } = useToast()
  const [createFunc] = useCreateRoleMutation()

  const createRole = async (data: CreateRoleRequest): Promise<boolean> => {
    try {
      const res = await createFunc(data)
      if (!res?.error) {
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Tạo role thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      return false
    }
  }
  return createRole
}

export {
  useGetUserManagementHook,
  useGetUserManagementByIdHook,
  useUpdateUserManagementHook,
  useDeleteUserManagementHook,
  useAssignRoleToUserHook,
  useGetAllRolesHook,
  useCreateRoleHook,
}
