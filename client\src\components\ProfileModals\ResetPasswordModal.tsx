import { Modal, Form, Input, Row, Col } from 'antd'
import Button from '@/components/Button'
import ModalButtonGroup from '@/components/ModalButtonGroup'
import styles from './Modal.module.scss'

type ResetPasswordModalProps = {
  open: boolean
  onClose: () => void
  form: any
  otpPassword: string
  setOtpPassword: (val: string) => void
  countdown: number
  handleSendOtp: (type: 'password') => void
  handleVerifyOtp: (type: 'password') => void
}

export default function ResetPasswordModal({
  open,
  onClose,
  form,
  otpPassword,
  setOtpPassword,
  countdown,
  handleSendOtp,
  handleVerifyOtp,
}: ResetPasswordModalProps) {
  return (
    <Modal title="Đặt lại mật khẩu" open={open} onCancel={onClose} footer={null} width={600}>
      <Form layout="vertical">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Mật khẩu cũ"
              name="oldPassword"
              rules={[{ required: true, message: '<PERSON>ui lòng nhập mật khẩu cũ' }]}
            >
              {' '}
              <Input.Password size="large" />{' '}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Mật khẩu mới"
              name="newPassword"
              rules={[{ required: true, message: 'Vui lòng nhập mật khẩu mới' }]}
            >
              {' '}
              <Input.Password size="large" />{' '}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Xác nhận mật khẩu mới"
              name="confirmPassword"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: 'Vui lòng xác nhận mật khẩu mới' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) return Promise.resolve()
                    return Promise.reject(new Error('Mật khẩu xác nhận không khớp!'))
                  },
                }),
              ]}
            >
              <Input.Password size="large" />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item label="Mã OTP xác nhận">
          <div className={styles.otpContainer}>
            <Input
              placeholder="Nhập mã OTP"
              value={otpPassword}
              onChange={(e) => setOtpPassword(e.target.value)}
              className={styles.otpInput}
            />
            <Button
              variant="primary"
              onClick={() => handleSendOtp('password')}
              disabled={countdown > 0}
            >
              {countdown > 0 ? `Gửi lại (${countdown}s)` : 'Gửi OTP'}
            </Button>
          </div>
        </Form.Item>
        
        <ModalButtonGroup
          onCancel={onClose}
          onConfirm={() => handleVerifyOtp('password')}
          cancelText="Hủy"
          confirmText="Đặt lại mật khẩu"
          confirmVariant="primary"
        />
      </Form>
    </Modal>
  )
}
