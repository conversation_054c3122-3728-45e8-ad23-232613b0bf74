import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

// Type cho request body của /cart/add
export interface AddToCartRequest {
  product_id: string
  quantity: number
  unit_price: number
  version_id: string
  selected_options: Record<string, any>
}

// Type cho response (có thể mở rộng sau)
export interface AddToCartResponse {
  data?: any
  message?: string
  statusCode?: number
}

// Type cho request body của /cart/update-quantity
export interface UpdateCartQuantityRequest {
  cart_item_id: string
  quantity: number
}

export interface UpdateCartQuantityResponse {
  data?: any
  message?: string
  statusCode?: number
}

// Type cho request body của /cart/item (DELETE)
export interface DeleteCartItemRequest {
  cart_item_id: string
}

export interface DeleteCartItemResponse {
  data?: any
  message?: string
  statusCode?: number
}

// Type cho response của /cart/clear
export interface ClearCartResponse {
  data?: any
  message?: string
  statusCode?: number
}

// Type cho response của /cart/me
export interface GetCartResponse {
  data?: any
  message?: string
  statusCode?: number
}

// ✅ BƯỚC 1: Thêm type cho response của API mới
export interface CheckInventoryResponse {
  data?: any
  message?: string
  statusCode?: number
}

export const cartApi = createApi({
  reducerPath: 'cartApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  tagTypes: ['Cart'],
  endpoints: (build) => ({
    // Thêm sản phẩm vào giỏ hàng
    addToCart: build.mutation<AddToCartResponse, AddToCartRequest>({
      query: (data: AddToCartRequest) => ({
        url: '/cart/add',
        method: 'POST',
        data: data,
        authRequired: true,
      }),
      invalidatesTags: ['Cart'],
    }),
    // Cập nhật số lượng sản phẩm trong giỏ hàng
    updateCartQuantity: build.mutation<UpdateCartQuantityResponse, UpdateCartQuantityRequest>({
      query: (data: UpdateCartQuantityRequest) => ({
        url: '/cart/update-quantity',
        method: 'POST',
        data: data,
        authRequired: true,
      }),
      invalidatesTags: ['Cart'],
    }),
    // Xóa 1 item khỏi giỏ hàng
    deleteCartItem: build.mutation<DeleteCartItemResponse, DeleteCartItemRequest>({
      query: (data: DeleteCartItemRequest) => ({
        url: `/cart/item?cart_item_id=${data.cart_item_id}`,
        method: 'DELETE',
        authRequired: true,
      }),
      invalidatesTags: ['Cart'],
    }),
    // Xóa toàn bộ giỏ hàng
    clearCart: build.mutation<ClearCartResponse, void>({
      query: () => ({
        url: '/cart/clear',
        method: 'POST',
        authRequired: true,
      }),
      invalidatesTags: ['Cart'],
    }),
    // Lấy giỏ hàng của user hiện tại
    getCart: build.query<GetCartResponse, void>({
      query: () => ({
        url: '/cart/me',
        method: 'GET',
        authRequired: true,
      }),
      providesTags: ['Cart'],
    }),

    // ✅ BƯỚC 2: Thêm endpoint mới để kiểm tra tồn kho
    checkInventory: build.mutation<CheckInventoryResponse, void>({
      query: () => ({
        url: '/cart/check-inventory',
        method: 'POST',
        authRequired: true,
      }),
      // Hành động này có thể làm thay đổi trạng thái tồn kho của sản phẩm trong giỏ,
      // nên chúng ta sẽ làm mới lại dữ liệu giỏ hàng sau khi gọi.
      invalidatesTags: ['Cart'],
    }),

    // Thêm mutation để fetch cart data khi cần
    fetchCart: build.mutation<GetCartResponse, void>({
      query: () => ({
        url: '/cart/me',
        method: 'GET',
        authRequired: true,
      }),
      // Không invalidate tags vì đây chỉ là fetch data
    }),
  }),
})

export const {
  useAddToCartMutation,
  useUpdateCartQuantityMutation,
  useDeleteCartItemMutation,
  useClearCartMutation,
  useGetCartQuery,
  useCheckInventoryMutation,
  useFetchCartMutation,
} = cartApi
