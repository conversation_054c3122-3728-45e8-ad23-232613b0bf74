import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaShoppingCart, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaTrash } from 'react-icons/fa';
import { Io<PERSON><PERSON>, IoList, IoClose } from 'react-icons/io5';
import { MdSort } from 'react-icons/md';
import { BiCart } from 'react-icons/bi';
import { FiHeart } from 'react-icons/fi';
import styles from './Favorites.module.scss';
// Import the hook
import { useGetFavoritesHook, useGetProductDetailHook, useToggleFavoriteHook } from '@/hooks/product/useproduct';
import { useAddToCartHook } from '@/hooks/cart/useCart';
import { useGetCartLazyHook } from '@/hooks/cart/useCart';
import { useNavigate } from 'react-router-dom';
import { axiosAuth } from '@/libs/axios/axiosAuth';
import logo from '@/assets/images/logo.png';
import { useSelector } from 'react-redux';
import type { RootState } from '@/libs/state/store';
import { ROUTES } from '@/constants/routes';
import Cookies from 'js-cookie';
import { useToast } from '@/components/Toast';

interface FavoriteProduct {
  id: string;
  name: string;
  price: string;
  originalPrice?: string;
  unit: string;
  thumbnail_url: string;
  stock: number;
  rating: number;
  reviewCount: number;
  discount?: number;
  isOCOP?: boolean;
  seller: string;
  category: string;
  addedAt: string;
  version_id?: string; // Added for buy now logic
}

interface FilterState {
  category: string;
  priceRange: [number, number];
  inStock: boolean;
  sortBy: 'newest' | 'price-low' | 'price-high' | 'rating';
}

// Helper to fetch product details in batch
const fetchProductDetails = async (
  favoriteList: FavoriteProduct[],
  getDetail: (id: string) => Promise<any>
): Promise<Record<string, any>> => {
  const results = await Promise.all(
    favoriteList.map(async (fav: FavoriteProduct) => {
      const res = await getDetail(fav.id);
      return { id: fav.id, detail: res };
    })
  );
  const map: Record<string, any> = {};
  results.forEach(({ id, detail }) => {
    map[id] = detail;
  });
  return map;
};

const Favorites: React.FC = () => {
  const navigate = useNavigate();
  const auth = useSelector((state: RootState) => state.auth);
  
  // Check authentication
  useEffect(() => {
    const accessToken = Cookies.get('accessToken');

    if (!accessToken || !auth?.id) {
      navigate(ROUTES.LOGIN, { replace: true });
      return;
    }

    // ✅ Permission check now handled by route guard
    // Customer role should have PERM_FAVORITE_MANAGE permission

  }, [auth?.id, auth?.permissions, navigate]);
  
  // Use the hook to get favorite products
  const { data, isLoading, error, refetch } = useGetFavoritesHook();
  const [favorites, setFavorites] = useState<FavoriteProduct[]>([]);
  const [filteredFavorites, setFilteredFavorites] = useState<FavoriteProduct[]>([]);
  

  

  const [productDetails, setProductDetails] = useState<Record<string, any>>({});
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [filters, setFilters] = useState<FilterState>({
    category: '',
    priceRange: [0, 1000000],
    inStock: false,
    sortBy: 'newest'
  });

  const addToCart = useAddToCartHook();
  const toggleFavorite = useToggleFavoriteHook();
  const { data: cartData, refetch: refetchCart } = useGetCartLazyHook();
  const { showError, showWarning, showSuccess } = useToast();
  

  // Replace mock data effect with real data from hook
  useEffect(() => {
    
    // Handle different data structures
    let productsData: any[] = [];
    if (data && Array.isArray(data)) {
      // Data is directly an array
      productsData = data;
    } else {
      // No data or invalid structure
      productsData = [];
    }
    
    
    if (productsData && productsData.length > 0) {
      
      // Adapt data to FavoriteProduct[] if needed
      const mapped = productsData.map((item: any, index: number) => {
        const mappedItem = {
          id: item.id,
          name: item.name,
          price: String(item.price || 0),
          originalPrice: item.originalPrice ? String(item.originalPrice) : undefined,
          unit: item.unit || '',
          thumbnail_url: item.thumbnail_url || item.image_url || logo,
          stock: item.stock ?? 0,
          rating: item.rating ?? 4.5, // Default rating
          reviewCount: item.reviewCount ?? 0,
          discount: item.discount,
          isOCOP: item.isOCOP,
          seller: item.source || 'TAP Store', // Use source as seller
          category: item.category_id || '', // Use category_id as category
          addedAt: item.addedAt || item.created_at || item.createdAt || new Date().toISOString(),
          version_id: item.version_id,
        };
        return mappedItem;
      }) as FavoriteProduct[];
      
      
      setFavorites(mapped);
      setFilteredFavorites(mapped);
    } else {
      
      setFavorites([]);
      setFilteredFavorites([]);
    }
  }, [data]);

  // Fetch all product details in batch when favorites change
  useEffect(() => {
    let cancelled = false;
    const getDetail = async (id: string) => {
      // Use axiosAuth with auth for backend API
      try {
        const res = await axiosAuth.get(`/products/${id}`);
        return res.data; // API trả về trực tiếp product object
      } catch (err) {
        return null;
      }
    };
    if (favorites.length > 0) {
      setLoadingDetails(true);
      fetchProductDetails(favorites, getDetail).then((map) => {
        if (!cancelled) setProductDetails(map);
        setLoadingDetails(false);
      });
    } else {
      setProductDetails({});
    }
    return () => { cancelled = true; };
  }, [favorites]);

  // Filter and search logic
  useEffect(() => {
    
    
    let filtered = favorites.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          product.seller.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = !filters.category || product.category === filters.category;
      const matchesPrice = parseInt(product.price) >= filters.priceRange[0] && 
                          parseInt(product.price) <= filters.priceRange[1];
      const matchesStock = !filters.inStock || product.stock > 0;

      return matchesSearch && matchesCategory && matchesPrice && matchesStock;
    });

    // Sort logic
    switch (filters.sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime());
        break;
      case 'price-low':
        filtered.sort((a, b) => parseInt(a.price) - parseInt(b.price));
        break;
      case 'price-high':
        filtered.sort((a, b) => parseInt(b.price) - parseInt(a.price));
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
    }

    
    setFilteredFavorites(filtered);
  }, [favorites, searchTerm, filters]);

  const formatPrice = (price: string) => {
    return new Intl.NumberFormat('vi-VN').format(parseInt(price));
  };

  const handleRemoveFromFavorites = (productId: string) => {
    setFavorites(prev => prev.filter(item => item.id !== productId));
  };

  const handleAddToCart = (productId: string) => {

  };

  const handleRemoveSelected = () => {
    setFavorites(prev => prev.filter(item => !selectedItems.includes(item.id)));
    setSelectedItems([]);
  };

  const handleSelectItem = (productId: string) => {
    setSelectedItems(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === filteredFavorites.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredFavorites.map(item => item.id));
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <span key={i} className={`${styles.star} ${i <= rating ? styles.filled : ''}`}>
          ★
        </span>
      );
    }
    return stars;
  };

  // Helper: handle buy now
  const handleBuyNow = async (product: FavoriteProduct) => {
    // Nếu có version, cần lấy version_id, ở đây demo chỉ lấy id sản phẩm
    // Nếu có nhiều version, cần sửa FavoriteProduct để truyền version_id
    const versionId = product.version_id || '';
    const unitPrice = parseFloat(product.price);
    const quantity = 1;
    const success = await addToCart({
      product_id: product.id,
      quantity,
      unit_price: unitPrice,
      version_id: versionId,
      selected_options: {},
    });
    if (success) {
      // Tạo dữ liệu đơn hàng tạm thời và chuyển sang trang thanh toán
      const orderItems = [{
        product_id: product.id,
        version_id: versionId,
        quantity,
        unit_price: unitPrice,
        name: product.name,
        thumbnail_url: product.thumbnail_url,
      }];
      const totals = {
        subtotal: unitPrice * quantity,
        discount: 0,
        shipping: 25000,
        total: unitPrice * quantity + 25000,
      };
      localStorage.setItem('checkoutData', JSON.stringify({ items: orderItems, totals }));
      navigate('/gio-hang/chi-tiet-don-hang', {
        state: {
          items: orderItems,
          totals,
        },
      });
    }
  };

  // Bỏ thích 1 sản phẩm
  const handleToggleFavorite = async (productId: string) => {
    try {
      const result = await toggleFavorite(productId);
      if (result !== null) {
        // Refresh danh sách favorites từ API
        refetch();
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  // Bỏ thích nhiều sản phẩm
  const handleRemoveSelectedFavorites = async () => {
    try {
      await Promise.all(selectedItems.map(id => toggleFavorite(id)));
      // Refresh danh sách favorites từ API
      refetch();
      setSelectedItems([]);
    } catch (error) {
      console.error('Error removing selected favorites:', error);
    }
  };

  // Child component to safely use product detail as prop
  const FavoriteProductCard: React.FC<{ product: FavoriteProduct; detail: any; loadingDetail: boolean; viewMode: string; }> = ({ product, detail, loadingDetail, viewMode }) => {
    // Xóa state quantity
    // const [quantity, setQuantity] = useState(1);
    
    const actualDetail = detail?.data || detail;
    const version = actualDetail?.product_versions?.[0];
    const versionId = version?.id || version?.sku_code || '';
    const stock = version?.stock ?? actualDetail?.stock ?? product.stock;
    const price = typeof version?.price === 'number' ? version.price : (typeof actualDetail?.price === 'number' ? actualDetail.price : Number(product.price));

    // Thêm vào giỏ hàng
    const handleAddToCartDetail = async () => {
      
      
      if (!actualDetail) {
        return;
      }
      
      // Kiểm tra stock trước khi thêm vào giỏ hàng
      if (stock < 1) {
        showWarning('Sản phẩm đã hết hàng!');
        return;
      }
      
      // Kiểm tra stock cơ bản
      
      
      // Kiểm tra xem có đủ stock để thêm vào giỏ hàng không
      if (stock < 1) {
        showWarning(`Sản phẩm đã hết hàng!`);
        return;
      }
      
      // Test with minimal data first
      const cartData: any = {
        product_id: product.id,
        quantity: 1,
        unit_price: price,
        selected_options: {},
      };
      
      // Only add version_id if it's not empty
      if (versionId && versionId.trim() !== '') {
        cartData.version_id = versionId;
      }
      
      
      // Sử dụng hook để thêm vào giỏ hàng
      const success = await addToCart(cartData);
      
      if (success) {
        // Refresh cart data
        await refetchCart();
      }
    };

    // Mua ngay
    const handleBuyNowDetail = async () => {
      if (!actualDetail) return;
      
      // Refetch lại chi tiết sản phẩm để lấy stock mới nhất
      let latestDetail = actualDetail;
      try {
        const res = await axiosAuth.get(`/products/${product.id}`);
        if (res?.data?.data) {
          latestDetail = res.data.data;
        }
      } catch (e) {}
      
      const version = latestDetail?.product_versions?.[0];
      const versionId = version?.id || version?.sku_code || '';
      const stock = version?.stock ?? latestDetail?.stock ?? product.stock;
      
      if (stock < 1) {
        showWarning('Sản phẩm đã hết hàng!');
        return;
      }
      
      // Kiểm tra stock cơ bản
      if (stock < 1) {
        showWarning(`Sản phẩm đã hết hàng!`);
        return;
      }
      
      // Kiểm tra sản phẩm đã có trong giỏ hàng chưa (giống hệt cách Cart xử lý)
      const cartItems = (cartData && (Array.isArray(cartData) ? cartData : cartData.data)) || [];
      const found = cartItems.find((item: any) => item.product_id === product.id && item.version_id === versionId);
      
      if (found) {
        // Nếu đã có trong giỏ, chuyển sang trang thanh toán luôn (không gọi addToCart)
        const orderItems = [{
          product_id: found.product_id,
          version_id: found.version_id,
          quantity: found.quantity,
          unit_price: parseFloat(found.unit_price),
          name: product.name,
          thumbnail_url: product.thumbnail_url,
        }];
        const subtotal = parseFloat(found.unit_price) * found.quantity;
        const totals = {
          subtotal: subtotal,
          discount: 0,
          shipping: 25000,
          total: subtotal + 25000,
        };
        localStorage.setItem('checkoutData', JSON.stringify({ items: orderItems, totals }));
        navigate('/gio-hang/chi-tiet-don-hang', {
          state: {
            items: orderItems,
            totals,
          },
        });
        return;
      }
      
      // Nếu chưa có thì thêm vào giỏ rồi chuyển sang thanh toán
      const success = await addToCart({
        product_id: product.id,
        quantity: 1, // Luôn là 1
        unit_price: price,
        version_id: versionId,
        selected_options: {},
      });
      
      if (success) {
        await refetchCart();
        const orderItems = [{
          product_id: product.id,
          version_id: versionId,
          quantity: 1, // Luôn là 1
          unit_price: price,
          name: product.name,
          thumbnail_url: product.thumbnail_url,
        }];
        const totals = {
          subtotal: price,
          discount: 0,
          shipping: 25000,
          total: price + 25000,
        };
        localStorage.setItem('checkoutData', JSON.stringify({ items: orderItems, totals }));
        navigate('/gio-hang/chi-tiet-don-hang', {
          state: {
            items: orderItems,
            totals,
          },
        });
      }
    };

    return (
      <div key={product.id} className={`${styles.productCard} ${viewMode === 'list' ? styles.listView : ''}`}>
        <div className={styles.productImage}>
          <img src={product.thumbnail_url} alt={product.name} />
          {product.discount && (
            <span className={styles.discountBadge}>-{product.discount}%</span>
          )}
          {product.isOCOP && (
            <span className={styles.ocopBadge}>OCOP</span>
          )}
          <button
            className={styles.favoriteButton}
            onClick={() => handleToggleFavorite(product.id)}
            title="Bỏ khỏi yêu thích"
          >
            <FiHeart />
          </button>
        </div>
        <div className={styles.productInfo}>
          <h3 className={styles.productName}>{product.name}</h3>
          <p className={styles.productSeller}>Bởi {product.seller}</p>
          <div className={styles.productRating}>
            <div className={styles.stars}>
              {renderStars(Math.floor(product.rating))}
            </div>
            <span className={styles.ratingText}>({product.reviewCount})</span>
          </div>
          <div className={styles.productPrice}>
            <span className={styles.currentPrice}>₫{formatPrice(product.price)}</span>
            {product.originalPrice && (
              <span className={styles.originalPrice}>₫{formatPrice(product.originalPrice)}</span>
            )}
            <span className={styles.unit}>/{product.unit}</span>
          </div>
          <div className={styles.productStock}>
            {stock > 0 ? (
              <span className={styles.inStock}>Còn {stock} {product.unit}</span>
            ) : (
              <span className={styles.outOfStock}>Hết hàng</span>
            )}
          </div>
          
          {/* Checkbox for bulk selection */}
          <div className={styles.productCheckbox}>
            <input
              type="checkbox"
              className={styles.selectCheckbox}
              checked={selectedItems.includes(product.id)}
              onChange={() => handleSelectItem(product.id)}
            />
            <span>Chọn sản phẩm</span>
          </div>
          
          <div className={styles.productActions}>
            <button
              className={styles.addToCartButton}
              onClick={handleAddToCartDetail}
              disabled={stock === 0 || loadingDetail}
            >
              <FaShoppingCart />
              Thêm 
            </button>
            <button
              className={styles.buyNowButton}
              onClick={handleBuyNowDetail}
              disabled={stock === 0 || loadingDetail}
            >
              Mua ngay
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderEmptyState = () => (
    <div className={styles.emptyState}>
      <BiCart className={styles.emptyIcon} />
      <h3>Danh sách yêu thích trống</h3>
      <p>Bạn chưa có sản phẩm nào trong danh sách yêu thích</p>
      <button className={styles.browseButton} onClick={() => window.location.href = '/mua-sam'}>
        Khám phá sản phẩm
      </button>
    </div>
  );

  
  if (isLoading) {
    return (
      <div className={styles.favoritesPage}>
        <div className={styles.container}>
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Đang tải danh sách yêu thích...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.favoritesPage}>
        <div className={styles.container}>
          <div className={styles.error}>
            <p>Có lỗi xảy ra khi tải danh sách yêu thích</p>
            <button onClick={() => refetch()}>Thử lại</button>
          </div>
        </div>
      </div>
    );
  }



  return (
    <div className={styles.favoritesPage}>
      <div className={styles.container}>
        {/* Header */}
        <div className={styles.pageHeader}>
          <h1 className={styles.pageTitle}>
            Danh sách yêu thích ({favorites.length})
          </h1>
          
          <div className={styles.headerActions}>
            <div className={styles.searchBar}>
              <FaSearch className={styles.searchIcon} />
              <input
                type="text"
                placeholder="Tìm kiếm sản phẩm yêu thích..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <button
              className={styles.filterButton}
              onClick={() => setShowFilters(!showFilters)}
            >
              <FaFilter />
              Lọc
            </button>
            
            <div className={styles.viewModeToggle}>
              <button
                className={`${styles.viewModeButton} ${viewMode === 'grid' ? styles.active : ''}`}
                onClick={() => setViewMode('grid')}
              >
                <IoGrid />
              </button>
              <button
                className={`${styles.viewModeButton} ${viewMode === 'list' ? styles.active : ''}`}
                onClick={() => setViewMode('list')}
              >
                <IoList />
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className={styles.filtersPanel}>
            <div className={styles.filterRow}>
              <div className={styles.filterGroup}>
                <label>Danh mục:</label>
                <select
                  value={filters.category}
                  onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                >
                  <option value="">Tất cả</option>
                  <option value="Đồ chơi">Đồ chơi</option>
                  <option value="Thực phẩm">Thực phẩm</option>
                  <option value="Thời trang">Thời trang</option>
                </select>
              </div>
              
              <div className={styles.filterGroup}>
                <label>Sắp xếp:</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as FilterState['sortBy'] }))}
                >
                  <option value="newest">Mới nhất</option>
                  <option value="price-low">Giá thấp đến cao</option>
                  <option value="price-high">Giá cao đến thấp</option>
                  <option value="rating">Đánh giá cao nhất</option>
                </select>
              </div>
              
              <div className={styles.filterGroup}>
                <label>
                  <input
                    type="checkbox"
                    checked={filters.inStock}
                    onChange={(e) => setFilters((prev) => ({ ...prev, inStock: e.target.checked }))}
                  />
                  Chỉ hiển thị còn hàng
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Bulk Actions */}
        {selectedItems.length > 0 && (
          <div className={styles.bulkActions}>
            <span>{selectedItems.length} sản phẩm đã chọn</span>
            <button onClick={handleSelectAll}>
              {selectedItems.length === filteredFavorites.length ? 'Bỏ chọn tất cả' : 'Chọn tất cả'}
            </button>
            <button onClick={handleRemoveSelectedFavorites} className={styles.removeSelectedButton}>
              Bỏ thích đã chọn
            </button>
          </div>
        )}

        {/* Products Grid */}
        <div className={styles.productsContainer}>
          {filteredFavorites.length > 0 ? (
            <div className={`${styles.productsGrid} ${viewMode === 'list' ? styles.listLayout : ''}`}>
              {filteredFavorites.map(product => (
                <FavoriteProductCard
                  key={product.id}
                  product={product}
                  detail={productDetails[product.id]}
                  loadingDetail={loadingDetails}
                  viewMode={viewMode}
                />
              ))}
            </div>
          ) : (
            favorites.length > 0 ? (
              <div className={styles.noResults}>
                <p>Không tìm thấy sản phẩm nào phù hợp với bộ lọc</p>
                <button onClick={() => {
                  setSearchTerm('');
                  setFilters({
                    category: '',
                    priceRange: [0, 1000000],
                    inStock: false,
                    sortBy: 'newest'
                  });
                }}>
                  Xóa bộ lọc
                </button>
              </div>
            ) : (
              renderEmptyState()
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default Favorites;
