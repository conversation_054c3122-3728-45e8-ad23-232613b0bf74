import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { UserService } from './services/user-profile.service';
import { UserAffiliate } from './entities/user_affiliates.entity';
import { UserLocation } from './entities/user_locations..entity';
import { Role } from './entities/role.entity';
import { UserProfile } from './entities/user_profile.entity';
import { OtpVerification } from './entities/otp_verification.entity';
import { UserQR } from './entities/user_qr.entity';
import { CloudinaryService } from 'src/cloudinary/cloudinary.service';
import { CloudinaryModule } from 'src/cloudinary/cloudinary.module';
import { RoleService } from './services/role.service';
import { UserAffiliateService } from './services/user_affiliate.service';
import { UserManagementService } from './services/user-management.service';
import { LogClientModule } from 'src/m_log/log-client.module';
import { MLogModule } from 'src/m_log/m_log.module';
import { AuthSession } from './entities/auth_sessions.entity';
import { User2FASettings } from './entities/user_2fa_settings.entity';
import { UserController } from './controllers/user-profile.controller';
import { M1BotController } from './controllers/m1-bot.controller';
import { RoleController } from './controllers/role.controller';
import { UserAffiliateController } from './controllers/user_affiliate.controller';
import { UserManagementController } from './controllers/user-management.controller';
import { M1BotService } from './services/m1-bot.service';
import { Permission } from './entities/permissions.entity';
import { RoleGroupPermission } from './entities/role_group_permissions.entity';
import { RoleGroup } from './entities/role_groups.entity';
import { AuthService } from '../auth/services/auth.service';
import { UserProvider } from './entities/user-provider.entity';
import { RoleScope } from './entities/role_scopes.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserLocation,
      UserQR,
      RoleGroup,
      RoleGroupPermission,
      User,
      Role,
      OtpVerification,
      UserProfile,
      UserAffiliate,
      UserProvider,
      User2FASettings,
      AuthSession,
      Permission,
      RoleScope,
    ]),
    CloudinaryModule,
    LogClientModule,
    MLogModule,
  ],
  controllers: [
    UserController,
    RoleController,
    UserAffiliateController,
    UserManagementController,
    M1BotController,
  ],
  providers: [
    UserService,
    CloudinaryService,
    RoleService,
    UserAffiliateService,
    UserManagementService,
    M1BotService,
  ],
  exports: [M1BotService, RoleService],
})
export class UserModule {}
