export function formatDate(date: string): string {
  return new Date(date).toLocaleDateString('vi-VN')
}

import dayjs, { Dayjs } from 'dayjs'

export const normalizeDateForAntD = (input: any): Dayjs | null => {
  if (!input) return null

  if (dayjs.isDayjs(input)) return input

  // Trường hợp là string nhưng đúng format ISO
  if (typeof input === 'string') {
    const parsed = dayjs(input)
    return parsed.isValid() ? parsed : null
  }

  // Trường hợp là object nhưng không phải dayjs
  if (typeof input === 'object' && input.hasOwnProperty('year')) {
    const { year, month, day } = input
    return dayjs(`${year}-${month}-${day}`)
  }

  return null
}
