import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, <PERSON>inColum<PERSON> } from 'typeorm';
import { RoleGroup } from './role_groups.entity';

@Entity('role_scopes')
export class RoleScope {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => RoleGroup)
  @JoinColumn({ name: 'role_group_id' })
  roleGroup: RoleGroup;

  @Column({ type: 'varchar' })
  locationType: 'province' | 'district' | 'commune';

  @Column({ type: 'varchar' })
  locationCode: string; // e.g., '931244'
}
