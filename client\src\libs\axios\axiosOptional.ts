// src/libs/axios/axiosOptional.ts
import axios from 'axios'
import Cookies from 'js-cookie'

/**
 * Axios instance cho optional authentication
 * - <PERSON><PERSON><PERSON> token nếu có (user đã đăng nhập)
 * - Không gửi token nếu không có (guest user)
 * - <PERSON><PERSON><PERSON><PERSON> redirect khi 401 (khác với axiosAuth)
 */
export const axiosOptional = axios.create({
  baseURL: 'http://localhost:8080',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 120000,
})

// Request interceptor - gửi token nếu có
axiosOptional.interceptors.request.use(
  async (config) => {
    const token = Cookies.get('accessToken')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor - không xử lý 401 (khác với axiosAuth)
axiosOptional.interceptors.response.use(
  (response) => response,
  (error) => {
    // Không xử lý 401, chỉ trả về lỗi
    return Promise.reject(error)
  }
)
