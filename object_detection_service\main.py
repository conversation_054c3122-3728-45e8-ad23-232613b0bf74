from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from PIL import Image
import torch
import io
import uvicorn


app = FastAPI()

# Cho phép CORS nếu c<PERSON><PERSON> g<PERSON><PERSON> từ frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Load YOLOv5 model từ repo local (tránh lỗi rate limit GitHub)
model = torch.hub.load('./yolov5', 'custom', path='tapODBest.pt', source='local', force_reload=True)

print(f"📦 Model loaded from: tapODBest.pt")
print(model)

@app.post("/detect")
async def detect(file: UploadFile = File(...)):
    try:
        image_bytes = await file.read()
        img = Image.open(io.BytesIO(image_bytes)).convert("RGB")
        results = model(img)
        # Lấy tên class/category dự đoán
        categories = results.names
        detected = results.pred[0]
        detected_classes = set()
        for *box, conf, cls in detected.tolist():
            detected_classes.add(categories[int(cls)])
        return {"categories": list(detected_classes)}
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.get("/")
def root():

    return {"message": "Object Detection Service is running"}


if __name__ == '__main__':
    uvicorn.run(app, host="0.0.0.0", port=8003)

# from torch.serialization import add_safe_globals
# from yolov5.models.yolo import DetectionModel
# from fastapi import FastAPI, File, UploadFile
# from fastapi.responses import JSONResponse
# from fastapi.middleware.cors import CORSMiddleware
# from PIL import Image
# import torch
# import io
# import uvicorn
# import os
# import pathlib


# # ---- [AUTO-CONVERT] Fix lỗi WindowsPath khi dùng model từ Windows ----
# def convert_paths(obj):
#     if isinstance(obj, dict):
#         return {k: convert_paths(v) for k, v in obj.items()}
#     elif isinstance(obj, list):
#         return [convert_paths(i) for i in obj]
#     elif isinstance(obj, tuple):
#         return tuple(convert_paths(i) for i in obj)
#     elif isinstance(obj, pathlib.WindowsPath):
#         return str(obj)
#     else:
#         return obj

# def convert_pt_file(original_path="best.pt", output_path="best-linux.pt"):
#     try:
#         print(f"🔁 Converting {original_path} -> {output_path}")
        
#         # Cho phép deserialize DetectionModel (phải import đúng class yolo)
#         add_safe_globals([DetectionModel])

#         torch.serialization.default_restore_location = lambda storage, loc: storage
#         ckpt = torch.load(original_path, map_location="cpu", weights_only=False)
#         ckpt = convert_paths(ckpt)

#         if "model" in ckpt:
#             ckpt["model"].to(torch.device("cpu")).eval()

#         torch.save(ckpt, output_path)
#         print("✅ Convert thành công")
#         return output_path
#     except Exception as e:
#         print(f"❌ Convert thất bại: {e}")
#         return original_path
# # -----------------------------------------------------------------------

# app = FastAPI()

# # Cho phép CORS nếu cần gọi từ frontend
# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=["*"],
#     allow_credentials=True,
#     allow_methods=["*"],
#     allow_headers=["*"],
# )

# # Check và convert model nếu cần
# converted_path = "best-linux.pt"
# if not os.path.exists(converted_path):
#     converted_path = convert_pt_file("best.pt", converted_path)

# # Load YOLOv5 model từ repo local
# print(f"📦 Loading model from: {converted_path}")

# model = torch.hub.load('./yolov5', 'custom', path=converted_path, source='local', force_reload=True)

# @app.post("/detect")
# async def detect(file: UploadFile = File(...)):
#     try:
#         image_bytes = await file.read()
#         img = Image.open(io.BytesIO(image_bytes)).convert("RGB")
#         results = model(img)
#         # Lấy tên class/category dự đoán
#         categories = results.names
#         detected = results.pred[0]
#         detected_classes = set()
#         for *box, conf, cls in detected.tolist():
#             detected_classes.add(categories[int(cls)])
#         return {"categories": list(detected_classes)}
#     except Exception as e:
#         return JSONResponse(status_code=500, content={"error": str(e)})

# @app.get("/")
# def root():
#     return {"message": "Object Detection Service is running"}

# if __name__ == '__main__':
#     uvicorn.run(app, host="0.0.0.0", port=8003)
