import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { UserProfile } from '../entities/user_profile.entity';
import { UpdateUserProfileDto } from '../dto/update-user-profile.dto';
import * as QRCode from 'qrcode';
import { OtpVerification } from '../entities/otp_verification.entity';
import { UserProfileResponseDto } from '../dto/user-profile-response.dto';
import { UserQR } from '../entities/user_qr.entity';
import { UserQRResponseDto } from '../dto/user-qr-response.dto';
import { CloudinaryService } from 'src/cloudinary/cloudinary.service';
import { AssignRoleDto } from '../dto/assign-role.dto';
import { Role } from '../entities/role.entity';
import { User2FASettings } from '../entities/user_2fa_settings.entity';
import { ChangePasswordDto } from '../dto/change-password.dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User) private readonly userRepo: Repository<User>,
    @InjectRepository(UserQR) private readonly qrRepo: Repository<UserQR>,
    @InjectRepository(Role) private readonly roleRepo: Repository<Role>,
    @InjectRepository(UserProfile) private readonly profileRepo: Repository<UserProfile>,
    @InjectRepository(OtpVerification) private readonly otpRepo: Repository<OtpVerification>,
    @InjectRepository(User2FASettings)
    private readonly user2faRepo: Repository<User2FASettings>,
    private readonly cloudinaryService: CloudinaryService,
  ) {}

  /**
   * ✏️ Cập nhật hồ sơ người dùng
   */
  async updateProfile(userId: string, dto: UpdateUserProfileDto) {
    const profile = await this.profileRepo.findOne({ where: { userId } });
    if (!profile) throw new NotFoundException('Không tìm thấy hồ sơ người dùng');

    // ✅ Cập nhật user (email, phone...)

    const user = await this.userRepo.findOneBy({ id: userId });
    if (!user) throw new NotFoundException('Không tìm thấy người dùng');

    if (dto.email && dto.email !== user.email) {
      const emailExist = await this.userRepo.findOneBy({ email: dto.email });
      if (emailExist) throw new ConflictException('Email đã được sử dụng');
      user.email = dto.email;
    }

    if (dto.phone && dto.phone !== user.phone) {
      const phoneExist = await this.userRepo.findOneBy({ phone: dto.phone });
      if (phoneExist) throw new ConflictException('Số điện thoại đã được sử dụng');
      user.phone = dto.phone;
    }

    await this.userRepo.save(user);

    // ✅ Cập nhật thông tin profile
    Object.assign(profile, {
      fullName: dto.fullName,
      citizenId: dto.citizenId,
      gender: dto.gender,
      dateOfBirth: dto.dateOfBirth,
      address: dto.address,
    });

    return this.profileRepo.save(profile);
  }

  /**
   * 🔍 Trả về thông tin hồ sơ người dùng (user + profile + trạng thái xác minh OTP)
   */
  async getProfile(userId: string): Promise<UserProfileResponseDto> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: ['profile', 'role'],
    });

    if (!user || !user.profile) {
      throw new NotFoundException('Không tìm thấy hồ sơ người dùng');
    }

    const verifiedEmail = await this.otpRepo.findOne({
      where: { target: user.email, isVerified: true },
    });

    const verifiedPhone = user.phone
      ? await this.otpRepo.findOne({ where: { target: user.phone, isVerified: true } })
      : null;

    const isEnable2FA = !!(await this.user2faRepo
      .createQueryBuilder('u2fa')
      .where('u2fa.user_Id = :userId', { userId: user.id })
      .andWhere('(u2fa.is_otp_enabled = true OR u2fa.is_totp_enabled = true)')
      .getOne());

    return {
      id: user.profile.id,
      referenceId: user.uidCode,
      fullName: user.profile.fullName,
      phoneNumber: user.phone || user.profile.phoneNumber,
      citizenId: user.profile.citizenId,
      gender: user.profile.gender as 'Nam' | 'Nữ',
      dateOfBirth: user.profile.dateOfBirth ?? undefined,
      avatarUrl: user.profile.avatarUrl,
      avatarId: undefined,
      role: user.role?.name || 'Người dùng',
      isVerifiedPhone: !!verifiedPhone,
      isVerifiedMail: !!verifiedEmail,
      email: user.email,
      uIdCode: user.uidCode,
      address: user.profile.address,
      isEnable2FA,
    };
  }

  /**
   * Đổi mật khẩu người dùng
   */
  async changePassword(userId: string, dto: ChangePasswordDto): Promise<void> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user || !user.password) throw new NotFoundException('Người dùng không hợp lệ');

    const isMatch = await bcrypt.compare(dto.currentPassword, user.password);
    if (!isMatch) throw new BadRequestException('Mật khẩu hiện tại không đúng');

    const hashed = await bcrypt.hash(dto.newPassword, 10);
    user.password = hashed;
    await this.userRepo.save(user);
  }

  /**
   * 📎 Tạo mới mã QR hồ sơ người dùng (hiển thị như danh thiếp)
   */
  async generateProfileQRCode(userId: string): Promise<UserQRResponseDto> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: ['profile', 'role'],
    });
    if (!user || !user.profile) {
      throw new NotFoundException('Không tìm thấy thông tin người dùng');
    }

    const content = `
👤 ${user.profile.fullName}
📧 ${user.email}
📞 ${user.phone || user.profile.phoneNumber || 'Chưa cập nhật'}
🧩 Vai trò: ${user.role?.name}
🔐 Mã người dùng: ${user.uidCode}
    `.trim();

    const qrCode = await QRCode.toDataURL(content);

    const newQr = this.qrRepo.create({
      user,
      qrCode,
      qrType: 'profile',
      description: 'Mã QR hồ sơ người dùng',
      isActive: true,
    });

    const saved = await this.qrRepo.save(newQr);

    return {
      id: saved.id,
      qrCode: saved.qrCode,
      qrType: saved.qrType,
      description: saved.description,
      linkUrl: saved.linkUrl,
      isActive: saved.isActive,
      createdAt: saved.createdAt,
      updatedAt: saved.updatedAt,
      user: {
        uidCode: user.uidCode,
        fullName: user.profile.fullName,
        email: user.email,
        phoneNumber: user.phone || user.profile.phoneNumber,
        role: user.role?.name,
      },
    };
  }

  /**
   * 📄 Lấy tất cả mã QR đã tạo của người dùng
   */
  async getAllUserQRCodes(userId: string): Promise<UserQRResponseDto[]> {
    const qrs = await this.qrRepo.find({
      where: { userId },
      relations: ['user', 'user.profile', 'user.role'],
      order: { createdAt: 'DESC' },
    });

    return qrs.map((qr) => ({
      id: qr.id,
      qrCode: qr.qrCode,
      qrType: qr.qrType,
      description: qr.description,
      linkUrl: qr.linkUrl,
      isActive: qr.isActive,
      createdAt: qr.createdAt,
      updatedAt: qr.updatedAt,
      user: {
        uidCode: qr.user.uidCode,
        fullName: qr.user.profile?.fullName,
        email: qr.user.email,
        phoneNumber: qr.user.phone || qr.user.profile?.phoneNumber,
        role: qr.user.role?.name,
      },
    }));
  }

  /**
   * ❌ Xoá một mã QR theo ID (kiểm tra quyền người tạo)
   */
  async deleteQRCode(userId: string, qrId: string) {
    const qr = await this.qrRepo.findOneBy({ id: qrId });
    if (!qr) throw new NotFoundException('QR không tồn tại');
    if (qr.userId !== userId) throw new ForbiddenException('Không có quyền xoá mã QR này');
    return this.qrRepo.remove(qr);
  }

  /**
   * 🔁 Tạo lại QR hồ sơ cá nhân (vô hiệu hoá các QR cũ)
   */
  async regenerateProfileQRCode(userId: string): Promise<UserQRResponseDto> {
    const oldQrs = await this.qrRepo.findBy({ userId, qrType: 'profile' });
    for (const qr of oldQrs) {
      qr.isActive = false;
      await this.qrRepo.save(qr);
    }
    return this.generateProfileQRCode(userId);
  }

  /**
   * Upload ảnh
   * @param userId
   * @param file
   * @returns
   */
  async uploadAvatar(userId: string, file: Express.Multer.File) {
    try {
      const user = await this.userRepo.findOne({
        where: { id: userId },
        relations: ['profile'],
      });

      if (!user || !user.profile) {
        throw new NotFoundException('Không tìm thấy thông tin người dùng');
      }

      // Xóa avatar cũ nếu có
      if (user.profile.avatarId) {
        await this.cloudinaryService.deleteFile(user.profile.avatarId);
      }

      const uploaded = await this.cloudinaryService.uploadFile(file);

      user.profile.avatarUrl = uploaded.secure_url;
      user.profile.avatarId = uploaded.public_id;

      await this.profileRepo.save(user.profile);

      return {
        avatarUrl: uploaded.secure_url,
        avatarPublicId: uploaded.public_id,
      };
    } catch (error) {
      console.error('Error uploading avatar:', error);
      throw new BadRequestException('Lỗi upload ảnh đại diện: ' + (error?.message || error));
    }
  }

  async assignRoleToUser(dto: AssignRoleDto) {
    const user = await this.userRepo.findOne({ where: { id: dto.userId } });
    if (!user) throw new NotFoundException('Người dùng không tồn tại');

    const role = await this.roleRepo.findOne({ where: { id: dto.roleId } });
    if (!role) throw new NotFoundException('Vai trò không tồn tại');

    user.role = role;
    user.role_id = role.id;

    await this.userRepo.save(user);
    return { message: `Gán quyền thành công: ${role.name} cho người dùng ${user.email}` };
  }
}
