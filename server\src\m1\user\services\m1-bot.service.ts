import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

const NLU_SCORE_THRESHOLD = parseFloat(process.env.NLU_SCORE_THRESHOLD || '0.3');

@Injectable()
export class M1BotService {
  private readonly logger = new Logger(M1BotService.name);

  async analyze(text: string) {
    try {
      const NLU_SERVER_URL = process.env.NLU_SERVER_URL || 'http://localhost:5005';

      const res = await axios.post(`${NLU_SERVER_URL}/nlu`, { text });
      const { intent, score, entities, reply: nluReply, suggestedReplies } = res.data;
      if (!intent || score < NLU_SCORE_THRESHOLD) {
        this.logger.log(`Fallback: text='${text}', score=${score}`);
        return {
          intent: null,
          reply: 'Xin lỗi, tôi chưa hiểu ý bạn. Vui lòng nhập lại hoặc liên hệ hỗ trợ.',
          score,
          entities,
          suggestedReplies: [],
        };
      }
      // Ưu tiên dùng reply từ NLU, nếu không có thì trả về undefined
      return { intent, score, entities, reply: nluReply, suggestedReplies: suggestedReplies || [] };
    } catch (err) {
      // Xử lý lỗi axios riêng biệt, không throw exception làm crash chương trình
      if (err.isAxiosError) {
        this.logger.error('Không thể kết nối NLU service:', err.message);
        console.error('Chi tiết lỗi:', err);
        return {
          intent: null,
          reply: 'Xin lỗi, hệ thống AI đang bận hoặc không kết nối được. Vui lòng thử lại sau.',
          score: 0,
          entities: {},
        };
      }
      this.logger.error('Lỗi không xác định trong analyze:', err);
      return {
        intent: null,
        reply: 'Xin lỗi, hệ thống đang bận. Vui lòng thử lại sau.',
        score: 0,
        entities: {},
      };
    }
  }
}
