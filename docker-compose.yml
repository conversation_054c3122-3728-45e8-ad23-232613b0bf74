version: '3.8'

services:
  server:
    image: tapviet-server:latest
    container_name: tapviet-server
    restart: always
    # depends_on removed since postgres and rabbitmq are not managed by compose
    ports:
      - "8080:8080"
    networks:
      - tapviet-net

  faiss_service:
    image: tap-faiss_service:latest
    container_name: tap-faiss-service
    restart: always
    ports:
      - "8002:8002"
    networks:
      - tapviet-net

  clip_service:
    image: tap-clip_service:latest
    container_name: tap-clip-service
    restart: always
    ports:
      - "8001:8001"
    networks:
      - tapviet-net

  object_detection_service:
    image: tap-object_detection_service:latest
    container_name: tap-object-detection-service
    restart: always
    ports:
      - "8003:8003"
    networks:
      - tapviet-net

  m1_bot_nlu:
    image: tap-m1-bot-nlu:latest
    container_name: tap-m1-bot-nlu
    restart: always
    ports:
      - "5005:5005"
    networks:
      - tapviet-net



networks:
  tapviet-net:
    driver: bridge