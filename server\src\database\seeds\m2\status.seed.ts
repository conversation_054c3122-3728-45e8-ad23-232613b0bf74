// src/database/seeds/status.seed.ts
import { DataSource, DeepPartial } from 'typeorm';
import { Status } from '../../../m2/shop/entities/status.entity';

export async function seedStatus(dataSource: DataSource) {
  const repo = dataSource.getRepository(Status);

  const statuses: DeepPartial<Status>[] = [
    {
      type: 'ORDER',
      code: 'CANCEL_REQUESTED',
      label: 'Yêu cầu hủy đơn',
      color: 'orange',
      order_index: 5,
      is_final: false,
      description: 'Đơn hàng đang chờ duyệt yêu cầu hủy từ người dùng',
    },
    // ===== PRODUCT =====
    { type: 'PRODUCT', code: 'DRAFT', label: 'Bản nháp', color: 'gray', order_index: 1 },
    { type: 'PRODUCT', code: 'ACTIVE', label: '<PERSON>ang bán', color: 'green', order_index: 2 },
    { type: 'PRODUCT', code: 'HIDDEN', label: 'Đã ẩn', color: 'yellow', order_index: 3 },
    { type: 'PRODUCT', code: 'OUT_OF_STOCK', label: 'Hết hàng', color: 'red', order_index: 4 },
    {
      type: 'PRODUCT',
      code: 'ARCHIVED',
      label: 'Lưu trữ',
      color: 'gray',
      order_index: 5,
      is_final: true,
    },

    // ===== ORDER =====
    { type: 'ORDER', code: 'PENDING', label: 'Chờ xác nhận', color: 'gray', order_index: 1 },
    { type: 'ORDER', code: 'CONFIRMED', label: 'Đã xác nhận', color: 'blue', order_index: 2 },
    { type: 'ORDER', code: 'PACKING', label: 'Đang đóng gói', color: 'orange', order_index: 3 },
    { type: 'ORDER', code: 'DELIVERING', label: 'Đang giao hàng', color: 'yellow', order_index: 4 },
    {
      type: 'ORDER',
      code: 'DELIVERED',
      label: 'Đã giao hàng',
      color: 'green',
      order_index: 5,
      is_final: false,
    },
    {
      type: 'ORDER',
      code: 'COMPLETED',
      label: 'Đã hoàn thành',
      color: 'blue',
      order_index: 6,
      is_final: true,
    },
    {
      type: 'ORDER',
      code: 'CANCELLED',
      label: 'Đã hủy',
      color: 'red',
      order_index: 6,
      is_final: true,
    },
    {
      type: 'ORDER',
      code: 'RETURNED',
      label: 'Đã hoàn hàng',
      color: 'purple',
      order_index: 7,
      is_final: true,
    },

    // ===== PAYMENT =====
    { type: 'PAYMENT', code: 'UNPAID', label: 'Chưa thanh toán', color: 'gray', order_index: 1 },
    {
      type: 'PAYMENT',
      code: 'PAID',
      label: 'Đã thanh toán',
      color: 'green',
      order_index: 2,
      is_final: true,
    },
    { type: 'PAYMENT', code: 'FAILED', label: 'Thanh toán thất bại', color: 'red', order_index: 3 },
    {
      type: 'PAYMENT',
      code: 'REFUNDED',
      label: 'Đã hoàn tiền',
      color: 'orange',
      order_index: 4,
      is_final: true,
    },

    // ===== IMAGE =====
    { type: 'IMAGE', code: 'UPLOADING', label: 'Đang tải lên', color: 'blue', order_index: 1 },
    { type: 'IMAGE', code: 'ACTIVE', label: 'Đã sẵn sàng', color: 'green', order_index: 2 },
    {
      type: 'IMAGE',
      code: 'FAILED',
      label: 'Tải lên lỗi',
      color: 'red',
      order_index: 3,
      is_final: true,
    },
    {
      type: 'IMAGE',
      code: 'DELETED',
      label: 'Đã xóa',
      color: 'gray',
      order_index: 4,
      is_final: true,
    },
  ];

  for (const s of statuses) {
    const exists = await repo.findOneBy({ type: s.type, code: s.code });
    if (!exists) {
      const entity = repo.create(s);
      await repo.save(entity);
    } else {
    }
  }

  console.log('✅ Seeded statuses!');
}
