// src/m1/user/entities/user-provider.entity.ts

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity('user_providers')
export class UserProvider {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, (user) => user.providers, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column()
  provider: 'google' | 'facebook' | 'zalo'; // Enum-like

  @Column()
  providerId: string; // ID từ Google/Facebook (sub, id...)

  @Column({ nullable: true })
  email: string;

  @CreateDateColumn()
  createdAt: Date;
}
