import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styles from './Home.module.scss';
import {
  FiShoppingCart,
  FiBell,
  FiHome,
  FiCalendar,
  FiCamera,
  FiChevronRight,
  FiStar,
  FiHeart,
  FiPlus,
  FiArrowLeft,
  FiArrowRight,
  FiMessageCircle,
  FiX,
  FiInfo,
  FiGlobe,
  FiBook,
  FiFileText,
  FiShield,
  FiLock,
  FiBriefcase,
  FiPhone,
  FiHeadphones,
  FiAward,
  FiMonitor,
  FiShare2,
} from 'react-icons/fi';

// Import assets
import tapLogo from '../../assets/images/logo.png';
import walletIcon from '../../assets/images/vi-tap.png';
import luckyWheelIcon from '../../assets/images/quay-thuong.png';
import voucherIcon from '../../assets/images/voucher.png';
import ecosystemIcon from '../../assets/images/he-sinh-thai.png';
import muasam from '../../assets/images/mua-sam.png';
import dichvu from '../../assets/images/dich-vu.png';
import lamdep from '../../assets/images/lam-dep.png';
import giaohang from '../../assets/images/giao-hang.png';
import goixe from '../../assets/images/dat-xe.png';
import dulich from '../../assets/images/du-lich.png';
import daotao from '../../assets/images/dao-tao.png';
import vieclam from '../../assets/images/viec-lam.png';
import yte from '../../assets/images/y-te.png';
import mxh from '../../assets/images/mxh.png';
import nongnghiep from '../../assets/images/nong-nghiep.png';
import baohiem from '../../assets/images/logo.png';
import ResponsiveImage from '../../components/ResponsiveImage';

// Interfaces
interface QuickAction {
  id: number;
  name: string;
  icon: string;
  color: string;
  url: string;
}

interface Category {
  id: number;
  name: string;
  image: string;
  url: string;
  subcategories?: Category[];
}

interface Product {
  id: number;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  isFavorite: boolean;
  isOCOP?: boolean;
  discount?: number;
  description?: string;
  seller?: string;
}

interface Partner {
  id: number;
  name: string;
  logo: string;
  url: string;
}

interface MenuSection {
  title: string;
  items: Array<{
    name: string;
    url: string;
    icon?: string;
  }>;
}

interface NewsItem {
  id: number;
  title: string;
  excerpt: string;
  image: string;
  date: string;
  category: string;
}

export default function HomePage() {
  // States
  const [isDesktop, setIsDesktop] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<number[]>([]);
  const [activeQuickAction, setActiveQuickAction] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState(1);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [cartCount, setCartCount] = useState(2);
  const [favoriteProducts, setFavoriteProducts] = useState<number[]>([2, 4]);
  const [isServicesExpanded, setIsServicesExpanded] = useState(false);
  const [isQuickActionsExpanded, setIsQuickActionsExpanded] = useState(false);
  // Detect screen size
  useEffect(() => {
    const grid = document.querySelector(`.${styles.quickActionsGridExpanded}`);
    if (grid) {
      if (isQuickActionsExpanded) {
        grid.classList.add(styles.visible);
      } else {
        grid.classList.remove(styles.visible);
      }
    }
  }, [isQuickActionsExpanded]);

  useEffect(() => {
    const grid = document.querySelector(`.${styles.categoriesGridExpanded}`);
    if (grid) {
      if (isServicesExpanded) {
        grid.classList.add(styles.visible);
      } else {
        grid.classList.remove(styles.visible);
      }
    }
  }, [isServicesExpanded]);

  useEffect(() => {
    const handleResize = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Auto slide for hero banner
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % 3);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  // Data
  const quickActions: QuickAction[] = [
    { id: 1, name: 'Ví TAP', icon: walletIcon, color: '#4CAF50', url: '/wallet' },
    { id: 2, name: 'Quay thưởng', icon: luckyWheelIcon, color: '#FF9800', url: '/lucky-wheel' },
    { id: 3, name: 'Voucher', icon: voucherIcon, color: '#E91E63', url: '/vouchers' },
    { id: 4, name: 'Hệ sinh thái TAP', icon: ecosystemIcon, color: '#2196F3', url: '/ecosystem' },
    { id: 5, name: 'Ví TAP', icon: walletIcon, color: '#4CAF50', url: '/wallet' },
    { id: 6, name: 'Quay thưởng', icon: luckyWheelIcon, color: '#FF9800', url: '/lucky-wheel' },
    { id: 7, name: 'Voucher', icon: voucherIcon, color: '#E91E63', url: '/vouchers' },
    { id: 8, name: 'Hệ sinh thái TAP', icon: ecosystemIcon, color: '#2196F3', url: '/ecosystem' },
  ];

  const categories: Category[] = [
    {
      id: 1,
      name: 'Mua sắm',
      image: muasam,
      url: '/mua-sam',
    },
    { id: 2, name: 'Dịch vụ', image: dichvu, url: '/services' },
    { id: 3, name: 'Làm đẹp', image: lamdep, url: '/beauty' },
    { id: 4, name: 'Giao hàng', image: giaohang, url: '/delivery' },
    { id: 5, name: 'Gọi xe', image: goixe, url: '/ride' },
    { id: 6, name: 'Du lịch', image: dulich, url: '/travel' },
    { id: 7, name: 'Đào tạo', image: daotao, url: '/education' },
    { id: 8, name: 'Việc làm', image: vieclam, url: '/jobs' },
    { id: 9, name: 'Y tế', image: yte, url: '/healthcare' },
    { id: 10, name: 'Mạng xã hội', image: mxh, url: '/social' },
    { id: 11, name: 'Nông nghiệp', image: nongnghiep, url: '/agriculture' },
    { id: 12, name: 'Bảo hiểm', image: baohiem, url: '/insurance' }
  ];

  const featuredProducts: Product[] = [
    {
      id: 1,
      name: 'Rau củ quả tươi',
      price: 20000,
      originalPrice: 25000,
      image: nongnghiep,
      rating: 4.0,
      isFavorite: false,
      isOCOP: true,
      discount: 20,
      description: 'Rau củ tươi ngon, sạch từ các HTX uy tín',
      seller: 'HTX An Phát'
    },
    {
      id: 2,
      name: 'Rau xanh hữu cơ',
      price: 35000,
      originalPrice: 40000,
      image: nongnghiep,
      rating: 4.0,
      isFavorite: true,
      isOCOP: true,
      discount: 12,
      description: 'Rau xanh hữu cơ không hóa chất',
      seller: 'HTX Xanh Sạch'
    },
    {
      id: 3,
      name: 'Gạo ST25 cao cấp',
      price: 45000,
      image: nongnghiep,
      rating: 4.8,
      isFavorite: false,
      isOCOP: true,
      description: 'Gạo ST25 thơm ngon, chất lượng xuất khẩu',
      seller: 'HTX Lúa Gạo Miền Tây'
    },
    {
      id: 4,
      name: 'Mật ong 100%',
      price: 120000,
      image: nongnghiep,
      rating: 4.5,
      isFavorite: true,
      isOCOP: true,
      description: 'Mật ong 100% nguyên chất',
      seller: 'HTX Ong Mật Tây Bắc'
    }
  ];

  const partners: Partner[] = [
    { id: 1, name: 'HTX Tân An Phát', logo: tapLogo, url: '/partners/tan-an-phat' },
    { id: 2, name: 'HTX Nông nghiệp Xanh', logo: tapLogo, url: '/partners/nong-nghiep-xanh' },
    { id: 3, name: 'HTX Thương mại Dịch vụ', logo: tapLogo, url: '/partners/thuong-mai' },
    { id: 4, name: 'HTX Công nghệ Số', logo: tapLogo, url: '/partners/cong-nghe-so' },
    { id: 5, name: 'HTX Du lịch Sinh thái', logo: tapLogo, url: '/partners/du-lich' },
    { id: 6, name: 'HTX Chế biến Thực phẩm', logo: tapLogo, url: '/partners/che-bien' }
  ];


  const bestSellingProducts: Product[] = [
  {
    id: 5,
    name: 'Trái cây sấy dẻo',
    price: 85000,
    originalPrice: 100000,
    image: nongnghiep,
    rating: 4.7,
    isFavorite: false,
    isOCOP: true,
    discount: 15,
    description: 'Trái cây sấy dẻo tự nhiên, không đường hóa học',
    seller: 'HTX Trái Cây Sạch'
  },
  {
    id: 6,
    name: 'Hạt điều rang muối',
    price: 120000,
    image: nongnghiep,
    rating: 4.9,
    isFavorite: true,
    isOCOP: false,
    description: 'Hạt điều rang muối giòn tan, thơm ngon',
    seller: 'HTX Bình Phước'
  },
  {
    id: 7,
    name: 'Nước mắm',
    price: 65000,
    originalPrice: 80000,
    image: nongnghiep,
    rating: 4.6,
    isFavorite: false,
    isOCOP: true,
    discount: 19,
    description: 'Nước mắm nguyên chất từ Phú Quốc',
    seller: 'HTX Phú Quốc'
  },
  {
    id: 8,
    name: 'Trà thái nguyên',
    price: 90000,
    image: nongnghiep,
    rating: 4.5,
    isFavorite: false,
    isOCOP: true,
    description: 'Trà xanh thơm tự nhiên, chất lượng cao',
    seller: 'HTX Trà Thái Nguyên'
  }
];

  // Icon mapping for menu
  const iconMap = {
    FiInfo: FiInfo,
    FiGlobe: FiGlobe,
    FiBook: FiBook,
    FiFileText: FiFileText,
    FiShield: FiShield,
    FiLock: FiLock,
    FiBriefcase: FiBriefcase,
    FiPhone: FiPhone,
    FiHeadphones: FiHeadphones,
    FiAward: FiAward,
    FiMonitor: FiMonitor
  };

  const menuSections: MenuSection[] = [
    {
      title: 'Về TAP',
      items: [
        { name: 'Giới thiệu HTX Tân An Phát', url: '/about', icon: 'FiInfo' },
        { name: 'Giới thiệu hệ sinh thái TAP', url: '/ecosystem', icon: 'FiGlobe' },
        { name: 'Hướng dẫn sử dụng', url: '/guide', icon: 'FiBook' },
        { name: 'Tin tức & Sự kiện', url: '/news', icon: 'FiFileText' }
      ]
    },
    {
      title: 'Chính sách',
      items: [
        { name: 'Điều khoản sử dụng', url: '/terms', icon: 'FiShield' },
        { name: 'Chính sách bảo mật', url: '/privacy', icon: 'FiLock' },
        { name: 'Chính sách kinh doanh', url: '/business-policy', icon: 'FiBriefcase' },
        { name: 'Liên hệ – Hỗ trợ', url: '/contact', icon: 'FiPhone' }
      ]
    },
    {
      title: 'Dịch vụ',
      items: [
        { name: 'Hỗ trợ khách hàng', url: '/support', icon: 'FiHeadphones' },
        { name: 'Đào tạo & Tư vấn', url: '/training', icon: 'FiAward' },
        { name: 'Chuyển đổi số', url: '/digital-transformation', icon: 'FiMonitor' }
      ]
    }
  ];

  const heroSlides = [
    {
      title: 'Chuyển đổi số - Chuyển đổi xanh',
      subtitle: 'Kết nối HTX – Đẩy mạnh tiêu thụ nông sản và dịch vụ địa phương',
      image: tapLogo,
      cta: 'Tham gia hệ sinh thái'
    },
    {
      title: 'Nông sản sạch - Chất lượng cao',
      subtitle: 'Sản phẩm OCOP 4-5 sao từ các HTX uy tín trên toàn quốc',
      image: nongnghiep,
      cta: 'Khám phá ngay'
    },
    {
      title: 'Dịch vụ đa dạng - Tiện ích',
      subtitle: 'Từ mua sắm đến dịch vụ, tất cả trong một ứng dụng',
      image: dichvu,
      cta: 'Trải nghiệm'
    }
  ];

  const newsItems: NewsItem[] = [
    {
      id: 1,
      title: 'HTX Tân An Phát đạt chứng nhận OCOP 4 sao',
      excerpt: 'Sản phẩm gạo ST25 của HTX được công nhận chất lượng cao',
      image: nongnghiep,
      date: '2025-07-01',
      category: 'Tin tức'
    },
    {
      id: 2,
      title: 'Chương trình khuyến mãi tháng 7',
      excerpt: 'Giảm giá lên đến 50% cho các sản phẩm nông sản sạch',
      image: muasam,
      date: '2025-07-02',
      category: 'Khuyến mãi'
    },
    {
      id: 3,
      title: 'Hội thảo chuyển đổi số cho HTX',
      excerpt: 'Tham gia miễn phí hội thảo về ứng dụng công nghệ',
      image: daotao,
      date: '2025-07-03',
      category: 'Sự kiện'
    }
  ];

  // Functions
  const toggleCategory = (categoryId: number) => {
    setExpandedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const toggleQuickActionsView = () => {
    setIsQuickActionsExpanded(!isQuickActionsExpanded);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleFavorite = (productId: number) => {
    setFavoriteProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const addToCart = (productId: number) => {
    setCartCount(prev => prev + 1);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search logic here
  };

  const nextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide(prev => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  const toggleServicesView = () => {
    setIsServicesExpanded(!isServicesExpanded);
  };

  const handleUserClick = () => {
    window.location.href = '/profile';
  };

  const handleShare = (product: Product) => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description || '',
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      alert('Đã copy link sản phẩm!');
    }
  };

  // Desktop Layout
  if (isDesktop) {
    return (
      <div className={styles.homePageDesktop}>
        {/* Hero Banner Desktop */}
        <section className={styles.heroBannerDesktop}>
          <div className={styles.heroContainer}>
            <div className={styles.heroSlider}>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentSlide}
                  initial={{ opacity: 0, x: 100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  transition={{ duration: 0.5 }}
                  className={styles.heroSlide}
                >
                  <div className={styles.heroContent}>
                    <div className={styles.heroText}>
                      <h1>{heroSlides[currentSlide].title}</h1>
                      <p>{heroSlides[currentSlide].subtitle}</p>
                      <button className={styles.ctaButton}>
                        {heroSlides[currentSlide].cta}
                      </button>
                    </div>
                    <div className={styles.heroImage}>
                      <img src={heroSlides[currentSlide].image} alt="Hero" />
                    </div>
                  </div>
                </motion.div>
              </AnimatePresence>

              <div className={styles.sliderControls}>
                <button className={styles.sliderButton} onClick={prevSlide}>
                  <FiArrowLeft />
                </button>
                <div className={styles.sliderDots}>
                  {heroSlides.map((_, index) => (
                    <button
                      key={index}
                      className={`${styles.sliderDot} ${index === currentSlide ? styles.active : ''}`}
                      onClick={() => setCurrentSlide(index)}
                    />
                  ))}
                </div>
                <button className={styles.sliderButton} onClick={nextSlide}>
                  <FiArrowRight />
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Actions Desktop */}
        <section className={styles.quickActionsDesktop}>
          <div className={styles.container}>
            <div className={styles.quickActionsGrid}>
              {quickActions.map(action => (
                <motion.div
                  key={action.id}
                  className={styles.quickActionCard}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => window.location.href = action.url}
                >
                  <div
                    className={styles.quickActionIcon}
                    style={{ backgroundColor: action.color }}
                  >
                    <img src={action.icon} alt={action.name} />
                  </div>
                  <h3>{action.name}</h3>
                  <p>Khám phá ngay</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Categories Desktop */}
        <section className={styles.categoriesDesktop}>
          <div className={styles.container}>
            <div className={styles.sectionHeader}>
              <h2>Danh mục sản phẩm & dịch vụ</h2>
              <div className={styles.categoryControls}>
                <button className={styles.scrollButton}>
                  <FiArrowLeft />
                </button>
                <button className={styles.scrollButton}>
                  <FiArrowRight />
                </button>
              </div>
            </div>

            <div className={styles.categoriesGridDesktop}>
              {categories.map(category => (
                <motion.div
                  key={category.id}
                  className={styles.categoryCardDesktop}
                  whileHover={{ scale: 1.05 }}
                  onClick={() => window.location.href = category.url}
                >
                  <div className={styles.categoryImageDesktop}>
                    <img src={category.image} alt={category.name} />
                  </div>
                  <h3>{category.name}</h3>
                  {category.subcategories && (
                    <div className={styles.subcategoriesDesktop}>
                      {category.subcategories.slice(0, 3).map(sub => (
                        <span key={sub.id} className={styles.subcategoryTag}>
                          {sub.name}
                        </span>
                      ))}
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Featured Products Desktop */}
        <section className={styles.featuredProductsDesktop}>
          <div className={styles.container}>
            <div className={styles.sectionHeader}>
              <h2>Sản phẩm nổi bật</h2>
              <div className={styles.productTabs}>
                <button
                  className={`${styles.tabButton} ${activeTab === 1 ? styles.active : ''}`}
                  onClick={() => setActiveTab(1)}
                >
                  Nông sản sạch
                </button>
                <button
                  className={`${styles.tabButton} ${activeTab === 2 ? styles.active : ''}`}
                  onClick={() => setActiveTab(2)}
                >
                  OCOP 4-5 sao
                </button>
                <button
                  className={`${styles.tabButton} ${activeTab === 3 ? styles.active : ''}`}
                  onClick={() => setActiveTab(3)}
                >
                  Ưu đãi hot
                </button>
              </div>
            </div>

            <div className={styles.productsGridDesktop}>
              {featuredProducts.map(product => (
                <motion.div
                  key={product.id}
                  className={styles.productCardDesktop}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className={styles.productImageDesktop}>
                    <ResponsiveImage
                      src={product.image}
                      alt={product.name}
                      aspectRatio="square"
                      objectFit="cover"
                      lazy={true}
                      sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 20vw"
                    />
                    {product.discount && (
                      <span className={styles.discountBadge}>-{product.discount}%</span>
                    )}
                    {product.isOCOP && (
                      <span className={styles.ocopBadge}>OCOP</span>
                    )}
                    <div className={styles.productOverlay}>
                      <button className={styles.quickViewButton}>
                        Xem nhanh
                      </button>
                      <button
                        className={`${styles.favoriteButtonDesktop} ${favoriteProducts.includes(product.id) ? styles.favorited : ''}`}
                        onClick={() => toggleFavorite(product.id)}
                      >
                        <FiHeart />
                      </button>
                    </div>
                  </div>

                  <div className={styles.productInfoDesktop}>
                    <h3>{product.name}</h3>
                    <p className={styles.productSeller}>{product.seller}</p>
                    <div className={styles.productRating}>
                      <div className={styles.ratingLeft}>
                        <div className={styles.stars}>
                          {[...Array(5)].map((_, i) => (
                            <FiStar
                              key={i}
                              className={i < Math.floor(product.rating) ? styles.starFilled : styles.starEmpty}
                            />
                          ))}
                        </div>
                        <span>({product.rating})</span>
                      </div>
                      <button
                        className={styles.shareButton}
                        onClick={() => handleShare(product)}
                      >
                        <FiShare2 />
                      </button>
                    </div>

                    <div className={styles.productPrice}>
                      <span className={styles.currentPrice}>
                        {product.price.toLocaleString()}đ
                      </span>
                      {product.originalPrice && (
                        <span className={styles.originalPrice}>
                          {product.originalPrice.toLocaleString()}đ
                        </span>
                      )}
                    </div>
                    <div className={styles.productActionsDesktop}>
                      <button
                        className={styles.addToCartButtonDesktop}
                        onClick={() => addToCart(product.id)}
                      >
                        <FiShoppingCart />
                        Thêm vào giỏ
                      </button>
                      <button className={styles.buyNowButtonDesktop}>
                        Mua ngay
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* News Section Desktop */}
        <section className={styles.newsDesktop}>
          <div className={styles.container}>
            <div className={styles.sectionHeader}>
              <h2>Tin tức & Sự kiện</h2>
              <a href="/news" className={styles.viewAllLink}>Xem tất cả</a>
            </div>

            <div className={styles.newsGrid}>
              {newsItems.map(news => (
                <div key={news.id} className={styles.newsCard}>
                  <div className={styles.newsImage}>
                    <ResponsiveImage
                      src={news.image}
                      alt={news.title}
                      aspectRatio="16-9"
                      objectFit="cover"
                      lazy={true}
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                    <span className={styles.newsCategory}>{news.category}</span>
                  </div>
                  <div className={styles.newsContent}>
                    <h3>{news.title}</h3>
                    <p>{news.excerpt}</p>
                    <div className={styles.newsDate}>
                      {new Date(news.date).toLocaleDateString('vi-VN')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Partners Desktop */}
        <section className={styles.partnersDesktop}>
          <div className={styles.container}>
            <h2>Đối tác hệ sinh thái TAP</h2>
            <div className={styles.partnersGridDesktop}>
              {partners.map(partner => (
                <motion.div
                  key={partner.id}
                  className={styles.partnerCardDesktop}
                  whileHover={{ scale: 1.05 }}
                  onClick={() => window.location.href = partner.url}
                >
                  <img src={partner.logo} alt={partner.name} />
                  <h4>{partner.name}</h4>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Newsletter Desktop */}
        <section className={styles.newsletterDesktop}>
          <div className={styles.container}>
            <div className={styles.newsletterContent}>
              <h2>Đăng ký nhận bản tin</h2>
              <p>Cập nhật thông tin sản phẩm, chương trình hỗ trợ, và xu hướng mới từ TAP</p>
              <form className={styles.newsletterForm}>
                <input type="email" placeholder="Nhập email của bạn" />
                <button type="submit">Đăng ký</button>
              </form>
            </div>
          </div>
        </section>

        {/* Footer Desktop */}
        <footer className={styles.footerDesktop}>
          <div className={styles.container}>
            <div className={styles.footerContent}>
              <div className={styles.footerSection}>
                <img src={tapLogo} alt="TAP" className={styles.footerLogo} />
                <p>Nền tảng số kết nối HTX và cộng đồng</p>
                <div className={styles.socialLinks}>
                  <a href="#" className={styles.socialLink}>Facebook</a>
                  <a href="#" className={styles.socialLink}>Zalo</a>
                  <a href="#" className={styles.socialLink}>YouTube</a>
                </div>
              </div>
              <div className={styles.footerSection}>
                <h4>Về TAP</h4>
                <ul>
                  <li><a href="/gioi-thieu">Giới thiệu</a></li>
                  <li><a href="/he-sinh-thai">Hệ sinh thái</a></li>
                  <li><a href="/tin-tuc">Tin tức</a></li>
                  <li><a href="/su-kien">Sự kiện</a></li>
                </ul>
              </div>
              <div className={styles.footerSection}>
                <h4>Hỗ trợ</h4>
                <ul>
                  <li><a href="/huong-dan">Hướng dẫn</a></li>
                  <li><a href="/lien-he">Liên hệ</a></li>
                  <li><a href="/bao-mat">Chính sách</a></li>
                  <li><a href="/dieu-khoan">Điều khoản</a></li>
                </ul>
              </div>
              <div className={styles.footerSection}>
                <h4>Dịch vụ</h4>
                <ul>
                  <li><a href="/mua-sam">Mua sắm</a></li>
                  <li><a href="/dich-vu">Dịch vụ HTX</a></li>
                  <li><a href="/chuyen-doi">Chuyển đổi số</a></li>
                  <li><a href="/dao-tao">Đào tạo</a></li>
                </ul>
              </div>
            </div>
            <div className={styles.footerBottom}>
              <p>© 2025 HTX Tân An Phát. Mọi quyền được bảo lưu.</p>
            </div>
          </div>
        </footer>
      </div>
    );
  }

  // Mobile Layout
  return (
    <div className={styles.homePage}>
      {/* Sidebar Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <>
            <motion.div
              className={styles.overlay}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={toggleMenu}
            />
            <motion.div
              className={styles.sidebarMenu}
              initial={{ x: '-100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'tween', duration: 0.3 }}
            >
              <div className={styles.menuHeader}>
                <img src={tapLogo} alt="TAP" className={styles.menuLogo} />
                <button className={styles.closeButton} onClick={toggleMenu}>
                  <FiX />
                </button>
              </div>

              <div className={styles.menuContent}>
                {menuSections.map((section, index) => (
                  <div key={index} className={styles.menuSection}>
                    <h3 className={styles.menuSectionTitle}>{section.title}</h3>
                    {section.items.map((item, itemIndex) => {
                      const IconComponent = iconMap[item.icon as keyof typeof iconMap];
                      return (
                        <a
                          key={itemIndex}
                          href={item.url}
                          className={styles.menuItem}
                          onClick={toggleMenu}
                        >
                          {IconComponent && <IconComponent className={styles.menuIcon} />}
                          {item.name}
                        </a>
                      );
                    })}
                  </div>
                ))}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Quick Actions */}
      <section className={styles.quickActions}>
        <div className={styles.sectionHeader}>
          <h2 className={styles.sectionTitle}>Hành động nhanh</h2>
          <button
            className={styles.expandButton}
            onClick={toggleQuickActionsView}
            aria-label={isQuickActionsExpanded ? 'Thu gọn danh sách hành động nhanh' : 'Mở rộng danh sách hành động nhanh'}
            aria-expanded={isQuickActionsExpanded}
          >
            {isQuickActionsExpanded ? 'Thu gọn' : 'Xem tất cả'}
            <FiChevronRight
              className={`${styles.expandArrow} ${isQuickActionsExpanded ? styles.expanded : ''}`}
            />
          </button>
        </div>

        {isQuickActionsExpanded ? (
          <div className={styles.quickActionsGridExpanded}>
            {quickActions.map(action => (
              <div
                key={action.id}
                className={styles.quickActionCardExpanded}
                onClick={() => {
                  setActiveQuickAction(action.id);
                  window.location.href = action.url;
                }}
              >
                <div
                  className={styles.quickActionImageExpanded}
                  style={{ backgroundColor: action.color }}
                >
                  <img src={action.icon} alt={action.name} />
                </div>
                <span className={styles.quickActionNameExpanded}>{action.name}</span>
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.quickActionsSlider}>
            {quickActions.slice(0, 8).map(action => (
              <div
                key={action.id}
                className={styles.quickActionSliderItem}
                role="button"
                tabIndex={0}
                onClick={() => {
                  setActiveQuickAction(action.id);
                  window.location.href = action.url;
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    setActiveQuickAction(action.id);
                    window.location.href = action.url;
                  }
                }}
              >
                <div
                  className={styles.quickActionSliderImage}
                  style={{ backgroundColor: action.color }}
                >
                  <img src={action.icon} alt={action.name} />
                </div>
                <span className={styles.quickActionSliderName}>{action.name}</span>
              </div>
            ))}
          </div>
        )}
      </section>


      {/* Categories */}
      <section className={styles.categories}>
        <div className={styles.sectionHeader}>
          <h2 className={styles.sectionTitle}>Sản phẩm - Dịch vụ</h2>
          <button
            className={styles.expandButton}
            onClick={toggleServicesView}
            aria-label={isServicesExpanded ? 'Thu gọn danh sách sản phẩm - dịch vụ' : 'Mở rộng danh sách sản phẩm - dịch vụ'}
            aria-expanded={isServicesExpanded}
          >
            {isServicesExpanded ? 'Thu gọn' : 'Xem tất cả'}
            <FiChevronRight
              className={`${styles.expandArrow} ${isServicesExpanded ? styles.expanded : ''}`}
            />
          </button>
        </div>

        {isServicesExpanded ? (
          <div className={styles.categoriesGridExpanded}>
            {categories.map(category => (
              <div
                key={category.id}
                className={styles.categoryCardExpanded}
                onClick={() => window.location.href = category.url}
              >
                <div className={styles.categoryImageExpanded}>
                  <img src={category.image} alt={category.name} />
                </div>
                <span className={styles.categoryNameExpanded}>{category.name}</span>
              </div>
            ))}
          </div>
        ) : (
          <div className={styles.categoriesSlider}>
            {categories.slice(0, 8).map(category => (
              <div
                key={category.id}
                className={styles.categorySliderItem}
                role="button"
                tabIndex={0}
                onClick={() => window.location.href = category.url}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    window.location.href = category.url;
                  }
                }}
              >
                <div className={styles.categorySliderImage}>
                  <img src={category.image} alt={category.name} />
                </div>
                <span className={styles.categorySliderName}>{category.name}</span>
              </div>
            ))}
          </div>
        )}
      </section>

      {/* Partners */}
      <section className={styles.partners}>
        <div className={styles.sectionHeader}>
          <h2 className={styles.sectionTitle}>Đối tác hệ sinh thái</h2>
        </div>

        <div className={styles.partnersSlider}>
          {partners.map(partner => (
            <div
              key={partner.id}
              className={styles.partnerItem}
              onClick={() => window.location.href = partner.url}
            >
              <img src={partner.logo} alt={partner.name} />
            </div>
          ))}
        </div>
      </section>

      {/* Featured Products */}
      <section className={styles.featuredProducts}>
        <div className={styles.sectionHeader}>
          <h2 className={styles.sectionTitle}>Sản phẩm nổi bật</h2>
          <p className={styles.sectionSubtitle}>Sản phẩm OCOP chất lượng cao</p>
        </div>

        <div className={styles.productsGrid}>
          {featuredProducts.map(product => (
            <motion.div
              key={product.id}
              className={styles.productCard}
              whileTap={{ scale: 0.98 }}
            >
              <div className={styles.productImage}>
                <ResponsiveImage
                  src={product.image}
                  alt={product.name}
                  aspectRatio="square"
                  objectFit="cover"
                  lazy={true}
                  sizes="(max-width: 480px) 50vw, (max-width: 768px) 33vw, 25vw"
                />
                {product.discount && (
                  <span className={styles.discountBadge}>-{product.discount}%</span>
                )}
                {product.isOCOP && (
                  <span className={styles.ocopBadge}>OCOP</span>
                )}
                <button
                  className={`${styles.favoriteButton} ${favoriteProducts.includes(product.id) ? styles.favorited : ''}`}
                  onClick={() => toggleFavorite(product.id)}
                >
                  <FiHeart />
                </button>
              </div>

              <div className={styles.productInfo}>
                <h3 className={styles.productName}>{product.name}</h3>
                <p className={styles.productSeller}>{product.seller}</p>
                <div className={styles.productRating}>
                  <div className={styles.ratingLeft}>
                    <FiStar className={styles.starIcon} />
                    <span>{product.rating}</span>
                  </div>
                  <button
                    className={styles.shareButton}
                    onClick={() => handleShare(product)}
                  >
                    <FiShare2 />
                  </button>
                </div>

                <div className={styles.productPrice}>
                  <span className={styles.currentPrice}>
                    {product.price.toLocaleString()}đ
                  </span>
                  {product.originalPrice && (
                    <span className={styles.originalPrice}>
                      {product.originalPrice.toLocaleString()}đ
                    </span>
                  )}
                </div>
                <div className={styles.productActions}>
                  <button className={styles.buyNowButton}>Mua ngay</button>
                  <button
                    className={styles.addToCartButton}
                    onClick={() => addToCart(product.id)}
                  >
                    <FiPlus />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </section>



      {/* Best Selling Products */}
<section className={styles.featuredProducts}>
  <div className={styles.sectionHeader}>
    <div>
      <h2 className={styles.sectionTitle}>Sản phẩm bán chạy nhất</h2>
      <p className={styles.sectionSubtitle}>Sản phẩm được ưa chuộng nhất hiện nay</p>
    </div>
  </div>
  <div className={styles.productsGrid}>
    {bestSellingProducts.map(product => (
      <div key={product.id} className={styles.productCard}>
        <div className={styles.productImage}>
          <ResponsiveImage
            src={product.image}
            alt={product.name}
            aspectRatio="square"
            objectFit="cover"
            lazy={true}
            sizes="(max-width: 480px) 50vw, (max-width: 768px) 33vw, 25vw"
          />
          {product.discount && (
            <span className={styles.discountBadge}>-{product.discount}%</span>
          )}
          {product.isOCOP && (
            <span className={styles.ocopBadge}>OCOP</span>
          )}
          <button
            className={`${styles.favoriteButton} ${product.isFavorite ? styles.favorited : ''}`}
            onClick={() => toggleFavorite(product.id)}
          >
            <FiHeart />
          </button>
        </div>
        <div className={styles.productInfo}>
          <h3 className={styles.productName}>{product.name}</h3>
          <p className={styles.productSeller}>{product.seller}</p>
          <div className={styles.productRating}>
            <div className={styles.ratingLeft}>
              <span className={styles.starIcon}><FiStar /></span>
              <span>{product.rating}</span>
            </div>
            <button className={styles.shareButton} onClick={() => handleShare(product)}>
              <FiShare2 />
            </button>
          </div>
          <div className={styles.productPrice}>
            <span className={styles.currentPrice}>{product.price.toLocaleString()}đ</span>
            {product.originalPrice && (
              <span className={styles.originalPrice}>{product.originalPrice.toLocaleString()}đ</span>
            )}
          </div>
          <div className={styles.productActions}>
            <button className={styles.buyNowButton}>Mua ngay</button>
            <button className={styles.addToCartButton} onClick={() => addToCart(product.id)}>
              <FiPlus />
            </button>
          </div>
        </div>
      </div>
    ))}
  </div>
</section>


      {/* News Section Mobile */}
      <section className={styles.newsMobile}>
        <div className={styles.sectionHeader}>
          <h2 className={styles.sectionTitle}>Tin tức & Sự kiện</h2>
          <a href="/news" className={styles.viewAllLink}>Xem tất cả</a>
        </div>

        <div className={styles.newsSlider}>
          {newsItems.map(news => (
            <div key={news.id} className={styles.newsCardMobile}>
              <div className={styles.newsImageMobile}>
                <ResponsiveImage
                  src={news.image}
                  alt={news.title}
                  aspectRatio="16-9"
                  objectFit="cover"
                  lazy={true}
                  sizes="(max-width: 480px) 80vw, 60vw"
                />
                <span className={styles.newsCategoryMobile}>{news.category}</span>
              </div>
              <div className={styles.newsContentMobile}>
                <h3>{news.title}</h3>
                <p>{news.excerpt}</p>
                <div className={styles.newsDateMobile}>
                  {new Date(news.date).toLocaleDateString('vi-VN')}
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Bottom Navigation */}
      <nav className={styles.bottomNav}>
        <div className={styles.bottomNavItem}>
          <FiHome />
          <span>Trang chủ</span>
        </div>
        <div className={styles.bottomNavItem}>
          <FiCalendar />
          <span>Sự kiện</span>
        </div>
        <div className={styles.bottomNavItem}>
          <FiCamera />
          <span>Quét QR</span>
        </div>
        <div className={styles.bottomNavItem}>
          <FiBell />
          <span>Thông báo</span>
        </div>
        <div className={styles.bottomNavItem}>
          <FiMessageCircle />
          <span>EVA</span>
        </div>
      </nav>
    </div>
  );
}
