import React, { useState, useEffect, useMemo } from 'react'
import {
  Table,
  Input,
  Tag,
  Space,
  Card,
  Statistic,
  Row,
  Col,
  Spin,
  Modal,
  Select,
  Upload,
  message,
  Image,
} from 'antd'
import {
  SearchOutlined,
  EyeOutlined,
  FolderOutlined,
  FileOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import { skipToken } from '@reduxjs/toolkit/query'
import { useToast } from '../../../components/Toast/index'
import Button from '../../../components/Button'
import ModalButtonGroup from '../../../components/ModalButtonGroup'
import styles from './CategoryManagement.module.scss'
import {
  useCategoryTree,
  useCreateCategory,
  useCategoryDetail,
  useUpdateCategory,
  useDeleteCategory,
  useUploadCategoryIcon,
} from '../../../hooks/product/useCategory'
import { useGetCategoryDetailQuery } from '../../../services/M2/ProductManagement/categoryManagement'
import type { CategoryItem } from '../../../services/M2/ProductManagement/categoryManagement'
import logo from '@/assets/images/logo.png'

const CategoryManagement: React.FC = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [addForm, setAddForm] = useState({
    name: '',
    slug: '',
    icon_url: '',
    parentId: '',
  })
  const [createCategory, { isLoading: isCreating }] = useCreateCategory()
  const [searchText, setSearchText] = useState('')
  const [isMobile, setIsMobile] = useState(false)

  // ✅ State cho modal chi tiết
  const [selectedCategory, setSelectedCategory] = useState<CategoryItem | null>(null)
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false)

  // ✅ State cho modal cập nhật
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editForm, setEditForm] = useState({
    name: '',
    slug: '',
    icon_url: '',
    parentId: '',
  })
  const [updateCategory, { isLoading: isUpdating }] = useUpdateCategory()

  // ✅ Hook xóa category
  const [deleteCategory, { isLoading: isDeleting }] = useDeleteCategory()

  // ✅ Hook upload icon
  const [uploadIcon, { isLoading: isUploadingIcon }] = useUploadCategoryIcon()

  // ✅ Toast notifications - Fixed
  const { showSuccess, showError, showWarning, showInfo } = useToast()

  // ✅ Lấy data từ API
  const {
    data: categoriesData,
    isLoading,
    error,
    refetch,
  } = useCategoryTree({
    type: 'tree',
  })

  // ✅ Lấy tree data trực tiếp từ API
  const categories = useMemo(() => {
    if (!categoriesData) return []
    if (Array.isArray(categoriesData)) {
      return categoriesData
    } else if (categoriesData.data && Array.isArray(categoriesData.data)) {
      return categoriesData.data
    }
    return []
  }, [categoriesData])

  // ✅ Error handling với toast - Fixed
  useEffect(() => {
    if (error) {
      showError('Không thể tải danh sách danh mục')
    }
  }, [error, showError])

  // Check mobile screen size
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768)
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // ✅ Upload icon handler
  const handleIconUpload = async (file: File, formType: 'add' | 'edit') => {
    // Validation
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      showError('Chỉ được chọn file ảnh!')
      return false
    }

    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isLt2M) {
      showError('Ảnh phải nhỏ hơn 2MB!')
      return false
    }

    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await uploadIcon(formData).unwrap()

      if (formType === 'add') {
        setAddForm((prev) => ({ ...prev, icon_url: response.icon_url }))
      } else {
        setEditForm((prev) => ({ ...prev, icon_url: response.icon_url }))
      }

      showSuccess('Upload icon thành công')
      return false // Prevent default upload behavior
    } catch (error) {
      showError('Upload icon thất bại')
      return false
    }
  }

  // Mobile columns - simplified for mobile view
  const mobileColumns = [
    {
      title: 'Danh mục',
      key: 'category',
      render: (_: any, record: CategoryItem) => (
        <div className={styles.mobileCategoryCard}>
          <div className={styles.mobileCategoryHeader}>
            <Image
              src={record.icon_url || logo}
              alt={record.name}
              width={32}
              height={32}
              className={styles.categoryIconMobile}
              fallback={logo}
            />
            <div className={styles.mobileCategoryInfo}>
              <div className={styles.mobileCategoryName}>{record.name}</div>
              <div className={styles.mobileCategorySlug}>{record.slug}</div>
              <div className={styles.mobileCategoryLevel}>
                {record.level === 0 ? (
                  <Tag color="blue">Danh mục gốc</Tag>
                ) : (
                  <Tag color="green">Danh mục con</Tag>
                )}
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      render: (_: any, record: CategoryItem) => (
        <div className={styles.mobileActions}>
          <Button
            variant="default"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            size="small"
            className={styles.mobileActionButton}
          >
            Xem
          </Button>
          <Button
            variant="default"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
            className={styles.mobileActionButton}
          >
            Sửa
          </Button>
          <Button
            variant="default"
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
            size="small"
            danger
            className={styles.mobileActionButton}
          >
            Xóa
          </Button>
        </div>
      ),
    },
  ]

  // ✅ Columns configuration
  const columns = [
    {
      title: 'Tên danh mục',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: CategoryItem) => {
        const isParent = !record.parentId
        const actualLevel = isParent ? 0 : 1

        return (
          <div className={styles.categoryName}>
            <Space>
              {record.icon_url ? (
                <Image
                  src={record.icon_url}
                  alt="icon"
                  width={20}
                  height={20}
                  style={{ objectFit: 'contain' }}
                  fallback={logo}
                  preview={{
                    mask: 'Xem ảnh gốc',
                    maskClassName: 'custom-mask',
                  }}
                />
              ) : isParent ? (
                <FolderOutlined />
              ) : (
                <FileOutlined />
              )}
              <span style={{ marginLeft: actualLevel * 20 }}>{text}</span>
            </Space>
          </div>
        )
      },
    },
    {
      title: 'Slug',
      dataIndex: 'slug',
      key: 'slug',
      render: (slug: string) => <code>{slug}</code>,
    },
    {
      title: 'Cấp độ',
      dataIndex: 'level',
      key: 'level',
      render: (level: number | null, record: CategoryItem) => {
        const actualLevel = record.parentId ? 1 : 0
        return <Tag color={actualLevel === 0 ? 'blue' : 'green'}>Cấp {actualLevel + 1}</Tag>
      },
    },
    {
      title: 'Trạng thái',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>{isActive ? 'Hoạt động' : 'Tạm dừng'}</Tag>
      ),
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleDateString('vi-VN'),
    },
    {
      title: 'Hành động',
      key: 'action',
      width: 150,
      render: (_: any, record: CategoryItem) => (
        <Space>
          <Button
            variant="default"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            size="small"
          >
            Xem
          </Button>
          <Button
            variant="default"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
          >
            Sửa
          </Button>
          <Button
            variant="danger"
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
            size="small"
          >
            Xóa
          </Button>
        </Space>
      ),
    },
  ]

  // ✅ Thêm mới category với toast - Fixed
  const handleAddCategory = async () => {
    if (!addForm.name || !addForm.slug) {
      showWarning('Vui lòng nhập đầy đủ tên và slug')
      return
    }
    try {
      await createCategory({
        name: addForm.name,
        slug: addForm.slug,
        icon_url: addForm.icon_url,
        parentId: addForm.parentId || undefined,
      }).unwrap()

      showSuccess(`Tạo danh mục "${addForm.name}" thành công`)

      setIsAddModalOpen(false)
      setAddForm({ name: '', slug: '', icon_url: '', parentId: '' })
      refetch()
    } catch (err) {
      showError('Tạo danh mục thất bại. Vui lòng thử lại sau')
    }
  }

  // ✅ Cập nhật category với toast - Fixed
  const handleUpdateCategory = async () => {
    if (!selectedCategory || !editForm.name || !editForm.slug) {
      showWarning('Vui lòng nhập đầy đủ tên và slug')
      return
    }

    try {
      const parentId = editForm.parentId ? editForm.parentId : null

      await updateCategory({
        id: selectedCategory.id,
        body: {
          name: editForm.name,
          slug: editForm.slug,
          icon_url: editForm.icon_url,
          parentId: parentId,
        },
      }).unwrap()

      const statusMessage =
        parentId === null
          ? `Cập nhật "${editForm.name}" thành công - Đã trở thành danh mục cha`
          : `Cập nhật danh mục "${editForm.name}" thành công`

      showSuccess(statusMessage)

      setIsEditModalOpen(false)
      setSelectedCategory(null)
      setEditForm({ name: '', slug: '', icon_url: '', parentId: '' })
      refetch()
    } catch (err) {
      showError('Cập nhật danh mục thất bại. Vui lòng thử lại sau')
    }
  }

  // ✅ Xóa category với toast - Fixed
  const handleDelete = async (category: CategoryItem) => {
    try {
      await deleteCategory(category.id).unwrap()
      showSuccess(`Xóa danh mục "${category.name}" thành công`)
      refetch()
    } catch (err) {
      showError('Xóa danh mục thất bại. Vui lòng thử lại sau')
    }
  }

  // ✅ Xem chi tiết - mở modal detail
  const handleView = (category: CategoryItem) => {
    setSelectedCategory(category)
    setIsDetailModalOpen(true)
  }

  // ✅ Cập nhật - mở modal edit
  const handleEdit = (category: CategoryItem) => {
    setSelectedCategory(category)
    setEditForm({
      name: category.name,
      slug: category.slug,
      icon_url: category.icon_url || '',
      parentId: category.parentId || '',
    })
    setIsEditModalOpen(true)
  }

  // ✅ Component chi tiết danh mục
  const CategoryDetailModal = () => {
    const id = selectedCategory?.id
    const { data, isLoading, error } = useGetCategoryDetailQuery(id ? id : skipToken)
    const detail = data?.data

    return (
      <Modal
        title="Chi tiết danh mục"
        open={isDetailModalOpen}
        onCancel={() => {
          setIsDetailModalOpen(false)
          setSelectedCategory(null)
        }}
        footer={null}
        width={600}
      >
        {isLoading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin size="large" />
            <p>Đang tải chi tiết danh mục...</p>
          </div>
        ) : error ? (
          <div style={{ textAlign: 'center', padding: '20px', color: '#ff4d4f' }}>
            <p>❌ Không thể tải chi tiết danh mục</p>
            <p>
              <small>Chi tiết lỗi: {JSON.stringify(error)}</small>
            </p>
          </div>
        ) : !detail ? (
          <div style={{ textAlign: 'center', padding: '20px', color: '#ff4d4f' }}>
            ❌ Không tìm thấy dữ liệu danh mục
          </div>
        ) : (
          <div>
            <p>
              <strong>ID:</strong> {detail.id}
            </p>
            <p>
              <strong>Tên:</strong> {detail.name || 'N/A'}
            </p>
            <p>
              <strong>Slug:</strong> {detail.slug || 'N/A'}
            </p>
            <p>
              <strong>Cấp độ:</strong> {detail.parentId ? 'Danh mục con' : 'Danh mục cha'}
            </p>
            <p>
              <strong>Trạng thái:</strong> {detail.is_active ? 'Hoạt động' : 'Tạm dừng'}
            </p>
            <p>
              <strong>Ngày tạo:</strong>{' '}
              {detail.created_at ? new Date(detail.created_at).toLocaleString('vi-VN') : 'N/A'}
            </p>
            <p>
              <strong>Icon:</strong>
              {detail.icon_url ? (
                <Image
                  src={detail.icon_url}
                  alt="icon"
                  width={32}
                  height={32}
                  style={{ marginLeft: 8, objectFit: 'contain' }}
                  fallback={logo}
                  preview={{
                    mask: 'Xem ảnh gốc',
                    maskClassName: 'custom-mask',
                  }}
                />
              ) : (
                <span style={{ marginLeft: 8, color: '#999' }}>Không có icon</span>
              )}
            </p>
          </div>
        )}

        <ModalButtonGroup
          onCancel={() => {
            setIsDetailModalOpen(false)
            setSelectedCategory(null)
          }}
          cancelText="Đóng"
          showConfirm={false}
        />
      </Modal>
    )
  }

  // ✅ Filter tree categories
  const filterTree = (nodes: CategoryItem[]): CategoryItem[] => {
    return nodes
      .map((node) => {
        const match =
          node.name.toLowerCase().includes(searchText.toLowerCase()) ||
          node.slug.toLowerCase().includes(searchText.toLowerCase())
        if (node.children && node.children.length > 0) {
          const filteredChildren = filterTree(node.children)
          if (filteredChildren.length > 0 || match) {
            return { ...node, children: filteredChildren }
          }
        } else if (match) {
          return node
        }
        return match ? node : null
      })
      .filter(Boolean) as CategoryItem[]
  }

  const filteredCategories = useMemo(() => {
    if (!searchText) return categories
    return filterTree(categories)
  }, [categories, searchText])

  // ✅ Loading state
  if (isLoading) {
    return (
      <div className={styles.container}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '400px',
          }}
        >
          <Spin size="large" />
          <span style={{ marginLeft: '16px' }}>Đang tải danh mục...</span>
        </div>
      </div>
    )
  }

  return (
    <Card>
      <div className={styles.container}>
        {/* Header */}
        <div className={styles.header}>
          <div className={styles.headerTitle}>
            <h1>Danh sách danh mục</h1>
            <p>Xem danh sách các danh mục sản phẩm</p>
          </div>
          <div className={styles.headerActions}>
            <Button
              variant="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsAddModalOpen(true)}
            >
              Thêm mới
            </Button>
            <Button
              variant="default"
              onClick={() => {
                refetch()
                showInfo('Đang tải lại danh sách danh mục...')
              }}
            >
              Tải lại
            </Button>
          </div>
        </div>

        {/* Modal thêm mới danh mục */}
        <Modal
          title="Thêm mới danh mục"
          open={isAddModalOpen}
          onCancel={() => setIsAddModalOpen(false)}
          footer={null}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
            <Input
              placeholder="Tên danh mục"
              value={addForm.name}
              onChange={(e) => setAddForm((f) => ({ ...f, name: e.target.value }))}
              style={{ marginBottom: 8 }}
            />
            <Input
              placeholder="Slug"
              value={addForm.slug}
              onChange={(e) => setAddForm((f) => ({ ...f, slug: e.target.value }))}
              style={{ marginBottom: 8 }}
            />

            {/* Upload Icon */}
            <div style={{ marginBottom: 8 }}>
              <div style={{ marginBottom: 4, fontWeight: 500 }}>Icon danh mục:</div>
              {addForm.icon_url && (
                <div style={{ marginBottom: 8, display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Image
                    src={addForm.icon_url}
                    alt="icon"
                    width={40}
                    height={40}
                    style={{ objectFit: 'contain', border: '1px solid #d9d9d9', borderRadius: 4 }}
                    fallback={logo}
                    preview={{
                      mask: 'Xem ảnh gốc',
                      maskClassName: 'custom-mask',
                    }}
                  />
                  <Button
                    variant="danger"
                    size="small"
                    onClick={() => setAddForm((prev) => ({ ...prev, icon_url: '' }))}
                  >
                    Xóa
                  </Button>
                </div>
              )}
              <Upload.Dragger
                beforeUpload={(file) => handleIconUpload(file, 'add')}
                showUploadList={false}
                accept="image/*"
                disabled={isUploadingIcon}
              >
                <div style={{ padding: '20px', textAlign: 'center' }}>
                  {isUploadingIcon ? (
                    <Spin size="large" />
                  ) : (
                    <UploadOutlined style={{ fontSize: 24, color: '#999', marginBottom: 8 }} />
                  )}
                  <div>
                    {isUploadingIcon
                      ? 'Đang upload...'
                      : 'Kéo thả file ảnh vào đây hoặc click để chọn'}
                  </div>
                  <div style={{ fontSize: '12px', color: '#999', marginTop: 4 }}>
                    Hỗ trợ: JPG, PNG, WebP, SVG. Tối đa 2MB.
                  </div>
                </div>
              </Upload.Dragger>
            </div>

            <Select
              allowClear
              showSearch
              placeholder="Chọn danh mục cha (nếu có)"
              value={addForm.parentId || undefined}
              onChange={(value) => setAddForm((f) => ({ ...f, parentId: value || '' }))}
              style={{ width: '100%' }}
              filterOption={(input, option) =>
                (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
              }
              options={categories
                .filter((cat) => !cat.parentId)
                .map((cat) => ({ label: cat.name, value: cat.id }))}
            />
          </div>

          <ModalButtonGroup
            onCancel={() => setIsAddModalOpen(false)}
            onConfirm={handleAddCategory}
            cancelText="Hủy"
            confirmText="Tạo mới"
            confirmLoading={isCreating}
          />
        </Modal>

        {/* ✅ Modal cập nhật danh mục */}
        <Modal
          title="Cập nhật danh mục"
          open={isEditModalOpen}
          onCancel={() => {
            setIsEditModalOpen(false)
            setSelectedCategory(null)
            setEditForm({ name: '', slug: '', icon_url: '', parentId: '' })
          }}
          footer={null}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
            <Input
              placeholder="Tên danh mục"
              value={editForm.name}
              onChange={(e) => setEditForm((f) => ({ ...f, name: e.target.value }))}
              style={{ marginBottom: 8 }}
            />
            <Input
              placeholder="Slug"
              value={editForm.slug}
              onChange={(e) => setEditForm((f) => ({ ...f, slug: e.target.value }))}
              style={{ marginBottom: 8 }}
            />

            {/* Upload Icon */}
            <div style={{ marginBottom: 8 }}>
              <div style={{ marginBottom: 4, fontWeight: 500 }}>Icon danh mục:</div>
              {editForm.icon_url && (
                <div style={{ marginBottom: 8, display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Image
                    src={editForm.icon_url}
                    alt="icon"
                    width={40}
                    height={40}
                    style={{ objectFit: 'contain', border: '1px solid #d9d9d9', borderRadius: 4 }}
                    fallback={logo}
                    preview={{
                      mask: 'Xem ảnh gốc',
                      maskClassName: 'custom-mask',
                    }}
                  />
                  <Button
                    variant="danger"
                    size="small"
                    onClick={() => setEditForm((prev) => ({ ...prev, icon_url: '' }))}
                  >
                    Xóa
                  </Button>
                </div>
              )}
              <Upload.Dragger
                beforeUpload={(file) => handleIconUpload(file, 'edit')}
                showUploadList={false}
                accept="image/*"
                disabled={isUploadingIcon}
              >
                <div style={{ padding: '20px', textAlign: 'center' }}>
                  {isUploadingIcon ? (
                    <Spin size="large" />
                  ) : (
                    <UploadOutlined style={{ fontSize: 24, color: '#999', marginBottom: 8 }} />
                  )}
                  <div>
                    {isUploadingIcon
                      ? 'Đang upload...'
                      : 'Kéo thả file ảnh vào đây hoặc click để chọn'}
                  </div>
                  <div style={{ fontSize: '12px', color: '#999', marginTop: 4 }}>
                    Hỗ trợ: JPG, PNG, WebP, SVG. Tối đa 2MB.
                  </div>
                </div>
              </Upload.Dragger>
            </div>

            <Select
              allowClear
              showSearch
              placeholder="Chọn danh mục cha (để trống để trở thành danh mục cha)"
              value={editForm.parentId || undefined}
              onChange={(value) => setEditForm((f) => ({ ...f, parentId: value || '' }))}
              style={{ width: '100%' }}
              filterOption={(input, option) =>
                (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
              }
              options={[
                { label: '-- Trở thành danh mục cha --', value: '' },
                ...categories
                  .filter((cat) => !cat.parentId && cat.id !== selectedCategory?.id)
                  .map((cat) => ({ label: cat.name, value: cat.id })),
              ]}
            />
            <div style={{ fontSize: '12px', color: '#666', fontStyle: 'italic' }}>
              💡 Ghi chú: Để trống "Danh mục cha" nếu muốn biến thành danh mục cha độc lập
            </div>
          </div>

          <ModalButtonGroup
            onCancel={() => {
              setIsEditModalOpen(false)
              setSelectedCategory(null)
              setEditForm({ name: '', slug: '', icon_url: '', parentId: '' })
            }}
            onConfirm={handleUpdateCategory}
            cancelText="Hủy"
            confirmText="Cập nhật"
            confirmLoading={isUpdating}
          />
        </Modal>

        {/* Modal chi tiết */}
        <CategoryDetailModal />

        {/* Search */}
        <div style={{ marginBottom: 16 }}>
          <Input
            placeholder="Tìm kiếm theo tên hoặc slug..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ maxWidth: 300 }}
          />
        </div>

        {/* Table */}
        <Table
          columns={isMobile ? mobileColumns : columns}
          dataSource={filteredCategories}
          rowKey="id"
          pagination={{
            pageSize: isMobile ? 5 : 10,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            showTotal: (total, range) =>
              isMobile
                ? `${range[0]}-${range[1]}/${total}`
                : `${range[0]}-${range[1]} của ${total} danh mục`,
            size: isMobile ? 'small' : 'default',
          }}
          expandable={
            isMobile
              ? undefined
              : {
                  defaultExpandAllRows: true,
                }
          }
          scroll={isMobile ? { x: 'max-content' } : undefined}
          className={styles.categoryTable}
        />
      </div>
    </Card>
  )
}

export default CategoryManagement
