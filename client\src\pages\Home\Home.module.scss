@import '../../styles/variables.scss';

// Base styles
* {
  box-sizing: border-box;
}
.shareButton {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #f5f5f5;
    color: #2196F3;
  }

  &:active {
    transform: scale(0.95);
  }

  svg {
    width: 14px;
    height: 14px;
  }
}

// Mobile Layout
.homePage {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  @media (max-width: 768px) {
    font-size: 14px;
    padding-bottom: 90px; // More space for bottom nav
  }

  @media (max-width: 480px) {
    padding-bottom: 100px;
  }
}


// Sidebar Menu
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 200;
}

.sidebarMenu {
  position: fixed;
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  background: white;
  z-index: 201;
  overflow-y: auto;

  .menuHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #eee;
    background: #4CAF50;
    color: white;

    .menuLogo {
      height: 32px;
    }

    .closeButton {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      padding: 4px;
      color: white;
      border-radius: 4px;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .menuContent {
    padding: 16px 0;

    .menuSection {
      margin-bottom: 24px;

      .menuSectionTitle {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 12px 0;
        padding: 0 16px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 12px;
        color: #666;
      }

      .menuItem {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        color: #333;
        text-decoration: none;
        transition: all 0.2s;

        &:hover {
          background: #f5f5f5;
          color: #4CAF50;
          padding-left: 20px;
        }

        .menuIcon {
          margin-right: 12px;
          font-size: 16px;
        }
      }
    }
  }
}

// Quick Actions
.quickActions {
  padding: $spacing-md;
  background: white;
  margin: $spacing-sm $spacing-md;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;

    .sectionTitle {
      @include heading-3;
      color: #333;
      margin: 0;
    }

    .expandButton {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      background: none;
      border: none;
      color: #4CAF50;
      font-size: $font-size-base;
      font-weight: $font-weight-medium;
      cursor: pointer;
      padding: $spacing-sm $spacing-md;
      border-radius: 20px;
      transition: all 0.2s;

      &:hover {
        background: rgba(76, 175, 80, 0.1);
      }

      .expandArrow {
        font-size: 16px;
        transition: transform 0.3s;

        &.expanded {
          transform: rotate(90deg);
        }
      }
    }
  }

  // Horizontal slider view (default)
  .quickActionsSlider {
    display: flex;
    padding: 0 $spacing-md;
    gap: $spacing-md;
    overflow-x: auto;
    padding-bottom: $spacing-sm;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 2px;
    }

    .quickActionSliderItem {
      flex-shrink: 0;
      width: 80px;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: transform 0.2s;

      &:hover {
        transform: translateY(-2px);
      }

      .quickActionSliderImage {
        width: 64px;
        height: 64px;
        background: white;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.2s;

        &:hover {
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        img {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          object-fit: cover;
        }
      }

      .quickActionSliderName {
        font-size: $font-size-sm;
        color: #333;
        text-align: center;
        line-height: $line-height-tight;
        font-weight: $font-weight-medium;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  // Expanded grid view (responsive columns)
  .quickActionsGridExpanded {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: $spacing-md;
    opacity: 0;
    transition: opacity 0.3s ease-in;

    // Responsive grid columns
    @media (min-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }

    @media (max-width: 480px) {
      grid-template-columns: repeat(3, 1fr);
      gap: $spacing-sm;
    }

    &.visible {
      opacity: 1;
    }

    .quickActionCardExpanded {
      background: white;
      border-radius: 12px;
      padding: 16px 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.2s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
      }

      .quickActionImageExpanded {
        width: 48px;
        height: 48px;
        margin-bottom: 8px;
        background: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
          height: 100%;
          border-radius: 8px;
          object-fit: cover;
        }
      }

      .quickActionNameExpanded {
        font-size: $font-size-sm;
        color: #333;
        text-align: center;
        line-height: $line-height-tight;
        font-weight: $font-weight-medium;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }
}

// Categories - UPDATED
.categories {
  padding: $spacing-md;
  background: white;
  margin: $spacing-sm $spacing-md;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .sectionTitle {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }

    .expandButton {
      display: flex;
      align-items: center;
      gap: 4px;
      background: none;
      border: none;
      color: #4CAF50;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 20px;
      transition: all 0.2s;

      &:hover {
        background: rgba(76, 175, 80, 0.1);
      }

      .expandArrow {
        font-size: 16px;
        transition: transform 0.3s;

        &.expanded {
          transform: rotate(90deg);
        }
      }
    }
  }

  .categoriesSlider {
    display: flex;
    padding: 0 $spacing-md;
    gap: $spacing-md;
    overflow-x: auto;
    padding-bottom: $spacing-sm;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 2px;
    }

    .categorySliderItem {
      flex-shrink: 0;
      width: 80px;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: transform 0.2s;

      &:hover {
        transform: translateY(-2px);
      }

      .categorySliderImage {
        width: 64px;
        height: 64px;
        background: white;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.2s;

        &:hover {
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        img {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          object-fit: cover;
        }
      }

      .categorySliderName {
        font-size: 12px;
        color: #333;
        text-align: center;
        line-height: 1.2;
        font-weight: 500;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  .categoriesGridExpanded {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: $spacing-md;
    opacity: 0;
    transition: opacity 0.3s ease-in;

    // Responsive grid columns
    @media (min-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }

    @media (max-width: 480px) {
      grid-template-columns: repeat(3, 1fr);
      gap: $spacing-sm;
    }

    &.visible {
      opacity: 1;
    }

    .categoryCardExpanded {
      background: white;
      border-radius: 12px;
      padding: 16px 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.2s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
      }

      .categoryImageExpanded {
        width: 48px;
        height: 48px;
        margin-bottom: 8px;
        background: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
          height: 100%;
          border-radius: 8px;
          object-fit: cover;
        }
      }

      .categoryNameExpanded {
        font-size: 12px;
        color: #333;
        text-align: center;
        line-height: 1.2;
        font-weight: 500;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }
}

// Partners
.partners {
  padding: $spacing-md;
  background: white;
  margin: $spacing-sm $spacing-md;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .sectionHeader {
    margin-bottom: $spacing-md;

    .sectionTitle {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .partnersSlider {
    display: flex;
    gap: $spacing-md;
    overflow-x: auto;
    padding-bottom: $spacing-sm;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 2px;
    }

    .partnerItem {
      flex-shrink: 0;
      width: 80px;
      height: 60px;
      background: #f9f9f9;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      img {
        max-width: 60px;
        max-height: 40px;
        opacity: 0.7;
        transition: opacity 0.2s;
      }

      &:hover img {
        opacity: 1;
      }
    }
  }
}

// Featured Products
.featuredProducts {
  padding: $spacing-md;

  .sectionHeader {
    margin-bottom: $spacing-md;

    .sectionTitle {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 4px 0;
    }

    .sectionSubtitle {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }

  .productsGrid {
    display: flex;
    flex-wrap: nowrap; // Không cho xuống dòng, giữ hàng ngang
    overflow-x: auto; // Cho phép lướt ngang
    gap: $spacing-md; // Khoảng cách giữa các sản phẩm
    padding-bottom: $spacing-sm; // Để scrollbar không che nội dung

    &::-webkit-scrollbar {
      height: 4px; // Độ cao của thanh scrollbar
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1; // Màu nền scrollbar
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ccc; // Màu thanh scrollbar
      border-radius: 2px;
    }

    .productCard {
      flex-shrink: 0; // Không cho card co lại, giữ kích thước cố định
      width: 160px; // Chiều rộng cố định cho mỗi card (tùy chỉnh theo ý)
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s;

      &:hover {
        transform: translateY(-2px);
      }

      .productImage {
        position: relative;
        aspect-ratio: 1;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .discountBadge {
          position: absolute;
          top: 8px;
          left: 8px;
          background: #FF5722;
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 10px;
          font-weight: 600;
        }

        .ocopBadge {
          position: absolute;
          top: 8px;
          right: 8px;
          background: #4CAF50;
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 10px;
          font-weight: 600;
        }

        .favoriteButton {
          position: absolute;
          bottom: 8px;
          right: 8px;
          background: rgba(255, 255, 255, 0.9);
          border: none;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s;

          &.favorited {
            background: #FF5722;
            color: white;
          }

          &:hover {
            transform: scale(1.1);
          }
        }
      }

      .productInfo {
        padding: 12px;

        .productName {
          font-size: $font-size-base;
          font-weight: $font-weight-medium;
          color: #333;
          margin: 0 0 4px 0;
          line-height: $line-height-normal;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .productSeller {
          @include caption;
          color: #666;
          margin: 0 0 8px 0;
          opacity: 0.8;
        }

        .productRating {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .ratingLeft {
            display: flex;
            align-items: center;
            gap: 4px;
          }

          .starIcon {
            color: #FFC107;
            font-size: 12px;
          }

          span {
            font-size: 12px;
            color: #666;
          }

          .shareButton {
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;

            &:hover {
              background-color: #f5f5f5;
              color: #2196F3;
            }

            &:active {
              transform: scale(0.95);
            }

            svg {
              width: 14px;
              height: 14px;
            }
          }
        }

        .productPrice {
          margin-bottom: 12px;

          .currentPrice {
            font-size: $font-size-md;
            font-weight: $font-weight-semibold;
            color: #FF5722;
          }

          .originalPrice {
            @include caption;
            color: #999;
            text-decoration: line-through;
            margin-left: 8px;
          }
        }

        .productActions {
          display: flex;
          gap: 8px;

          .buyNowButton {
            flex: 1;
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: $font-size-sm;
            font-weight: $font-weight-medium;
            cursor: pointer;
            transition: background 0.2s;

            &:hover {
              background: #45a049;
            }
          }

          .addToCartButton {
            background: #f5f5f5;
            border: none;
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;

            &:hover {
              background: #e0e0e0;
            }
          }
        }
      }
    }
  }

}

// News Mobile
.newsMobile {
  padding: $spacing-md;

  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;

    .sectionTitle {
      @include heading-3;
      color: #333;
      margin: 0;
    }

    .viewAllLink {
      color: #4CAF50;
      text-decoration: none;
      font-size: $font-size-base;
      font-weight: $font-weight-medium;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .newsSlider {
    display: flex;
    gap: $spacing-md;
    overflow-x: auto;
    padding-bottom: $spacing-sm;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 2px;
    }

    .newsCardMobile {
      flex-shrink: 0;
      width: 280px;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .newsImageMobile {
        position: relative;
        height: 160px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .newsCategoryMobile {
          position: absolute;
          top: 8px;
          left: 8px;
          background: rgba(76, 175, 80, 0.9);
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 10px;
          font-weight: 600;
        }
      }

      .newsContentMobile {
        padding: 12px;

        h3 {
          font-size: $font-size-base;
          font-weight: $font-weight-semibold;
          color: #333;
          margin: 0 0 8px 0;
          line-height: $line-height-normal;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        p {
          @include caption;
          color: #666;
          margin: 0 0 8px 0;
          line-height: $line-height-normal;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .newsDateMobile {
          @include small-text;
          color: #999;
        }
      }
    }
  }
}

// Bottom Navigation
.bottomNav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-around;
  padding: $spacing-sm 0;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

  .bottomNavItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: $spacing-sm;
    transition: color 0.2s;
    min-width: 60px;
    min-height: 44px; // Touch target
    justify-content: center;

    &:hover {
      color: #4CAF50;
    }

    &.active {
      color: #4CAF50;
    }

    svg {
      font-size: 22px; // Larger for better touch
      margin-bottom: $spacing-xs;
    }

    .evaIcon {
      width: 22px;
      height: 22px;
      margin-bottom: $spacing-xs;
      border-radius: 50%;
    }

    span {
      font-size: $font-size-xs; // Consistent with typography scale
      color: #666;
      font-weight: $font-weight-medium;
    }

    // Mobile responsive
    @media (max-width: 480px) {
      min-width: 50px;
      padding: $spacing-sm $spacing-xs;

      svg {
        font-size: 20px;
      }

      .evaIcon {
        width: 20px;
        height: 20px;
      }

      span {
        font-size: $font-size-xs;
      }
    }
  }
}

// Desktop Layout
.homePageDesktop {
  min-height: 100vh;
  background: #f8f9fa;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}


// Hero Banner Desktop
.heroBannerDesktop {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  color: white;
  padding: 60px 0;
  position: relative;
  overflow: hidden;

  .heroContainer {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .heroSlider {
    position: relative;

    .heroSlide {
      .heroContent {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 60px;
        align-items: center;

        .heroText {
          h1 {
            font-size: 48px;
            font-weight: 700;
            margin: 0 0 20px 0;
            line-height: 1.2;
          }

          p {
            font-size: 18px;
            margin: 0 0 30px 0;
            opacity: 0.9;
            line-height: 1.6;
          }

          .ctaButton {
            background: white;
            color: #4CAF50;
            border: none;
            padding: 16px 32px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }
          }
        }

        .heroImage {
          text-align: center;

          img {
            max-width: 100%;
            height: auto;
            max-height: 400px;
            object-fit: contain;
          }
        }
      }
    }

    .sliderControls {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;
      margin-top: 40px;

      .sliderButton {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }

      .sliderDots {
        display: flex;
        gap: 8px;

        .sliderDot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.5);
          border: none;
          cursor: pointer;
          transition: background 0.2s;

          &.active {
            background: white;
          }
        }
      }
    }
  }
}

// Quick Actions Desktop
.quickActionsDesktop {
  padding: 60px 0;

  .quickActionsGrid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;

    .quickActionCard {
      background: white;
      padding: 30px;
      border-radius: 16px;
      text-align: center;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      cursor: pointer;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
      }

      .quickActionIcon {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;

        img {
          width: 40px;
          height: 40px;
        }
      }

      h3 {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: #333;
      }

      p {
        font-size: 14px;
        color: #666;
        margin: 0;
      }
    }
  }
}

// Categories Desktop
.categoriesDesktop {
  padding: 60px 0;
  background: white;

  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;

    h2 {
      font-size: 32px;
      font-weight: 700;
      color: #333;
      margin: 0;
    }

    .categoryControls {
      display: flex;
      gap: 12px;

      .scrollButton {
        width: 48px;
        height: 48px;
        border: 2px solid #e0e0e0;
        background: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          border-color: #4CAF50;
          color: #4CAF50;
        }
      }
    }
  }

  .categoriesGridDesktop {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 24px;

    .categoryCardDesktop {
      background: #f9f9f9;
      padding: 24px;
      border-radius: 16px;
      text-align: center;
      transition: all 0.3s;
      cursor: pointer;

      &:hover {
        background: white;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .categoryImageDesktop {
        width: 64px;
        height: 64px;
        margin: 0 auto 16px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 12px;
        }
      }

      h3 {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 12px 0;
        color: #333;
      }

      .subcategoriesDesktop {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .subcategoryTag {
          font-size: 12px;
          color: #666;
          background: #f0f0f0;
          padding: 2px 8px;
          border-radius: 10px;
          text-align: center;
        }
      }
    }
  }
}

// Products Desktop
.featuredProductsDesktop {
  padding: 60px 0;

  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;

    h2 {
      font-size: 32px;
      font-weight: 700;
      color: #333;
      margin: 0;
    }

    .productTabs {
      display: flex;
      gap: 8px;

      .tabButton {
        padding: 12px 24px;
        border: 2px solid #e0e0e0;
        background: white;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.2s;
        font-weight: 500;

        &.active {
          border-color: #4CAF50;
          background: #4CAF50;
          color: white;
        }

        &:hover:not(.active) {
          border-color: #4CAF50;
          color: #4CAF50;
        }
      }
    }
  }

  .productsGridDesktop {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;

    .productCardDesktop {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      transition: all 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);

        .productOverlay {
          opacity: 1;
        }
      }

      .productImageDesktop {
        position: relative;
        aspect-ratio: 1;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .discountBadge {
          position: absolute;
          top: 12px;
          left: 12px;
          background: #FF5722;
          color: white;
          padding: 6px 12px;
          border-radius: 15px;
          font-size: 12px;
          font-weight: 600;
        }

        .ocopBadge {
          position: absolute;
          top: 12px;
          right: 12px;
          background: #4CAF50;
          color: white;
          padding: 6px 12px;
          border-radius: 15px;
          font-size: 12px;
          font-weight: 600;
        }

        .productOverlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          opacity: 0;
          transition: opacity 0.3s;

          .quickViewButton {
            background: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: transform 0.2s;

            &:hover {
              transform: scale(1.05);
            }
          }

          .favoriteButtonDesktop {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            width: 44px;
            height: 44px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;

            &.favorited {
              background: #FF5722;
              color: white;
            }

            &:hover {
              transform: scale(1.1);
            }
          }
        }
      }

      .productInfoDesktop {
        padding: 20px;

        h3 {
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 8px 0;
          color: #333;
          line-height: 1.3;
        }

        .productSeller {
          font-size: 12px;
          color: #666;
          margin: 0 0 12px 0;
          opacity: 0.8;
        }

        .productRating {
          display: flex;
          align-items: center;
          justify-content: space-between; // dãy sao bên trái, share button bên phải
          gap: 8px;
          margin-bottom: 12px;

          .stars {
            display: flex;
            gap: 2px;

            .starFilled {
              color: #FFC107;
              font-size: 14px;
            }

            .starEmpty {
              color: #e0e0e0;
              font-size: 14px;
            }
          }

          span {
            font-size: 14px;
            color: #666;
          }
        }

        .productPrice {
          margin-bottom: 16px;

          .currentPrice {
            font-size: 18px;
            font-weight: 700;
            color: #FF5722;
          }

          .originalPrice {
            font-size: 14px;
            color: #999;
            text-decoration: line-through;
            margin-left: 8px;
          }
        }

        .productActionsDesktop {
          display: flex;
          gap: 8px;

          .addToCartButtonDesktop {
            flex: 1;
            background: #f5f5f5;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.2s;

            &:hover {
              background: #e0e0e0;
            }
          }

          .buyNowButtonDesktop {
            flex: 1;
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.2s;

            &:hover {
              background: #45a049;
            }
          }
        }
      }
    }
  }
}

// News Desktop
.newsDesktop {
  padding: 60px 0;
  background: white;

  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;

    h2 {
      font-size: 32px;
      font-weight: 700;
      color: #333;
      margin: 0;
    }

    .viewAllLink {
      color: #4CAF50;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .newsGrid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;

    .newsCard {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s;
      cursor: pointer;

      &:hover {
        transform: translateY(-5px);
      }

      .newsImage {
        position: relative;
        height: 200px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .newsCategory {
          position: absolute;
          top: 12px;
          left: 12px;
          background: rgba(76, 175, 80, 0.9);
          color: white;
          padding: 6px 12px;
          border-radius: 15px;
          font-size: 12px;
          font-weight: 600;
        }
      }

      .newsContent {
        padding: 20px;

        h3 {
          font-size: 18px;
          font-weight: 600;
          margin: 0 0 12px 0;
          color: #333;
          line-height: 1.3;
        }

        p {
          font-size: 14px;
          color: #666;
          margin: 0 0 12px 0;
          line-height: 1.5;
        }

        .newsDate {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
}

// Partners Desktop
.partnersDesktop {
  padding: 60px 0;
  background: white;

  h2 {
    text-align: center;
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 40px 0;
    color: #333;
  }

  .partnersGridDesktop {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 24px;

    .partnerCardDesktop {
      background: #f9f9f9;
      padding: 30px;
      border-radius: 16px;
      text-align: center;
      transition: all 0.3s;
      cursor: pointer;

      &:hover {
        background: white;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      img {
        height: 60px;
        margin-bottom: 16px;
        opacity: 0.7;
        transition: opacity 0.3s;
      }

      h4 {
        font-size: 14px;
        font-weight: 600;
        margin: 0;
        color: #333;
      }

      &:hover img {
        opacity: 1;
      }
    }
  }
}

// Newsletter Desktop
.newsletterDesktop {
  padding: 60px 0;
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  color: white;

  .newsletterContent {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;

    h2 {
      font-size: 32px;
      font-weight: 700;
      margin: 0 0 16px 0;
    }

    p {
      font-size: 16px;
      margin: 0 0 32px 0;
      opacity: 0.9;
    }

    .newsletterForm {
      display: flex;
      gap: 12px;
      max-width: 400px;
      margin: 0 auto;

      input {
        flex: 1;
        padding: 16px;
        border: none;
        border-radius: 25px;
        font-size: 14px;
        outline: none;

        &::placeholder {
          color: #999;
        }
      }

      button {
        background: white;
        color: #4CAF50;
        border: none;
        padding: 16px 32px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: transform 0.2s;

        &:hover {
          transform: translateY(-2px);
        }
      }
    }
  }
}

// Footer Desktop
.footerDesktop {
  background: #333;
  color: white;
  padding: 60px 0 20px;

  .footerContent {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;

    .footerSection {
      h4 {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 20px 0;
        color: #4CAF50;
      }

      p {
        color: #ccc;
        line-height: 1.6;
        margin: 0 0 20px 0;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          margin-bottom: 12px;

          a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.2s;

            &:hover {
              color: #4CAF50;
            }
          }
        }
      }

      .socialLinks {
        display: flex;
        gap: 16px;

        .socialLink {
          color: #ccc;
          text-decoration: none;
          padding: 8px 16px;
          border: 1px solid #555;
          border-radius: 20px;
          transition: all 0.2s;
          font-size: 14px;

          &:hover {
            color: #4CAF50;
            border-color: #4CAF50;
          }
        }
      }

      .footerLogo {
        height: 48px;
        margin-bottom: 16px;
      }
    }
  }

  .footerBottom {
    border-top: 1px solid #555;
    padding-top: 20px;
    text-align: center;

    p {
      color: #999;
      margin: 0;
      font-size: 14px;
    }
  }
}

// Responsive adjustments
@media (max-width: 1200px) {
  .homePageDesktop {
    .container {
      max-width: 100%;
      padding: 0 16px;
    }

    .categoriesGridDesktop {
      grid-template-columns: repeat(4, 1fr);
    }

    .partnersGridDesktop {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

@media (max-width: 768px) {
  .homePageDesktop {
    .productsGridDesktop {
      grid-template-columns: repeat(2, 1fr);
    }

    .newsGrid {
      grid-template-columns: 1fr;
    }

    .footerContent {
      grid-template-columns: 1fr;
      gap: 24px;
    }
  }
}

// Animation keyframes
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

// Utility classes
.fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

// Loading states
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}