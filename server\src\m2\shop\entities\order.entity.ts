import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { User } from '../../../m1/user/entities/user.entity';
import { Status } from './status.entity';
import { OrderItem } from './order_item.entity';

import { Payment } from './payment.entity';

export enum OrderSource {
  WEB = 'web',
  APP = 'app',
  ZALO = 'zalo',
  GUEST = 'guest',
}

@Entity('orders')
export class Order {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid', { nullable: true })
  user_id?: string;
  @ManyToOne(() => User, (user) => user.orders, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @Column({ type: 'varchar', length: 20, nullable: true })
  guest_phone?: string;

  @Column({ type: 'varchar', length: 150, nullable: true })
  guest_email?: string;

  @Column({ type: 'uuid' })
  status_id: string;
  @ManyToOne(() => Status, (status) => status.orders, { nullable: false, onDelete: 'RESTRICT' })
  @JoinColumn({ name: 'status_id' })
  status: Status;

  @Column({ type: 'enum', enum: OrderSource })
  source: OrderSource;

  @Column({ type: 'decimal', precision: 12, scale: 2 })
  total_price: string;

  @Column({ type: 'varchar', length: 100 })
  shipping_name: string;

  @Column({ type: 'varchar', length: 20 })
  shipping_phone: string;

  @Column({ type: 'text' })
  shipping_address: string;

  @Column({ type: 'text', nullable: true })
  shipping_latlng?: string;

  @Column({ type: 'text', nullable: true })
  note?: string;

  @Column({ type: 'uuid', nullable: true })
  payment_id?: string;

  @ManyToOne(() => Payment, (payment) => payment.orders, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'payment_id' })
  payment?: Payment;

  @Column({ type: 'varchar', length: 50, nullable: true })
  ref_code?: string;

  @Column({ type: 'boolean' })
  is_guest: boolean;

  @Column({ type: 'boolean', default: false })
  suspected: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;

  @OneToMany(() => OrderItem, (item) => item.order, { cascade: true })
  orderItems: OrderItem[];
}
