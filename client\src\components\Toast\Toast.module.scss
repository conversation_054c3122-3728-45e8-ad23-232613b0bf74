// .toast {
//       position: relative;
//       font-family: Arial, sans-serif;
//       font-size: 16px;
//       line-height: 1.5;
//       border-radius: 12px;
//       box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
//       display: flex;
//       align-items: center;
//       justify-content: space-between;
//       overflow: visible;
//       margin-top: 24px;
//     }
    
//     .toast button {
//       margin-top: 8px;
//     }
    
//     .success {
//       background-color: #28a745;
//       color: white;
//     }
    
//     .warning {
//       background-color: #ffc107;
//       color: white;
//     }
    
//     .error {
//       background-color: #dc3545;
//       color: white;
//     }
    
//     .info {
//       background-color: #17a2b8;
//       color: white;
//     }
    
//     .toastIcon {
//       position: absolute;
//       bottom: -17px;
//       left: 12px;
//       display: flex;
//       align-items: center;
//       justify-content: center;
//       z-index: 1;
//       width: 42px;
//       height: 42px;
//       color: white !important;
//       background-size: inherit;
//       overflow: auto;
//     }
    
//     .success .toastIcon {
//       background-color: #218838;
//     }
    
//     .warning .toastIcon {
//       background-color: #e0a800;
//     }
    
//     .warning .toastIcon svg {
//       margin-bottom: 8px;
//     }
    
//     .info .toastIcon svg {
//       margin-bottom: 8px;
//     }
    
//     .error .toastIcon {
//       background-color: #c82333;
//     }
    
//     .error .toastIcon svg {
//       transform: translateY(2px);
//       width: 20px;
//       height: 20px;
//     }
    
//     .info .toastIcon {
//       background-color: #138496;
//     }
    
//     .progressBar {
//       background: white !important;
//       height: 4px;
//       border-radius: 4px;
//     }
    
//     .toastMessage div {
//       margin-left: 62px !important;
//     }
    
//     .toastDescription {
//       font-size: 12px;
//       margin-left: 62px;
//     }