import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, <PERSON>in<PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { Role } from './role.entity';

@Entity('role_groups')
export class RoleGroup {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  code: string; // e.g., R04.02

  @Column()
  name: string; // e.g., "Chuyên gia nông nghiệp"

  @Column({ nullable: true })
  description: string;

  @ManyToOne(() => Role, (role) => role.roleGroups, { nullable: false, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'roleId' })
  role: Role;

  @Column()
  roleId: string;

  @Column('simple-array', { nullable: true })
  moduleScope: string[]; // e.g., ["M3", "M7"]
}
