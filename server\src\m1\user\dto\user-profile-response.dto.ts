// src/user/dto/update-user-profile.dto.ts
import { ApiProperty } from '@nestjs/swagger';

export class UserProfileResponseDto {
  id?: string;

  uIdCode?: string;

  referenceId?: string;

  fullName?: string;

  email?: string;

  phoneNumber?: string;

  citizenId?: string;

  gender: string;

  dateOfBirth?: Date;

  avatarUrl?: string;

  avatarId?: string;

  address?: string;

  role?: string;

  isVerifiedPhone?: boolean;

  isVerifiedMail?: boolean;
  isEnable2FA?: boolean;
}
