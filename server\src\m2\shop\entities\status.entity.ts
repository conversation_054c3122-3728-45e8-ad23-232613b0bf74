import {
  <PERSON><PERSON><PERSON>,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  PrimaryGeneratedColumn,
  Index,
} from 'typeorm';
import { Order } from './order.entity';
import { ProductImage } from './product_image.entity';

@Entity('statuses')
@Index(['type', 'label'], { unique: true })
export class Status {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 50 })
  type: string;

  @Column({ type: 'varchar', length: 100 })
  label: string;

  @Column({ type: 'varchar', length: 50 })
  code: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  color?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  icon?: string;

  @Column({ type: 'boolean', default: false })
  is_final?: boolean;

  @Column({ type: 'int', nullable: true })
  order_index?: number;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;

  @OneToMany(() => Order, (order) => order.status)
  orders: Order[];

  @OneToMany(() => ProductImage, (img) => img.status)
  product_images: ProductImage[];
}
