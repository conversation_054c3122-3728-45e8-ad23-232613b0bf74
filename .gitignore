# ==============================
# 🧼 General Build Artifacts
node_modules/
dist/
build/
dist-ssr/
.temp/
.tmp/
coverage/
.nyc_output/

# ==============================
# 🔍 Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*
lerna-debug.log*

# ==============================
# 🧪 Runtime data
pids
*.pid
*.seed
*.pid.lock
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# ==============================
# 🔐 Environment Files
.env
.env.development
.env.production
.env.local
.env.*.local

# ==============================
# 🧠 IDE/Editor
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# Python virtual environments
.venv/
venv/
object_detection_service/yolov5/