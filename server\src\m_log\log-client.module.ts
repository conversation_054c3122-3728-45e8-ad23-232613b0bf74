// src/m_log/log-client.module.ts
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';

const LogClientProvider = ClientsModule.registerAsync([
  {
    name: 'LOG_SERVICE',
    imports: [ConfigModule],
    inject: [ConfigService],
    useFactory: (config: ConfigService) => ({
      transport: Transport.RMQ,
      options: {
        urls: [config.get<string>('RABBITMQ_URL')!],
        queue: config.get<string>('RABBITMQ_QUEUE'),
        queueOptions: { durable: true },
      },
    }),
  },
  {
    name: 'IMAGE_VECTOR_SERVICE', // thêm client mới
    imports: [ConfigModule],
    inject: [ConfigService],
    useFactory: (config: ConfigService) => ({
      transport: Transport.RMQ,
      options: {
        urls: [config.get<string>('RABBITMQ_URL')!],
        queue: config.get<string>('IMAGE_VECTOR_QUEUE'),
        queueOptions: { durable: true },
      },
    }),
  },
]);

@Module({
  imports: [LogClientProvider],
  exports: [LogClientProvider],
})
export class LogClientModule {}
