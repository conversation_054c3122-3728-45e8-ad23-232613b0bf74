// Script: Export thông tin sản phẩm ra file JSON để sinh embedding
// Chạy: npx ts-node src/m2/ai_trainning/pipeline.product-to-vector.ts
import { AppDataSource } from '../../data-source';
import { Product } from '../shop/entities/products.entity';
import * as fs from 'fs';

async function main() {
  await AppDataSource.initialize();
  const repo = AppDataSource.getRepository(Product);
  const products = await repo.find();

  // Tuỳ chỉnh trường mô tả sản phẩm cho embedding
  const data = products.map((p) => ({
    product_id: p.id,
    name: p.name,
    description: p.description,
    // Thêm trường khác nếu cần
  }));

  fs.writeFileSync('product_info_for_vector.json', JSON.stringify(data, null, 2));
  console.log('Exported product_info_for_vector.json');
}

main().then(() => process.exit(0));
