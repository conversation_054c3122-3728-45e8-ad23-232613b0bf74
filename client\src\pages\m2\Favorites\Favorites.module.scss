.favoritesPage {
    min-height: 100vh;
    background: #f8f9fa;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    padding: 16px 0;
  }
  
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    
    @media (max-width: 768px) {
      padding: 0 16px;
    }
    
    @media (max-width: 576px) {
      padding: 0 12px;
    }
  }
  
  // Loading State
  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #666;
  
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #4CAF50;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }
  
    p {
      font-size: 16px;
      margin: 0;
    }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  // Error State
  .error {
    text-align: center;
    padding: 60px 20px;
    color: #666;
  
    h3 {
      color: #FF5722;
      margin-bottom: 16px;
      font-size: 24px;
    }
  
    p {
      margin-bottom: 16px;
      font-size: 16px;
    }
  
    button {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s;
  
      &:hover {
        background: #45a049;
        transform: translateY(-2px);
      }
    }
  }
  
  // Page Header
  .pageHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
  
    .pageTitle {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 28px;
      font-weight: 700;
      color: #333;
      margin: 0;
  
    }
  
    .headerActions {
      display: flex;
      align-items: center;
      gap: 16px;
  
      .searchBar {
        position: relative;
        width: 300px;
  
        .searchIcon {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: #666;
          font-size: 16px;
        }
  
        input {
          width: 100%;
          padding: 12px 12px 12px 40px;
          border: 2px solid #e0e0e0;
          border-radius: 8px;
          font-size: 14px;
          outline: none;
          transition: border-color 0.2s;
  
          &:focus {
            border-color: #4CAF50;
          }
  
          &::placeholder {
            color: #999;
          }
        }
      }
  
      .filterButton {
        display: flex;
        align-items: center;
        gap: 8px;
        background: white;
        border: 2px solid #e0e0e0;
        padding: 12px 16px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 14px;
        color: #666;
  
        &:hover {
          border-color: #4CAF50;
          color: #4CAF50;
        }
      }
  
      .viewModeToggle {
        display: flex;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        overflow: hidden;
  
        .viewModeButton {
          background: white;
          border: none;
          padding: 12px;
          cursor: pointer;
          transition: all 0.2s;
          color: #666;
          font-size: 16px;
  
          &:hover {
            background: #f5f5f5;
          }
  
          &.active {
            background: #4CAF50;
            color: white;
          }
        }
      }
    }
  }
  
  // Filters Panel
  .filtersPanel {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
    .filterRow {
      display: flex;
      gap: 24px;
      align-items: center;
      flex-wrap: wrap;
  
      .filterGroup {
        display: flex;
        flex-direction: column;
        gap: 8px;
  
        label {
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }
  
        select {
          padding: 8px 12px;
          border: 2px solid #e0e0e0;
          border-radius: 8px;
          font-size: 14px;
          outline: none;
          transition: border-color 0.2s;
  
          &:focus {
            border-color: #4CAF50;
          }
        }
  
        input[type="checkbox"] {
          margin-right: 8px;
          accent-color: #4CAF50;
        }
      }
    }
  }
  
  // Bulk Actions
  .bulkActions {
    display: flex;
    align-items: center;
    gap: 16px;
    background: #fff3e0;
    border: 1px solid #ffb74d;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 24px;
  
    span {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  
    button {
      background: none;
      border: none;
      color: #4CAF50;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background 0.2s;
  
      &:hover {
        background: rgba(76, 175, 80, 0.1);
      }
    }
  
    .removeSelectedButton {
      color: #FF5722;
  
      &:hover {
        background: rgba(255, 87, 34, 0.1);
      }
    }
  }
  
  // Products Container
  .productsContainer {
    .productsGrid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 16px;
  
      &.listLayout {
        grid-template-columns: 1fr;
      }
      
      // Responsive design
      @media (max-width: 1400px) {
        grid-template-columns: repeat(4, 1fr);
      }
      
      @media (max-width: 1200px) {
        grid-template-columns: repeat(3, 1fr);
      }
      
      @media (max-width: 992px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
      }
      
      @media (max-width: 576px) {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }
  
    .noResults {
      text-align: center;
      padding: 60px 20px;
      color: #666;
  
      p {
        font-size: 16px;
        margin-bottom: 16px;
      }
  
      button {
        background: #4CAF50;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s;
  
        &:hover {
          background: #45a049;
          transform: translateY(-2px);
        }
      }
    }
  }
  
  // Product Card
  .productCard {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
  
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      
      .productImage img {
        transform: scale(1.05);
      }
    }
  
    &.listView {
      display: flex;
      flex-direction: row;
      align-items: center;
  
      .productImage {
        width: 200px;
        height: 150px;
        flex-shrink: 0;
      }
  
      .productInfo {
        flex: 1;
        padding: 16px;
      }
    }
  
    .productImage {
      position: relative;
      width: 100%;
      height: 200px;
      overflow: hidden;
  
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
      }
  
      .checkboxTopLeft {
        position: absolute;
        top: 8px;
        left: 8px;
        z-index: 2;
  
        .selectCheckbox {
          width: 18px;
          height: 18px;
          accent-color: #4CAF50;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 3px;
        }
      }
  
      .discountBadge {
        position: absolute;
        top: 8px;
        left: 8px;
        background: #FF5722;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        z-index: 1;
        animation: pulse 2s infinite;
      }
  
      .ocopBadge {
        position: absolute;
        top: 36px;
        left: 8px;
        background: #4CAF50;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        z-index: 1;
      }
      
      @keyframes pulse {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
        100% {
          transform: scale(1);
        }
      }
  
      .favoriteButton {
        position: absolute;
        top: 12px;
        right: 12px;
        background: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #ff4444;
        z-index: 2;
        
        &:hover {
          background: white;
          transform: scale(1.1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        svg {
          font-size: 16px;
          transition: all 0.3s ease;
        }
      }
      
      .cardActions {
        position: absolute;
        bottom: 8px;
        right: 8px;
        display: flex;
        gap: 8px;
  
        .removeButton {
          background: rgba(255, 87, 34, 0.9);
          color: white;
          border: none;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s;
          font-size: 12px;
  
          &:hover {
            background: #FF5722;
            transform: scale(1.1);
          }
        }
      }
    }
  
    .productInfo {
      padding: 12px;
  
      .productName {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin: 0 0 6px 0;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
  
      .productSeller {
        font-size: 12px;
        color: #666;
        margin: 0 0 8px 0;
      }
  
      .productRating {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
  
        .stars {
          display: flex;
          gap: 2px;
  
          .star {
            color: #ddd;
            font-size: 14px;
  
            &.filled {
              color: #FFC107;
            }
          }
        }
  
        .ratingText {
          font-size: 12px;
          color: #666;
        }
      }
  
      .productPrice {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
  
        .currentPrice {
          font-size: 16px;
          font-weight: 700;
          color: #FF5722;
        }
  
        .originalPrice {
          font-size: 14px;
          color: #999;
          text-decoration: line-through;
        }
  
        .unit {
          font-size: 14px;
          color: #666;
        }
      }
  
      .productStock {
        margin-bottom: 12px;
  
        .inStock {
          color: #4CAF50;
          font-size: 12px;
          font-weight: 500;
        }
  
        .outOfStock {
          color: #FF5722;
          font-size: 12px;
          font-weight: 500;
        }
      }
      
      .productCheckbox {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        font-size: 12px;
        color: #666;
        
        .selectCheckbox {
          width: 16px;
          height: 16px;
          accent-color: #4CAF50;
        }
      }
  
      .productActions {
        display: flex;
        gap: 8px;
  
        .addToCartButton {
          flex: 1;
          background: white;
          color: #4CAF50;
          border: 2px solid #4CAF50;
          padding: 10px 12px;
          border-radius: 8px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.3s ease;
  
          &:hover:not(:disabled) {
            background: #4CAF50;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
          }
  
          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
  
        .buyNowButton {
          flex: 1;
          background: #4CAF50;
          color: white;
          border: 2px solid #4CAF50;
          padding: 10px 12px;
          border-radius: 8px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.3s ease;
  
          &:hover:not(:disabled) {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
          }
  
          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }
    }
  }
  
  // Empty State
  .emptyState {
    text-align: center;
    padding: 80px 20px;
    color: #666;
  
    .emptyIcon {
      font-size: 80px;
      color: #ddd;
      margin-bottom: 24px;
    }
  
    h3 {
      font-size: 24px;
      color: #333;
      margin: 0 0 16px 0;
    }
  
    p {
      font-size: 16px;
      margin: 0 0 32px 0;
    }
  
    .browseButton {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 14px 28px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      transition: all 0.2s;
  
      &:hover {
        background: #45a049;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
      }
    }
  }
  
  // Responsive Design
  @media (max-width: 768px) {
    .container {
      padding: 0 12px;
    }
  
    .pageHeader {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
  
      .pageTitle {
        font-size: 24px;
      }
  
      .headerActions {
        flex-direction: column;
        gap: 12px;
  
        .searchBar {
          width: 100%;
        }
  
        .filterButton,
        .viewModeToggle {
          width: 100%;
        }
      }
    }
  
    .filtersPanel {
      .filterRow {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
  
        .filterGroup {
          width: 100%;
        }
      }
    }
  
    .bulkActions {
      flex-wrap: wrap;
      gap: 8px;
    }
  
    .productsGrid {
      grid-template-columns: 1fr;
      gap: 16px;
  
      &.listLayout {
        .productCard {
          flex-direction: column;
  
          .productImage {
            width: 100%;
            height: 200px;
          }
        }
      }
    }
  
    .productCard {
      .productImage {
        height: 200px;
      }
  
      .productInfo {
        .productActions {
          flex-direction: column;
          gap: 8px;
  
          .addToCartButton,
          .buyNowButton {
            width: 100%;
          }
        }
      }
    }
  }
  
  @media (max-width: 480px) {
    .pageHeader {
      .pageTitle {
        font-size: 20px;
      }
    }
  
    .productCard {
      .productImage {
        height: 180px;
      }
  
      .productInfo {
        padding: 12px;
  
        .productName {
          font-size: 14px;
        }
  
        .productPrice .currentPrice {
          font-size: 16px;
        }
      }
    }
  }
  