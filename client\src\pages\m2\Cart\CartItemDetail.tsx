import React from 'react';
import { useGetProductDetailHook } from '@/hooks/product/useproduct'; 
import { Link } from 'react-router-dom';
import styles from './Cart.module.scss';
import { Popconfirm } from 'antd';
import Button from '@/components/Button';
import { DeleteOutlined } from '@ant-design/icons';
import logo from '@/assets/images/logo.png';

// Interface cho thông tin tồn kho được truyền xuống
interface InventoryInfo {
  available: number;
  enough: boolean;
}

interface CartItemDetailProps {
  item: any;
  inventoryInfo?: InventoryInfo; 
  onUpdateQuantity: (itemId: string, newQuantity: number) => void;
  onRemoveItem: (itemId: string) => void;
  onSelectItem: (itemId: string) => void;
}

const CartItemDetail: React.FC<CartItemDetailProps> = ({ item, inventoryInfo, onUpdateQuantity, onRemoveItem, onSelectItem }) => {
  const { data: productDetail, isLoading, error } = useGetProductDetailHook(item.product_id);

  if (isLoading) {
    return <div className={styles.cartItem}>Đang tải thông tin sản phẩm...</div>;
  }

  if (error || !productDetail) {
    return (
      <div className={`${styles.cartItem} ${styles.outOfStock}`}>
        <div style={{ padding: '10px', textAlign: 'center', width: '100%' }}>
          Không thể tải thông tin cho sản phẩm ID: {item.product_id}.
          {error && <p style={{color: 'red', fontSize: '12px'}}>Lý do: {JSON.stringify((error as any).data)}</p>}
        </div>
      </div>
    );
  }
  
  const productData: any = productDetail;
  const displayedName = productData?.name || `Sản phẩm ${item.product_id}`;
  const displayedImage = productData?.thumbnail_url || logo;
  const displayedPrice = parseFloat(item.unit_price) || 0;

  // ✅ BƯỚC 1: TÌM TÊN PHIÊN BẢN TỪ `product_versions`
  // Dùng `item.version_id` để tìm đúng object trong mảng `product_versions`
  const versionInfo = productData.product_versions?.find((v: any) => v.id === item.version_id);
  // Lấy ra thuộc tính 'name' từ object đó, ví dụ: "V1"
  const displayedVersion = versionInfo?.name;

  // Vô hiệu hóa nút tăng số lượng nếu vượt quá số lượng tồn kho
  const isIncreaseDisabled = inventoryInfo ? item.quantity >= inventoryInfo.available : false;

  return (
    <div className={styles.cartItem}>
      <div className={styles.itemSelect}>
        <input type="checkbox" checked={item.selected} onChange={() => onSelectItem(item.id)} />
      </div>
      <div className={styles.itemImage}>
        <img src={displayedImage} alt={displayedName} />
      </div>
      <div className={styles.itemInfo}>
        <Link to={`/product/${productData.id || item.product_id}`} className={styles.itemName}>{displayedName}</Link>
        
        {/* ✅ BƯỚC 2: HIỂN THỊ BIẾN `displayedVersion` */}
        {/* Dòng này sẽ hiển thị tên phiên bản thật thay vì ID */}
        {displayedVersion && <div className={styles.itemVariant}>{displayedVersion}</div>}
        
        {/* Hiển thị cảnh báo nếu tồn kho không đủ */}
        {inventoryInfo && !inventoryInfo.enough && (
          <div className={styles.stockWarning}>⚠️ Chỉ còn {inventoryInfo.available} sản phẩm.</div>
        )}
      </div>
      <div className={styles.itemPrice}>
        {displayedPrice.toLocaleString()}đ
      </div>
      <div className={styles.quantityControl}>
        <button onClick={() => onUpdateQuantity(item.id, item.quantity - 1)} disabled={item.quantity <= 1}>–</button>
        <span>{item.quantity}</span>
        <button onClick={() => onUpdateQuantity(item.id, item.quantity + 1)} disabled={isIncreaseDisabled}>+</button>
      </div>
      <div className={styles.itemTotal}>
        {(displayedPrice * item.quantity).toLocaleString()}đ
      </div>
      <Popconfirm
        title="Xóa sản phẩm"
        description="Bạn chắc chắn muốn xóa?"
        onConfirm={() => onRemoveItem(item.id)}
        okText="Xóa"
        cancelText="Hủy"
        placement="topRight"
      >
        <Button 
          variant="danger"
          icon={<DeleteOutlined />} 
          size="small"
          className={styles.removeItem} 
        >
          Xóa
        </Button>
      </Popconfirm>
    </div>
  );
};

export default CartItemDetail;
