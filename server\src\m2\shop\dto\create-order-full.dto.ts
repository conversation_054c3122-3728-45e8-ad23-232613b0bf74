import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsOptional,
  IsEnum,
  IsBoolean,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { OrderSource } from '../entities/order.entity';

export class CreateOrderItemDto {
  @IsString()
  @ApiProperty()
  product_id: string;

  @IsString()
  @ApiPropertyOptional({ required: false })
  version_id?: string;

  @IsNumber()
  @ApiProperty()
  quantity: number;

  @IsNumber()
  @ApiProperty()
  unit_price: number;

  @IsOptional()
  @ApiPropertyOptional()
  selected_options?: any;
}

export class CreateOrderFullDto {
  @IsString()
  @ApiProperty({ enum: ['cart', 'buy_now'] })
  source: 'cart' | 'buy_now';
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateOrderItemDto)
  @ApiProperty({ type: [CreateOrderItemDto] })
  items: CreateOrderItemDto[];

  @IsString()
  @ApiProperty()
  shipping_address: string;

  @IsString()
  @ApiProperty()
  receiver_name: string;

  @IsString()
  @ApiProperty()
  receiver_phone: string;

  @IsOptional()
  @ApiPropertyOptional()
  note?: string;

  @IsOptional()
  @ApiPropertyOptional()
  guest_phone?: string;

  @IsOptional()
  @ApiPropertyOptional()
  guest_email?: string;

  @IsOptional()
  @ApiPropertyOptional()
  ref_code?: string;

  @IsOptional()
  @ApiPropertyOptional()
  is_guest?: boolean;
}
