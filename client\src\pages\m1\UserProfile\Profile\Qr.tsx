import { useEffect, useState } from 'react'
import { useGetQrHook } from '@/hooks/profile/qr/useQr'
import type { QrResponse } from '@/services/M1/Qr/qr'

interface Props {
  uIdCode: string
}

const UserQr = ({ uIdCode }: Props) => {
  const { getQr } = useGetQrHook()
  const [qr, setQrCode] = useState<QrResponse | null>(null)

  useEffect(() => {
    const fetch = async () => {
      if (uIdCode) {
        const res = await getQr()
        setQrCode(res)
      }
    }
    fetch()
  }, [uIdCode])

  return (
    <>{qr ? <img width={160} height={160} src={qr.qrCode} alt="QR code" /> : 'Đang tải QR...'}</>
  )
}

export default UserQr
