export const checkPermission = (
  userPermissions: string[] = [],
  required: string | string[],
  userRole?: string
): boolean => {
  // Admin role (R05) has full access to all routes
  if (userRole === 'R05') {
    return true
  }
  
  // Users with admin full access permission have access to all routes
  if (userPermissions.includes('PERM_ADMIN_FULL_ACCESS')) {
    return true
  }
  
  if (!userPermissions?.length) return false
  if (Array.isArray(required)) {
    return required.some((p) => userPermissions.includes(p))
  }
  return userPermissions.includes(required)
}
