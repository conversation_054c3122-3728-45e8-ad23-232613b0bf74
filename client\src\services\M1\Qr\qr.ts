import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../../libs/axios/axiosBase'

const baseUrl = 'http://localhost:8080'

export const qrApi = createApi({
  reducerPath: 'qrApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  endpoints: (build) => ({
    qr: build.mutation<QrResponse, null>({
      query: () => ({
        url: '/users/profile/qr',
        method: 'POST',
        authRequired: true,
      }),
    }),
  }),
})

export const { useQrMutation } = qrApi

export interface QrUser {
  uidCode: string
  fullName: string
  email: string
  phoneNumber: string
  role: string
}

export interface QrResponse {
  id: string
  qrCode: string
  qrType: string
  description: string
  linkUrl: string | null
  isActive: boolean
  createdAt: string
  updatedAt: string
  user: QrUser
}
