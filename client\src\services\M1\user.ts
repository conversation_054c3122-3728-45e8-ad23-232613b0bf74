import { createApi } from '@reduxjs/toolkit/query/react'
import { axiosBaseQuery } from '../../libs/axios/axiosBase'
import type { GetUserProfileParams } from './auth'

const baseUrl = 'http://localhost:8080'

export const userApi = createApi({
  reducerPath: 'userApi',
  baseQuery: axiosBaseQuery({ baseUrl }),
  endpoints: (build) => ({
    getAllRole: build.query({
      query: () => ({
        url: '/roles',
        method: 'GET',
        authRequired: false,
      }),
    }),

    getAllRolesForAdmin: build.query({
      query: () => ({
        url: '/roles/admin',
        method: 'GET',
        authRequired: true,
      }),
    }),
    createRole: build.mutation({
      query: (data: CreateRoleRequest) => ({
        url: '/roles',
        method: 'POST',
        data: data,
        authRequired: false,
      }),
    }),

    getUserProfile: build.query<UserProfileResponse, null>({
      query: () => ({
        url: '/users/profile',
        method: 'GET',
        authRequired: true,
      }),
    }),

    updateProfile: build.mutation({
      query: (data: UpdateProfileProps) => ({
        url: '/users/profile',
        method: 'PUT',
        data: data,
        authRequired: true,
      }),
    }),

    updateAvatar: build.mutation({
      query: (file: File) => {
        const formData = new FormData()
        formData.append('file', file)

        return {
          url: '/users/avatar',
          method: 'POST',
          data: formData,
          authRequired: true,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      },
    }),
    getAllRoleGroups: build.query({
      query: (roleId?: string) => ({
        url: roleId ? `/roles/groups?roleId=${roleId}` : '/roles/groups',
        method: 'GET',
        authRequired: true,
      }),
    }),
    assignRoleGroupToUser: build.mutation({
      query: (data: { userId: string; roleGroupCode: string }) => ({
        url: '/roles/assign-group',
        method: 'POST',
        data,
        authRequired: true,
      }),
    }),
  }),
})

export const {
  useGetAllRoleQuery,
  useGetAllRolesForAdminQuery,
  useCreateRoleMutation,
  useGetUserProfileQuery,
  useUpdateProfileMutation,
  useUpdateAvatarMutation,
  useGetAllRoleGroupsQuery,
  useAssignRoleGroupToUserMutation,
} = userApi

export interface RoleResponse {
  id: string
  role_code: string
  name: string
  description: string
}

export interface CreateRoleRequest {
  role_code: string
  name: string
  description: string
}
export type UpdateProfileProps = {
  fullName: string
  phone: string
  citizenId: string
  gender: string
  dateOfBirth: string
  email: string
  address: string
}

export type UserProfileResponse = {
  id: string
  referenceId: string
  fullName: string
  phoneNumber: string
  citizenId: string
  gender: 'Nam' | 'Nữ' | 'Khác' | string // string fallback nếu API lỡ bắn bất ngờ
  dateOfBirth: string // ISO format, parse ra Date nếu cần
  avatarUrl: string | null
  role: string
  isVerifiedPhone: boolean
  isVerifiedMail: boolean
  email: string
  uIdCode: string
  address: string
  isEnable2FA: boolean
}
