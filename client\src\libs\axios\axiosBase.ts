import type { BaseQueryFn } from '@reduxjs/toolkit/query'
import { AxiosError, type AxiosRequestConfig } from 'axios'
import { axiosAuth } from './axiosAuth'
import { axiosPublic } from './axiosPublic'
import { axiosOptional } from './axiosOptional'

interface AxiosBaseQueryProps {
  url: string
  method?: AxiosRequestConfig['method']
  data?: AxiosRequestConfig['data']
  params?: AxiosRequestConfig['params']
  headers?: AxiosRequestConfig['headers']
  authRequired?: boolean
  authOptional?: boolean // Thêm option mới
}
export const axiosBaseQuery =
  <T extends Record<string, unknown> = Record<string, unknown>>({
    baseUrl,
  }: {
    baseUrl: string
  }): BaseQueryFn<AxiosBaseQueryProps & T, unknown, unknown> =>
  async ({
    url,
    method,
    data,
    params,
    authRequired = false,
    authOptional = false,
    ...config
  }: AxiosBaseQueryProps & T) => {
    try {
      // Chọn instance phù hợp
      let instance
      if (authRequired) {
        instance = axiosAuth
      } else if (authOptional) {
        instance = axiosOptional
      } else {
        instance = axiosPublic
      }
      const result = await instance({
        url,
        method,
        data,
        params,
        ...config,
      })

      // ✅ Unwrap chuẩn
      return { data: result.data?.data ?? result.data }
    } catch (axiosError) {
      const err = axiosError as AxiosError
      return {
        error: {
          status: err.response?.status || 500,
          data: err.response?.data || 'Unknown error',
        },
      }
    }
  }
