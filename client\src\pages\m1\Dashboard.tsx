import { Card, Col, Row, Statistic, Spin } from 'antd'
import {
  UserOutlined,
  ShoppingCartOutlined,
  AppstoreOutlined,
  TeamOutlined,
} from '@ant-design/icons'
import { useGetUserManagementHook } from '@/hooks/system/useUserManagement'
import { useGetProductsHook } from '@/hooks/product/useproduct'
import { useGetAdminOrdersHook } from '@/hooks/orders/useOrderManagement'
import { useGetAffiliateStatsHook } from '@/hooks/affiliate/useAffiliate'

export default function Dashboard() {
  // Lấy tổng số người dùng
  const { data: userData, isLoading: userLoading } = useGetUserManagementHook({ page: 1, limit: 1 })
  // Lấy tổng số sản phẩm
  const { data: productData, isLoading: productLoading } = useGetProductsHook({ page: 1, size: 1 })
  // L<PERSON>y tổng số đơn hàng
  const { data: orderData, isLoading: orderLoading } = useGetAdminOrdersHook({ page: 1, size: 1 })
  // Lấy tổng số affiliate (nếu có)
  const { data: affiliateStats, isLoading: affiliateLoading } = useGetAffiliateStatsHook()

  const totalUsers = userData?.total || 0
  const totalProducts = productData?.total || 0
  const totalOrders = orderData?.data?.total || 0
  const totalAffiliates = affiliateStats?.totalSystem || 0

  const loading = userLoading || productLoading || orderLoading || affiliateLoading

  return (
    <div style={{ padding: 24 }}>
      <h2 style={{ marginBottom: 24 }}>Bảng điều khiển tổng quan</h2>
      <Spin spinning={loading} tip="Đang tải dữ liệu...">
        <Row gutter={[24, 24]}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic title="Tổng người dùng" value={totalUsers} prefix={<UserOutlined />} />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Tổng sản phẩm"
                value={totalProducts}
                prefix={<AppstoreOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Tổng đơn hàng"
                value={totalOrders}
                prefix={<ShoppingCartOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic title="Tổng affiliate" value={totalAffiliates} prefix={<TeamOutlined />} />
            </Card>
          </Col>
        </Row>
        <div style={{ marginTop: 32 }}>
          <h4>Debug affiliateStats:</h4>
          <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 8 }}>
            {JSON.stringify(affiliateStats, null, 2)}
          </pre>
        </div>
      </Spin>
    </div>
  )
}
