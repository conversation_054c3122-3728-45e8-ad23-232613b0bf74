// src/m2/shop/controllers/product.controller.ts
import { UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  Body,
  Controller,
  Post,
  Req,
  UseGuards,
  Param,
  Put,
  Delete,
  HttpCode,
  Get,
  Query,
} from '@nestjs/common';
import { ProductService } from '../services/product.service';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { UpdateProductImagesDto } from '../dto/update-product-images.dto';
import { UpdateProductAttributesDto } from '../dto/update-product-attributes.dto';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../m1/auth/jwt-auth.guard';
import { Product } from '../entities/products.entity';
import { UserBehaviorLogService } from '../../../m_log/log/user-behavior-log.service';
import { ProductVersionDto } from '../dto/product-version.dto';
import { ProductListRequestDto } from '../dto/product-list-request.dto';
import { M2ProductRecommenderService } from '../services/m2ProductRecommender.service';
import { AllowAnonymous } from '../../../common/decorators/allow-anonymous.decorator';

@ApiTags('Product')
@Controller('products')
export class ProductController {
  constructor(
    private readonly productService: ProductService,
    private readonly userBehaviorLogService: UserBehaviorLogService,
    private readonly productRecommender: M2ProductRecommenderService,
  ) {}

  // PUBLIC ENDPOINTS (không cần đăng nhập)
  /**
   * Lấy danh sách sản phẩm công khai (có phân trang, tìm kiếm, lọc, sắp xếp)
   */
  @Get('public')
  @AllowAnonymous()
  @ApiOperation({
    summary: 'Lấy danh sách sản phẩm công khai (có phân trang, tìm kiếm, lọc, sắp xếp)',
  })
  async getPublicProductList(@Query() query: ProductListRequestDto, @Req() req: any) {
    return this.productService.findAllWithVersions(query);
  }

  /**
   * Tìm sản phẩm bằng hình ảnh (công khai)
   */
  @Get('public/search-by-image')
  @AllowAnonymous()
  @UseInterceptors(FileInterceptor('image'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary',
          description: 'Ảnh sản phẩm (file upload)',
        },
      },
      required: ['image'],
    },
  })
  @ApiOperation({ summary: 'Tìm sản phẩm bằng hình ảnh (công khai)' })
  async searchByImagePublic(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      return { error: 'No image uploaded' };
    }
    return this.productService.searchProductByImageCategory(file);
  }

  /**
   * Tìm kiếm sản phẩm bằng hình ảnh (AI object detection)
   */
  @Post('search-by-image')
  @UseInterceptors(FileInterceptor('image'))
  @ApiOperation({ summary: 'Tìm kiếm sản phẩm bằng hình ảnh (AI object detection)' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary',
          description: 'Ảnh sản phẩm (file upload)',
        },
      },
      required: ['image'],
    },
  })
  async searchByImage(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      return { error: 'No image uploaded' };
    }
    return this.productService.searchProductByImageCategory(file);
  }

  /**
   * Lấy chi tiết sản phẩm công khai
   */
  @Get('public/:id')
  @AllowAnonymous()
  @ApiOperation({ summary: 'Lấy chi tiết sản phẩm công khai' })
  async getPublicProductDetail(@Param('id') id: string) {
    return this.productService.findOneWithVersions(id);
  }

  // SHOP ENDPOINTS (cho người bán quản lý sản phẩm của họ)
  /**
   * Lấy danh sách sản phẩm của shop (người bán)
   */
  @Get('shop')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Lấy danh sách sản phẩm của shop (người bán)' })
  async getShopProductList(@Query() query: ProductListRequestDto, @Req() req: any) {
    const userId = req.user?.userId;
    return this.productService.findAllWithVersionsForShop(userId, query);
  }

  @Get('admin')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Lấy danh sách sản phẩm cho admin (bao gồm thông tin người bán)' })
  async getAdminProductList(@Query() query: ProductListRequestDto, @Req() req: any) {
    return this.productService.findAllWithVersionsForAdmin(query);
  }

  @Get('superadmin')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('access-token')
  @ApiOperation({
    summary: 'Lấy danh sách sản phẩm cho superadmin (bao gồm thông tin người bán và khu vực bán)',
  })
  async getSuperAdminProductList(@Query() query: ProductListRequestDto, @Req() req: any) {
    return this.productService.findAllWithVersionsForSuperAdmin(query);
  }

  // LEGACY ENDPOINTS (để tương thích ngược)

  /**
   * Lấy thông tin inventory cho một sản phẩm
   */
  @Get(':id/inventory')
  @AllowAnonymous() // Cho phép guest xem thông tin tồn kho
  @ApiOperation({ summary: 'Lấy thông tin inventory cho một sản phẩm' })
  async getProductInventory(@Param('id') id: string, @Query('version_id') versionId?: string) {
    return this.productService.getProductInventory(id, versionId);
  }

  /**
   * Lấy thống kê đánh giá và số lượng đã bán cho sản phẩm
   */
  @Get(':id/stats')
  @AllowAnonymous()
  @ApiOperation({ summary: 'Lấy thống kê đánh giá và số lượng đã bán cho sản phẩm' })
  async getProductStats(@Param('id') id: string) {
    return this.productService.getProductStats(id);
  }

  /**
   * ➕ Tạo sản phẩm mới
   * @param dto Thông tin sản phẩm từ người dùng
   * @returns Sản phẩm vừa được lưu
   */
  @Post()
  @ApiOperation({ summary: 'Tạo sản phẩm mới' })
  @ApiResponse({ status: 201, type: Product })
  async createProduct(@Body() dto: CreateProductDto, @Req() req: any): Promise<Product> {
    const userId = req.user?.userId;
    const product = await this.productService.create(dto, userId);
    this.userBehaviorLogService.emitUserBehavior({
      user_id: userId,
      event_type: 'create',
      target_type: 'product',
      target_id: product.id,
      product_id: product.id,
      event_data: { dto },
      description: 'User created a product',
    });
    return product;
  }

  /**
   * Cập nhật toàn bộ sản phẩm
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật toàn bộ sản phẩm' })
  @ApiResponse({ status: 200, type: Product })
  async updateProduct(
    @Param('id') id: string,
    @Body() dto: UpdateProductDto,
    @Req() req: any,
  ): Promise<Product> {
    const userId = req.user?.userId;
    const product = await this.productService.update(id, dto, userId);
    this.userBehaviorLogService.emitUserBehavior({
      user_id: userId,
      event_type: 'update',
      target_type: 'product',
      target_id: id,
      product_id: id,
      event_data: { dto },
      description: 'User updated a product',
    });
    return product;
  }

  /**
   * Cập nhật images cho sản phẩm
   */
  @Put(':id/images')
  @ApiOperation({ summary: 'Cập nhật images cho sản phẩm' })
  @ApiResponse({ status: 200, type: Product })
  async updateProductImages(
    @Param('id') id: string,
    @Body() dto: UpdateProductImagesDto,
    @Req() req: any,
  ): Promise<Product> {
    const userId = req.user?.userId;
    return this.productService.updateImages(id, dto.imageIds, userId);
  }

  /**
   * Cập nhật thuộc tính cho sản phẩm
   */
  @Put(':id/attributes')
  @ApiOperation({ summary: 'Cập nhật thuộc tính cho sản phẩm' })
  @ApiResponse({ status: 200, type: Product })
  async updateProductAttributes(
    @Param('id') id: string,
    @Body() dto: UpdateProductAttributesDto[],
    @Req() req: any,
  ): Promise<Product> {
    const userId = req.user?.userId;
    return this.productService.updateAttributes(id, dto, userId);
  }

  /**
   * Tắt sản phẩm (ẩn khỏi public)
   */
  @Put(':id/disable')
  @ApiOperation({ summary: 'Tắt (ẩn) sản phẩm' })
  @ApiResponse({ status: 200, type: Product })
  async disableProduct(@Param('id') id: string, @Req() req: any): Promise<Product> {
    const userId = req.user?.userId;
    return this.productService.disable(id, userId);
  }

  /**
   * Xóa sản phẩm
   */
  @Delete(':id')
  @HttpCode(204)
  @ApiOperation({ summary: 'Xóa sản phẩm' })
  async deleteProduct(@Param('id') id: string, @Req() req: any): Promise<void> {
    const userId = req.user?.userId;
    await this.productService.delete(id, userId);
  }

  /**
   * Lấy chi tiết sản phẩm (kèm version nếu có) - hỗ trợ cả guest và user đã đăng nhập
   */
  @Get(':id')
  @AllowAnonymous() // Cho phép cả guest và user đã đăng nhập truy cập
  @ApiOperation({
    summary: 'Lấy chi tiết sản phẩm (kèm version, QR code) - hỗ trợ cả guest và user',
  })
  async getProductDetail(@Param('id') id: string, @Req() req: any) {
    const userId = req.user?.userId;

    // Chỉ ghi log nếu user đã đăng nhập
    if (userId) {
      this.userBehaviorLogService.emitUserBehavior({
        user_id: userId,
        event_type: 'view',
        target_type: 'product',
        target_id: id,
        product_id: id,
        event_data: {},
        description: 'User viewed product detail',
      });
    }

    return this.productService.findOneWithVersions(id);
  }

  /**
   * Gợi ý sản phẩm phù hợp cho user (dựa trên vector embedding)
   */
  @Get('recommend-for-user/:user_id')
  @ApiOperation({ summary: 'Gợi ý sản phẩm phù hợp cho user (AI vector)' })
  async recommendProductsForUser(@Param('user_id') user_id: string, @Query('topN') topN?: string) {
    const n = topN ? parseInt(topN, 10) : 5;
    return this.productRecommender.suggestProductsForUser(user_id, n);
  }

  /**
   * Ghi log khi user click vào sản phẩm gợi ý AI
   */
  @Post('log-ai-suggestion-click')
  @ApiOperation({ summary: 'Ghi log khi user click vào sản phẩm gợi ý AI' })
  async logAiSuggestionClick(
    @Body() body: { user_id: string; product_id: string; extra?: any },
    @Req() req: any,
  ) {
    const userId = body.user_id || req.user?.userId;
    this.userBehaviorLogService.emitUserBehavior({
      user_id: userId,
      event_type: 'ai_suggestion_click',
      target_type: 'product',
      target_id: body.product_id,
      product_id: body.product_id,
      event_data: body.extra || {},
      description: 'User clicked AI suggestion',
    });
    return { success: true };
  }
}
