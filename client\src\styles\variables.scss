// Colors
$primary: #eb6d20;
$third: #eb8426;
$light: #d6d6d6;
$medium: #5f5f5f;
$border: #e9e9e9;
$text: #3e3e3e;
$subtext: #9a9a9a;
$paragraph: #292929;
$white: #ffffff;
$black: #040404;
$gray-bg: #f5f6f8;

// Fonts
$font-main: 'Poppins', sans-serif;

// Typography Scale - Consistent font sizes
$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-base: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 28px;
$font-size-4xl: 32px;
$font-size-5xl: 36px;

// Line heights
$line-height-tight: 1.2;
$line-height-normal: 1.4;
$line-height-relaxed: 1.6;

// Font weights
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Text Styles Mixins
@mixin heading-1 {
  font-size: $font-size-3xl;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
}

@mixin heading-2 {
  font-size: $font-size-2xl;
  font-weight: $font-weight-semibold;
  line-height: $line-height-tight;
}

@mixin heading-3 {
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
  line-height: $line-height-normal;
}

@mixin body-text {
  font-size: $font-size-base;
  font-weight: $font-weight-normal;
  line-height: $line-height-normal;
}

@mixin body-text-lg {
  font-size: $font-size-md;
  font-weight: $font-weight-normal;
  line-height: $line-height-relaxed;
}

@mixin caption {
  font-size: $font-size-sm;
  font-weight: $font-weight-normal;
  line-height: $line-height-normal;
}

@mixin small-text {
  font-size: $font-size-xs;
  font-weight: $font-weight-normal;
  line-height: $line-height-normal;
}

// Color variables
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$info-color: #1890ff;

// Button styles
$button-primary-bg: #1890ff;
$button-primary-hover-bg: #40a9ff;
$button-primary-active-bg: #096dd9;
$button-primary-text: #ffffff;

$button-default-bg: #ffffff;
$button-default-border: #d9d9d9;
$button-default-hover-border: #40a9ff;
$button-default-hover-text: #40a9ff;
$button-default-text: #595959;

$button-danger-bg: #ff4d4f;
$button-danger-hover-bg: #ff7875;
$button-danger-active-bg: #d9363e;
$button-danger-text: #ffffff;

// Button sizes - Touch-friendly (minimum 44px for mobile)
$button-height-lg: 48px;
$button-height-md: 44px;
$button-height-sm: 44px; // Minimum touch target size

// Button border radius
$button-border-radius: 8px;

// Button padding
$button-padding-lg: 14px 20px;
$button-padding-md: 12px 16px;
$button-padding-sm: 12px 16px; // Increased for better touch target

// Button font sizes - Using typography scale
$button-font-size-lg: $font-size-md;
$button-font-size-md: $font-size-base;
$button-font-size-sm: $font-size-base; // Consistent readability

// Spacing scale - Consistent spacing system
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// Button transitions
$button-transition: all 0.2s ease;

// Modal button styles
$modal-button-gap: 8px;
$modal-button-min-width: 80px;