import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsEnum, IsBoolean } from 'class-validator';
import { OrderSource } from '../entities/order.entity';

export class CreateOrderDto {
  @IsOptional()
  @IsString()
  user_id?: string;

  @IsOptional()
  @IsString()
  guest_phone?: string;

  @IsOptional()
  @IsString()
  guest_email?: string;

  @IsString()
  status_id: string;

  @IsEnum(OrderSource)
  source: OrderSource;

  @IsNumber()
  total_price: number;

  @IsString()
  shipping_name: string;

  @IsString()
  shipping_phone: string;

  @IsString()
  shipping_address: string;

  @IsOptional()
  @IsString()
  note?: string;

  @IsOptional()
  @IsString()
  ref_code?: string;

  @IsBoolean()
  is_guest: boolean;

  @IsOptional()
  @IsBoolean()
  suspected?: boolean;
}
