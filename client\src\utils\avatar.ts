import maleDefaultAvt from '@/assets/img/mook-avatar-male.png'

/**
 * Utility function để xử lý avatar mặc định
 * @param avatarUrl - URL avatar của user (có thể null/undefined/empty)
 * @param gender - <PERSON><PERSON><PERSON><PERSON> tính của user (optional, mặc định là male)
 * @returns URL avatar hợp lệ
 */
export const getAvatarUrl = (avatarUrl?: string | null, gender?: 'male' | 'female'): string => {
  // Nếu có avatarUrl và không rỗng, sử dụng nó
  if (avatarUrl && avatarUrl.trim() !== '') {
    return avatarUrl
  }
  
  // Nếu không có avatar, sử dụng avatar mặc định
  // Hiện tại chỉ sử dụng male avatar, có thể mở rộng sau
  return maleDefaultAvt
}

/**
 * Props cho img tag với fallback avatar
 */
export const getAvatarProps = (avatarUrl?: string | null, alt?: string) => ({
  src: getAvatarUrl(avatarUrl),
  alt: alt || 'Avatar',
  onError: (e: React.SyntheticEvent<HTMLImageElement>) => {
    // Fallback khi load avatar thất bại
    e.currentTarget.src = maleDefaultAvt
  }
})
