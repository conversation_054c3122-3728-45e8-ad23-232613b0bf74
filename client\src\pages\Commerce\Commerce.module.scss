/* =================================================================
   COMMERCE PAGE STYLES
   Organized by: Desktop First → Mobile Responsive
================================================================= */

/* -----------------------------------------------------------------
   1. GLOBAL STYLES & VARIABLES
----------------------------------------------------------------- */
@import '../../styles/variables.scss';

.commercePage {
  min-height: 100vh;
  background: #f8f9fa;
  position: relative;
}

.mainContent {
  padding-top: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-md;

  @media (max-width: 768px) {
    padding: 0 $spacing-sm;
  }
}

.sectionTitle {
  text-align: center;
  font-size: 2.2rem;
  color: #333;
  margin-bottom: 16px;
  font-weight: 700;
}

.sectionSubtitle {
  text-align: center;
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 50px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.ctaButton {
  display: inline-block;
  padding: 15px 30px;
  background: white;
  color: #4CAF50;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
}

/* -----------------------------------------------------------------
   2. ANIMATIONS & KEYFRAMES
----------------------------------------------------------------- */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

@keyframes tabPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* =================================================================
   DESKTOP STYLES (Default - 1024px+)
================================================================= */

/* -----------------------------------------------------------------
   LOADING COMPONENT
----------------------------------------------------------------- */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e0e0e0;
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  p {
    margin-top: 16px;
    color: #666;
  }
}

/* -----------------------------------------------------------------
   FILTER SIDEBAR
----------------------------------------------------------------- */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 320px;
  height: 100vh;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
  padding: 0;
}

.sidebarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
  
  h2 {
    font-size: 1.2rem;
    color: #333;
    margin: 0;
  }
}

.sidebarClose {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: #e0e0e0;
  }
  
  svg {
    width: 20px;
    height: 20px;
  }
}

.filterSection {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.filterTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.filterItem {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  
  input[type="checkbox"],
  input[type="radio"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
  
  label {
    font-size: 0.9rem;
    color: #666;
    cursor: pointer;
    flex: 1;
  }
}

.categoryFilterItem {
  margin-bottom: 4px;
}

.categoryHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 4px;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
  
  label {
    font-size: 0.9rem;
    color: #333;
    cursor: pointer;
    flex: 1;
    font-weight: 500;
  }
}

.categoryCount {
  font-size: 0.8rem;
  color: #999;
  margin-left: auto;
}

.categoryArrow {
  width: 16px;
  height: 16px;
  color: #666;
  transition: transform 0.2s;
  
  &.rotated {
    transform: rotate(180deg);
  }
}

.subCategories {
  margin-left: 24px;
  margin-top: 2px;
  padding-left: 8px;
  border-left: 2px solid #e0e0e0;
  padding-bottom: 4px;
}

.subCategoryItem {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  padding: 2px 0;
  
  input[type="checkbox"] {
    width: 14px;
    height: 14px;
    cursor: pointer;
  }
  
  label {
    font-size: 0.85rem;
    color: #666;
    cursor: pointer;
    flex: 1;
  }
}

.clearFiltersButton {
  width: calc(100% - 40px);
  margin: 20px;
  padding: 12px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background: #e03e3e;
  }
}

/* -----------------------------------------------------------------
   FILTER TOGGLE BUTTON
----------------------------------------------------------------- */
.filterToggle {
  padding: 20px 0;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  
  .container {
    display: flex;
    justify-content: flex-start;
  }
}

.filterToggleBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background: #45a049;
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
}

/* -----------------------------------------------------------------
   HERO BANNER
----------------------------------------------------------------- */
.heroBanner {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  padding: 80px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.bannerContent {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 16px;
  position: relative;
  z-index: 2;
  
  h1 {
    font-size: 2.8rem;
    margin-bottom: 20px;
    font-weight: 700;
    line-height: 1.2;
  }
  
  p {
    font-size: 1.3rem;
    opacity: 0.9;
    margin-bottom: 30px;
    line-height: 1.5;
  }
}

/* -----------------------------------------------------------------
   BRANDS SECTION
----------------------------------------------------------------- */
.brandsSection {
  padding: 80px 0;
  background: #f8f9fa;
}

.brandsContainer {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.brandsGrid {
  display: flex;
  gap: 30px;
  overflow-x: auto;
  padding: 20px 0;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  max-width: 100%;
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.brandItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 150px;
  flex-shrink: 0;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.brandLogo {
  width: 80px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 12px;
  border-radius: 8px;
}

.brandName {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.scrollButtonContainer {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 20px;
}

.scrollButton {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #4CAF50;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &:hover {
    background: #45a049;
    transform: scale(1.05);
  }
  
  svg {
    width: 18px;
    height: 18px;
  }
}

/* -----------------------------------------------------------------
   TAB NAVIGATION
----------------------------------------------------------------- */
.styleTabsContainer {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 40px 0;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  flex-wrap: wrap;
}

.styleTab {
  padding: 12px 24px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #666;
  white-space: nowrap;
  
  &:hover {
    background: #e9ecef;
    border-color: #4CAF50;
  }
  
  &.activeTab {
    background: #4CAF50;
    color: white;
    border-color: #4CAF50;
    
    // Hiệu ứng nhấp nháy nhẹ cho tab "Tìm kiếm bằng ảnh" khi được chuyển đến
    &.imageSearchTab {
      animation: tabPulse 0.6s ease-in-out;
    }
  }
}

/* -----------------------------------------------------------------
   COLLECTION SECTION
----------------------------------------------------------------- */
.collectionSection {
  padding: 60px 0;
  background: white;
}

.tabContent {
  margin-top: 40px;
}

.collectionGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: $spacing-lg;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $spacing-md;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: $spacing-sm;
  }
}

.collectionCard {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.collectionImageContainer {
  height: 200px;
  overflow: hidden;
}

.collectionImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.collectionName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 16px 20px 8px;
  line-height: 1.4;
}

.collectionDescription {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 20px 20px;
}

.noContent {
  text-align: center;
  color: #666;
  font-size: 1.1rem;
  padding: 60px 20px;
  grid-column: 1 / -1;
}

/* -----------------------------------------------------------------
   CATEGORIES SECTION
----------------------------------------------------------------- */
.categoriesSection {
  padding: 60px 0;
  background: white;
}

.loadingCategories {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 1.1rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;

  .sectionTitle {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin: 0;
  }

  .categoryControls {
    display: flex;
    gap: 12px;

    .scrollButton {
      width: 48px;
      height: 48px;
      border: 2px solid #e0e0e0;
      background: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      color: #666;
      font-size: 18px;

      &:hover {
        border-color: #4CAF50;
        color: #4CAF50;
        background: #f8f9fa;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

.categoriesGrid {
  display: grid;
  grid-template-rows: repeat(2, 1fr);
  grid-auto-flow: column;
  grid-auto-columns: minmax(150px, 1fr);
  gap: 20px;
  overflow-x: auto;
  scroll-behavior: smooth;
  padding-bottom: 16px;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

.categoryCard {
  background: #f9f9f9;
  padding: 24px;
  border-radius: 16px;
  text-align: center;
  transition: all 0.3s;
  cursor: pointer;
  min-width: 180px;

  &:hover {
    background: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .categoryImage {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 12px;
    }
  }

  h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: #333;
  }
}

/* -----------------------------------------------------------------
   PRODUCTS SECTION
----------------------------------------------------------------- */
.productsSection {
  padding: 60px 0;
  background: #f8f9fa;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  
  h2 {
    font-size: 1.8rem;
    color: #333;
  }
  
  p {
    color: #666;
    font-size: 1rem;
  }
}

.productsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-lg;
  margin-bottom: $spacing-lg;

  /* Responsive grid columns */
  @media (min-width: 1200px) {
    grid-template-columns: repeat(5, 1fr);
    gap: $spacing-lg;
  }

  @media (min-width: 992px) and (max-width: 1199px) {
    grid-template-columns: repeat(4, 1fr);
    gap: $spacing-md;
  }

  @media (min-width: 768px) and (max-width: 991px) {
    grid-template-columns: repeat(3, 1fr);
    gap: $spacing-md;
  }

  @media (max-width: 767px) {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-sm;
    padding: 0 $spacing-sm;
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-sm;
    margin-bottom: $spacing-md;
  }
}

.categoriesGrid {
  grid-template-rows: repeat(2, 1fr);
  grid-auto-flow: column;
  grid-auto-columns: minmax(150px, 1fr);
  gap: 20px;
}

.productCard {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-width: 0;
  display: flex;
  flex-direction: column;
  height: 300px;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.productImage {
  position: relative;
  height: 160px;
  overflow: hidden;
  background-color: #f0f0f0;
  flex-shrink: 0;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  &:hover img {
    transform: scale(1.05);
  }
}

.discountBadge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: #ff4444;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
}

.favoriteButton {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #ccc;
  z-index: 2;

  &:hover {
    background: white;
    transform: scale(1.1);
  }

  &.favorited {
    color: #ff4444;
  }
}

.cartButton {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: #4CAF50;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  z-index: 2;

  &:hover {
    background: #45a049;
    transform: scale(1.1);
  }
}

.productInfo {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.productName {
  font-size: 0.95rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.productRating {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
  font-size: 0.85rem;
  color: #333;
}

.starIcon {
  color: #FFC107;
  font-size: 1rem;
}

.ratingNumber {
  font-weight: 600;
}

.productOrigin {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 4px;
}

.productStatus {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 12px;
  
  &.inStock {
    color: #4CAF50;
  }
  
  &.outOfStock {
    color: #ff4444;
  }
}



.productPrice {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 0;
  margin-top: auto;
}

.currentPrice {
  font-size: 1rem;
  font-weight: 700;
  color: #4CAF50;
}

.originalPrice {
  font-size: 0.9rem;
  color: #999;
  text-decoration: line-through;
}

.addToCartBtn {
  width: 100%;
  padding: 10px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: auto;
  
  &:hover {
    background: #45a049;
  }
  
  &:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
}

.noProducts {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  grid-column: 1 / -1;
  
  p {
    font-size: 1.1rem;
  }
}

/* =================================================================
   TABLET RESPONSIVE (769px - 1024px)
================================================================= */
@media (max-width: 1024px) and (min-width: 769px) {
  .categoriesGrid {
    grid-template-rows: repeat(2, 1fr);
    grid-auto-flow: column;
    grid-auto-columns: minmax(120px, 1fr);
    gap: 16px;
  }
  
  .categoryCard {
    padding: 20px;
    min-width: 160px;

    .categoryImage {
      width: 56px;
      height: 56px;
    }

    h3 {
      font-size: 15px;
    }
  }

  .brandsGrid {
    gap: 20px;
  }
  
  .brandItem {
    min-width: 140px;
  }
  
  .styleTabsContainer {
    padding: 30px 20px;
  }
  
  .productsGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
  
  .collectionGrid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  }
}

/* =================================================================
   MOBILE RESPONSIVE (0px - 768px)
================================================================= */
@media (max-width: 768px) {
  
  /* -----------------------------------------------------------------
     GLOBAL MOBILE ADJUSTMENTS
  ----------------------------------------------------------------- */
  .sectionTitle {
    font-size: 1.8rem;
  }
  
  .bannerContent {
    h1 {
      font-size: 2rem;
    }
    
    p {
      font-size: 1rem;
    }
  }
  
  .filterToggle {
    padding: 16px 0;
  }
  
  /* -----------------------------------------------------------------
     SIDEBAR MOBILE
  ----------------------------------------------------------------- */
  .sidebar {
    width: 100%;
    max-width: 320px;
  }
  
  /* -----------------------------------------------------------------
     BRANDS MOBILE
  ----------------------------------------------------------------- */
  .brandsGrid {
    gap: 16px;
    padding: 16px 0;
  }
  
  .brandItem {
    min-width: 120px;
    padding: 16px;
  }
  
  .brandLogo {
    width: 60px;
    height: 60px;
  }
  
  /* -----------------------------------------------------------------
     TAB NAVIGATION MOBILE - Horizontal Scroll
  ----------------------------------------------------------------- */
  .styleTabsContainer {
    justify-content: flex-start;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 20px 16px;
    gap: 12px;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    position: relative;
    
    /* Hide scrollbar */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
    
    /* Add fade effect on edges */
    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      width: 20px;
      pointer-events: none;
      z-index: 1;
    }
    
    &::before {
      left: 0;
      background: linear-gradient(to right, rgba(255,255,255,1), rgba(255,255,255,0));
    }
    
    &::after {
      right: 0;
      background: linear-gradient(to left, rgba(255,255,255,1), rgba(255,255,255,0));
    }
  }

  .styleTab {
    flex-shrink: 0;
    padding: 10px 20px;
    font-size: 0.9rem;
    min-width: fit-content;
    scroll-snap-align: start;
  }
  
  /* -----------------------------------------------------------------
     CATEGORIES MOBILE
  ----------------------------------------------------------------- */
  .sectionHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 24px;

    .sectionTitle {
      font-size: 24px;
    }

    .categoryControls {
      align-self: flex-end;
    }
  }
  
  .categoriesGrid {
    grid-template-rows: repeat(2, 1fr);
    grid-auto-flow: column;
    grid-auto-columns: minmax(100px, 1fr);
    gap: 12px;
  }
  
  .categoryCard {
    padding: 16px;
    min-width: 140px;

    .categoryImage {
      width: 48px;
      height: 48px;
      margin-bottom: 12px;
    }

    h3 {
      font-size: 14px;
    }
  }
  
  /* -----------------------------------------------------------------
     PRODUCTS MOBILE - Minimalist Layout (3 columns)
  ----------------------------------------------------------------- */
  .sectionHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 0 16px;
    
    h2 {
      font-size: 1.5rem;
    }
  }
  
  .productsGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 0 16px;
  }

  .productCard {
    width: 100%;
    height: auto;
    min-height: 240px;
  }

  .productImage {
    height: 120px;
  }

  .productInfo {
    padding: 10px;
  }

  // ✅ HIỂN THỊ: Tên sản phẩm
  .productName {
    font-size: 0.85rem;
    line-height: 1.2;
    margin-bottom: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  // ❌ ẨN: Seller, Origin
  .productSeller,
  .productOrigin {
    display: none;
  }

  // ✅ HIỂN THỊ: Rating
  .productRating {
    font-size: 0.75rem;
    margin-bottom: 6px;
  }

  // ✅ HIỂN THỊ: Trạng thái còn hàng
  .productStatus {
    font-size: 0.75rem;
    margin-bottom: 6px;
    color: #4CAF50;
    font-weight: 500;
  }

  // ✅ HIỂN THỊ: Giá
  .productPrice {
    margin-bottom: 8px;
    margin-top: auto;
  }

  .currentPrice {
    font-size: 0.9rem;
    font-weight: 700;
    color: #4CAF50;
  }

  .originalPrice {
    font-size: 0.75rem;
    color: #999;
    text-decoration: line-through;
  }



  // Giữ lại favorite button, cart button và discount badge
  .favoriteButton {
    width: 24px;
    height: 24px;
    top: 6px;
    right: 6px;
  }

  .cartButton {
    width: 24px;
    height: 24px;
    bottom: 6px;
    right: 6px;
  }

  .discountBadge {
    top: 6px;
    left: 6px;
    padding: 2px 4px;
    font-size: 0.65rem;
  }
  
  /* -----------------------------------------------------------------
     COLLECTION MOBILE
  ----------------------------------------------------------------- */
  .collectionGrid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }
  
  .collectionImageContainer {
    height: 180px;
  }
}

/* =================================================================
   SMALL MOBILE (0px - 375px) - 4 columns layout
================================================================= */
@media (max-width: 375px) {
  .categoriesGrid {
    grid-template-rows: repeat(2, 1fr);
    grid-auto-flow: column;
    grid-auto-columns: minmax(80px, 1fr);
    gap: 8px;
  }
  
  .categoryCard {
    padding: 12px;
    min-width: 120px;

    .categoryImage {
      width: 40px;
      height: 40px;
      margin-bottom: 8px;
    }

    h3 {
      font-size: 12px;
    }
  }

  .productsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .productImage {
    height: 70px;
  }

  .productInfo {
    padding: 6px;
  }

  .productName {
    font-size: 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .productStatus {
    font-size: 0.7rem;
    margin-bottom: 4px;
  }

  .productRating {
    font-size: 0.65rem;
    margin-bottom: 4px;
  }

  .currentPrice {
    font-size: 0.8rem;
  }

  .originalPrice {
    font-size: 0.7rem;
  }
}

/* -----------------------------------------------------------------
   PAGINATION STYLES
----------------------------------------------------------------- */
.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  margin-bottom: 20px;
  padding: 20px 0;
}

.paginationContainer :global(.ant-pagination) {
  display: flex;
  align-items: center;
  gap: 8px;
}

.paginationContainer :global(.ant-pagination-item) {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  min-width: 32px;
  height: 32px;
  line-height: 30px;
  text-align: center;
  background: white;
  transition: all 0.3s;
}

.paginationContainer :global(.ant-pagination-item:hover) {
  border-color: #4CAF50;
  color: #4CAF50;
}

.paginationContainer :global(.ant-pagination-item-active) {
  background: #4CAF50;
  border-color: #4CAF50;
  color: white;
}

.paginationContainer :global(.ant-pagination-prev),
.paginationContainer :global(.ant-pagination-next) {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  min-width: 32px;
  height: 32px;
  line-height: 30px;
  background: white;
  transition: all 0.3s;
}

.paginationContainer :global(.ant-pagination-prev:hover),
.paginationContainer :global(.ant-pagination-next:hover) {
  border-color: #4CAF50;
  color: #4CAF50;
}

.paginationContainer :global(.ant-pagination-options) {
  margin-left: 16px;
}

.paginationContainer :global(.ant-select-selector) {
  border-radius: 6px;
  border-color: #d9d9d9;
}

.paginationContainer :global(.ant-pagination-total-text) {
  margin-right: 16px;
  color: #666;
  font-size: 14px;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .paginationContainer {
    margin-top: 20px;
    padding: 15px 0;
  }

  .paginationContainer :global(.ant-pagination) {
    flex-wrap: wrap;
    justify-content: center;
  }

  .paginationContainer :global(.ant-pagination-total-text) {
    order: -1;
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
    margin-right: 0;
  }

  .paginationContainer :global(.ant-pagination-options) {
    margin-left: 8px;
  }
}
