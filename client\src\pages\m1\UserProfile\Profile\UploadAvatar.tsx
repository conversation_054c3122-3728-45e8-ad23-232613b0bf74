import { Upload, message } from 'antd'
import ImgCrop from 'antd-img-crop'
import type { UploadProps } from 'antd'
import type { RcFile } from 'antd/es/upload'

interface Props {
  onUploadSuccess: (file: File, previewUrl: string) => void
  children: React.ReactNode // 👈 Cho phép truyền nút tuỳ chỉnh
}

const UploadAvatar = ({ onUploadSuccess, children }: Props) => {
  const MAX_SIZE_MB = 1

  const beforeUpload = async (file: RcFile) => {
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      message.error('Chỉ chấp nhận file ảnh!')
      return Upload.LIST_IGNORE
    }

    const isTooBig = file.size / 1024 / 1024 > MAX_SIZE_MB
    if (isTooBig) {
      message.error(`Ảnh vượt quá ${MAX_SIZE_MB}MB.`)
      return Upload.LIST_IGNORE
    }

    const previewUrl = URL.createObjectURL(file)
    onUploadSuccess(file, previewUrl)
    return Upload.LIST_IGNORE
  }

  const props: UploadProps = {
    maxCount: 1,
    showUploadList: false,
    beforeUpload,
  }

  return (
    <ImgCrop rotationSlider>
      <Upload {...props}>{children}</Upload>
    </ImgCrop>
  )
}

export default UploadAvatar
