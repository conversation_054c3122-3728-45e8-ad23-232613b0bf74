import React from 'react'
import styles from './Header.module.scss'
import { FiSearch, FiUser, FiShoppingCart, FiMenu, FiX } from 'react-icons/fi'

interface NavItem {
  name: string
  url: string
  active?: boolean
}

interface HeaderProps {
  logo: string
  navItems: NavItem[]
  cartCount: number
  onSidebarToggle?: () => void
  isSidebarOpen?: boolean
  isMobile?: boolean
  // Thêm props khác nếu cần
}

export default function Header({
  logo,
  navItems,
  cartCount,
  onSidebarToggle,
  isSidebarOpen,
  isMobile,
}: HeaderProps) {
  return (
    <header className={styles.header}>
      <div className={styles.headerContainer}>
        {isMobile && (
          <button className={styles.sidebarToggle} onClick={onSidebarToggle}>
            {isSidebarOpen ? <FiX /> : <FiMenu />}
          </button>
        )}
        <a href="/">
          <img src={logo} alt="TAP Logo" width={180} height={44} />
        </a>
        <nav className={styles.mainNav}>
          {navItems.map((item, idx) => (
            <a
              key={idx}
              href={item.url}
              className={item.active ? styles.activeNavItem : ''}
            >
              {item.name}
            </a>
          ))}
        </nav>
        <div className={styles.searchAndIcons}>
          <div className={styles.searchBar}>
            <input type="text" placeholder="Tìm kiếm sản phẩm..." />
            <FiSearch className={styles.searchIcon} />
          </div>
          <div className={styles.userActions}>
            <a href="/profile" className={styles.profileIndicator}>
              <FiUser className={styles.actionIcon} />
            </a>
            <a href="/cart" className={styles.cartIndicator}>
              <FiShoppingCart className={styles.actionIcon} />
              <span className={styles.cartBadge}>{cartCount}</span>
            </a>
          </div>
        </div>
      </div>
    </header>
  )
}
