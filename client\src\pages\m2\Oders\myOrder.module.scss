.myOrdersPage {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background-color: #f5f5f5;
}

// Container cho Tabs
.tabsContainer {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);

  :global(.ant-tabs-nav) {
    margin-bottom: 0 !important;
  }
  
  :global(.ant-tabs-nav::before) {
    border-bottom: 1px solid #f0f0f0 !important;
  }

  // Làm cho các tab trải đều ra (gi<PERSON> nguyên như cũ)
  :global(.ant-tabs-nav-list) {
    display: flex;
    width: 100%;
  }

  :global(.ant-tabs-tab) {
    flex-grow: 1;
    justify-content: center;
    margin: 0 !important;
    padding: 12px 0 !important;
  }

  // ✅ SỬA LỖI: Ẩn hoàn toàn phần khung nội dung của Tabs
  :global(.ant-tabs-content-holder) {
    display: none;
  }

  // Chỉ sửa lỗi trên màn hình nhỏ
  @media (max-width: 768px) {
    overflow-x: auto; // Cho phép scroll ngang trên màn hình nhỏ
    
    :global(.ant-tabs-nav) {
      min-width: max-content; // Đảm bảo không bị cắt
    }
    
    :global(.ant-tabs-nav-list) {
      min-width: max-content; // Đảm bảo không bị cắt
    }

    :global(.ant-tabs-tab) {
      flex: 0 0 auto; // Không co giãn, giữ kích thước cố định
      padding: 12px 16px !important; // Tăng padding để dễ click
      white-space: nowrap; // Không xuống dòng
      min-width: 120px; // Kích thước tối thiểu cho mỗi tab
    }
  }

  @media (max-width: 480px) {
    :global(.ant-tabs-tab) {
      padding: 8px 12px !important;
      min-width: 100px;
      font-size: 14px;
    }
  }
}

.searchBar {
  margin-top: 16px;
  background-color: #fff;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
}

.centeredContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.orderList {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.emptyOrders {
  background-color: #fff;
  padding: 40px;
  border-radius: 4px;
}

// --- Các style khác giữ nguyên ---
.orderCard {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  
  .headerLeft {
    display: flex;
    flex-direction: column;
    gap: 4px;
    
    .shopName { 
      font-weight: 600; 
      font-size: 16px;
      color: #1890ff;
    }
    
    .orderId { 
      font-size: 12px; 
      color: #666;
      font-family: 'Courier New', monospace;
    }
    
    .orderDate { 
      font-size: 12px; 
      color: #666;
    }
  }
  
  .statusText { 
    color: #52c41a; 
    font-size: 14px; 
    font-weight: 600;
    padding: 4px 12px;
    background-color: #f6ffed;
    border-radius: 6px;
    border: 1px solid #b7eb8f;
  }
}

.cardBody { 
  padding: 0; 
}

// === THÊM MỚI: Styles cho tóm tắt sản phẩm ===
.productSummary {
  padding: 16px 24px;
  background: linear-gradient(135deg, #f0f2f5 0%, #fafafa 100%);
  border-bottom: 1px solid #e8e8e8;
  
  .summaryHeader {
    margin-bottom: 12px;
    
    .summaryTitle {
      font-size: 14px;
      font-weight: 600;
      color: #1890ff;
    }
  }
  
  .productList {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  
  .summaryItem {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
    
    .itemNumber {
      font-size: 12px;
      color: #666;
      font-weight: 500;
      min-width: 20px;
    }
    
    .itemName {
      flex: 1;
      font-size: 13px;
      color: #333;
      font-weight: 500;
      line-height: 1.4;
    }
    
    .itemQuantity {
      font-size: 12px;
      color: #1890ff;
      background-color: #e6f7ff;
      padding: 2px 8px;
      border-radius: 12px;
      font-weight: 500;
      border: 1px solid #91d5ff;
    }
  }
}

.cardFooter {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 16px;
  padding: 16px 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-top: 1px solid #e8e8e8;
  
  .totalAmount {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    
    strong { 
      font-size: 24px; 
      color: #1890ff; 
      font-weight: 600;
    }
  }
  
  .actionButtons {
    display: flex;
    gap: 8px;
    
    :global(.ant-btn) { 
      min-width: 140px; 
      height: 36px; 
      font-size: 14px;
      padding: 4px 16px;
      border-radius: 6px;
    }
  }
}

.productItem {
    display: flex; 
    align-items: flex-start; 
    gap: 16px; 
    padding: 20px; 
    border-top: 1px solid #e8e8e8; 
    cursor: pointer;
    background-color: #ffffff;
    
    &:hover { 
      background: linear-gradient(135deg, #f0f2f5 0%, #fafafa 100%);
    }
    
    transition: all 0.3s ease;
}
.productImage { 
    width: 80px; 
    height: 80px; 
    object-fit: cover; 
    border: 1px solid #eee; 
    border-radius: 8px;
    flex-shrink: 0; 
}
.productInfo { 
    flex-grow: 1; 
    display: flex; 
    flex-direction: column; 
    gap: 6px; 
}
.productName {
    font-size: 16px; 
    color: #262626; 
    line-height: 1.4;
    font-weight: 500;
    display: -webkit-box; 
    -webkit-line-clamp: 2; 
    -webkit-box-orient: vertical; 
    overflow: hidden;
}
.productVariation { 
    font-size: 14px; 
    color: #8c8c8c;
    font-style: italic;
}
.productDetails {
    display: flex;
    flex-direction: column;
    gap: 2px;
    
    .productQuantity, .unitPrice { 
        font-size: 13px; 
        color: #595959;
    }
}
.priceInfo { 
    text-align: right; 
    display: flex;
    flex-direction: column;
    justify-content: center;
    
    .currentPrice { 
        color: #1890ff; 
        font-weight: 600;
        font-size: 16px;
    } 
}

// === THÊM MỚI: Styles cho modal đánh giá ===
.reviewProductItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
}

.reviewProductImage {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border: 1px solid #eee;
  border-radius: 4px;
}

.reviewProductInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.reviewProductName {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.reviewProductVariation {
  font-size: 14px;
  color: #666;
}

.ratingSection {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ratingText {
  color: #ffa500;
  font-weight: 500;
}

.uploadButtons {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.uploadButton {
  border: 1px dashed #d9d9d9;
  color: #ff4d4f;
  
  &:hover {
    border-color: #ff4d4f;
  }
}

// Modal product item (đã có)
.modalProductItem {
  cursor: default;
  &:hover { background-color: transparent; }
}

.modalTotalAmount {
  justify-content: flex-end;
  margin-top: 16px;
}

// Styles cho nút trong modal xem đánh giá
.reviewActionButtons {
  display: flex;
  gap: 8px;
  
  :global(.ant-btn) {
    min-width: 80px !important;
    height: 32px !important;
    font-size: 14px;
    padding: 4px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// Styles cho nút trong modal footer
:global(.ant-modal-footer) {
  .ant-btn {
    min-width: 100px;
    height: 32px;
    font-size: 14px;
    padding: 4px 16px;
  }
}

// === THÊM MỚI: Styles cho giao diện đánh giá cải thiện ===
.reviewCard {
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fafafa;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
  }
}

.reviewHeader {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 12px;
}

.reviewProductThumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.reviewContent {
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  margin-bottom: 16px;
  line-height: 1.6;
  color: #262626;
  white-space: pre-wrap;
}

.reviewImages {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.reviewImage {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.reviewActions {
  display: flex;
  gap: 8px;
  align-items: center;
}

// Responsive cho modal đánh giá
@media (max-width: 768px) {
  .reviewCard {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .reviewHeader {
    flex-direction: column;
    gap: 8px;
  }
  
  .reviewProductThumbnail {
    width: 50px;
    height: 50px;
  }
  
  .reviewImage {
    width: 60px;
    height: 60px;
  }
  
  .reviewActions {
    flex-direction: column;
    width: 100%;
    
    :global(.ant-btn) {
      width: 100%;
      min-width: unset !important;
    }
  }
}

// Responsive cho card đơn hàng
@media (max-width: 768px) {
  .cardHeader {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
    
    .headerLeft {
      width: 100%;
    }
    
    .statusText {
      align-self: flex-end;
    }
  }
  
  .productSummary {
    padding: 12px 16px;
    
    .summaryHeader .summaryTitle {
      font-size: 13px;
    }
    
    .summaryItem {
      .itemName {
        font-size: 12px;
      }
      
      .itemQuantity {
        font-size: 11px;
        padding: 1px 4px;
      }
    }
  }
  
  .productItem {
    padding: 16px;
    gap: 12px;
  }
  
  .productImage {
    width: 60px;
    height: 60px;
  }
  
  .productName {
    font-size: 14px;
  }
  
  .productDetails {
    .productQuantity, .unitPrice {
      font-size: 12px;
    }
  }
  
  .priceInfo .currentPrice {
    font-size: 14px;
  }
}
