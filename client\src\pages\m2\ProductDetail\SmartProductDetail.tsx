import React from 'react'
import { useSelector } from 'react-redux'
import type { RootState } from '@/libs/state/store'
import ProtectedProductDetail from './ProtectedProductDetail'
import ProductDetailGuest from './ProductDetailGuest'

const SmartProductDetail: React.FC = () => {
  const authUser = useSelector((state: RootState) => state.auth)
  const isAuthenticated = !!authUser?.id

  return isAuthenticated ? <ProtectedProductDetail /> : <ProductDetailGuest />
}

export default SmartProductDetail 