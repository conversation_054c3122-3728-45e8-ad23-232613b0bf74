@import '@styles/variables';

.productCard {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: $white;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .imageContainer {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s ease;
    }

    &:hover img {
      transform: scale(1.05);
    }

    .favoriteButton {
      position: absolute;
      top: 10px;
      right: 10px;
      background: $white;
      border: 1px solid $border;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 2;
    }
  }

  .productInfo {
    padding: 16px;

    .productName {
      font-size: 14px;
      color: $text;
      margin-bottom: 8px;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .productPrice {
      color: $primary;
      font-weight: 600;
      font-size: 16px;
    }
  }
}