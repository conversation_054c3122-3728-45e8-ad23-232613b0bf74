// ProductDetail.module.scss - Thêm styles cho error và QR section
.productDetailPage {
    min-height: 100vh;
    background: #f8f9fa;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    padding: 16px 0;
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
  }
  
  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    font-size: 18px;
    color: #666;
  }
  
  // Error state
  .error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    background: white;
    border-radius: 12px;
    margin: 20px 0;
    padding: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
    h2 {
      color: #333;
      margin-bottom: 16px;
    }
  
    p {
      color: #666;
      margin-bottom: 24px;
      text-align: center;
    }
  
    button {
      background: #4CAF50;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      transition: background 0.2s;
  
      &:hover {
        background: #45a049;
      }
    }
  }
  
  // Breadcrumb
  .breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #666;
  
    a {
      color: #4CAF50;
      text-decoration: none;
      transition: color 0.2s;
  
      &:hover {
        color: #2E7D32;
      }
    }
  
    span {
      color: #666;
  
      &:last-child {
        color: #333;
        font-weight: 500;
      }
    }
  }
  
  // Product Main Section
  .productMain {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    margin-bottom: 24px;
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  // Product Images - TAP Style
  .productImages {
    .imageGallery {
      display: flex;
      gap: 12px;
  
      .thumbnailList {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 72px;
  
        .thumbnailItem {
          width: 64px;
          height: 64px;
          border: 2px solid #e0e0e0;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.2s;
  
          &.active {
            border-color: #4CAF50;
            box-shadow: 0 0 0 1px rgba(76, 175, 80, 0.3);
          }
  
          &:hover {
            border-color: #4CAF50;
            transform: scale(1.05);
          }
  
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
  
      .mainImageContainer {
        position: relative;
        flex: 1;
        aspect-ratio: 1;
        border: 2px solid #e0e0e0;
        border-radius: 12px;
        overflow: hidden;
        background: white;
  
        .mainImage {
          width: 100%;
          height: 100%;
          object-fit: cover;
          cursor: zoom-in;
        }
  
        .zoomButton {
          position: absolute;
          top: 16px;
          right: 16px;
          background: rgba(76, 175, 80, 0.9);
          color: white;
          border: none;
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s;
  
          &:hover {
            background: #4CAF50;
            transform: scale(1.1);
          }
        }
  
        .imageNavButton {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          background: rgba(255, 255, 255, 0.9);
          border: none;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s;
          font-size: 18px;
          color: #4CAF50;
  
          &:hover {
            background: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            transform: translateY(-50%) scale(1.1);
          }
  
          &.prevButton {
            left: 16px;
          }
  
          &.nextButton {
            right: 16px;
          }
        }
  
        .imageCounter {
          position: absolute;
          bottom: 16px;
          right: 16px;
          background: rgba(76, 175, 80, 0.9);
          color: white;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
  }
  
  // Product Info
  .productInfo {
    .productHeader {
      margin-bottom: 20px;
  
      .productName {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin: 0 0 12px 0;
        line-height: 1.4;
      }
  
      .productRating {
        display: flex;
        align-items: center;
        justify-content: space-between;
  
        .ratingLeft {
          display: flex;
          align-items: center;
          gap: 12px;
  
          .stars {
            display: flex;
            gap: 2px;
          }
  
          .ratingText {
            font-size: 16px;
            color: #4CAF50;
            font-weight: 500;
          }
        }
  
        .ratingRight {
          display: flex;
          align-items: center;
          gap: 16px;
  
          .reviewCount,
          .soldCount {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  
    .priceSection {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      padding: 20px;
      border-radius: 12px;
      margin-bottom: 24px;
      border: 1px solid #e0e0e0;
  
      .priceContainer {
        display: flex;
        align-items: center;
        gap: 12px;
  
        .currentPrice {
          font-size: 28px;
          font-weight: 700;
          color: #4CAF50;
        }
  
        .unit {
          font-size: 16px;
          color: #666;
        }
      }
    }
  
    .variantSection {
      margin-bottom: 24px;
  
      h3 {
        font-size: 16px;
        color: #333;
        margin: 0 0 12px 0;
        font-weight: 600;
      }
  
      .variantGrid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 12px;
        max-width: 100%;

        @media (max-width: 768px) {
          grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
          gap: 8px;
        }

        .variantItem {
          border: 2px solid #e0e0e0;
          border-radius: 12px;
          padding: 12px;
          cursor: pointer;
          transition: all 0.2s;
          text-align: center;
          min-height: 120px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          width: 100%;

          @media (max-width: 768px) {
            min-height: 100px;
            padding: 8px;
          }

          &.active {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
            box-shadow: 0 0 0 1px rgba(76, 175, 80, 0.3);
          }

          &:hover {
            border-color: #4CAF50;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          }

          .variantImage {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 8px;
            margin: 0 auto 8px auto;
            flex-shrink: 0;

            @media (max-width: 768px) {
              width: 40px;
              height: 40px;
              margin-bottom: 6px;
            }
          }

          .variantInfo {
            display: flex;
            flex-direction: column;
            gap: 4px;
            flex: 1;
            justify-content: space-between;

            .variantName {
              font-size: 12px;
              color: #333;
              font-weight: 500;
              line-height: 1.3;
              word-wrap: break-word;
              overflow-wrap: break-word;
              margin-bottom: 4px;

              @media (max-width: 768px) {
                font-size: 11px;
              }
            }

            .variantPrice {
              font-size: 12px;
              color: #4CAF50;
              font-weight: 600;
              margin-top: auto;

              @media (max-width: 768px) {
                font-size: 11px;
              }
            }
          }
        }
      }
    }
  
    .quantitySection {
      margin-bottom: 24px;
  
      h3 {
        font-size: 16px;
        color: #333;
        margin: 0 0 12px 0;
        font-weight: 600;
      }
  
      .quantityControls {
        display: flex;
        align-items: center;
        gap: 16px;
  
        .quantitySelector {
          display: flex;
          align-items: center;
          border: 2px solid #e0e0e0;
          border-radius: 8px;
          overflow: hidden;
  
          .quantityButton {
            background: #f5f5f5;
            border: none;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 16px;
            font-weight: 600;
            color: #4CAF50;
  
            &:hover {
              background: #4CAF50;
              color: white;
            }
          }
  
          .quantityInput {
            width: 60px;
            height: 40px;
            border: none;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            outline: none;
            background: white;
            color: #333;
          }
        }
  
        .stockInfo {
          font-size: 14px;
          color: #4CAF50;
          font-weight: 500;
        }
      }
    }
  
    .actionSection {
      .actionButtons {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
  
        .addToCartButton {
          flex: 1;
          background: white;
          color: #4CAF50;
          border: 2px solid #4CAF50;
          padding: 14px 24px;
          border-radius: 8px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          transition: all 0.2s;
  
          &:hover:not(:disabled) {
            background: #4CAF50;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
          }
  
          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
  
        .buyNowButton {
          flex: 1;
          background: #4CAF50;
          color: white;
          border: 2px solid #4CAF50;
          padding: 14px 24px;
          border-radius: 8px;
          cursor: pointer;
          font-size: 16px;
          font-weight: 600;
          transition: all 0.2s;
  
          &:hover:not(:disabled) {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
          }
  
          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }
  
      .secondaryActions {
        display: flex;
        gap: 12px;
  
        .favoriteButton,
        .shareButton {
          background: white;
          border: 2px solid #e0e0e0;
          padding: 10px 20px;
          border-radius: 8px;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: #666;
          transition: all 0.2s;
  
          &:hover {
            border-color: #2196F3;
            color: #2196F3;
            transform: translateY(-2px);
          }
        }
  
        .favoriteButton:hover {
          border-color: #FF5722;
          color: #FF5722;
        }
  
        // ✅ SỬA CSS: Đảm bảo trạng thái favorited hoạt động đúng
        .favoriteButton.favorited {
          border-color: #FF1744 !important;
          color: #FF1744 !important;
          background: #fff0f3 !important;
        }
        .favoriteButton.favorited:hover {
          border-color: #D50000 !important;
          color: #D50000 !important;
          background: #ffeaea !important;
        }
        .favoriteButton.favorited svg {
          color: #FF1744 !important;
          fill: #FF1744 !important;
        }
        .favoriteButton svg {
          color: #666 !important;
          fill: #666 !important;
          transition: color 0.2s, fill 0.2s;
        }
      }
    }
  
    // QR Code Section
    .qrSection {
      margin-top: 24px;
      padding: 20px;
      background: #f9f9f9;
      border-radius: 12px;
      border: 1px solid #e0e0e0;
  
      h3 {
        font-size: 16px;
        color: #333;
        margin: 0 0 16px 0;
        font-weight: 600;
      }
  
      .qrCode {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
  
        img {
          width: 120px;
          height: 120px;
          margin-bottom: 12px;
          border-radius: 8px;
        }
  
        p {
          font-size: 14px;
          color: #666;
          margin: 0;
        }
      }
    }
  }
  
  // Stars component
  .star {
    background: none;
    border: none;
    cursor: default;
    padding: 0;
    margin: 0;
    font-size: 14px;
    color: #FFC107;
  
    &.interactive {
      cursor: pointer;
      transition: all 0.2s;
  
      &:hover {
        transform: scale(1.3);
        color: #FF8F00;
      }
    }
  }
  
  // Product Attributes
  .productAttributes {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
    h2 {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0 0 20px 0;
      padding-bottom: 12px;
      border-bottom: 2px solid #4CAF50;
    }
  
    .attributeGrid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
  
      .attributeItem {
        display: flex;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
  
        &:last-child {
          border-bottom: none;
        }
  
        .attributeName {
          flex: 1;
          font-size: 14px;
          color: #666;
          font-weight: 500;
        }
  
        .attributeValue {
          flex: 1;
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }
      }
    }
  }
  
  // Product Description
  .productDescription {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
    h2 {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0 0 20px 0;
      padding-bottom: 12px;
      border-bottom: 2px solid #4CAF50;
    }
  
    .descriptionContent {
      font-size: 16px;
      color: #666;
      line-height: 1.6;
  
      p {
        margin: 0;
      }
    }
  }
  
  // Comments Section
  .commentsSection {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
    h2 {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0 0 20px 0;
      padding-bottom: 12px;
      border-bottom: 2px solid #4CAF50;
    }
  
    .ratingOverview {
      background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
      padding: 20px;
      border-radius: 12px;
      margin-bottom: 24px;
      border: 1px solid rgba(76, 175, 80, 0.2);
  
      .ratingScore {
        display: flex;
        align-items: center;
        gap: 16px;
  
        .scoreNumber {
          font-size: 32px;
          font-weight: 700;
          color: #4CAF50;
        }
  
        .scoreStars {
          display: flex;
          gap: 4px;
        }
  
        .totalReviews {
          font-size: 16px;
          color: #666;
          font-weight: 500;
        }
      }
    }
  
    .addCommentForm {
      border: 2px solid #e0e0e0;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 24px;
      background: #f9f9f9;
  
      h3 {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 16px 0;
        color: #333;
      }
  
      .ratingInput {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
  
        span {
          font-size: 16px;
          color: #666;
          font-weight: 500;
        }
  
        .stars {
          display: flex;
          gap: 4px;
        }
      }
  
      .commentInput {
        width: 100%;
        min-height: 100px;
        padding: 16px;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        font-size: 16px;
        font-family: inherit;
        resize: vertical;
        margin-bottom: 16px;
        outline: none;
        transition: border-color 0.2s;
  
        &:focus {
          border-color: #4CAF50;
        }
  
        &::placeholder {
          color: #999;
        }
      }
  
      .submitCommentButton {
        background: #4CAF50;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.2s;
  
        &:hover {
          background: #45a049;
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
      }
    }
  
    .commentsList {
      .commentItem {
        border-bottom: 1px solid #f0f0f0;
        padding: 20px 0;
  
        &:last-child {
          border-bottom: none;
        }
  
        .commentHeader {
          display: flex;
          gap: 16px;
          margin-bottom: 12px;
  
          .userAvatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #e0e0e0;
          }
  
          .commentMeta {
            flex: 1;
  
            h4 {
              font-size: 16px;
              font-weight: 600;
              color: #333;
              margin: 0 0 8px 0;
            }
  
            .commentRating {
              display: flex;
              gap: 2px;
              margin-bottom: 6px;
  
              .star {
                font-size: 14px;
              }
            }
  
            .commentDate {
              font-size: 12px;
              color: #999;
            }
          }
        }
  
        .commentText {
          font-size: 16px;
          color: #333;
          line-height: 1.6;
          margin: 0;
          padding-left: 64px;
        }
      }
    }
  }
  
  // Image Modal
  .imageModal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  
    .modalContent {
      position: relative;
      max-width: 90vw;
      max-height: 90vh;
  
      .closeModal {
        position: absolute;
        top: -60px;
        right: 0;
        background: rgba(76, 175, 80, 0.9);
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        padding: 12px;
        border-radius: 50%;
        transition: all 0.2s;
  
        &:hover {
          background: #4CAF50;
          transform: scale(1.1);
        }
      }
  
      .modalImage {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 12px;
      }
    }
  }
  
  // Responsive
  @media (max-width: 1024px) {
    .productMain {
      grid-template-columns: 1fr;
      gap: 24px;
    }
  
    .productImages .imageGallery {
      flex-direction: column;
      
      .thumbnailList {
        flex-direction: row;
        width: 100%;
        overflow-x: auto;
        padding-bottom: 8px;
      }
    }
  }
  
  @media (max-width: 768px) {
    .container {
      padding: 0 12px;
    }
  
    .productMain {
      padding: 16px;
    }
  
    .productInfo {
      .productName {
        font-size: 20px;
      }
  
      .priceSection .priceContainer .currentPrice {
        font-size: 24px;
      }
  
      .variantSection .variantGrid {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
      }
  
      .actionSection .actionButtons {
        flex-direction: column;
        gap: 8px;
      }
    }
  
    .productAttributes .attributeGrid {
      grid-template-columns: 1fr;
    }
  }
  