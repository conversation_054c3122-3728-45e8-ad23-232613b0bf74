import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../../m1/user/entities/user.entity';
import { Product } from './products.entity';
import { ProductVersion } from './product_version.entity';

@Entity('cart_items')
export class CartItem {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  user_id: string;
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column('uuid')
  product_id: string;
  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'product_id' })
  product: Product;

  @Column({ type: 'int' })
  quantity: number;

  @Column({ type: 'decimal', precision: 12, scale: 2 })
  unit_price: string;

  @Column('uuid', { nullable: true })
  version_id?: string;
  @ManyToOne(() => ProductVersion, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'version_id' })
  version?: ProductVersion;

  @Column({ type: 'jsonb', nullable: true })
  selected_options?: any;

  @Column({ type: 'boolean', default: false })
  is_checked: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;
}
