import { useQrMutation, type QrResponse } from '@/services/M1/Qr/qr'
import { useToast } from '../../../components/Toast'
import { useEffect } from 'react'
import { useLoadingStore } from '@/utils/loadingStore'
import { getCookie } from '@/private/cookies'
import { data } from 'react-router-dom'

// Dùng cho POST, PUT, DELETE
const useGetQrHook = () => {
  const [getQrMutation] = useQrMutation() // mutation trả QR code

  const getQr = async (): Promise<QrResponse | null> => {
    try {
      const res = await getQrMutation(null).unwrap()
      return res || null
    } catch (error: any) {
      console.error('Lỗi khi lấy QR:', error)
      return null
    }
  }

  return { getQr }
}

// Xuất ra
export { useGetQrHook }
