import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, Jo<PERSON><PERSON><PERSON>umn } from 'typeorm';
import { User } from '../../user/entities/user.entity';

@Entity('user_affiliates')
export class UserAffiliate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // ID của người dùng được gắn hệ thống tuyến
  @Column()
  userId: string;

  @ManyToOne(() => User, (user) => user.affiliates, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  // Người giới thiệu trực tiếp (F1)
  @Column({ nullable: true })
  referrerId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'referrerId' })
  referrer: User;

  // <PERSON><PERSON><PERSON><PERSON> hệ thống từ F2–F5
  @Column({ nullable: true, type: 'varchar' })
  level_1: string;

  @Column({ nullable: true, type: 'varchar' })
  level_2: string;

  @Column({ nullable: true, type: 'varchar' })
  level_3: string;

  @Column({ nullable: true, type: 'varchar' })
  level_4: string;

  @Column({ nullable: true, type: 'varchar' })
  level_5: string;
}
