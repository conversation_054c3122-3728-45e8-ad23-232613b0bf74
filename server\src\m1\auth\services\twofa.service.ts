import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
  Inject,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

import { User } from '../../user/entities/user.entity';
import { OtpVerification } from '../../user/entities/otp_verification.entity';
import { RegisterDto } from '../dto/register.dto';
import { LoginDto } from '../dto/login.dto';
import { AuthResponse, Login2FARequire } from '../dto/auth-response.dto';
import { MailService } from 'src/mail/mail.service';
import { Role } from '../../user/entities/role.entity';
import { UserProfile } from '../../user/entities/user_profile.entity';
import { UserAffiliateService } from '../../user/services/user_affiliate.service';
import { SocialLoginDto } from '../dto/social-login.dto';
import axios from 'axios';
import { UserProvider } from '../../user/entities/user-provider.entity';
import { ConfigService } from '@nestjs/config';
import * as speakeasy from 'speakeasy';
import * as qrcode from 'qrcode';
import { TotpVerifyDto } from '../dto/totp-verify.dto';
import { User2FASettings } from '../../user/entities/user_2fa_settings.entity';
import { VerifyOtpDto } from '../dto/verify-otp.dto';
import { SendOtpDto } from '../dto/send-otp.dto';
import { VerifyLogin2FADto } from '../dto/verify-login-2fa.dto';
import { AuthSession } from '../../user/entities/auth_sessions.entity';
import e, { Request } from 'express';
import { AuthSessionResponse } from '../dto/auth-session-response.dto';
import { ClientProxy } from '@nestjs/microservices';
import { RoleGroup } from '../../user/entities/role_groups.entity';
import { RoleGroupPermission } from '../../user/entities/role_group_permissions.entity';
import { OtpService } from './otp.service';
import { SessionService } from './session.service';
import { TokenService } from './token.service';

@Injectable()
export class TwofaService {
  constructor(
    @InjectRepository(User) private readonly userRepo: Repository<User>,
    @InjectRepository(OtpVerification) private readonly otpRepo: Repository<OtpVerification>,
    @InjectRepository(User2FASettings) private readonly user2FARepo: Repository<User2FASettings>,
    private readonly mailService: MailService,
    @Inject('LOG_SERVICE')
    private readonly logClient: ClientProxy,
    private readonly otpService: OtpService,
    private readonly sessionService: SessionService,
    private readonly tokenService: TokenService,
  ) {}

  async sendOtp2FA(dto: SendOtpDto) {
    const otpCode = this.otpService.generateOtpCode(); // sử dụng lại hàm tạo OTP giống forgot password

    const otp = this.otpRepo.create({
      target: dto.target,
      otpCode,
      expiredAt: new Date(Date.now() + 5 * 60 * 1000), // 5 phút
      status: 'valid',
      isVerified: false,
    });

    await this.otpRepo.save(otp);

    // ✅ Gửi OTP qua email (tận dụng lại mailService.sendOtpEmail)
    if (dto.target.includes('@')) {
      const user = await this.userRepo.findOne({
        where: [{ email: dto.target }, { phone: dto.target }],
        relations: ['profile'],
      });
      const name = user?.profile?.fullName || 'bạn';

      await this.mailService.sendOtpEmail2FA(dto.target, name, otpCode);
    } else {
      // TODO: Gửi SMS nếu là số điện thoại
    }

    return { message: 'Đã gửi mã xác thực OTP 2FA' };
  }

  // verifyOtp2FA(dto): xác minh mã OTP xác thực 2FA
  async verifyOtp2FA(dto: VerifyOtpDto) {
    const otp = await this.otpRepo.findOne({
      where: { target: dto.target, otpCode: dto.otp, status: 'valid' },
    });

    if (!otp || new Date() > otp.expiredAt) {
      throw new UnauthorizedException('Mã OTP không hợp lệ hoặc đã hết hạn');
    }

    otp.status = 'used';
    otp.isVerified = true;
    await this.otpRepo.save(otp);

    const user = await this.userRepo.findOne({
      where: [{ email: dto.target }, { phone: dto.target }],
    });
    if (!user) throw new NotFoundException('Không tìm thấy người dùng');

    const setting = await this.user2FARepo.findOne({ where: { user_id: user.id } });
    if (setting) {
      setting.is_otp_enabled = true;
      setting.verified_at = new Date();
      await this.user2FARepo.save(setting);
    } else {
      await this.user2FARepo.save({
        user_id: user.id,
        is_otp_enabled: true,
        verified_at: new Date(),
      });
    }

    await this.mailService.send2FAActived(dto.target, 'bạn');

    return { message: 'Xác minh OTP 2FA thành công' };
  }

  /**
   * Xác minh mã OTP trước khi tắt 2FA OTP
   */
  async confirmDisableOtp(dto: VerifyOtpDto): Promise<void> {
    const otp = await this.otpRepo.findOne({
      where: { target: dto.target, otpCode: dto.otp, status: 'valid' },
    });

    if (!otp || new Date() > otp.expiredAt) {
      throw new UnauthorizedException('Mã OTP không hợp lệ hoặc đã hết hạn');
    }

    otp.status = 'used';
    otp.isVerified = true;
    await this.otpRepo.save(otp);

    const user = await this.userRepo.findOne({ where: { email: dto.target } });
    if (user) {
      await this.user2FARepo.update({ user_id: user.id }, { is_otp_enabled: false });

      this.logClient.emit('log.update', {
        module: 'User',
        action: 'DISABLE_2FA',
        performedBy: user.id,
        targetId: user.id,
        description: `Người dùng đã tắt 2FA OTP`,
        timestamp: new Date().toISOString(),
        data: {
          before: { otp_enabled: true },
          after: { otp_enabled: false },
        },
      });
    }
  }

  // disable2FAMethod(userId, method): tắt OTP hoặc TOTP
  async sendOtpToDisable2FA(userId: string): Promise<{ message: string }> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: ['profile'],
    });

    if (!user || (!user.email && !user.phone)) {
      throw new NotFoundException('Không tìm thấy người dùng hoặc không có thông tin liên hệ');
    }

    const target = user.email ?? user.phone;
    const fullName = user?.profile?.fullName || 'bạn';
    const otpCode = this.otpService.generateOtpCode();

    const otp = this.otpRepo.create({
      target,
      otpCode,
      expiredAt: new Date(Date.now() + 5 * 60 * 1000),
      status: 'valid',
      isVerified: false,
    });

    await this.otpRepo.save(otp);

    if (target.includes('@')) {
      await this.mailService.sendOtpEmailDisable2FA(target, fullName, otpCode);
    } else {
      // TODO: Gửi SMS nếu cần
    }

    return { message: 'Đã gửi mã OTP xác minh để tắt 2FA' };
  }

  // setupTotp(userId): tạo secret_key và mã QR
  async setupTotp(userId: string) {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: ['twoFactorSettings'],
    });
    if (!user) throw new NotFoundException('User không tồn tại');

    const secret = speakeasy.generateSecret({ name: `TAP (${user.email || user.phone})` });
    const qr = await qrcode.toDataURL(secret.otpauth_url);

    const setting = user.twoFactorSettings || this.user2FARepo.create({ user_id: user.id });
    setting.totp_secret_key = secret.base32;
    await this.user2FARepo.save(setting);

    return { qrCode: qr, secret: secret.base32 };
  }

  // verifyTotp(dto): xác minh mã TOTP nhập vào
  async verifyTotp(dto: TotpVerifyDto) {
    const setting = await this.user2FARepo.findOne({ where: { user_id: dto.userId } });
    if (!setting || !setting.totp_secret_key) throw new BadRequestException('Chưa thiết lập TOTP');

    const verified = speakeasy.totp.verify({
      secret: setting.totp_secret_key,
      encoding: 'base32',
      token: dto.token,
      window: 1,
    });

    if (!verified) throw new UnauthorizedException('Mã TOTP không hợp lệ');

    setting.is_totp_enabled = true;
    setting.verified_at = new Date();
    await this.user2FARepo.save(setting);

    return { message: 'Xác minh TOTP thành công' };
  }

  // ✅ Xác minh OTP hoặc TOTP khi đăng nhập (sau khi login username/password thành công)
  async verifyLogin2FA(dto: VerifyLogin2FADto, request: Request): Promise<AuthResponse> {
    const user = await this.userRepo.findOne({ where: { id: dto.userId }, relations: ['profile'] });
    if (!user) throw new NotFoundException('Không tìm thấy người dùng');

    const setting = await this.user2FARepo.findOne({ where: { user_id: user.id } });
    if (!setting) throw new UnauthorizedException('Tài khoản chưa bật xác thực 2 yếu tố');

    if (setting.is_totp_enabled && dto.totpCode) {
      const isValid = this.verifyTotp({
        userId: dto.userId,
        token: dto.totpCode,
      });
      if (!isValid) throw new UnauthorizedException('Mã xác thực TOTP không hợp lệ');
    } else if (setting.is_otp_enabled) {
      const otp = await this.otpRepo.findOne({
        where: { target: user.email || user.phone, otpCode: dto.otpCode, status: 'valid' },
      });
      if (!otp || new Date() > otp.expiredAt) {
        throw new UnauthorizedException('Mã OTP không hợp lệ hoặc đã hết hạn');
      }
      otp.status = 'used';
      otp.isVerified = true;
      await this.otpRepo.save(otp);
    } else {
      throw new UnauthorizedException('Tài khoản chưa bật OTP hoặc TOTP');
    }

    // ✅ Gửi mail cảnh báo đăng nhập thành công bằng 2FA
    if (user.email) {
      const fullName = user?.profile?.fullName || 'bạn';
      await this.mailService.login2FA(user.email, fullName);
    }

    // ✅ Sau khi xác minh đúng → sinh token
    const tokens = await this.tokenService.generateTokens(user);

    await this.sessionService.checkUnrecognizedDevice(user, request);

    return {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
    };
  }
}
