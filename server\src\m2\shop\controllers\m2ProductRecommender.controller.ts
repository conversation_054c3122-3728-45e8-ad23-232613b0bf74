import { Controller, Get, Param, Query } from '@nestjs/common';
import { M2ProductRecommenderService } from '../services/m2ProductRecommender.service';

@Controller('m2-recommender')
export class M2ProductRecommenderController {
  constructor(private readonly recommender: M2ProductRecommenderService) {}

  // API gợi ý sản phẩm cho user
  @Get('suggest-products/:user_id')
  async suggestProducts(@Param('user_id') user_id: string, @Query('topN') topN?: string) {
    const n = topN ? parseInt(topN, 10) : 5;
    return this.recommender.suggestProductsForUser(user_id, n);
  }
}
