import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../../m1/user/entities/user.entity';
import { Order } from './order.entity';

export enum TaxType {
  VAT = 'VAT', // Thuế GTGT
  PERSONAL_INCOME = 'PERSONAL_INCOME', // Thuế TNCN
  CORPORATE_INCOME = 'CORPORATE_INCOME', // Thuế TNDN
  INVESTMENT_INCOME = 'INVESTMENT_INCOME', // Thuế thu nhập từ đầu tư
  COOPERATIVE = 'COOPERATIVE', // Thuế HTX
}

export enum UserRole {
  R01 = 'R01', // Thành viên HTX – sản xuất nông nghiệp
  R02 = 'R02', // <PERSON><PERSON><PERSON><PERSON> bán hàng TMĐT (h<PERSON>, DN nhỏ)
  R03 = 'R03', // Người tiêu dùng / khách mua hàng
  R04 = 'R04', // Chuyên gia – <PERSON><PERSON><PERSON> – Giảng viên
  R05 = 'R05', // Quản lý nội bộ, admin hệ thống
  R06 = 'R06', // Tài xế, người giao hàng
  R07 = 'R07', // Thành viên góp vốn (tổ chức/cá nhân)
  R08 = 'R08', // Ban điều hành HTX
}

@Entity('tax_calculations')
@Index(['user_id', 'calculation_period'])
@Index(['calculation_period', 'tax_type'])
export class TaxCalculation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  @Index()
  user_id: string;

  @ManyToOne(() => User, { nullable: false, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'uuid', nullable: true })
  order_id?: string;

  @ManyToOne(() => Order, { nullable: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'order_id' })
  order?: Order;

  @Column({
    type: 'enum',
    enum: UserRole,
    comment: 'Vai trò người dùng theo chính sách thuế TAP',
  })
  user_role: UserRole;

  @Column({
    type: 'enum',
    enum: TaxType,
    comment: 'Loại thuế áp dụng',
  })
  tax_type: TaxType;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: 'Doanh thu chịu thuế',
  })
  taxable_revenue: number;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 4,
    comment: 'Thuế suất áp dụng (0.0000 -> 1.0000)',
  })
  tax_rate: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: 'Số tiền thuế phải nộp',
  })
  tax_amount: number;

  @Column({
    type: 'varchar',
    length: 7,
    comment: 'Kỳ tính thuế (YYYY-MM hoặc YYYY-QQ)',
  })
  @Index()
  calculation_period: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Đã nộp thuế chưa',
  })
  is_paid: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Có được HTX đại diện không',
  })
  htx_coverage: boolean;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: 'Mã khu vực (province_code)',
  })
  region_code?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Ghi chú về cách tính thuế',
  })
  calculation_note?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Metadata bổ sung (threshold info, exemptions, etc.)',
  })
  metadata?: any;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
