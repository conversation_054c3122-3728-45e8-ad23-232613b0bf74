import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { DataSource } from 'typeorm';
import { seedExtendedRBAC } from './seeds/m1/extended-role.seed';

async function runExtendedRoleSeed() {
  console.log('🚀 Starting Extended Role Seeding...');

  try {
    const app = await NestFactory.createApplicationContext(AppModule);
    const dataSource = app.get(DataSource);

    await seedExtendedRBAC(dataSource);

    console.log('✅ Extended Role Seeding completed successfully!');
    await app.close();
  } catch (error) {
    console.error('❌ Extended Role Seeding failed:', error);
    process.exit(1);
  }
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  runExtendedRoleSeed();
}

export { runExtendedRoleSeed };
