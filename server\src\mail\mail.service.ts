import { Injectable } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';

@Injectable()
export class MailService {
  constructor(private readonly mailerService: MailerService) {}

  async sendWelcomeEmail(toEmail: string, fullName: string, uid: string): Promise<void> {
    await this.mailerService.sendMail({
      to: toEmail,
      subject: '🎉 Chào mừng đến với TAP – Hệ sinh thái số cộng đồng',
      template: 'email/welcome',
      context: {
        fullName,
        uid,
      },
    });
  }

  async sendOtpEmail(email: string, fullName: string, otpCode: string) {
    await this.mailerService.sendMail({
      to: email,
      subject: '📩 Mã xác minh tài khoản TAP',
      template: 'email/verify-email',
      context: {
        fullName,
        otpCode,
      },
    });
  }

  async sendResetPasswordEmail(email: string, fullName: string, otpCode: string) {
    await this.mailerService.sendMail({
      to: email,
      subject: '🔐 <PERSON>ã khôi phục mật khẩu TAP',
      template: 'email/reset-password',
      context: {
        fullName,
        otpCode,
      },
    });
  }

  async sendOtpEmail2FA(email: string, fullName: string, otpCode: string) {
    await this.mailerService.sendMail({
      to: email,
      subject: 'Xác thực 2 bước – TAP',
      template: 'email/otp-2fa',
      context: { fullName, otpCode },
    });
  }

  async send2FAActived(email: string, fullName: string) {
    await this.mailerService.sendMail({
      to: email,
      subject: 'Xác thực 2 bước – TAP',
      template: 'email/2fa-activated',
      context: { fullName },
    });
  }

  async login2FA(email: string, fullName: string) {
    await this.mailerService.sendMail({
      to: email,
      subject: 'Xác nhận đăng nhập qua 2FA',
      template: 'email/2fa-login-success',
      context: { fullName },
    });
  }

  async waringLoginNewDevice(email: string, fullName: string, ip: string, userAgent: string) {
    await this.mailerService.sendMail({
      to: email,
      subject: 'Cảnh báo đăng nhập trên thiết bị lạ',
      template: 'email/unrecognized-device-login',
      context: { fullName, ip, userAgent },
    });
  }

  async sendOtpEmailDisable2FA(email: string, fullName: string, otpCode: string) {
    await this.mailerService.sendMail({
      to: email,
      subject: 'Xác nhận tắt xác thực 2 bước - TAP',
      template: 'email/otp-2fa',
      context: { fullName, otpCode },
    });
  }
}
