// src/m2/shop/controllers/status.controller.ts
import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Status } from '../entities/status.entity';
import { StatusService } from '../services/status.service';

@ApiTags('Status')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
@Controller('status')
export class StatusController {
  constructor(private readonly statusService: StatusService) {}

  /**
   * 📄 L<PERSON>y danh sách trạng thái theo nhóm (PRODUCT, ORDER, USER...)
   * @param type Nhóm trạng thái cần lấy
   * @returns Danh sách trạng thái theo nhóm
   */
  @Get()
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách status theo nhóm' })
  @ApiQuery({ name: 'type', enum: ['PRODUCT', 'ORDER', 'PAYMENT', 'IMAGE'], required: true })
  @ApiResponse({ status: 200, type: [Status] })
  async getStatusByType(@Query('type') type: string): Promise<Status[]> {
    return this.statusService.getStatusByType(type);
  }
}
