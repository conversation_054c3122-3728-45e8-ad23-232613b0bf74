import React from 'react';
import Button from '../Button';
import styles from './ModalButtonGroup.module.scss';

export interface ModalButtonGroupProps {
  onCancel?: () => void;
  onConfirm?: () => void;
  onSecondary?: () => void;
  cancelText?: string;
  confirmText?: string;
  secondaryText?: string;
  confirmLoading?: boolean;
  cancelLoading?: boolean;
  secondaryLoading?: boolean;
  disabled?: boolean;
  showCancel?: boolean;
  showConfirm?: boolean;
  showSecondary?: boolean;
  confirmVariant?: 'primary' | 'default' | 'danger' | 'success' | 'warning';
  cancelVariant?: 'primary' | 'default' | 'danger' | 'success' | 'warning';
  secondaryVariant?: 'primary' | 'default' | 'danger' | 'success' | 'warning';
  className?: string;
}

const ModalButtonGroup: React.FC<ModalButtonGroupProps> = ({
  onCancel,
  onConfirm,
  onSecondary,
  cancelText = 'Hủy',
  confirmText = 'Xác nhận',
  secondaryText = 'Thao tác khác',
  confirmLoading = false,
  cancelLoading = false,
  secondaryLoading = false,
  disabled = false,
  showCancel = true,
  showConfirm = true,
  showSecondary = false,
  confirmVariant = 'primary',
  cancelVariant = 'default',
  secondaryVariant = 'default',
  className = '',
}) => {
  return (
    <div className={`${styles.modalButtonGroup} ${className}`}>
      {showCancel && (
        <Button
          variant={cancelVariant}
          onClick={onCancel}
          loading={cancelLoading}
          disabled={disabled}
          size="middle"
        >
          {cancelText}
        </Button>
      )}
      
      {showSecondary && (
        <Button
          variant={secondaryVariant}
          onClick={onSecondary}
          loading={secondaryLoading}
          disabled={disabled}
          size="middle"
        >
          {secondaryText}
        </Button>
      )}
      
      {showConfirm && (
        <Button
          variant={confirmVariant}
          onClick={onConfirm}
          loading={confirmLoading}
          disabled={disabled}
          size="middle"
        >
          {confirmText}
        </Button>
      )}
    </div>
  );
};

export default ModalButtonGroup; 