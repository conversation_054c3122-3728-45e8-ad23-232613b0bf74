import { useEffect } from 'react'
import {
  useGetShopInventoryQuery,
  useGetShopInventoryDetailQuery,
  useUpdateShopVariantsMutation,
  useQuickUpdateShopStockMutation,
  useGetShopInventoryHistoryQuery,
  useImportShopInventoryMutation,
  useExportShopInventoryQuery,
  useGetShopAlertsQuery,
} from '../../services/M2/InventoryManagement/InventoryShop/inventoryShop'
import { useToast } from '../../components/Toast'
import { useLoadingStore } from '@/utils/loadingStore'

// Hook lấy danh sách tồn kho shop
const useGetShopInventoryHook = (params: any) => {
  const { data, error, isLoading, refetch, isFetching } = useGetShopInventoryQuery(params)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy chi tiết tồn kho shop
const useGetShopInventoryDetailHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetShopInventoryDetailQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true
  })
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy lịch sử tồn kho shop
const useGetShopInventoryHistoryHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetShopInventoryHistoryQuery(id, {
    skip: !id
  })
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook lấy cảnh báo tồn kho shop
const useGetShopAlertsHook = () => {
  const { data, error, isLoading, refetch, isFetching } = useGetShopAlertsQuery()
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook export tồn kho shop
const useExportShopInventoryHook = (params: any) => {
  const { data, error, isLoading, refetch, isFetching } = useExportShopInventoryQuery(params)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}

// Hook cập nhật variants tồn kho
const useUpdateShopVariantsHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useUpdateShopVariantsMutation()

  const update = async (id: string, variants: any[]): Promise<boolean> => {
    try {
      const res = await updateFunc({ id, variants })
      if (!res?.error) {
        showSuccess('Cập nhật variants thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Cập nhật variants thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Cập nhật variants thất bại' + e)
      return false
    }
  }

  return update
}

// Hook cập nhật nhanh tồn kho
const useQuickUpdateShopStockHook = () => {
  const { showSuccess, showError } = useToast()
  const [updateFunc] = useQuickUpdateShopStockMutation()

  const update = async (id: string, variantId: string, change: number): Promise<boolean> => {
    try {
      const res = await updateFunc({ id, variantId, change })
      if (!res?.error) {
        showSuccess('Cập nhật tồn kho thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Cập nhật tồn kho thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Cập nhật tồn kho thất bại' + e)
      return false
    }
  }

  return update
}

// Hook import tồn kho
const useImportShopInventoryHook = () => {
  const { showSuccess, showError } = useToast()
  const [importFunc] = useImportShopInventoryMutation()

  const importInventory = async (file: File): Promise<any> => {
    const formData = new FormData()
    formData.append('file', file)

    try {
      const res = await importFunc(formData)
      if (!res?.error) {
        showSuccess('Import tồn kho thành công')
        return res.data
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Import tồn kho thất bại ' + messageDes)
        return null
      }
    } catch (e) {
      showError('Import tồn kho thất bại' + e)
      return null
    }
  }

  return importInventory
}

// Xuất ra
export {
  useGetShopInventoryHook,
  useGetShopInventoryDetailHook,
  useGetShopInventoryHistoryHook,
  useGetShopAlertsHook,
  useExportShopInventoryHook,
  useUpdateShopVariantsHook,
  useQuickUpdateShopStockHook,
  useImportShopInventoryHook,
}
