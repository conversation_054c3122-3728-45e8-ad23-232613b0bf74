import React, { useState } from 'react'
import { Card, Typography, Collapse, Alert, Space, Button } from 'antd'
import { 
  EnvironmentOutlined, 
  InfoCircleOutlined,
  ChromeOutlined,
  GlobalOutlined,
  WindowsOutlined,
  AppleOutlined
} from '@ant-design/icons'

const { Text, Title } = Typography
const { Panel } = Collapse

interface LocationPermissionGuideProps {
  onClose: () => void
}

const LocationPermissionGuide: React.FC<LocationPermissionGuideProps> = ({ onClose }) => {
  const [activeKey, setActiveKey] = useState<string | string[]>(['chrome'])

  const browserGuides = [
    {
      key: 'chrome',
      title: 'Google Chrome',
      icon: <ChromeOutlined />,
      steps: [
        'Nhấn vào biểu tượng khóa 🔒 ở thanh địa chỉ',
        '<PERSON><PERSON><PERSON> "Cho phép" trong phần "Vị trí"',
        'Hoặc vào Cài đặt > <PERSON><PERSON><PERSON> mật và quyền riêng tư > Vị trí',
        '<PERSON><PERSON><PERSON> "Cho phép các trang web yêu cầu vị trí của bạn"'
      ]
    },
    {
      key: 'firefox',
      title: 'Mozilla Firefox',
      icon: <WindowsOutlined />,
      steps: [
        'Nhấn vào biểu tượng khóa 🔒 ở thanh địa chỉ',
        'Chọn "Cho phép" trong phần "Vị trí"',
        'Hoặc vào Cài đặt > Quyền riêng tư & Bảo mật > Vị trí',
        'Chọn "Luôn hỏi" hoặc "Cho phép"'
      ]
    },
    {
      key: 'safari',
      title: 'Safari',
      icon: <AppleOutlined />,
      steps: [
        'Nhấn vào biểu tượng khóa 🔒 ở thanh địa chỉ',
        'Chọn "Cho phép" trong phần "Vị trí"',
        'Hoặc vào Safari > Tùy chọn > Trang web > Vị trí',
        'Chọn "Cho phép" cho trang web này'
      ]
    },
    {
      key: 'edge',
      title: 'Microsoft Edge',
      icon: <GlobalOutlined />,
      steps: [
        'Nhấn vào biểu tượng khóa 🔒 ở thanh địa chỉ',
        'Chọn "Cho phép" trong phần "Vị trí"',
        'Hoặc vào Cài đặt > Cookie và quyền trang web > Vị trí',
        'Bật "Cho phép các trang web yêu cầu vị trí của bạn"'
      ]
    }
  ]

  return (
    <Card 
      title={
        <Space>
          <EnvironmentOutlined />
          <span>Hướng dẫn cấp quyền truy cập vị trí</span>
        </Space>
      }
      extra={<Button type="text" onClick={onClose}>✕</Button>}
      style={{ marginBottom: 16 }}
    >
      <Alert
        message="Thông tin quan trọng"
        description="Chức năng lấy vị trí yêu cầu quyền truy cập từ trình duyệt. Vui lòng làm theo hướng dẫn bên dưới để cấp quyền."
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Collapse 
        activeKey={activeKey} 
        onChange={setActiveKey}
        ghost
      >
        {browserGuides.map(browser => (
          <Panel 
            key={browser.key}
            header={
              <Space>
                {browser.icon}
                <span>{browser.title}</span>
              </Space>
            }
          >
            <ol style={{ paddingLeft: 20 }}>
              {browser.steps.map((step, index) => (
                <li key={index} style={{ marginBottom: 8 }}>
                  <Text>{step}</Text>
                </li>
              ))}
            </ol>
          </Panel>
        ))}
      </Collapse>

      <Alert
        message="Lưu ý"
        description="Sau khi cấp quyền, hãy thử lại chức năng lấy vị trí. Nếu vẫn gặp lỗi, có thể do thiết bị không có GPS hoặc đang ở trong nhà."
        type="warning"
        showIcon
        style={{ marginTop: 16 }}
      />
    </Card>
  )
}

export default LocationPermissionGuide 