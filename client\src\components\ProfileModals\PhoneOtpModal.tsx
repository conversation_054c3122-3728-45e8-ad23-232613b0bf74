import { Modal, Form, Input } from 'antd'
import Button from '@/components/Button'
import ModalButtonGroup from '@/components/ModalButtonGroup'
import styles from './Modal.module.scss'

type PhoneOtpModalProps = {
  open: boolean
  onClose: () => void
  form: any
  otpPhone: string
  setOtpPhone: (val: string) => void
  countdown: number
  handleSendOtp: (type: 'phone') => void
  handleVerifyOtp: (type: 'phone') => void
}

export default function PhoneOtpModal({
  open,
  onClose,
  form,
  otpPhone,
  setOtpPhone,
  countdown,
  handleSendOtp,
  handleVerifyOtp,
}: PhoneOtpModalProps) {
  return (
    <Modal title="Xác thực số điện thoại" open={open} onCancel={onClose} footer={null}>
      <Form form={form} layout="vertical">
        {' '}
        {/* Connect the form instance here */}
        <Form.Item label="Số điện thoại mới" name="phone">
          {' '}
          {/* Add name prop */}
          <Input disabled />
        </Form.Item>
        <Form.Item label="Mã OTP">
          <div className={styles.otpContainer}>
            <Input
              placeholder="Nhập mã OTP"
              value={otpPhone}
              onChange={(e) => setOtpPhone(e.target.value)}
              className={styles.otpInput}
            />
            <Button variant="primary" onClick={() => handleSendOtp('phone')} disabled={countdown > 0}>
              {countdown > 0 ? `Gửi lại (${countdown}s)` : 'Gửi OTP'}
            </Button>
          </div>
        </Form.Item>
        
        <ModalButtonGroup
          onCancel={onClose}
          onConfirm={() => handleVerifyOtp('phone')}
          cancelText="Hủy"
          confirmText="Xác thực"
          confirmVariant="primary"
        />
      </Form>
    </Modal>
  )
}
