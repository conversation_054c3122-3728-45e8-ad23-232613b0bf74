import { ApiProperty } from '@nestjs/swagger';

export class UpdateInventoryVariantDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  stock: number;

  @ApiProperty()
  minStock: number;

  @ApiProperty({ required: false })
  batchNumber?: string;

  @ApiProperty({ required: false })
  expiryDate?: string;
}

export class QuickStockUpdateDto {
  @ApiProperty()
  change: number;
}

export class ImportInventoryDto {
  @ApiProperty({ type: 'string', format: 'binary' })
  file: any;
}
