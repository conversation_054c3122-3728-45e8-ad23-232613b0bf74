import React from 'react'
import { Modal, Typography } from 'antd'
import { ExclamationCircleOutlined, CloseCircleOutlined } from '@ant-design/icons'

const { Text } = Typography

type ConfirmModalProps = {
  isOpen: boolean
  title: string
  message: string
  onConfirm: () => void
  onClose: () => void
  type?: 'warning' | 'error'
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  title,
  message,
  onConfirm,
  onClose,
  type = 'warning',
}) => {
  const icon =
    type === 'warning' ? (
      <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: 24 }} />
    ) : (
      <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: 24 }} />
    )

  return (
    <Modal
      open={isOpen}
      title={
        <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {icon}
          {title}
        </span>
      }
      onOk={onConfirm}
      onCancel={onClose}
      okText="Cập nhật"
      cancelText="Hủy"
      centered
      maskClosable={false}
    >
      <Text>{message}</Text>
    </Modal>
  )
}

export default ConfirmModal
