import { Test, TestingModule } from '@nestjs/testing';
import { OtpService } from '../services/otp.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { OtpVerification } from '../../user/entities/otp_verification.entity';
import { User } from '../../user/entities/user.entity';
import { User2FASettings } from '../../user/entities/user_2fa_settings.entity';
import { MailService } from 'src/mail/mail.service';
import { Repository } from 'typeorm';

describe('OtpService', () => {
  let service: OtpService;
  let otpRepo: Repository<OtpVerification>;
  let mailService: MailService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OtpService,
        {
          provide: getRepositoryToken(User),
          useValue: { findOne: jest.fn() },
        },
        {
          provide: getRepositoryToken(OtpVerification),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User2FASettings),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: MailService,
          useValue: {
            sendOtpEmail: jest.fn(),
            sendResetPasswordEmail: jest.fn(),
            sendOtpEmail2FA: jest.fn(),
          },
        },
        {
          provide: 'LOG_SERVICE',
          useValue: { emit: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<OtpService>(OtpService);
    otpRepo = module.get(getRepositoryToken(OtpVerification));
    mailService = module.get(MailService);
  });

  it('should send OTP via email', async () => {
    const target = '<EMAIL>';
    const fakeOtp = { otpCode: '123456' };

    (otpRepo.create as jest.Mock).mockReturnValue(fakeOtp);
    (otpRepo.save as jest.Mock).mockResolvedValue(undefined);

    await service.sendOtp(target);

    expect(otpRepo.create).toHaveBeenCalledWith(
      expect.objectContaining({ target, status: 'valid', isVerified: false }),
    );
    expect(otpRepo.save).toHaveBeenCalledWith(fakeOtp);
    expect(mailService.sendOtpEmail).toHaveBeenCalledWith(target, 'Bạn', expect.any(String));
  });

  it('should verify valid OTP', async () => {
    const otp = {
      otpCode: '123456',
      expiredAt: new Date(Date.now() + 60 * 1000),
      status: 'valid',
      isVerified: false,
    };
    (otpRepo.findOne as jest.Mock).mockResolvedValue(otp);
    (otpRepo.save as jest.Mock).mockResolvedValue(undefined);

    const result = await service.verifyOtp('<EMAIL>', '123456');
    expect(result).toBe('Xác minh thành công');
    expect(otp.status).toBe('used');
    expect(otp.isVerified).toBe(true);
    expect(otpRepo.save).toHaveBeenCalledWith(otp);
  });

  it('should throw error for invalid OTP', async () => {
    (otpRepo.findOne as jest.Mock).mockResolvedValue(null);

    await expect(service.verifyOtp('<EMAIL>', '999999')).rejects.toThrow(
      'Mã OTP không hợp lệ hoặc đã hết hạn',
    );
  });
});
