import { Modu<PERSON> } from '@nestjs/common';
import { LogService } from './log/log.service';
import { LogController } from './log/log.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SystemLog } from './entities/log.entity';
import { UserBehavior } from '../m2/shop/entities/user_behavior.entity';
import { UserBehaviorLogService } from './log/user-behavior-log.service';
import { LogClientModule } from './log-client.module';
import { AiImageVector } from 'src/m2/shop/entities';
import { ImageVectorizeController } from 'src/m2/shop/workers/image-vectorize.controller';

@Module({
  imports: [TypeOrmModule.forFeature([SystemLog, UserBehavior, AiImageVector]), LogClientModule],
  providers: [LogService, UserBehaviorLogService],
  controllers: [LogController],
  exports: [UserBehaviorLogService],
})
export class MLogModule {}
