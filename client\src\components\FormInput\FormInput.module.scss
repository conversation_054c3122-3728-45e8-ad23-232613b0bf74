@import '../../styles/variables.scss';

.container {
  margin-bottom: $spacing-md;
  
  &.fullWidth {
    width: 100%;
  }
}

// Label styles
.label {
  display: block;
  margin-bottom: $spacing-xs;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: #333;
  line-height: $line-height-normal;
}

.required {
  color: #ff4d4f;
  margin-left: 2px;
}

// Input wrapper styles
.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  
  // Size variants
  &.size-small {
    min-height: 36px;
    
    .input {
      padding: $spacing-sm $spacing-md;
      font-size: $font-size-sm;
    }
    
    &.hasIcon .input {
      padding-left: 36px;
    }
  }
  
  &.size-medium {
    min-height: 44px; // Touch-friendly
    
    .input {
      padding: $spacing-md $spacing-md;
      font-size: $font-size-base;
    }
    
    &.hasIcon .input {
      padding-left: 44px;
    }
  }
  
  &.size-large {
    min-height: 48px;
    
    .input {
      padding: $spacing-md $spacing-lg;
      font-size: $font-size-md;
    }
    
    &.hasIcon .input {
      padding-left: 52px;
    }
  }
  
  // Variant styles
  &.variant-default {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    background: white;
    
    &.focused {
      border-color: #4CAF50;
      box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    }
    
    &.error {
      border-color: #ff4d4f;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
  }
  
  &.variant-filled {
    border: 1px solid transparent;
    border-radius: 8px;
    background: #fafafa;
    
    &.focused {
      background: white;
      border-color: #4CAF50;
      box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    }
    
    &.error {
      background: #fff2f0;
      border-color: #ff4d4f;
    }
  }
  
  &.variant-borderless {
    border: none;
    border-bottom: 2px solid #d9d9d9;
    border-radius: 0;
    background: transparent;
    
    &.focused {
      border-bottom-color: #4CAF50;
    }
    
    &.error {
      border-bottom-color: #ff4d4f;
    }
  }
}

// Icon styles
.icon {
  position: absolute;
  left: $spacing-md;
  z-index: 1;
  color: #8c8c8c;
  font-size: 16px;
  
  .size-small & {
    left: $spacing-sm;
    font-size: 14px;
  }
  
  .size-large & {
    left: $spacing-lg;
    font-size: 18px;
  }
}

// Input styles
.input {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  color: #333;
  font-family: inherit;
  transition: all 0.3s ease;
  
  &::placeholder {
    color: #bfbfbf;
    font-weight: $font-weight-normal;
  }
  
  &:focus {
    outline: none;
  }
  
  // Prevent zoom on iOS
  @media (max-width: 768px) {
    font-size: 16px !important;
  }
}

// Message styles
.errorMessage {
  margin-top: $spacing-xs;
  font-size: $font-size-sm;
  color: #ff4d4f;
  line-height: $line-height-normal;
}

.helpText {
  margin-top: $spacing-xs;
  font-size: $font-size-sm;
  color: #8c8c8c;
  line-height: $line-height-normal;
}

// Responsive adjustments
@media (max-width: 768px) {
  .container {
    margin-bottom: $spacing-sm;
  }
  
  .inputWrapper {
    &.size-small {
      min-height: 40px;
    }
    
    &.size-medium {
      min-height: 44px;
    }
    
    &.size-large {
      min-height: 48px;
    }
  }
  
  .label {
    font-size: $font-size-base;
  }
}

@media (max-width: 480px) {
  .inputWrapper {
    &.size-small {
      min-height: 44px; // Ensure touch targets
      
      .input {
        padding: $spacing-md $spacing-sm;
      }
    }
    
    &.size-medium {
      min-height: 44px;
      
      .input {
        padding: $spacing-md $spacing-sm;
      }
    }
    
    &.size-large {
      min-height: 48px;
      
      .input {
        padding: $spacing-md $spacing-md;
      }
    }
  }
}

// Ant Design overrides
:global {
  .ant-input {
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    background: transparent !important;
    
    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }
  }
  
  .ant-input-password {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    
    .ant-input {
      background: transparent !important;
    }
    
    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }
  }
  
  .ant-input-affix-wrapper {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    padding: 0 !important;
    
    &:focus,
    &:hover,
    &.ant-input-affix-wrapper-focused {
      border: none !important;
      box-shadow: none !important;
    }
  }
}
