import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserVectorEmbedding } from '../entities/user_vector_embedding.entity';
import { ProductVectorEmbedding } from '../entities/product_vector_embedding.entity';

@Injectable()
export class M2ProductRecommenderService {
  constructor(
    @InjectRepository(UserVectorEmbedding)
    private readonly userVectorRepo: Repository<UserVectorEmbedding>,
    @InjectRepository(ProductVectorEmbedding)
    private readonly productVectorRepo: Repository<ProductVectorEmbedding>,
  ) {}

  // Tính cosine similarity giữa 2 vector
  private cosineSimilarity(a: number[], b: number[]): number {
    const dot = a.reduce((sum, ai, i) => sum + ai * b[i], 0);
    const normA = Math.sqrt(a.reduce((sum, ai) => sum + ai * ai, 0));
    const normB = Math.sqrt(b.reduce((sum, bi) => sum + bi * bi, 0));
    if (normA === 0 || normB === 0) return 0;
    return dot / (normA * normB);
  }

  // Gợi ý sản phẩm cho user
  async suggestProductsForUser(
    user_id: string,
    topN = 5,
  ): Promise<{ product_id: string; score: number }[]> {
    const user = await this.userVectorRepo.findOne({ where: { user_id } });
    if (!user) return [];
    const products = await this.productVectorRepo.find();
    const results = products
      .map((p) => ({
        product_id: p.product_id,
        score: this.cosineSimilarity(user.vector, p.vector),
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, topN);
    return results;
  }
}
