import { Injectable, NotFoundException, BadRequestException, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductVersion } from '../entities/product_version.entity';
import { CreateProductVersionDto, UpdateProductVersionDto } from '../dto/product-version.dto';
import { ClientProxy } from '@nestjs/microservices';
import { LogActionType } from 'src/constants';

@Injectable()
export class ProductVersionService {
  constructor(
    @InjectRepository(ProductVersion)
    private readonly versionRepo: Repository<ProductVersion>,
    @Inject('LOG_SERVICE')
    private readonly logClient: ClientProxy,
  ) {}

  async create(product_id: string, dto: CreateProductVersionDto): Promise<ProductVersion> {
    try {
      const version = this.versionRepo.create({ ...dto, product_id });
      const saved = await this.versionRepo.save(version);
      this.logClient.emit('log.update', {
        module: 'ProductVersion',
        action: LogActionType.CREATE,
        performedBy: null,
        targetId: saved.id,
        description: `Tạo version mới cho product ${product_id}: ${saved.name}`,
        timestamp: new Date().toISOString(),
        data: saved,
      });
      return saved;
    } catch (error) {
      throw new BadRequestException('Tạo version thất bại: ' + (error?.message || error));
    }
  }

  async update(id: string, dto: UpdateProductVersionDto): Promise<ProductVersion> {
    try {
      const version = await this.versionRepo.findOne({ where: { id } });
      if (!version) throw new NotFoundException('Version not found');
      Object.assign(version, dto);
      const saved = await this.versionRepo.save(version);
      this.logClient.emit('log.update', {
        module: 'ProductVersion',
        action: LogActionType.UPDATE,
        performedBy: null,
        targetId: saved.id,
        description: `Cập nhật version ${saved.id}: ${saved.name}`,
        timestamp: new Date().toISOString(),
        data: saved,
      });
      return saved;
    } catch (error) {
      throw new BadRequestException('Cập nhật version thất bại: ' + (error?.message || error));
    }
  }

  async delete(id: string): Promise<void> {
    try {
      const version = await this.versionRepo.findOne({ where: { id } });
      await this.versionRepo.delete(id);
      this.logClient.emit('log.update', {
        module: 'ProductVersion',
        action: LogActionType.DELETE,
        performedBy: null,
        targetId: id,
        description: `Xóa version ${id}: ${version?.name || ''}`,
        timestamp: new Date().toISOString(),
        data: version,
      });
    } catch (error) {
      throw new BadRequestException('Xóa version thất bại: ' + (error?.message || error));
    }
  }

  async findByProduct(product_id: string): Promise<ProductVersion[]> {
    try {
      return await this.versionRepo.find({ where: { product_id } });
    } catch (error) {
      throw new BadRequestException('Lấy danh sách version thất bại: ' + (error?.message || error));
    }
  }
}
