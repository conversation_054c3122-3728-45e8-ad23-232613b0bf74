import {
    useGetAffiliateMeQuery,
    useGetAffiliateTreeQuery,
    useGetAffiliateStatsQuery,
    useGetAffiliateDirectQuery,
    useGetAffiliateIndirectQuery,
} from '@/services/M1/affiliate'
import { useToast } from '../../components/Toast'
import { useEffect } from 'react'
import { useLoadingStore } from '@/utils/loadingStore'

// Dùng cho GET - Lấy mã giới thiệu của user hiện tại
const useGetAffiliateMeHook = () => {
    const { data, error, isLoading, refetch, isFetching } = useGetAffiliateMeQuery(null)
    const { setLoading } = useLoadingStore()

    useEffect(() => {
        setLoading(isLoading || isFetching)
    }, [isLoading, isFetching])

    return {
        data,
        isLoading,
        error,
        refetch,
    }
}

// Dùng cho GET - Lấy cây hệ thống affiliate
const useGetAffiliateTreeHook = () => {
    const { data, error, isLoading, refetch, isFetching } = useGetAffiliateTreeQuery(null)
    const { setLoading } = useLoadingStore()

    useEffect(() => {
        setLoading(isLoading || isFetching)
    }, [isLoading, isFetching])

    return {
        data,
        isLoading,
        error,
        refetch,
    }
}

// Dùng cho GET - Lấy thống kê tổng quan
const useGetAffiliateStatsHook = () => {
    const { data, error, isLoading, refetch, isFetching } = useGetAffiliateStatsQuery(null)
    const { setLoading } = useLoadingStore()

    useEffect(() => {
        setLoading(isLoading || isFetching)
    }, [isLoading, isFetching])

    return {
        data,
        isLoading,
        error,
        refetch,
    }
}

// Dùng cho GET - Lấy danh sách F1
const useGetAffiliateDirectHook = () => {
    const { data, error, isLoading, refetch, isFetching } = useGetAffiliateDirectQuery(null)
    const { setLoading } = useLoadingStore()

    useEffect(() => {
        setLoading(isLoading || isFetching)
    }, [isLoading, isFetching])

    return {
        data,
        isLoading,
        error,
        refetch,
    }
}

const useGetAffiliateIndirectHook = (level: number) => {
  const { data, error, isLoading, refetch, isFetching } = useGetAffiliateIndirectQuery(level)
  const { setLoading } = useLoadingStore()

  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching])

  return {
    data,
    isLoading,
    error,
    refetch,
  }
}


// Xuất ra
export {
    useGetAffiliateMeHook,
    useGetAffiliateTreeHook,
    useGetAffiliateStatsHook,
    useGetAffiliateDirectHook,
    useGetAffiliateIndirectHook,
}
