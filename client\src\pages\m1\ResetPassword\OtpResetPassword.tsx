import { useState, useEffect } from 'react'
import { Button, Form, FormGroup, Label, Input, FormFeedback } from 'reactstrap'
import { useNavigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Key } from 'lucide-react'
import { toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import loginIllustration from '../../../assets/images/logo.png'
import { ROUTES } from '@/constants/routes'
import { useVerifyOtpPasswordHook } from '@/hooks/auth/useResetPassword'

interface Style {
  [key: string]: React.CSSProperties
}

const OtpResetPassword = () => {
  const [otp, setOtp] = useState('')
  const [otpError, setOtpError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [otpSuccess, setOtpSuccess] = useState('')
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const verifyOtp = useVerifyOtpPasswordHook()
  const navigate = useNavigate()
  const location = useLocation()

  // Retrieve email from navigation state
  const email = location.state?.email || ''

  useEffect(() => {
    window.scrollTo(0, 0)
    return () => {
      window.scrollTo(0, 0)
    }
  }, [])

  const validateOtp = (otp: string): boolean => {
    const otpRegex = /^\d{6}$/
    return otpRegex.test(otp)
  }

  const handleOtpChange = (value: string) => {
    setOtp(value)
    setOtpError('')
  }

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault()
    setOtpError('')
    setOtpSuccess('')
    setIsLoading(true)

    // Kiểm tra ký tự không phải số
    if (!/^\d*$/.test(otp)) {
      toast.error('Mã OTP chỉ được chứa chữ số!', {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      })
      setOtpError('Mã OTP chỉ được chứa chữ số')
      setIsLoading(false)
      return
    }

    // Kiểm tra độ dài OTP
    if (otp.length !== 6) {
      toast.error('Mã OTP phải đúng 6 chữ số!', {
        position: 'top-right',
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      })
      setOtpError('Mã OTP phải đúng 6 chữ số')
      setIsLoading(false)
      return
    }

    try {
      const success = await verifyOtp({ otp, target: email })
      if (success) {
        setOtpSuccess('Mã OTP đã được xác nhận thành công')
        setOtp('')
        navigate(ROUTES.CHANGE_PASSWORD, { state: { email } })
      } else {
        setOtpError('Mã OTP không hợp lệ. Vui lòng thử lại.')
      }
    } catch (err: any) {
      setOtpError('Mã OTP không hợp lệ. Vui lòng thử lại.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <motion.div
      style={styles.container}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        style={styles.header}
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <img src={loginIllustration} alt="Logo TAP" style={styles.logo} />
      </motion.div>

      <div style={styles.formContainer}>
        <motion.h3
          style={styles.subTitle}
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4 }}
        >
          Xác nhận mã OTP
        </motion.h3>

        <Form onSubmit={handleVerifyOtp}>
          {otpSuccess && <div style={styles.successMessage}>{otpSuccess}</div>}
          <FormGroup>
            <Label style={styles.label}>Mã OTP</Label>
            <div style={styles.inputWrapper}>
              <Key style={styles.inputIcon} />
              <Input
                id="otp"
                type="text"
                value={otp}
                onChange={(e) => handleOtpChange(e.target.value)}
                placeholder="Nhập mã OTP 6 chữ số"
                invalid={!!otpError}
                required
                style={
                  focusedField === 'otp' ? { ...styles.input, ...styles.inputFocus } : styles.input
                }
                onFocus={() => setFocusedField('otp')}
                onBlur={() => setFocusedField(null)}
              />
            </div>
            {otpError && <FormFeedback>{otpError}</FormFeedback>}
          </FormGroup>
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              color="primary"
              block
              type="submit"
              disabled={isLoading}
              style={
                isLoading
                  ? { ...styles.submitButton, ...styles.submitButtonDisabled }
                  : styles.submitButton
              }
              onMouseEnter={(e) =>
                !isLoading && Object.assign(e.currentTarget.style, styles.submitButtonHover)
              }
              onMouseLeave={(e) =>
                !isLoading && Object.assign(e.currentTarget.style, styles.submitButton)
              }
            >
              <Key
                style={{
                  width: '1.25rem',
                  height: '1.25rem',
                  marginRight: '0.5rem',
                  display: 'inline-block',
                }}
              />
              {isLoading ? 'Đang xử lý...' : 'Xác nhận OTP'}
            </Button>
          </motion.div>
          <div style={styles.registerContainer}>
            <div
              style={styles.link}
              onClick={() => navigate(ROUTES.FORGOT_PASSWORD)}
              onMouseEnter={(e) => Object.assign(e.currentTarget.style, styles.linkHover)}
              onMouseLeave={(e) => Object.assign(e.currentTarget.style, styles.link)}
            >
              Quay lại đặt lại mật khẩu
            </div>
          </div>
        </Form>
      </div>
    </motion.div>
  )
}

export default OtpResetPassword

const styles: Style = {
  container: {
    minHeight: '100vh',
    width: '100%',
    background: '#FFFFFF',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 0,
    margin: 0,
    overflow: 'hidden',
    position: 'absolute',
    top: 0,
    left: 0,
  },
  header: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: '10px 0',
  },
  logo: {
    width: '150px',
    height: '150px',
  },
  formContainer: {
    backgroundColor: '#FFFFFF',
    padding: '2.5rem',
    borderRadius: '15px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
    width: '100%',
    maxWidth: '500px',
    margin: 0,
  },
  subTitle: {
    fontSize: '1.4rem',
    color: '#7A7A7A',
    textAlign: 'center',
    marginBottom: '1.5rem',
    userSelect: 'none',
  },
  label: {
    color: '#2E2E2E',
    fontSize: '1.1rem',
    marginBottom: '0.5rem',
  },
  inputWrapper: {
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    marginBottom: '1rem',
  },
  input: {
    border: '1px solid #E0E0E0',
    borderRadius: '8px',
    color: '#2E2E2E',
    width: '100%',
    padding: '0.75rem 2.5rem 0.75rem 2.5rem',
    fontSize: '1.1rem',
    transition: 'all 0.2s ease',
  },
  inputFocus: {
    borderColor: '#34A853',
    boxShadow: '0 0 0 0.2rem rgba(52, 168, 83, 0.4)',
  },
  inputIcon: {
    position: 'absolute',
    left: '0.75rem',
    color: '#7A7A7A',
  },
  link: {
    color: '#2D9CDB',
    textDecoration: 'none',
    fontSize: '1.1rem',
  },
  linkHover: {
    color: '#34A853',
    textDecoration: 'underline',
  },
  submitButton: {
    backgroundColor: '#2D9CDB',
    borderColor: '#2D9CDB',
    color: '#FFFFFF',
    width: '100%',
    padding: '0.75rem',
    borderRadius: '8px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all 0.3s ease',
    fontSize: '1.1rem',
  },
  submitButtonHover: {
    backgroundColor: '#34A853',
    borderColor: '#34A853',
    transform: 'scale(1.05)',
  },
  submitButtonDisabled: {
    backgroundColor: '#B2DFDB',
    borderColor: '#B2DFDB',
    cursor: 'not-allowed',
  },
  registerContainer: {
    textAlign: 'center',
    marginTop: '1.5rem',
    fontSize: '1.1rem',
  },
  successMessage: {
    color: '#34A853',
    textAlign: 'center',
    marginBottom: '1.5rem',
    fontSize: '1.1rem',
  },
}
