import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';

@Entity('otp_verification')
export class OtpVerification {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  target: string; // Email hoặc phone

  @Column()
  otpCode: string;

  @Column()
  expiredAt: Date;

  @Column({ default: 'valid' })
  status: 'valid' | 'used' | 'expired';

  @Column({ default: false })
  isVerified: boolean;

  @CreateDateColumn()
  createdAt: Date;
}
