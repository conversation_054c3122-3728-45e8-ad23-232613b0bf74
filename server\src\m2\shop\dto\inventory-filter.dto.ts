import { ApiPropertyOptional } from '@nestjs/swagger';

export class InventoryFilterDto {
  @ApiPropertyOptional()
  category?: string;

  @ApiPropertyOptional()
  stockStatus?: string;

  @ApiPropertyOptional()
  warehouse?: string;

  @ApiPropertyOptional()
  productStatus?: string;

  @ApiPropertyOptional()
  searchText?: string;

  @ApiPropertyOptional({ type: Number })
  page?: number;

  @ApiPropertyOptional({ type: Number })
  pageSize?: number;
}
