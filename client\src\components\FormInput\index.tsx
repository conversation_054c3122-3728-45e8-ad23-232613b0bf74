import React, { useState, forwardRef } from 'react';
import { Input, type InputProps } from 'antd';
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import styles from './FormInput.module.scss';

interface FormInputProps extends Omit<InputProps, 'size' | 'variant' | 'status'> {
  label?: string;
  error?: string;
  required?: boolean;
  icon?: React.ReactNode;
  helpText?: string;
  size?: 'small' | 'medium' | 'large';
  variant?: 'outlined' | 'filled' | 'borderless';
  fullWidth?: boolean;
  status?: 'error' | 'warning' | '';
}

const FormInput = forwardRef<any, FormInputProps>(({
  label,
  error,
  required = false,
  icon,
  helpText,
  size = 'medium',
  variant = 'outlined',
  fullWidth = true,
  className = '',
  type = 'text',
  ...props
}, ref) => {
  const [focused, setFocused] = useState(false);

  const containerClasses = [
    styles.container,
    fullWidth ? styles.fullWidth : '',
    className
  ].filter(Boolean).join(' ');

  const inputWrapperClasses = [
    styles.inputWrapper,
    styles[`size-${size}`],
    styles[`variant-${variant}`],
    focused ? styles.focused : '',
    error ? styles.error : '',
    icon ? styles.hasIcon : ''
  ].filter(Boolean).join(' ');

  const inputClasses = [
    styles.input,
    styles[`size-${size}`]
  ].filter(Boolean).join(' ');

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setFocused(true);
    props.onFocus?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setFocused(false);
    props.onBlur?.(e);
  };

  const renderInput = () => {
    const commonProps = {
      ...props,
      ref,
      className: inputClasses,
      onFocus: handleFocus,
      onBlur: handleBlur,
      status: error ? ('error' as const) : undefined
    };

    if (type === 'password') {
      return (
        <Input.Password
          {...commonProps}
          iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
        />
      );
    }

    if (type === 'textarea') {
      const handleTextAreaFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
        setFocused(true);
        props.onFocus?.(e as any);
      };

      const handleTextAreaBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
        setFocused(false);
        props.onBlur?.(e as any);
      };

      const textAreaProps = {
        ref,
        className: inputClasses,
        onFocus: handleTextAreaFocus,
        onBlur: handleTextAreaBlur,
        status: error ? ('error' as const) : undefined,
        placeholder: props.placeholder,
        value: props.value,
        defaultValue: props.defaultValue,
        onChange: props.onChange as any,
        disabled: props.disabled,
        readOnly: props.readOnly,
        maxLength: props.maxLength,
        showCount: props.showCount,
      };
      return (
        <Input.TextArea
          {...textAreaProps}
          autoSize={{ minRows: 3, maxRows: 6 }}
        />
      );
    }

    return <Input {...commonProps} type={type} />;
  };

  return (
    <div className={containerClasses}>
      {label && (
        <label className={styles.label}>
          {label}
          {required && <span className={styles.required}>*</span>}
        </label>
      )}
      
      <div className={inputWrapperClasses}>
        {icon && <div className={styles.icon}>{icon}</div>}
        {renderInput()}
      </div>

      {error && (
        <div className={styles.errorMessage}>
          {error}
        </div>
      )}

      {helpText && !error && (
        <div className={styles.helpText}>
          {helpText}
        </div>
      )}
    </div>
  );
});

FormInput.displayName = 'FormInput';

export default FormInput;
