import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
  Inject,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from '../../user/entities/user.entity';
import { OtpVerification } from '../../user/entities/otp_verification.entity';
import { MailService } from 'src/mail/mail.service';
import { Role } from '../../user/entities/role.entity';
import { UserProfile } from '../../user/entities/user_profile.entity';
import { UserAffiliateService } from '../../user/services/user_affiliate.service';
import { UserProvider } from '../../user/entities/user-provider.entity';
import { ConfigService } from '@nestjs/config';
import { User2FASettings } from '../../user/entities/user_2fa_settings.entity';
import { VerifyOtpDto } from '../dto/verify-otp.dto';
import { SendOtpDto } from '../dto/send-otp.dto';
import { AuthSession } from '../../user/entities/auth_sessions.entity';
import { ClientProxy } from '@nestjs/microservices';
import { RoleGroup } from '../../user/entities/role_groups.entity';
import { RoleGroupPermission } from '../../user/entities/role_group_permissions.entity';

@Injectable()
export class OtpService {
  constructor(
    @InjectRepository(User) private readonly userRepo: Repository<User>,
    @InjectRepository(OtpVerification) private readonly otpRepo: Repository<OtpVerification>,
    @InjectRepository(User2FASettings) private readonly user2FARepo: Repository<User2FASettings>,
    private readonly mailService: MailService,
    @Inject('LOG_SERVICE')
    private readonly logClient: ClientProxy,
  ) {}
  /**
   * 1️⃣ Gửi mã OTP xác minh (qua email hoặc phone)
   * @param target Email hoặc số điện thoại
   */
  async sendOtp(target: string): Promise<void> {
    const otpCode = this.generateOtpCode(); // 👉 gọi hàm tạo mã

    const otp = this.otpRepo.create({
      target,
      otpCode,
      expiredAt: new Date(Date.now() + 5 * 60 * 1000), // 5 phút
      status: 'valid',
      isVerified: false,
    });

    await this.otpRepo.save(otp);

    // Gửi qua Email hoặc chuẩn bị cho gửi SMS
    if (target.includes('@')) {
      await this.mailService.sendOtpEmail(target, 'Bạn', otpCode);
    } else {
      // TODO: Gửi SMS
    }
  }

  /**
   * Tạo mã OTP gồm 6 ký tự ngẫu nhiên (bao gồm chữ và số)
   */
  generateOtpCode(length = 6): string {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 2️⃣ Xác minh mã OTP
   * @param target Email hoặc phone đã gửi OTP
   * @param otpCode Mã OTP người dùng nhập
   * @returns Chuỗi thông báo nếu thành công
   */
  async verifyOtp(target: string, otpCode: string): Promise<string> {
    // Lấy bản ghi OTP mới nhất và còn hiệu lực
    const otp = await this.otpRepo.findOne({
      where: { target, otpCode, status: 'valid' },
      // order: { createdAt: 'DESC' },
    });

    // Kiểm tra mã đúng và chưa hết hạn
    if (!otp || new Date() > otp.expiredAt) {
      throw new UnauthorizedException('Mã OTP không hợp lệ hoặc đã hết hạn');
    }

    // Cập nhật trạng thái: đã dùng và đã xác minh
    otp.status = 'used';
    otp.isVerified = true;
    await this.otpRepo.save(otp);

    return 'Xác minh thành công';
  }

  /**
   * Gửi mail khôi phục mật khẩu chứa OTP
   * @param userId ID người dùng (lấy từ payload refreshToken)
   * @param refreshToken Refresh token người dùng gửi lên
   * @returns AccessToken + RefreshToken mới
   */
  async sendResetPasswordOtp(email: string) {
    const user = await this.userRepo.findOne({ where: { email } });
    if (!user) throw new NotFoundException('Email không tồn tại');

    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    const otp = this.otpRepo.create({
      target: email,
      otpCode,
      isVerified: false,
      expiredAt: new Date(Date.now() + 5 * 60 * 1000),
    });

    await this.otpRepo.save(otp);
    await this.mailService.sendResetPasswordEmail(
      email,
      'Bạn',
      `Mã khôi phục mật khẩu: ${otpCode}`,
    );
  }

  /**
   * Xác thực OTP qua mail
   * @param userId ID người dùng (lấy từ payload refreshToken)
   * @param refreshToken Refresh token người dùng gửi lên
   * @returns AccessToken + RefreshToken mới
   */
  async verifyResetOtp(email: string, otpCode: string) {
    const otp = await this.otpRepo.findOne({
      where: { target: email, otpCode, isVerified: false },
    });
    if (!otp || otp.expiredAt < new Date())
      throw new BadRequestException('OTP không hợp lệ hoặc đã hết hạn');

    otp.isVerified = true;
    await this.otpRepo.save(otp);
    return true;
  }

  /**
   * Đổi mật khẩu
   * @param userId ID người dùng (lấy từ payload refreshToken)
   * @param refreshToken Refresh token người dùng gửi lên
   * @returns AccessToken + RefreshToken mới
   */
  async resetPassword(email: string, newPassword: string) {
    const otp = await this.otpRepo.findOne({ where: { target: email, isVerified: true } });
    if (!otp) throw new ForbiddenException('Chưa xác minh OTP');

    const user = await this.userRepo.findOne({ where: { email } });
    if (!user) throw new NotFoundException('Người dùng không tồn tại');

    user.password = await bcrypt.hash(newPassword, 10);
    await this.userRepo.save(user);

    // Thu hồi mã OTP đã dùng
    otp.isVerified = false;
    await this.otpRepo.save(otp);
  }
}
