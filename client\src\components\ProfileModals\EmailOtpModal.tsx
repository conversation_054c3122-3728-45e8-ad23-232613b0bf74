import { Modal, Form, Input } from 'antd'
import Button from '@/components/Button'
import ModalButtonGroup from '@/components/ModalButtonGroup'
import styles from './Modal.module.scss'

type EmailOtpModalProps = {
  open: boolean
  onClose: () => void
  form: any
  otpEmail: string
  setOtpEmail: (val: string) => void
  countdown: number
  handleSendOtp: (type: 'email') => void
  handleVerifyOtp: (type: 'email') => void
}

export default function EmailOtpModal({
  open,
  onClose,
  form,
  otpEmail,
  setOtpEmail,
  countdown,
  handleSendOtp,
  handleVerifyOtp,
}: EmailOtpModalProps) {
  return (
    <Modal title="Xác thực email" open={open} onCancel={onClose} footer={null}>
      <Form layout="vertical">
        <Form.Item label="Email mới">
          <Input value={form.getFieldValue('email')} disabled />
        </Form.Item>
        <Form.Item label="Mã OTP">
          <div className={styles.otpContainer}>
            <Input
              placeholder="Nhập mã OTP"
              value={otpEmail}
              onChange={(e) => setOtpEmail(e.target.value)}
              className={styles.otpInput}
            />
            <Button variant="primary" onClick={() => handleSendOtp('email')} disabled={countdown > 0}>
              {countdown > 0 ? `Gửi lại (${countdown}s)` : 'Gửi OTP'}
            </Button>
          </div>
        </Form.Item>
        
        <ModalButtonGroup
          onCancel={onClose}
          onConfirm={() => handleVerifyOtp('email')}
          cancelText="Hủy"
          confirmText="Xác thực"
          confirmVariant="primary"
        />
      </Form>
    </Modal>
  )
}
