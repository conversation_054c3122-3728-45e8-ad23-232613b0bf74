@echo off
REM AI Training Pipeline Script for Windows
REM Chạy lần lư<PERSON> các bước pipeline AI cho module M2

REM Đảm bảo script đư<PERSON><PERSON> chạy từ thư mục server\
cd /d %~dp0\..\..

REM Kiểm tra venv
if not exist venv (
  echo [ERROR] Python venv chưa tồn tại. Hãy tạo venv trước khi chạy pipeline!
  echo Gợi ý: python -m venv venv && venv\Scripts\activate && pip install -r requirements.txt
  exit /b 1
)

set PYTHON_EXEC=venv\Scripts\python.exe

REM 1. Export product data to JSON
npm run export:product-to-vector || exit /b

REM 2. Sinh embedding cho sản phẩm (Python)
%PYTHON_EXEC% src\m2\ai_trainning\product_to_vector.py || exit /b

REM 3. Insert/update product vector vào DB
npm run insert:product-embeddings || exit /b

REM 4. Export user behavior log to JSON
npm run export:user-to-vector || exit /b

REM 5. Sinh embedding cho user (Python)
%PYTHON_EXEC% src\m2\ai_trainning\user_to_vector.py || exit /b

REM 6. Insert/update user vector vào DB
npm run insert:user-embeddings || exit /b

echo AI training pipeline completed!
