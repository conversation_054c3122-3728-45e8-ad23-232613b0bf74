// File: hooks/product/useCategory.ts
import { useGetCategoriesQuery, useCreateCategoryMutation, useGetCategoryDetailQuery, useUpdateCategoryMutation, useDeleteCategoryMutation, useUploadCategoryIconMutation } from '../../services/M2/ProductManagement/categoryManagement';
// Hook xóa category
export function useDeleteCategory() {
  return useDeleteCategoryMutation();
}
// Hook cập nhật category
export function useUpdateCategory() {
  return useUpdateCategoryMutation();
}
// Hook lấy chi tiết category theo id
export function useCategoryDetail(id: string) {
  return useGetCategoryDetailQuery(id);
}
// Hook tạo mới category
export function useCreateCategory() {
  return useCreateCategoryMutation();
}
// Hook upload icon category
export function useUploadCategoryIcon() {
  return useUploadCategoryIconMutation();
}
import { useEffect } from 'react';
import { useLoadingStore } from '../../utils/loadingStore';

// 📝 Define interface cho params
interface CategoryParams {
  type: 'tree' | 'flat';
  parent_id?: string;
}

// Hook chính - support cả tree và flat
export function useCategoryTree(params: CategoryParams = { type: 'tree' }) {
  const { data, isLoading, error, refetch, isFetching } = useGetCategoriesQuery(params);
  const { setLoading } = useLoadingStore();

  useEffect(() => {
    setLoading(isLoading || isFetching);
  }, [isLoading, isFetching, setLoading]);

  return {
    data,
    isLoading,
    error,
    refetch,
    isFetching,
  };
}

// Hook riêng cho flat view
export function useCategoryFlat(parent_id?: string) {
  return useCategoryTree({ type: 'flat', parent_id });
}

// Hook động - có thể switch type
export function useCategoryDynamic(viewMode: 'tree' | 'flat', parent_id?: string) {
  const params: CategoryParams = {
    type: viewMode,
    parent_id: viewMode === 'flat' ? parent_id : undefined
  };
  
  return useCategoryTree(params);
}

export {
  useGetCategoriesQuery,
  useCreateCategoryMutation,
  useGetCategoryDetailQuery,
  useUpdateCategoryMutation,
  useDeleteCategoryMutation,
  useUploadCategoryIconMutation,
};
