import { Controller, Get, Post, Body, Query, Param, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { TaxCalculationService } from '../services/tax-calculation.service';
import { UserRole } from '../entities/tax-calculation.entity';

@ApiTags('Tax Management')
@Controller('tax')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
export class TaxController {
  constructor(private readonly taxService: TaxCalculationService) {}

  @Post('calculate')
  @ApiOperation({ summary: 'Tính thuế cho user trong kỳ nhất định' })
  @ApiResponse({ status: 201 })
  async calculateTax(
    @Body()
    body: {
      userId?: string;
      period: string;
      revenue: number;
      userRole?: UserRole;
      regionCode?: string;
    },
    @Req() req: any,
  ) {
    const userId = body.userId || req.user?.userId;
    return this.taxService.calculateTaxForUser(
      userId,
      body.period,
      body.revenue,
      body.userRole,
      body.regionCode,
    );
  }

  @Get('summary/region/:regionCode')
  @ApiOperation({ summary: 'Lấy tổng hợp thuế theo khu vực (Admin/SuperAdmin)' })
  @ApiResponse({ status: 200 })
  async getTaxSummaryByRegion(
    @Param('regionCode') regionCode: string,
    @Query('period') period: string,
  ) {
    return this.taxService.getTaxSummaryByRegion(regionCode, period);
  }

  @Get('recommendations')
  @ApiOperation({ summary: 'Lấy gợi ý tối ưu thuế cho user hiện tại' })
  @ApiResponse({ status: 200 })
  async getRecommendations(
    @Query('currentRole') currentRole: UserRole,
    @Query('annualRevenue') annualRevenue: number,
    @Req() req: any,
  ) {
    const userId = req.user?.userId;
    return this.taxService.getTaxOptimizationRecommendations(userId, currentRole, annualRevenue);
  }

  @Get('policy')
  @ApiOperation({ summary: 'Lấy chính sách thuế theo vai trò' })
  @ApiResponse({ status: 200 })
  async getTaxPolicy(@Query('role') role?: UserRole) {
    // Return tax policy for specific role or all roles
    const policies = {
      R01: 'Thành viên HTX – sản xuất nông nghiệp: Không chịu thuế trực tiếp, HTX đại diện kê khai',
      R02: 'Người bán hàng TMĐT: TNCN/GTGT nếu vượt ngưỡng, có thể ủy quyền HTX',
      R03: 'Người tiêu dùng: Không chịu thuế',
      R04: 'Chuyên gia – Bác sĩ – Giảng viên: TNCN/GTGT nếu tự doanh',
      R05: 'Quản lý nội bộ, admin: Không chịu thuế trực tiếp',
      R06: 'Tài xế, người giao hàng: TNCN nếu thu nhập cao',
      R07: 'Thành viên góp vốn: Thuế thu nhập từ đầu tư vốn',
      R08: 'Ban điều hành HTX: TNCN theo lương, phụ cấp',
    };

    if (role) {
      return { role, policy: policies[role] || 'Không tìm thấy chính sách cho vai trò này' };
    }

    return { policies };
  }
}
