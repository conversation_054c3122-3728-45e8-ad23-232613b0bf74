# TAP Project Technical Documentation (2025)

## Module 1: Authentication & Authorization, User Management

### 1. Database Structure

#### Main Tables
| Table                        | Primary Key | Foreign Keys                | Description                                  |
|------------------------------|-------------|-----------------------------|----------------------------------------------|
| users                        | id (uuid)   | role_id → roles(id)         | User account, login, status, role            |
| user_profiles                | id (uuid)   | user_id → users(id)         | User profile details                         |
| roles                        | id (uuid)   |                             | User roles, permissions                      |
| permissions                  | id (uuid)   |                             | Permission definitions                       |
| role_groups                  | id (uuid)   |                             | Role groupings                              |
| role_group_permissions       | id (uuid)   | group_id, permission_id     | Permissions per group                        |
| role_scopes                  | id (uuid)   | role_id                     | Scope per role                               |
| user_affiliates              | id (uuid)   | userId, referrerId → users  | Affiliate tree, referral system              |
| user_locations               | id (uuid)   | user_id → users(id)         | User's administrative location               |
| user_qr                      | id (uuid)   | userId → users(id)          | QR code for user                             |
| otp_verification             | id (uuid)   |                             | OTP for login, password reset                |
| auth_sessions                | id (uuid)   | user_id → users(id)         | User session management                      |
| user_2fa_settings            | id (uuid)   | user_id → users(id)         | 2FA settings                                 |
| user-provider                | id (uuid)   | user_id → users(id)         | Social login provider                        |

#### Entities (server/src/m1/user/entities)
- auth_sessions.entity.ts
- otp_verification.entity.ts
- permissions.entity.ts
- role.entity.ts
- role_groups.entity.ts
- role_group_permissions.entity.ts
- role_scopes.entity.ts
- user-provider.entity.ts
- user.entity.ts
- user_2fa_settings.entity.ts
- user_affiliates.entity.ts
- user_locations..entity.ts
- user_profile.entity.ts
- user_qr.entity.ts

#### Key Columns & Types
- `users`: id (uuid, PK), uid_code (varchar, unique), phone (varchar, unique), email (varchar, unique, nullable), password (varchar), refreshToken (varchar, nullable), referral_by (varchar, nullable), status (varchar, default 'active'), role_id (uuid, FK), created_at/updated_at (timestamp)
- `roles`: id (uuid, PK), role_code (varchar), name (varchar), description (text)
- `user_profiles`: id (uuid, PK), user_id (uuid, FK), name, gender, avatar, birthday, phone, email, ...
- `user_affiliates`: id (uuid, PK), userId (uuid, FK), referrerId (uuid, FK), level_1~5 (varchar)
- `user_locations`: id (uuid, PK), user_id (uuid, FK), province_code, district_code, commune_code (varchar)
- `user_qr`: id (uuid, PK), userId (uuid, FK), qrCode (text), qrType (varchar), description (varchar), linkUrl (varchar), isActive (boolean)
- `otp_verification`: id (uuid, PK), target (varchar), otpCode (varchar), expiredAt (timestamp), status (varchar), isVerified (boolean)

### 2. Main Features
- Đăng ký, đăng nhập, xác thực OTP, phân quyền, quản lý hồ sơ, quản lý địa bàn, hệ thống giới thiệu (affiliate), sinh mã QR cá nhân, quên mật khẩu, đổi mật khẩu, ghi log hành vi người dùng.
- Hỗ trợ xác thực hai lớp (2FA): gửi mã OTP qua email/SMS khi đăng nhập hoặc thực hiện thao tác nhạy cảm.

---

## Module 2: E-commerce and Services

### 1. Database Structure

#### Main Tables
| Table                        | Primary Key | Foreign Keys                              | Description                                 |
|------------------------------|-------------|-------------------------------------------|---------------------------------------------|
| products                     | id (uuid)   | category_id → categories(id)              | Product info, status, price, ...            |
| product_versions             | id (uuid)   | product_id → products(id)                 | Versioned product details                   |
| categories                   | id (uuid)   | parent_id → categories(id)                | Product categories                          |
| orders                       | id (uuid)   | user_id → users(id)                       | Order info                                  |
| order_items                  | id (uuid)   | order_id → orders(id), product_id → products(id) | Order details                        |
| cart_item                    | id (uuid)   | cart_id, product_id                       | Cart items                                  |
| combo_item                   | id (uuid)   | combo_id, product_id                      | Combo items                                 |
| favorite                     | id (uuid)   | user_id, product_id                       | User favorites                              |
| flash_sale                   | id (uuid)   | product_id                                | Flash sale info                             |
| inventory                    | id (uuid)   | product_id                                | Inventory info                              |
| payment                      | id (uuid)   | order_id                                  | Payment info                                |
| product_attribute            | id (uuid)   | product_id                                | Product attributes                          |
| product_categories           | id (uuid)   | product_id, category_id                   | Product-category mapping                    |
| product_combo                | id (uuid)   | product_id, combo_id                      | Product-combo mapping                       |
| product_comment              | id (uuid)   | product_id, user_id                       | Product comments                            |
| product_image                | id (uuid)   | product_id                                | Product images                              |
| product_qr_code              | id (uuid)   | product_id                                | Product QR code                             |
| product_review               | id (uuid)   | product_id, user_id                       | Product reviews                             |
| product_tag                  | id (uuid)   | product_id, tag_id                        | Product-tag mapping                         |
| product_vector_embedding     | id (uuid)   | product_id → products(id)                 | AI vector for product                       |
| user_vector_embedding        | id (uuid)   | user_id → users(id)                       | AI vector for user                          |
| user_behavior_log            | id (uuid)   | user_id → users(id), product_id → products(id) | User actions log                      |
| ai_image_vectors             | id (uuid)   | product_id                                | AI image vectors                            |
| ai_search_log                | id (uuid)   | user_id                                   | AI search log                               |
| ai_suggestion                | id (uuid)   | user_id, product_id                       | AI suggestion log                           |
| ai_training_data             | id (uuid)   | id                                        | AI training data                            |
| feedback_reply               | id (uuid)   | feedback_id                               | Feedback replies                            |
| geo_discount                 | id (uuid)   | id                                        | Geo-based discounts                         |
| order_payment                | id (uuid)   | order_id                                  | Order payment info                          |
| order_status_log             | id (uuid)   | order_id                                  | Order status log                            |
| report_flag                  | id (uuid)   | id                                        | Report flags                                |
| status                       | id (uuid)   | id                                        | Status table                                |
| tag                          | id (uuid)   | id                                        | Tag table                                   |
| user_behavior                | id (uuid)   | user_id                                   | User behavior                               |
| user_feedback                | id (uuid)   | user_id                                   | User feedback                               |
| violation_report             | id (uuid)   | id                                        | Violation reports                           |
| voucher                      | id (uuid)   | id                                        | Voucher info                                |

#### Entities (server/src/m2/shop/entities)
- ai_image_vectors.entity.ts
- ai_search_log.entity.ts
- ai_suggestion.entity.ts
- ai_training_data.entity.ts
- cart_item.entity.ts
- combo_item.entity.ts
- favorite.entity.ts
- feedback_reply.entity.ts
- flash_sale.entity.ts
- geo_discount.entity.ts
- inventory.entity.ts
- order.entity.ts
- order_item.entity.ts
- order_payment.entity.ts
- order_status_log.entity.ts
- payment.entity.ts
- products.entity.ts
- product_attribute.entity.ts
- product_categories.entity.ts
- product_combo.entity.ts
- product_comment.entity.ts
- product_image.entity.ts
- product_qr_code.entity.ts
- product_review.entity.ts
- product_tag.entity.ts
- product_vector_embedding.entity.ts
- product_version.entity.ts
- report_flag.entity.ts
- status.entity.ts
- tag.entity.ts
- user_behavior.entity.ts
- user_feedback.entity.ts
- user_vector_embedding.entity.ts
- violation_report.entity.ts
- voucher.entity.ts

#### Key Columns & Types
- `products`: id (uuid, PK), name, description, price, status, category_id (uuid, FK), created_at, updated_at
- `product_versions`: id (uuid, PK), product_id (uuid, FK), version, data, created_at
- `categories`: id (uuid, PK), name, parent_id (uuid, FK, nullable)
- `orders`: id (uuid, PK), user_id (uuid, FK), status, total, created_at
- `order_items`: id (uuid, PK), order_id (uuid, FK), product_id (uuid, FK), quantity, price
- `product_vector_embedding`: id (uuid, PK), product_id (uuid, FK), vector (jsonb/array), updated_at
- `user_vector_embedding`: id (uuid, PK), user_id (uuid, FK), vector (jsonb/array), updated_at
- `user_behavior_log`: id (uuid, PK), user_id (uuid, FK), event_type, target_type, target_id, product_id (uuid, FK), event_data (jsonb), description, created_at

### 2. Main Features
- Quản lý sản phẩm, phiên bản sản phẩm, danh mục, đơn hàng, giỏ hàng, thanh toán
- Gợi ý sản phẩm theo AI vector (recommend-for-user)
- Ghi log hành vi người dùng (xem, tìm kiếm, click gợi ý AI...)
- Pipeline AI: export dữ liệu, sinh embedding (Python), insert/update vector vào DB
- API chuẩn RESTful: CRUD sản phẩm, đơn hàng, danh mục, gợi ý sản phẩm, log hành vi
- Đảm bảo chuẩn hóa response, tracking đầy đủ hành vi người dùng

---

## Notes
- Các bảng vector embedding và log hành vi là nền tảng cho AI recommendation.
- Mọi hành vi quan trọng (xem, tìm kiếm, click gợi ý AI) đều được log lại.
- Pipeline AI gồm: export dữ liệu (TS), sinh embedding (Python), insert/update vector (TS), đồng bộ hóa dữ liệu.
- Đảm bảo bảo mật thông tin người dùng, không commit file .env, node_modules, venv lên git.
