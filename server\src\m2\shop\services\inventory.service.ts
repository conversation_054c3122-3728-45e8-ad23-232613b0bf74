import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike, In } from 'typeorm';
import { Inventory } from '../entities/inventory.entity';
import { Product } from '../entities/products.entity';
import { ProductVersion } from '../entities/product_version.entity';
import { ProductCategory } from '../entities/product_categories.entity';
import { UpdateInventoryVariantDto, QuickStockUpdateDto } from '../dto/inventory.dto';
import { InventoryFilterDto } from '../dto/inventory-filter.dto';

@Injectable()
export class InventoryService {
  constructor(
    @InjectRepository(Inventory)
    private readonly inventoryRepo: Repository<Inventory>,
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,
    @InjectRepository(ProductVersion)
    private readonly versionRepo: Repository<ProductVersion>,
    @InjectRepository(ProductCategory)
    private readonly categoryRepo: Repository<ProductCategory>,
  ) {}

  async getInventoryList(filter: InventoryFilterDto) {
    // Xây dựng query filter
    const qb = this.inventoryRepo
      .createQueryBuilder('inventory')
      .leftJoinAndSelect('inventory.product', 'product')
      .leftJoinAndSelect('inventory.version', 'version');

    if (filter.category) {
      qb.leftJoinAndSelect('product.category', 'category').andWhere('category.name = :category', {
        category: filter.category,
      });
    }
    if (filter.stockStatus) {
      if (filter.stockStatus === 'out_of_stock') {
        qb.andWhere('inventory.stock_quantity = 0');
      } else if (filter.stockStatus === 'low') {
        qb.andWhere('inventory.stock_quantity < inventory.threshold_min');
      } else if (filter.stockStatus === 'sufficient') {
        qb.andWhere('inventory.stock_quantity >= inventory.threshold_min');
      }
    }
    if (filter.warehouse) {
      qb.andWhere('inventory.location_code = :warehouse', { warehouse: filter.warehouse });
    }
    if (filter.productStatus) {
      qb.andWhere('product.status = :status', { status: filter.productStatus });
    }
    if (filter.searchText) {
      qb.andWhere('product.name ILIKE :search OR product.slug ILIKE :search', {
        search: `%${filter.searchText}%`,
      });
    }
    // Paging
    const page = filter.page || 1;
    const pageSize = filter.pageSize || 20;
    qb.skip((page - 1) * pageSize).take(pageSize);

    const [items, total] = await qb.getManyAndCount();
    return { items, total, page, pageSize };
  }

  async getInventoryDetail(id: string) {
    return this.inventoryRepo.findOne({
      where: { id },
      relations: ['product', 'version'],
    });
  }

  async updateVariants(id: string, variants: UpdateInventoryVariantDto[]) {
    // Cập nhật từng biến thể
    for (const variant of variants) {
      await this.versionRepo.update(variant.id, {
        stock: variant.stock,
        // Có thể cập nhật thêm các trường khác nếu cần
      });
      await this.inventoryRepo.update(
        { product_id: id, version_id: variant.id },
        {
          stock_quantity: variant.stock,
          threshold_min: variant.minStock,
          note: variant.batchNumber || undefined,
        },
      );
    }
    return { success: true };
  }

  async quickUpdateStock(id: string, variantId: string, change: number) {
    const inventory = await this.inventoryRepo.findOne({
      where: { product_id: id, version_id: variantId },
    });
    if (!inventory) return { error: 'Not found' };
    inventory.stock_quantity = Math.max(0, (inventory.stock_quantity || 0) + change);
    await this.inventoryRepo.save(inventory);
    await this.versionRepo.update(variantId, { stock: inventory.stock_quantity });
    return { success: true, stock: inventory.stock_quantity };
  }

  async getInventoryHistory(id: string) {
    // TODO: implement history logic (order_items, import/export logs...)
    return [];
  }

  async importInventory(file: Express.Multer.File) {
    // TODO: implement import logic
    return { success: true };
  }

  async exportInventory(filter: InventoryFilterDto) {
    // TODO: implement export logic
    return { success: true };
  }

  async getLowStockAlerts() {
    const items = await this.inventoryRepo.find({
      where: [
        { stock_quantity: 0 },
        { stock_quantity: In([null, undefined]), threshold_min: In([null, undefined]) },
      ],
      relations: ['product', 'version'],
    });
    return items;
  }
}
