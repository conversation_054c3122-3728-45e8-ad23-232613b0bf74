import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemLog } from '../entities/log.entity';

@Injectable()
export class LogService {
  constructor(
    @InjectRepository(SystemLog)
    private logRepo: Repository<SystemLog>,
  ) {}

  /**
   * 📝 Ghi log hệ thống
   * @param payload dữ liệu log cần ghi
   */
  async saveLog(payload: Partial<SystemLog>) {
    const log = this.logRepo.create(payload);
    return this.logRepo.save(log);
  }
}
