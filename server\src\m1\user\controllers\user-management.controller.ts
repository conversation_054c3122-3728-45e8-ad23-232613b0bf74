import { Controller, Get, Put, Body, UseGuards, Delete, Param, Query } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UpdateUserByAdminDto } from '../dto/update-user-by-admin.dto';
import { UserManagementService } from '../services/user-management.service';
import { GetUserQueryDto } from '../dto/get-user-query.dto';
import { Permissions } from 'src/common/decorators/permissions.decorator';

@ApiTags('Users Management')
@ApiBearerAuth('access-token')
@UseGuards(AuthGuard('jwt'))
@Controller('users-management')
export class UserManagementController {
  constructor(private readonly userService: UserManagementService) {}
  /**
   * GET /admin/users
   * <PERSON><PERSON><PERSON> danh sách người dùng (admin có thể lọc theo role, tỉnh, trạng thái...)
   */
  @Get('admin/user-management')
  @ApiOperation({
    summary: 'L<PERSON>y danh sách người dùng (admin)',
    description: 'Có thể lọc theo vai trò, trạng thái, từ khoá và phân trang.',
  })
  async getUsers(@Query() query: GetUserQueryDto) {
    return this.userService.getUserList(query);
  }

  /**
   * GET /admin/users/:id
   * Lấy chi tiết một người dùng cụ thể
   */
  @Get('admin/user-management/:id')
  @ApiOperation({ summary: 'Lấy theo ID' })
  async getUser(@Param('id') id: string) {
    return this.userService.getUserById(id);
  }

  /**
   * PUT /admin/users/:id
   * Admin cập nhật thông tin người dùng
   */
  @Put('admin/user-management/:id')
  @ApiOperation({ summary: 'Cập nhật' })
  async updateUser(@Param('id') id: string, @Body() dto: UpdateUserByAdminDto) {
    return this.userService.updateUserByAdmin(id, dto);
  }

  /**
   * DELETE /admin/users/:id
   * Admin xoá logic tài khoản người dùng
   */
  @Delete('admin/user-management/:id')
  @ApiOperation({ summary: 'Xóa' })
  @Permissions('PERM_USER_DELETE')
  async softDeleteUser(@Param('id') id: string) {
    return this.userService.softDeleteUser(id);
  }
}
