import { <PERSON>, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AiImageVector } from '../entities/ai_image_vectors.entity';
import { Product } from '../entities/products.entity';
import axios from 'axios';
import { EventPattern, Payload, Ctx, RmqContext } from '@nestjs/microservices';

@Controller()
export class ImageVectorizeController {
  constructor(
    @InjectRepository(AiImageVector)
    private readonly aiImageVectorRepo: Repository<AiImageVector>,
  ) {}

  @EventPattern('image-vectorize')
  async handleImageVectorize(@Payload() data: any, @Ctx() context: RmqContext) {
    const { productId, imageId, imageUrl } = data;
    try {
      // Tải ảnh về từ imageUrl
      const imageResp = await axios.get(imageUrl, { responseType: 'arraybuffer' });
      const imageBuffer = Buffer.from(imageResp.data, 'binary');

      // Gửi file ảnh lên CLIP service (multipart/form-data, field 'file')
      const clipServiceUrl = process.env.CLIP_SERVICE_URL || 'http://localhost:8001/vectorize';
      const FormData = require('form-data');
      const form = new FormData();
      form.append('file', imageBuffer, { filename: imageId + '.jpg' });

      const response = await axios.post(clipServiceUrl, form, {
        headers: form.getHeaders(),
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
      });
      const vector = response.data.vector;
      // Lưu vào DB
      await this.aiImageVectorRepo.save({
        product_id: productId,
        image_id: imageId,
        vector: JSON.stringify(vector),
        model: 'clip',
      });

      // Tích hợp gửi vector sang FAISS service
      const faissServiceUrl = process.env.FAISS_SERVICE_URL || 'http://localhost:8002/add_vector';
      try {
        await axios.post(faissServiceUrl, {
          image_id: imageId,
          vector: vector,
        });
        Logger.log(`[FAISS] Vector added: imageId=${imageId}`);
      } catch (faissErr) {
        Logger.error(`[FAISS] ERROR: imageId=${imageId}`, faissErr);
      }

      Logger.log(`[image-vectorize] DONE: productId=${productId}, imageId=${imageId}`);
    } catch (err) {
      Logger.error(`[image-vectorize] ERROR: productId=${productId}, imageId=${imageId}`, err);
    }
  }
}
