import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  Index,
  Unique,
} from 'typeorm';
import { ProductCategory } from './product_categories.entity';

import { User } from '../../../m1/user/entities/user.entity';
import { AiImageVector } from './ai_image_vectors.entity';
import { ProductTag } from './product_tag.entity';
import { ProductImage } from './product_image.entity';
import { OrderItem } from './order_item.entity';
import { Inventory } from './inventory.entity';
import { ComboItem } from './combo_item.entity';
import { GeoDiscount } from './geo_discount.entity';

import { ProductReview } from './product_review.entity';
import { ProductComment } from './product_comment.entity';
import { ProductAttribute } from './product_attribute.entity';
import { ProductVersion } from './product_version.entity';
import { Status } from './status.entity';

export enum ProductSource {
  MANUAL = 'manual',
  AI_GENERATED = 'ai_generated',
}

@Unique(['user_id', 'slug'])
@Index(['user_id', 'slug'])
@Entity('products')
export class Product {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @Column({ type: 'uuid' })
  user_id: string;

  @ManyToOne(() => User, { nullable: false, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Index()
  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Index()
  @Column({ type: 'varchar', length: 150 })
  slug: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Index()
  @Column({ type: 'uuid' })
  category_id: string;
  @ManyToOne(() => ProductCategory, { nullable: false, onDelete: 'RESTRICT' })
  @JoinColumn({ name: 'category_id' })
  category: ProductCategory;

  @Column({ type: 'decimal', precision: 12, scale: 2 })
  price: number;

  @Column({ type: 'varchar', length: 20 })
  unit: string;

  @Column({ type: 'int' })
  stock: number;

  @Index()
  @Column({ type: 'uuid', name: 'status_id' })
  status_id: string;

  @ManyToOne(() => Status, { nullable: false, onDelete: 'RESTRICT' })
  @JoinColumn({ name: 'status_id' })
  status: Status;

  @Column({ type: 'boolean', default: true })
  is_public: boolean;

  @Column({ type: 'varchar', length: 255, nullable: true })
  seo_keywords?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  thumbnail_url?: string;

  @Column({ type: 'enum', enum: ProductSource, nullable: true })
  source?: ProductSource;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;

  @OneToMany(() => AiImageVector, (ai) => ai.product)
  ai_image_vectors: AiImageVector[];
  @OneToMany(() => ProductTag, (pt) => pt.product)
  product_tags: ProductTag[];
  @OneToMany(() => ProductImage, (pi) => pi.product)
  product_images: ProductImage[];
  @OneToMany(() => OrderItem, (oi) => oi.product)
  order_items: OrderItem[];
  @OneToMany(() => Inventory, (inv) => inv.product)
  inventories: Inventory[];
  @OneToMany(() => ComboItem, (ci) => ci.product)
  combo_items: ComboItem[];
  @OneToMany(() => GeoDiscount, (gd) => gd.product)
  geo_discounts: GeoDiscount[];
  @OneToMany(() => ProductReview, (pr) => pr.product)
  product_reviews: ProductReview[];
  @OneToMany(() => ProductComment, (pc) => pc.product)
  product_comments: ProductComment[];
  @OneToMany(() => ProductAttribute, (pa) => pa.product)
  product_attributes: ProductAttribute[];
  @OneToMany(() => ProductVersion, (pv) => pv.product)
  product_versions: ProductVersion[];
}
