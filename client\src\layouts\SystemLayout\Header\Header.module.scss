.header-wrapper {
  width: 100%;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.header-content {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.icon-menu-wrapper {
  cursor: pointer;
}

.user-dropdown {
  cursor: pointer;

  :global(.ant-dropdown-trigger) {
    outline: none;
  }
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background-color: #f5f5f5;
    opacity: 0.9;
  }

  &:active {
    transform: scale(0.98);
  }
}

.user-greeting {
  font-size: 14px;
  color: #888;
  line-height: 1.2;
  white-space: nowrap;
}

.user-name {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  white-space: nowrap;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e0e0e0;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
  }
}

// Custom dropdown styles
:global(.ant-dropdown) {
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  z-index: 1050; // Đảm bảo dropdown hiển thị trên các element khác

  :global(.ant-dropdown-menu) {
    border-radius: 8px;
    padding: 8px 0;
    min-width: 180px; // Đảm bảo đủ width cho text
    background-color: #ffffff;
    border: 1px solid #f0f0f0;

    :global(.ant-dropdown-menu-item) {
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.4;
      display: flex;
      align-items: center;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f5f5f5;
      }

      :global(.anticon) {
        margin-right: 12px;
        font-size: 16px;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    :global(.ant-dropdown-menu-item-danger) {
      color: #ff4d4f;

      &:hover {
        background-color: #fff2f0;
        color: #ff4d4f;
      }

      :global(.anticon) {
        color: #ff4d4f;
      }
    }

    :global(.ant-dropdown-menu-item-divider) {
      margin: 4px 0;
      border-color: #f0f0f0;
    }
  }
}

// Mobile responsive (768px and below)
@media (max-width: 768px) {
  .header-wrapper {
    height: 56px;
    padding: 0 12px;
  }

  .user-info {
    gap: 6px;
    padding: 6px 8px;
  }

  .user-greeting {
    font-size: 12px;
    display: none; // Ẩn "Xin chào!" trên mobile để tiết kiệm không gian
  }

  .user-name {
    font-size: 14px;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .avatar {
    width: 32px;
    height: 32px;
    border-width: 1px;
  }

  // Dropdown styles for mobile
  :global(.ant-dropdown) {
    :global(.ant-dropdown-menu) {
      min-width: 180px;

      :global(.ant-dropdown-menu-item) {
        padding: 14px 16px;
        font-size: 15px; // Larger touch target
      }
    }
  }
}

// Extra small mobile (480px and below)
@media (max-width: 480px) {
  .header-wrapper {
    padding: 0 8px;
  }

  .user-info {
    gap: 4px;
    padding: 4px 6px;
  }

  .user-name {
    font-size: 13px;
    max-width: 80px;
  }

  .avatar {
    width: 28px;
    height: 28px;
  }
}

// Large screens (1200px and above)
@media (min-width: 1200px) {
  .header-wrapper {
    padding: 0 24px;
  }

  .user-info {
    gap: 10px;
    padding: 10px 16px;
  }

  .user-greeting {
    font-size: 15px;
  }

  .user-name {
    font-size: 16px;
  }

  .avatar {
    width: 40px;
    height: 40px;
  }
}