import React, { useState, useEffect } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import styles from './Header.module.scss'
import tapLogo from '../../../assets/images/logo.png'
import { useGetCartHook } from '@/hooks/cart/useCart'
import { ROUTES } from '@/constants/routes'
import { useSelector, useDispatch } from 'react-redux'
import type { RootState } from '@/libs/state/store'
import { logout } from '@/libs/features/auth/authSlice'
import { useLogout } from '@/hooks/auth/useLogin'
import NotificationModal from '@/components/Notification/NotificationModal'

import { Upload, Button, Modal, message, Dropdown, Menu } from 'antd'
import { InboxOutlined, LogoutOutlined, UserOutlined } from '@ant-design/icons'
import { useSearchProductByImageHook } from '@/hooks/product/useproduct'
import { getAvatarProps } from '@/utils/avatar'
import { useGetUserProfileHook } from '@/hooks/user'

interface HeaderProps {
  onSearch?: (query: string) => void
  onImageSearchResult?: (result: any) => void
  cartItemCount?: number
}

const Header: React.FC<HeaderProps> = ({
  onSearch,
  onImageSearchResult,
  cartItemCount: propCartItemCount,
}) => {
  // Tìm kiếm bằng hình ảnh
  const [showImageSearch, setShowImageSearch] = useState(false)
  const [imageSearchLoading, setImageSearchLoading] = useState(false)
  const [imageSearchResult, setImageSearchResult] = useState<any>(null)
  const { search: searchByImage, isLoading: isImageSearchLoading } = useSearchProductByImageHook()

  const handleOpenImageSearch = () => setShowImageSearch(true)
  const handleCloseImageSearch = () => {
    setShowImageSearch(false)
    setImageSearchResult(null)
  }
  const handleDraggerChange = async (info: any) => {
    const { status } = info.file
    if (status === 'done' || status === 'success' || info.file.originFileObj) {
      const file = info.file.originFileObj
      if (file) {
        setImageSearchLoading(true)
        try {
          const res = await searchByImage(file)
          setImageSearchResult(res)

          if (onImageSearchResult) {
            // Nếu có callback prop, gọi nó (cho Commerce page)
            onImageSearchResult(res)
          } else {
            // Nếu không có callback, navigate đến Commerce page với image search result
            // Sử dụng sessionStorage để truyền data
            sessionStorage.setItem('imageSearchResult', JSON.stringify(res))
            navigate(ROUTES.COMMERCE + '?imageSearch=true')
          }

          setShowImageSearch(false) // Đóng modal sau khi tìm kiếm thành công
        } catch (err) {
          message.error('Lỗi khi nhận diện hình ảnh')
        }
        setImageSearchLoading(false)
      }
    }
  }
  const [searchQuery, setSearchQuery] = useState('')
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isNotificationModalOpen, setIsNotificationModalOpen] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()

  // Lấy thông tin user từ Redux
  const auth = useSelector((state: RootState) => state.auth)
  const { Logoutfunc } = useLogout()

  // Lấy cart count từ query hook - tự động refetch khi cart thay đổi
  const cartHookResult = auth?.id ? useGetCartHook() : null
  const cartData = cartHookResult?.data || null
  const refetchCart = cartHookResult?.refetch || (() => {})

  const cartItems = (cartData && (Array.isArray(cartData) ? cartData : cartData.data)) || []
  const cartItemCount = propCartItemCount || cartItems.length

  // Lấy thông tin profile để có avatar mới nhất - chỉ khi đã đăng nhập
  const { data: userProfile } = auth?.id ? useGetUserProfileHook() : { data: null }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()

    const trimmedQuery = searchQuery.trim()

    // Nếu có search query và không đang ở trang Commerce
    if (trimmedQuery && location.pathname !== ROUTES.COMMERCE) {
      // Navigate đến trang Commerce với search query
      navigate(`${ROUTES.COMMERCE}?search=${encodeURIComponent(trimmedQuery)}`)
    } else if (trimmedQuery && location.pathname === ROUTES.COMMERCE) {
      // Nếu đã ở trang Commerce, trigger search trực tiếp
      if (onSearch) {
        onSearch(trimmedQuery)
      }
    }
  }

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)
  }

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (onSearch) {
        onSearch(searchQuery)
      }
    }, 300) // 300ms delay

    return () => clearTimeout(timeoutId)
  }, [searchQuery, onSearch])

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  const handleNotificationClick = () => {
    setIsNotificationModalOpen(true)
  }

  const handleAccountClick = () => {
    if (auth?.id) {
      navigate(ROUTES.PROFILE)
    } else {
      navigate(ROUTES.LOGIN)
    }
  }

  const handleCartClick = async () => {
    // Gọi API cart/me trước khi navigate để đảm bảo có dữ liệu mới nhất
    if (auth?.id) {
      await refetchCart()
    }
    navigate(ROUTES.CART)
  }

  const handleFavoritesClick = () => {
    if (auth?.id) {
      navigate(ROUTES.FAVORITES)
    } else {
      navigate(ROUTES.LOGIN)
    }
  }

  const handleLogout = async () => {
    try {
      await Logoutfunc()
    } catch (error) {
      console.error('Logout failed:', error)
      // Fallback - force redirect anyway
      window.location.href = ROUTES.LOGIN
    }
  }

  // Hàm tiện ích để kiểm tra và thêm class active
  const getNavLinkClass = (path: string) => {
    let isActive = false

    if (path === ROUTES.COMMERCE) {
      // Tab "Mua sắm" active cho các trang liên quan đến commerce
      isActive =
        location.pathname === ROUTES.COMMERCE ||
        location.pathname === ROUTES.CART ||
        location.pathname === ROUTES.FAVORITES ||
        location.pathname.startsWith('/gio-hang') ||
        location.pathname.startsWith('/san-pham')
    } else {
      // Các tab khác chỉ active khi exact match
      isActive = location.pathname === path
    }

    return `${styles.navLink} ${isActive ? styles.active : ''}`
  }

  const getMobileLinkClass = (path: string) => {
    let isActive = false

    if (path === ROUTES.COMMERCE) {
      // Tab "Mua sắm" active cho các trang liên quan đến commerce
      isActive =
        location.pathname === ROUTES.COMMERCE ||
        location.pathname === ROUTES.CART ||
        location.pathname === ROUTES.FAVORITES ||
        location.pathname.startsWith('/gio-hang') ||
        location.pathname.startsWith('/san-pham')
    } else {
      // Các tab khác chỉ active khi exact match
      isActive = location.pathname === path
    }

    return `${styles.mobileMenuLink} ${isActive ? styles.active : ''}`
  }

  return (
    <>
      {/* Desktop Header */}
      <header className={`${styles.header} ${styles.desktopHeader}`}>
        <div className={styles.container}>
          <div className={styles.logoSection}>
            <Link to={ROUTES.HOME}>
              <img src={tapLogo} alt="TAP" className={styles.logo} />
            </Link>
            <div className={styles.tagline}>Nơi bắt đầu cuộc sống thịnh vượng</div>
          </div>

          <div className={styles.searchSection}>
            <form onSubmit={handleSearch} className={styles.searchForm}>
              <input
                type="text"
                placeholder="Tìm kiếm sản phẩm, dịch vụ..."
                value={searchQuery}
                onChange={handleSearchChange}
                className={styles.searchInput}
              />
              <button type="submit" className={styles.searchButton}>
                <svg className={styles.searchIcon} viewBox="0 0 24 24">
                  <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
                </svg>
              </button>
            </form>
            <button
              className={styles.imageSearchButton}
              onClick={handleOpenImageSearch}
              disabled={imageSearchLoading || isImageSearchLoading}
            >
              <InboxOutlined className={styles.imageSearchIcon} />
              <span className={styles.imageSearchText}>Tìm bằng ảnh</span>
            </button>
          </div>
          <div className={styles.userActions}>
            <button className={styles.notificationBtn} onClick={handleNotificationClick}>
              <svg className={styles.icon} viewBox="0 0 24 24">
                <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z" />
              </svg>
              Thông báo
            </button>
            {auth?.id ? (
              <Dropdown
                overlay={
                  <Menu>
                    <Menu.Item key="profile" icon={<UserOutlined />} onClick={handleAccountClick}>
                      Tài khoản
                    </Menu.Item>
                    {/* Quản lý - chỉ hiển thị với quản trị viên */}
                    {auth?.role?.role_code === 'R05' && (
                      <Menu.Item
                        key="admin"
                        icon={<UserOutlined />}
                        onClick={() => navigate(ROUTES.USER_MANAGEMENT)}
                      >
                        Quản lý
                      </Menu.Item>
                    )}
                    <Menu.Divider />
                    <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
                      Đăng xuất
                    </Menu.Item>
                  </Menu>
                }
                trigger={['click']}
                placement="bottomRight"
              >
                <button className={styles.accountBtn} style={{ minWidth: 100 }}>
                  <img
                    {...getAvatarProps(userProfile?.avatarUrl, userProfile?.fullName)}
                    className={styles.userAvatar}
                    style={{ width: 24, height: 24, borderRadius: '50%', marginRight: 8 }}
                  />
                  Tài khoản
                </button>
              </Dropdown>
            ) : (
              <button className={styles.accountBtn} onClick={handleAccountClick}>
                <svg className={styles.icon} viewBox="0 0 24 24">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                </svg>
                Đăng nhập
              </button>
            )}
            <button className={styles.favoritesBtn} onClick={handleFavoritesClick}>
              <svg className={styles.icon} viewBox="0 0 24 24">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
              </svg>
              Yêu thích
            </button>
            <button className={styles.cartBtn} onClick={handleCartClick}>
              <svg className={styles.icon} viewBox="0 0 24 24">
                <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" />
              </svg>
              Giỏ hàng
              {cartItemCount > 0 && <span className={styles.cartBadge}>{cartItemCount}</span>}
            </button>
          </div>
        </div>

        <nav className={styles.navigation}>
          <div className={styles.container}>
            <ul className={styles.navList}>
              <li className={styles.navItem}>
                <Link to={ROUTES.HOME} className={getNavLinkClass(ROUTES.HOME)}>
                  Trang chủ
                </Link>
              </li>
              <li className={styles.navItem}>
                <Link to={ROUTES.COMMERCE} className={getNavLinkClass(ROUTES.COMMERCE)}>
                  Mua sắm
                </Link>
              </li>
              <li className={styles.navItem}>
                <Link to={ROUTES.SERVICES} className={getNavLinkClass(ROUTES.SERVICES)}>
                  Dịch vụ HTX
                </Link>
              </li>
              <li className={styles.navItem}>
                <Link to={ROUTES.NEWS} className={getNavLinkClass(ROUTES.NEWS)}>
                  Tin tức - Sự kiện
                </Link>
              </li>

              <li className={styles.navItem}>
                <Link to={ROUTES.TRANSFER} className={getNavLinkClass(ROUTES.TRANSFER)}>
                  Chuyển đổi số
                </Link>
              </li>
              <li className={styles.navItem}>
                <Link to={ROUTES.CONTACT} className={getNavLinkClass(ROUTES.CONTACT)}>
                  Liên hệ
                </Link>
              </li>
            </ul>
          </div>
        </nav>
      </header>

      {/* Mobile Header */}
      <header className={`${styles.header} ${styles.mobileHeader}`}>
        <div className={styles.mobileTopBar}>
          <div className={styles.mobileLeft}>
            <button className={styles.menuButton} onClick={toggleMobileMenu} aria-label="Menu">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z" />
              </svg>
            </button>
            <Link to={ROUTES.HOME}>
              <img src={tapLogo} alt="TAP" className={styles.mobileLogo} />
            </Link>
          </div>
          <div className={styles.mobileRight}>
            <button
              className={styles.mobileImageSearchBtn}
              onClick={handleOpenImageSearch}
              disabled={imageSearchLoading || isImageSearchLoading}
            >
              <InboxOutlined className={styles.icon} />
            </button>
            <button className={styles.mobileSearchBtn} onClick={() => navigate(ROUTES.COMMERCE)}>
              <svg className={styles.icon} viewBox="0 0 24 24">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
              </svg>
            </button>
            <button className={styles.mobileFavoritesBtn} onClick={handleFavoritesClick}>
              <svg className={styles.icon} viewBox="0 0 24 24">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
              </svg>
            </button>
            <button className={styles.mobileNotificationBtn} onClick={handleNotificationClick}>
              <svg className={styles.icon} viewBox="0 0 24 24">
                <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z" />
              </svg>
            </button>
            <button className={styles.mobileCartBtn} onClick={handleCartClick}>
              <svg className={styles.icon} viewBox="0 0 24 24">
                <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" />
              </svg>
              {cartItemCount > 0 && <span className={styles.cartBadge}>{cartItemCount}</span>}
            </button>
          </div>
        </div>

        {/* Mobile Menu Overlay */}
        {isMobileMenuOpen && (
          <div className={styles.mobileMenuOverlay} onClick={toggleMobileMenu}>
            <div className={styles.mobileMenuContent} onClick={(e) => e.stopPropagation()}>
              <div className={styles.mobileMenuHeader}>
                <img src={tapLogo} alt="TAP" className={styles.mobileLogo} />
                <button className={styles.closeMenuBtn} onClick={toggleMobileMenu}>
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                  </svg>
                </button>
              </div>
              <nav className={styles.mobileMenuNav}>
                <ul className={styles.mobileMenuList}>
                  <li>
                    <Link
                      to={ROUTES.HOME}
                      className={getMobileLinkClass(ROUTES.HOME)}
                      onClick={toggleMobileMenu}
                    >
                      Trang chủ
                    </Link>
                  </li>
                  <li>
                    <Link
                      to={ROUTES.SERVICES}
                      className={getMobileLinkClass(ROUTES.SERVICES)}
                      onClick={toggleMobileMenu}
                    >
                      Dịch vụ HTX
                    </Link>
                  </li>
                  <li>
                    <Link
                      to={ROUTES.NEWS}
                      className={getMobileLinkClass(ROUTES.NEWS)}
                      onClick={toggleMobileMenu}
                    >
                      Tin tức - Sự kiện
                    </Link>
                  </li>
                  <li>
                    <Link
                      to={ROUTES.COMMERCE}
                      className={getMobileLinkClass(ROUTES.COMMERCE)}
                      onClick={toggleMobileMenu}
                    >
                      Mua sắm
                    </Link>
                  </li>
                  <li>
                    <Link
                      to={ROUTES.FAVORITES}
                      className={getMobileLinkClass(ROUTES.FAVORITES)}
                      onClick={toggleMobileMenu}
                    >
                      Sản phẩm yêu thích
                    </Link>
                  </li>
                  <li>
                    <Link
                      to={ROUTES.MY_ORDER}
                      className={getMobileLinkClass(ROUTES.MY_ORDER)}
                      onClick={toggleMobileMenu}
                    >
                      Đơn hàng
                    </Link>
                  </li>
                  <li>
                    <Link
                      to={ROUTES.CART}
                      className={getMobileLinkClass(ROUTES.CART)}
                      onClick={toggleMobileMenu}
                    >
                      Giỏ hàng
                    </Link>
                  </li>
                  <li>
                    <Link
                      to={ROUTES.PROFILE}
                      className={getMobileLinkClass(ROUTES.PROFILE)}
                      onClick={toggleMobileMenu}
                    >
                      Tài khoản
                    </Link>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        )}
      </header>

      {/* Modal tìm kiếm bằng ảnh - Dùng chung cho cả desktop và mobile */}
      <Modal
        title={<span>Tìm kiếm sản phẩm bằng hình ảnh</span>}
        open={showImageSearch}
        onCancel={handleCloseImageSearch}
        footer={null}
        centered
        width={480}
        styles={{ body: { paddingTop: 16 } }}
        destroyOnClose
      >
        <Upload.Dragger
          name="image"
          accept="image/*"
          customRequest={({ file, onSuccess }) => {
            setTimeout(() => {
              if (onSuccess) onSuccess('ok')
            }, 0)
          }}
          showUploadList={false}
          disabled={imageSearchLoading || isImageSearchLoading}
          multiple={false}
          onChange={handleDraggerChange}
          style={{ marginBottom: 16 }}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">Nhấn hoặc kéo thả ảnh vào đây để tìm kiếm</p>
          <p className="ant-upload-hint">Chỉ hỗ trợ file ảnh</p>
        </Upload.Dragger>
        {imageSearchLoading || isImageSearchLoading ? (
          <div style={{ textAlign: 'center', margin: '16px 0' }}>Đang nhận diện...</div>
        ) : imageSearchResult ? (
          <div style={{ marginTop: 16 }}>
            <h4>Kết quả nhận diện:</h4>
            {imageSearchResult.error ? (
              <div style={{ color: 'red' }}>{imageSearchResult.error}</div>
            ) : (
              <>
                <div>
                  Nhóm AI nhận diện:{' '}
                  {Array.isArray(imageSearchResult.detectedCategories)
                    ? imageSearchResult.detectedCategories
                        .map((c: any) => c.name || c.class || '')
                        .filter((v: string) => v)
                        .join(', ')
                    : ''}
                </div>
                <div>
                  Số sản phẩm tìm thấy:{' '}
                  {Array.isArray(imageSearchResult.products)
                    ? imageSearchResult.products.length
                    : 0}
                </div>
                <div style={{ maxHeight: 200, overflowY: 'auto' }}>
                  {Array.isArray(imageSearchResult.products) &&
                    imageSearchResult.products.map((p: any) => (
                      <div key={p.id} style={{ borderBottom: '1px solid #eee', padding: 4 }}>
                        <b>{p.name}</b> - {p.price?.toLocaleString()}đ
                      </div>
                    ))}
                </div>
              </>
            )}
          </div>
        ) : null}
        <div style={{ textAlign: 'right', marginTop: 24 }}>
          <Button onClick={handleCloseImageSearch} type="default">
            Đóng
          </Button>
        </div>
      </Modal>

      {/* Notification Modal */}
      <NotificationModal
        visible={isNotificationModalOpen}
        onClose={() => setIsNotificationModalOpen(false)}
      />
    </>
  )
}

export default Header
