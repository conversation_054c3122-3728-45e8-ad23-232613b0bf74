// src/pages/m1/Register/Register.module.scss
.container {
  min-height: 100vh;
  width: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
}

.header {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 10px 0;
}

.logo {
  width: 150px;
  height: 150px;

  @media (min-width: 768px) {
    width: 150px;
    height: 150px;
  }
}

.title {
  font-size: 2.2rem;
  font-weight: bold;
  color: #2e2e2e;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  user-select: none;

  @media (min-width: 768px) {
    font-size: 3rem;
  }
}

.formContainer {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 500px;
  margin: 0;

  @media (min-width: 768px) {
    max-width: 500px;
    padding: 2.5rem;
  }
}

.subTitle {
  font-size: 1.2rem;
  color: #7a7a7a;
  text-align: center;
  margin-bottom: 1rem;
  user-select: none;

  @media (min-width: 768px) {
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
  }
}

.socialButtons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;

  @media (min-width: 768px) {
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
}

.socialButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  color: #2e2e2e;
  padding: 0.5rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  width: 150px;

  @media (min-width: 768px) {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    width: 180px;
  }

  &:hover {
    background-color: #f5f5f5;
    border-color: #34a853;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.divider {
  text-align: center;
  color: #7a7a7a;
  margin-bottom: 1rem;
  position: relative;

  @media (min-width: 768px) {
    margin-bottom: 1.5rem;
  }
}

.dividerLine {
  border-bottom: 1px solid #e0e0e0;
  position: absolute;
  top: 50%;
  width: 45%;
  transform: translateY(-50%);

  &.left {
    left: 0;
  }

  &.right {
    right: 0;
  }
}

.dividerText {
  background-color: #ffffff;
  padding: 0 0.5rem;
  position: relative;
  z-index: 1;
  font-size: 1rem;

  @media (min-width: 768px) {
    padding: 0 1rem;
    font-size: 1.1rem;
  }
}

.label {
  color: #2e2e2e;
  font-size: 1rem;
  margin-bottom: 0.3rem;

  @media (min-width: 768px) {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 0.8rem;

  @media (min-width: 768px) {
    margin-bottom: 1rem;
  }
}

.input {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  color: #2e2e2e;
  width: 100%;
  padding: 0.6rem 2.5rem 0.6rem 2rem;
  font-size: 1rem;
  transition: all 0.2s ease;

  @media (min-width: 768px) {
    padding: 0.75rem 2.5rem 0.75rem 2.5rem;
    font-size: 1.1rem;
  }

  &:focus {
    border-color: #34a853;
    box-shadow: 0 0 0 0.2rem rgba(52, 168, 83, 0.4);
    outline: none;
  }
}

.inputIcon {
  position: absolute;
  left: 0.5rem;
  color: #7a7a7a;

  @media (min-width: 768px) {
    left: 0.75rem;
  }
}

.passwordToggle {
  position: absolute;
  right: 0.5rem;
  cursor: pointer;
  color: #7a7a7a;

  @media (min-width: 768px) {
    right: 0.75rem;
  }
}

.checkboxContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;

  @media (min-width: 768px) {
    margin-bottom: 1.5rem;
  }
}

.checkbox {
  color: #2e2e2e;
  display: flex;
  align-items: center;
  font-size: 1rem;
  gap: 0.3rem; // Thêm dòng này để tạo khoảng cách
  cursor: pointer; // Thêm cursor pointer cho UX tốt hơn

  @media (min-width: 768px) {
    font-size: 1.1rem;
  }
}

.link {
  color: #2d9cdb;
  text-decoration: none;
  font-size: 1rem;

  @media (min-width: 768px) {
    font-size: 1.1rem;
  }

  &:hover {
    color: #34a853;
    text-decoration: underline;
  }
}

.submitButton {
  background-color: #2d9cdb;
  border-color: #2d9cdb;
  color: #ffffff;
  width: 100%;
  padding: 0.6rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 1rem;
  border: none;
  cursor: pointer;

  @media (min-width: 768px) {
    padding: 0.75rem;
    font-size: 1.1rem;
  }

  &:hover:not(:disabled) {
    background-color: #34a853;
    border-color: #34a853;
    transform: scale(1.05);
  }

  &:disabled {
    background-color: #b2dfdb;
    border-color: #b2dfdb;
    cursor: not-allowed;
  }
}

.loginContainer {
  text-align: center;
  margin-top: 1rem;
  font-size: 1rem;

  @media (min-width: 768px) {
    font-size: 1.1rem;
    margin-top: 1.5rem;
  }
}

.verifyButton {
  background: none;
  border: none;
  color: #2d9cdb;
  padding: 0.5rem 0;
  cursor: pointer;
  font-size: 0.9rem;
  text-align: left;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;

  @media (min-width: 768px) {
    font-size: 1rem;
  }

  &:disabled {
    color: #b2dfdb;
    cursor: not-allowed;
  }

  &:hover:not(:disabled) {
    text-decoration: underline;
  }
}

.otpInputWrapper {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  margin-bottom: 0.8rem;

  @media (min-width: 768px) {
    margin-bottom: 1rem;
  }
}

.otpInput {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  color: #2e2e2e;
  width: 100%;
  padding: 0.6rem 1rem;
  font-size: 1rem;
  transition: all 0.2s ease;

  @media (min-width: 768px) {
    font-size: 1.1rem;
  }

  &:focus {
    border-color: #34a853;
    box-shadow: 0 0 0 0.2rem rgba(52, 168, 83, 0.4);
    outline: none;
  }
}

// Animation classes
.fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

.slideUp {
  animation: slideUp 0.4s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}