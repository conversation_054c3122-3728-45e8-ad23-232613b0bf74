// User Profile Permissions
export const USER_PROFILE_PERMISSIONS = {
  VIEW_SELF: 'PERM_USER_VIEW_SELF',
  UPDATE_SELF: 'PERM_USER_UPDATE_SELF',
  TOGGLE_2FA: 'PERM_USER_2FA_TOGGLE',
  VIEW_SESSIONS: 'PERM_SESSION_LIST',
} as const

// Admin User Management Permissions
export const ADMIN_USER_PERMISSIONS = {
  LIST: 'PERM_USER_LIST',
  VIEW_DETAIL: 'PERM_USER_VIEW_DETAIL',
  UPDATE: 'PERM_USER_UPDATE',
  DELETE: 'PERM_USER_DELETE',
  FULL_ACCESS: 'PERM_ADMIN_FULL_ACCESS',
} as const

// Affiliate Permissions
export const AFFILIATE_PERMISSIONS = {
  LIST: 'PERM_AFFILIATE_LIST',
} as const

// E-commerce Permissions
export const ECOMMERCE_PERMISSIONS = {
  // Order Permissions
  ORDER_CREATE: 'PERM_ORDER_CREATE',
  ORDER_VIEW_SELF: 'PERM_ORDER_VIEW_SELF',
  ORDER_VIEW_ALL: 'PERM_ORDER_VIEW_ALL',
  ORDER_UPDATE: 'PERM_ORDER_UPDATE',
  ORDER_DELETE: 'PERM_ORDER_DELETE',

  // Cart & Favorite Permissions
  CART_MANAGE: 'PERM_CART_MANAGE',
  FAVORITE_MANAGE: 'PERM_FAVORITE_MANAGE',

  // Product Permissions
  PRODUCT_VIEW: 'PERM_PRODUCT_VIEW',
  PRODUCT_MANAGE: 'PERM_PRODUCT_MANAGE',
  CATEGORY_MANAGE: 'PERM_CATEGORY_MANAGE',
  INVENTORY_MANAGE: 'PERM_INVENTORY_MANAGE',
} as const

// Permission Groups for different user types
export const PERMISSION_GROUPS = {
  // Regular User (Customer)
  CUSTOMER: [
    USER_PROFILE_PERMISSIONS.VIEW_SELF,
    USER_PROFILE_PERMISSIONS.UPDATE_SELF,
    USER_PROFILE_PERMISSIONS.TOGGLE_2FA,
    USER_PROFILE_PERMISSIONS.VIEW_SESSIONS,
    ECOMMERCE_PERMISSIONS.ORDER_CREATE,
    ECOMMERCE_PERMISSIONS.ORDER_VIEW_SELF, // ✅ THÊM: Customer cần xem đơn hàng của mình
    ECOMMERCE_PERMISSIONS.CART_MANAGE,
    ECOMMERCE_PERMISSIONS.FAVORITE_MANAGE,
    ECOMMERCE_PERMISSIONS.PRODUCT_VIEW,
  ],

  // Seller (Business User)
  SELLER: [
    USER_PROFILE_PERMISSIONS.VIEW_SELF,
    USER_PROFILE_PERMISSIONS.UPDATE_SELF,
    USER_PROFILE_PERMISSIONS.TOGGLE_2FA,
    USER_PROFILE_PERMISSIONS.VIEW_SESSIONS,
    ECOMMERCE_PERMISSIONS.ORDER_CREATE,
    ECOMMERCE_PERMISSIONS.ORDER_VIEW_SELF,
    ECOMMERCE_PERMISSIONS.CART_MANAGE,
    ECOMMERCE_PERMISSIONS.FAVORITE_MANAGE,
    ECOMMERCE_PERMISSIONS.PRODUCT_VIEW,
    ECOMMERCE_PERMISSIONS.PRODUCT_MANAGE,
    ECOMMERCE_PERMISSIONS.CATEGORY_MANAGE,
    ECOMMERCE_PERMISSIONS.INVENTORY_MANAGE,
    ECOMMERCE_PERMISSIONS.ORDER_VIEW_ALL,
    ECOMMERCE_PERMISSIONS.ORDER_UPDATE,
  ],

  // Admin
  ADMIN: [
    USER_PROFILE_PERMISSIONS.VIEW_SELF,
    USER_PROFILE_PERMISSIONS.UPDATE_SELF,
    USER_PROFILE_PERMISSIONS.TOGGLE_2FA,
    USER_PROFILE_PERMISSIONS.VIEW_SESSIONS,
    ECOMMERCE_PERMISSIONS.ORDER_CREATE,
    ECOMMERCE_PERMISSIONS.ORDER_VIEW_SELF,
    ECOMMERCE_PERMISSIONS.CART_MANAGE,
    ECOMMERCE_PERMISSIONS.FAVORITE_MANAGE,
    ECOMMERCE_PERMISSIONS.PRODUCT_VIEW,
    ECOMMERCE_PERMISSIONS.PRODUCT_MANAGE,
    ECOMMERCE_PERMISSIONS.CATEGORY_MANAGE,
    ECOMMERCE_PERMISSIONS.INVENTORY_MANAGE,
    ECOMMERCE_PERMISSIONS.ORDER_VIEW_ALL,
    ECOMMERCE_PERMISSIONS.ORDER_UPDATE,
    ADMIN_USER_PERMISSIONS.LIST,
    ADMIN_USER_PERMISSIONS.VIEW_DETAIL,
    ADMIN_USER_PERMISSIONS.UPDATE,
    ADMIN_USER_PERMISSIONS.DELETE,
    ADMIN_USER_PERMISSIONS.FULL_ACCESS,
    AFFILIATE_PERMISSIONS.LIST,
    ECOMMERCE_PERMISSIONS.ORDER_DELETE,
  ],
} as const

// Helper function to check if user has any of the required permissions
export const hasAnyPermission = (
  userPermissions: string[],
  requiredPermissions: string[],
  userRole?: string
): boolean => {
  // Admin role (R05) has full access to all routes
  if (userRole === 'R05') {
    return true
  }

  // Users with admin full access permission have access to all routes
  if (userPermissions.includes('PERM_ADMIN_FULL_ACCESS')) {
    return true
  }

  return requiredPermissions.some((permission) => userPermissions.includes(permission))
}

// Helper function to check if user has all required permissions
export const hasAllPermissions = (
  userPermissions: string[],
  requiredPermissions: string[],
  userRole?: string
): boolean => {
  // Admin role (R05) has full access to all routes
  if (userRole === 'R05') {
    return true
  }

  // Users with admin full access permission have access to all routes
  if (userPermissions.includes('PERM_ADMIN_FULL_ACCESS')) {
    return true
  }

  return requiredPermissions.every((permission) => userPermissions.includes(permission))
}

// Permission descriptions for UI
export const PERMISSION_DESCRIPTIONS = {
  [USER_PROFILE_PERMISSIONS.VIEW_SELF]: 'Xem hồ sơ cá nhân',
  [USER_PROFILE_PERMISSIONS.UPDATE_SELF]: 'Cập nhật hồ sơ cá nhân',
  [USER_PROFILE_PERMISSIONS.TOGGLE_2FA]: 'Bật/tắt xác thực 2 yếu tố',
  [USER_PROFILE_PERMISSIONS.VIEW_SESSIONS]: 'Xem phiên đăng nhập',

  [ADMIN_USER_PERMISSIONS.LIST]: 'Xem danh sách người dùng',
  [ADMIN_USER_PERMISSIONS.VIEW_DETAIL]: 'Xem chi tiết người dùng',
  [ADMIN_USER_PERMISSIONS.UPDATE]: 'Cập nhật người dùng',
  [ADMIN_USER_PERMISSIONS.DELETE]: 'Xóa người dùng',
  [ADMIN_USER_PERMISSIONS.FULL_ACCESS]: 'Quyền truy cập toàn bộ hệ thống',

  [AFFILIATE_PERMISSIONS.LIST]: 'Xem danh sách affiliate',

  [ECOMMERCE_PERMISSIONS.ORDER_CREATE]: 'Tạo đơn hàng',
  [ECOMMERCE_PERMISSIONS.ORDER_VIEW_SELF]: 'Xem đơn hàng của mình',
  [ECOMMERCE_PERMISSIONS.ORDER_VIEW_ALL]: 'Xem tất cả đơn hàng',
  [ECOMMERCE_PERMISSIONS.ORDER_UPDATE]: 'Cập nhật đơn hàng',
  [ECOMMERCE_PERMISSIONS.ORDER_DELETE]: 'Xóa đơn hàng',
  [ECOMMERCE_PERMISSIONS.CART_MANAGE]: 'Quản lý giỏ hàng',
  [ECOMMERCE_PERMISSIONS.FAVORITE_MANAGE]: 'Quản lý sản phẩm yêu thích',
  [ECOMMERCE_PERMISSIONS.PRODUCT_VIEW]: 'Xem sản phẩm',
  [ECOMMERCE_PERMISSIONS.PRODUCT_MANAGE]: 'Quản lý sản phẩm',
  [ECOMMERCE_PERMISSIONS.CATEGORY_MANAGE]: 'Quản lý danh mục',
  [ECOMMERCE_PERMISSIONS.INVENTORY_MANAGE]: 'Quản lý hàng tồn kho',
} as const
