import {
  useGetMyOrdersQuery,
  useGetOrderByIdQuery,
  useCreateOrderMutation,
  useRequestCancelOrderMutation,
  useConfirmReceivedOrderMutation,
  type CreateOrderRequest,
} from '@/services/M2/Orders/order'
import { useToast } from '@/components/Toast'
import { useEffect } from 'react'
import { useLoadingStore } from '@/utils/loadingStore'

// Hook cho GET: lấy danh sách đơn hàng
const useGetMyOrdersHook = () => {
  const { data, error, isLoading, refetch, isFetching } = useGetMyOrdersQuery()
  const { setLoading } = useLoadingStore()
  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching])
  return { data, isLoading, error, refetch }
}

// Hook cho GET: lấy chi tiết đơn hàng
const useGetOrderByIdHook = (id: string) => {
  const { data, error, isLoading, refetch, isFetching } = useGetOrderByIdQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true,
    refetchOnReconnect: true,
  })
  const { setLoading } = useLoadingStore()
  useEffect(() => {
    setLoading(isLoading || isFetching)
  }, [isLoading, isFetching, setLoading])
  return { data, isLoading, error, refetch }
}

// Hook cho POST: tạo đơn hàng
const useCreateOrderHook = () => {
  const { showSuccess, showError } = useToast()
  const [createFunc] = useCreateOrderMutation()
  const create = async (data: CreateOrderRequest): Promise<boolean> => {
    try {
      const res = await createFunc(data)
      if (!res?.error) {
        showSuccess('Tạo đơn hàng thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Tạo đơn hàng thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Tạo đơn hàng thất bại ' + e)
      return false
    }
  }
  return create
}

// Hook cho POST: yêu cầu hủy đơn hàng
const useRequestCancelOrderHook = () => {
  const { showSuccess, showError } = useToast()
  const [cancelFunc] = useRequestCancelOrderMutation()
  const cancel = async (id: string): Promise<boolean> => {
    try {
      const res = await cancelFunc(id)
      if (!res?.error) {
        showSuccess('Yêu cầu hủy đơn hàng thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Yêu cầu hủy đơn hàng thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Yêu cầu hủy đơn hàng thất bại ' + e)
      return false
    }
  }
  return cancel
}

// Hook cho POST: xác nhận đã nhận hàng
const useConfirmReceivedOrderHook = () => {
  const { showSuccess, showError } = useToast()
  const [confirmFunc] = useConfirmReceivedOrderMutation()
  const confirm = async (id: string): Promise<boolean> => {
    try {
      const res = await confirmFunc(id)
      if (!res?.error) {
        showSuccess('Xác nhận đã nhận hàng thành công')
        return true
      } else {
        const messageDes = (res.error as { data: { message: string } })?.data.message
        showError('Xác nhận đã nhận hàng thất bại ' + messageDes)
        return false
      }
    } catch (e) {
      showError('Xác nhận đã nhận hàng thất bại ' + e)
      return false
    }
  }
  return confirm
}

export {
  useGetMyOrdersHook,
  useGetOrderByIdHook,
  useCreateOrderHook,
  useRequestCancelOrderHook,
  useConfirmReceivedOrderHook,
}
