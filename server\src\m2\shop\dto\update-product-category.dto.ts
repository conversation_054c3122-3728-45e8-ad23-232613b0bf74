import { PartialType, ApiPropertyOptional } from '@nestjs/swagger';
import { CreateProductCategoryDto } from './create-product-category.dto';

export class UpdateProductCategoryDto extends PartialType(CreateProductCategoryDto) {
  @ApiPropertyOptional({
    description: 'UUID của category cha (nếu muốn bỏ liên kết thì truyền null)',
    example: null,
    nullable: true,
  })
  parentId?: string | undefined;
}
