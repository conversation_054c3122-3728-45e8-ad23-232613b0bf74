import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { ViteImageOptimizer } from 'vite-plugin-image-optimizer'
import * as path from 'path'
import { createHtmlPlugin } from 'vite-plugin-html'

export default defineConfig({
  plugins: [
    react(),
    ViteImageOptimizer(),
    createHtmlPlugin({
      entry: '/src/main.tsx',
      template: 'public/index.html',
      inject: {
        data: {
          title: 'My Vite App',
          injectScript: '<script src="./inject.js"></script>',
        },
      },
    }),
  ],
  server: {
    port: 3000,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (urlPath) => urlPath.replace(/^\/api/, ''),
      },
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@commerce': path.resolve(__dirname, './src/components/commerce'), // Thêm alias mới
      '@styles': path.resolve(__dirname, './src/styles'), // <PERSON>as cho styles
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@styles/variables.scss";`, // Tự động import biến SCSS
      },
    },
  },
})
