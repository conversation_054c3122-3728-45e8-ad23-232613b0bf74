import { Injectable, NotFoundException, OnModuleInit } from '@nestjs/common';
import axios from 'axios';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductImage } from '../entities/product_image.entity';
import { Product } from '../entities/products.entity';
import { AiImageVector } from '../entities/ai_image_vectors.entity';

@Injectable()
export class ImageSearchService implements OnModuleInit {
  async onModuleInit() {
    console.log('[ImageSearch] Bắt đầu đồng bộ vectors lên FAISS...');
    try {
      await this.syncVectorsToFaiss();
      console.log('[ImageSearch] FAISS index đã sẵn sàng!');
    } catch (err) {
      console.error('[ImageSearch] Lỗi đồng bộ FAISS:', err);
    }
  }
  constructor(
    @InjectRepository(ProductImage)
    private readonly imageRepo: Repository<ProductImage>,
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,
    @InjectRepository(AiImageVector)
    private readonly vectorRepo: Repository<AiImageVector>,
  ) {}

  async searchProductByImage(file: Express.Multer.File, userId: string) {
    // 1. Gửi file lên CLIP service để lấy vector
    const clipServiceBase = process.env.CLIP_SERVICE_URL || 'http://localhost:8001';
    const clipServiceUrl = `${clipServiceBase}/vectorize`;
    const FormData = require('form-data');
    const form = new FormData();
    form.append('file', file.buffer, { filename: file.originalname });
    const response = await axios.post(clipServiceUrl, form, {
      headers: form.getHeaders(),
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });
    const vector = response.data.vector;
    console.log('[ImageSearch] Vector from CLIP:', vector ? vector.slice(0, 5) : null);

    // 2. Gửi vector sang FAISS service để tìm nearest image_id
    const faissServiceBase = process.env.FAISS_SERVICE_URL || 'http://localhost:8002';
    const faissServiceUrl = `${faissServiceBase}/search_vector`;
    console.log('[ImageSearch] Sending vector to FAISS:', faissServiceUrl);
    const faissRes = await axios.post(faissServiceUrl, {
      vector: vector,
      top_k: 1,
    });
    const results = faissRes.data.results;
    console.log('[ImageSearch] FAISS results:', results);
    if (!results || results.length === 0) {
      console.log('[ImageSearch] Không tìm thấy sản phẩm phù hợp trong FAISS');
      throw new NotFoundException('Không tìm thấy sản phẩm phù hợp');
    }
    const imageId = results[0];
    console.log('[ImageSearch] Nearest imageId:', imageId);

    // 3. Truy vấn DB lấy thông tin sản phẩm theo image_id
    const vectorEntity = await this.vectorRepo.findOne({ where: { image_id: imageId } });
    console.log('[ImageSearch] DB AiImageVector:', vectorEntity);
    if (!vectorEntity) {
      console.log(
        '[ImageSearch] Không tìm thấy vector ảnh sản phẩm trong DB với imageId:',
        imageId,
      );
      throw new NotFoundException('Không tìm thấy ảnh sản phẩm');
    }
    const product = await this.productRepo.findOne({ where: { id: vectorEntity.product_id } });
    console.log('[ImageSearch] DB Product:', product);
    if (!product) {
      console.log(
        '[ImageSearch] Không tìm thấy sản phẩm trong DB với product_id:',
        vectorEntity.product_id,
      );
      throw new NotFoundException('Không tìm thấy sản phẩm');
    }

    // 4. Trả về sản phẩm
    return product;
  }

  // Hàm đồng bộ toàn bộ vectors lên FAISS khi khởi động
  async syncVectorsToFaiss() {
    const allVectors = await this.vectorRepo.find();
    const vectors = allVectors.map((v) => (v.vector ? JSON.parse(v.vector) : []));
    const imageIds = allVectors.map((v) => v.image_id);
    const faissServiceBase = process.env.FAISS_SERVICE_URL || 'http://localhost:8002';
    const faissServiceUrl = `${faissServiceBase}/build_index`;
    console.log('[ImageSearch] [SYNC] Đang gửi vectors lên FAISS:', faissServiceUrl);
    const faissRes = await axios.post(faissServiceUrl, { vectors, image_ids: imageIds });
    if (faissRes.data && faissRes.data.status === 'built') {
      console.log(`[ImageSearch] [SYNC] FAISS đã build xong index, count: ${faissRes.data.count}`);
    } else {
      console.warn('[ImageSearch] [SYNC] FAISS trả về:', faissRes.data);
    }
  }

  // Hàm thêm vector mới vào FAISS khi có sản phẩm mới
  async addVectorToFaiss(vector: number[], imageId: string) {
    const faissServiceBase = process.env.FAISS_SERVICE_URL || 'http://localhost:8002';
    const faissServiceUrl = `${faissServiceBase}/add_vector`;
    console.log('[ImageSearch] Adding new vector to FAISS:', imageId);
    await axios.post(faissServiceUrl, { vector, image_id: imageId });
    console.log('[ImageSearch] Add vector to FAISS done:', imageId);
  }
}
