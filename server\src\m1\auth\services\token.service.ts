import { Injectable, UnauthorizedException, Inject } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from '../../user/entities/user.entity';
import { AuthResponse } from '../dto/auth-response.dto';
import { ConfigService } from '@nestjs/config';
import { RoleGroup } from '../../user/entities/role_groups.entity';
import { RoleGroupPermission } from '../../user/entities/role_group_permissions.entity';

@Injectable()
export class TokenService {
  constructor(
    private readonly config: ConfigService,
    private readonly jwtService: JwtService,
    @InjectRepository(User) private readonly userRepo: Repository<User>,
    @InjectRepository(RoleGroup) private readonly roleGroupRepo: Repository<RoleGroup>,
    @InjectRepository(RoleGroupPermission)
    private readonly rgpRepo: Repository<RoleGroupPermission>,
  ) {}
  /**
   * 5️⃣ Sinh accessToken + refreshToken cho người dùng
   * @param user Đối tượng người dùng
   * @returns AuthResponse chứa token
   */
  async generateTokens(user: User): Promise<AuthResponse> {
    try {
      const roleGroup = await this.roleGroupRepo.findOne({
        where: { code: user.roleGroupCode },
      });

      if (!roleGroup) {
        throw new UnauthorizedException('Không xác định được vai trò nghiệp vụ');
      }

      const rgPermissions = await this.rgpRepo.find({
        where: { roleGroup: { id: roleGroup.id } },
        relations: ['permission'],
      });

      const permissionCodes = rgPermissions.map((p) => p.permission.code);

      const payload = {
        sub: user.id,
        email: user.email,
        roleGroupCode: roleGroup.code,
        permissions: permissionCodes,
      };

      const accessExpires = this.config.get<string>('JWT_ACCESS_EXPIRES_IN') || '15m';
      const refreshExpires = this.config.get<string>('JWT_REFRESH_EXPIRES_IN') || '7d';
      const bcryptSalt = Number(this.config.get<string>('JWT_BCRYPT_SALT') || 10);

      const accessToken = await this.jwtService.signAsync(payload, {
        expiresIn: accessExpires,
      });

      const refreshToken = await this.jwtService.signAsync(payload, {
        expiresIn: refreshExpires,
      });

      const hashedRefresh = await bcrypt.hash(refreshToken, bcryptSalt);
      await this.userRepo.update(user.id, { refreshToken: hashedRefresh });

      return { accessToken, refreshToken };
    } catch (error) {
      console.error('Lỗi khi sinh token:', error);
    }
    return { accessToken: '', refreshToken: '' };
  }

  /**
   * Làm mới accessToken bằng refreshToken hợp lệ
   * @param userId ID người dùng (lấy từ payload refreshToken)
   * @param refreshToken Refresh token người dùng gửi lên
   * @returns AccessToken + RefreshToken mới
   */
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      // Giải mã refreshToken để lấy userId
      const payload: any = await this.jwtService.verifyAsync(refreshToken);
      const userId = payload.sub;

      const user = await this.userRepo.findOneBy({ id: userId });

      if (!user || !user.refreshToken) {
        throw new UnauthorizedException('Tài khoản không hợp lệ hoặc chưa đăng nhập');
      }

      // So sánh refreshToken người dùng gửi với bản đã hash lưu trong DB
      const tokenMatch = await bcrypt.compare(refreshToken, user.refreshToken);
      if (!tokenMatch) {
        throw new UnauthorizedException('Refresh token không hợp lệ');
      }

      // Sinh lại token mới
      return this.generateTokens(user);
    } catch (error) {
      console.error('Lỗi làm mới token:', error);
      throw new UnauthorizedException('Không thể làm mới token');
    }
  }
  /**
   * Làm mới accessToken bằng refreshToken hợp lệ
   * @param userId ID người dùng (lấy từ payload refreshToken)
   * @param refreshToken Refresh token người dùng gửi lên
   * @returns AccessToken + RefreshToken mới
   */
  async getProfileFromToken(token: string) {
    let payload: any;

    try {
      payload = await this.jwtService.verifyAsync(token);
    } catch (e) {
      throw new UnauthorizedException('Token không hợp lệ');
    }

    const user = await this.userRepo.findOne({
      where: { id: payload.sub },
      select: ['id', 'email', 'role', 'createdAt'],
    });

    if (!user) throw new UnauthorizedException('Tài khoản không tồn tại');

    return {
      ...user,
      ...payload,
    };
  }
}
