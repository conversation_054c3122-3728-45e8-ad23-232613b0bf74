// src/m2/shop/dto/create-product.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  IsNumber,
  IsString,
  IsArray,
  ValidateNested,
  IsInt,
  IsObject,
  IsBoolean,
} from 'class-validator';

export class ProductImageDto {
  @ApiProperty()
  @IsOptional()
  image_url: string;

  @ApiProperty()
  @IsOptional()
  public_image_id: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  is_thumbnail?: boolean;
}

export class ProductAttributeDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  attribute_name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  value: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  unit?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  is_searchable: boolean;
}

export class ProductVersionDto {
  @ApiProperty()
  name: string;

  @ApiProperty()
  price: number;

  @ApiProperty()
  stock: number;

  @ApiProperty({ required: false, description: 'Phân loại key-value: size, màu' })
  options?: Record<string, string>;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sku_code?: string;
}

export class CreateProductDto {
  // ...existing code...
  @ApiProperty({
    required: false,
    description: 'ID trạng thái sản phẩm (status_id) - FE không cần truyền khi tạo mới',
  })
  @IsOptional()
  @IsString()
  status_id?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Mô tả sản phẩm do người dùng nhập hoặc AI sinh' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  price: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  unit?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  category_id?: string;

  @ApiProperty({ type: [ProductImageDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageDto)
  product_images?: ProductImageDto[];

  @ApiProperty({ type: [ProductAttributeDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductAttributeDto)
  product_attribute?: ProductAttributeDto[];

  @ApiProperty({ type: [ProductVersionDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductAttributeDto)
  product_versions?: ProductVersionDto[];
}
