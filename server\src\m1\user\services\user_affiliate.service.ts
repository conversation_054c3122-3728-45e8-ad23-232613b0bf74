import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { UserAffiliate } from '../entities/user_affiliates.entity';
import { ReferralSummaryDto } from '../dto/user-affiliate.dto';

@Injectable()
export class UserAffiliateService {
  constructor(
    @InjectRepository(UserAffiliate)
    private affiliateRepo: Repository<UserAffiliate>,

    @InjectRepository(User)
    private userRepo: Repository<User>,
  ) {}

  async createAffiliateChain(newUserId: string, referrerId: string): Promise<void> {
    const levels: string[] = [];
    let currentRef: string | null = referrerId;

    for (let i = 0; i < 5; i++) {
      if (!currentRef) break;
      const record = await this.affiliateRepo.findOne({
        where: { user: { id: currentRef } },
        relations: ['referrer'],
      });
      levels.push(currentRef);
      currentRef = record?.referrer?.id ?? null;
    }

    const affiliate = this.affiliateRepo.create({
      user: this.userRepo.create({ id: newUserId }),
      referrer: this.userRepo.create({ id: referrerId }),
      level_1: levels[0],
      level_2: levels[1],
      level_3: levels[2],
      level_4: levels[3],
      level_5: levels[4],
    });

    await this.affiliateRepo.save(affiliate);
  }

  async getReferralSummary(userId: string): Promise<ReferralSummaryDto> {
    const f1 = await this.affiliateRepo.count({
      where: { referrer: { id: userId } },
    });

    const system = await this.affiliateRepo.count({
      where: [
        { level_1: userId },
        { level_2: userId },
        { level_3: userId },
        { level_4: userId },
        { level_5: userId },
      ],
    });

    return { userId, totalF1: f1, totalSystem: system };
  }

  async getMyReferralInfo(userId: string) {
    const record = await this.affiliateRepo.findOne({
      where: { user: { id: userId } },
      relations: ['referrer'],
    });

    // Always fetch user info from userRepo
    const user = await this.userRepo.findOneBy({ id: userId });

    const domain = process.env.DOMAIN_NAME || 'http://localhost:3000';
    return {
      myId: userId,
      referrerId: record?.referrer?.id ?? null,
      referralLink: `${domain}/dang-ky?ref=${user?.uidCode ?? ''}`,
    };
  }

  async getAffiliateTree(userId: string) {
    const tree = await this.buildTree(userId, 1, 5);
    return {
      userId,
      tree,
    };
  }

  private async buildTree(userId: string, currentLevel: number, maxLevel: number): Promise<any[]> {
    if (currentLevel > maxLevel) return [];

    const affiliates = await this.affiliateRepo.find({
      where: { referrer: { id: userId } },
      relations: ['user', 'user.profile'],
    });

    const results = await Promise.all(
      affiliates.map(async (record) => ({
        id: record.user.id,
        name: record.user.profile?.fullName ?? '(Không tên)',
        joinedAt: record.user.createdAt,
        level: currentLevel,
        children: await this.buildTree(record.user.id, currentLevel + 1, maxLevel),
      })),
    );

    return results;
  }

  async getAffiliateStats(userId: string) {
    const summary = await this.getReferralSummary(userId);
    return {
      ...summary,
      potentialCommission: summary.totalSystem * 5000, // ví dụ mỗi F có thể mang lại 5K
    };
  }

  async getDirectInvitees(userId: string) {
    return this.affiliateRepo.find({
      where: { referrer: { id: userId } },
      relations: ['user'],
    });
  }

  async getIndirectInvitees(userId: string, level: number) {
    if (level < 2 || level > 5) throw new NotFoundException('Level phải từ 2 đến 5');

    return this.affiliateRepo.find({
      where: { [`level_${level}`]: userId },
      relations: ['user'],
    });
  }

  async getFullAffiliateNetwork(userId: string) {
    return this.affiliateRepo.find({
      where: [
        { level_1: userId },
        { level_2: userId },
        { level_3: userId },
        { level_4: userId },
        { level_5: userId },
      ],
      relations: ['user'],
    });
  }

  async getMyReferrer(userId: string) {
    const record = await this.affiliateRepo.findOne({
      where: { user: { id: userId } },
      relations: ['referrer'],
    });

    return {
      myId: userId,
      referrer: record?.referrer || null,
    };
  }
}
