.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: 12px;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
  }

  &Title {
    h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      color: #262626;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 22px;
      }
    }

    p {
      margin: 4px 0 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  &Actions {
    display: flex;
    gap: 12px;
    align-items: center;

    @media (max-width: 768px) {
      width: 100%;
      justify-content: space-between;
    }
  }
}

.alertButton {
  border-radius: 8px;
  height: 36px;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  color: #595959;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    border-color: #fa8c16;
    color: #fa8c16;
    background-color: #fff7e6;
  }

  @media (max-width: 768px) {
    height: 32px;
    padding: 0 8px;
    font-size: 12px;
  }
}

.actionButton {
  border-radius: 8px;
  height: 36px;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  color: #595959;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    background-color: #f8f9ff;
  }

  @media (max-width: 768px) {
    height: 32px;
    padding: 0 8px;
    font-size: 12px;
  }
}

// Mobile Search Container
.mobileSearchContainer {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;

  @media (min-width: 769px) {
    display: none;
  }
}

.mobileSearchInput {
  flex: 1;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  height: 36px;
  transition: all 0.2s ease;

  &:focus,
  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;
  }
}

.mobileFilterButton {
  border-radius: 8px;
  height: 36px;
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  background-color: #ffffff;
  color: #595959;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    background-color: #f8f9ff;
  }
}

// Stock Alert
.stockAlert {
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid #faad14;
  background-color: #fffbe6;
}

// Stats
.statsRow {
  margin-bottom: 24px;

  @media (max-width: 768px) {
    display: none;
  }
}

.mobileStatsContainer {
  margin-bottom: 16px;

  @media (min-width: 769px) {
    display: none;
  }

  :global(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header) {
    padding: 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    margin-bottom: 8px;
  }

  :global(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box) {
    padding: 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
  }
}

.mobileStatCard {
  text-align: center;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.mobileStatValue {
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.mobileStatLabel {
  font-size: 12px;
  color: #8c8c8c;
}

.statCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  :global(.ant-card-body) {
    padding: 20px;
  }

  :global(.ant-statistic-title) {
    color: #8c8c8c;
    font-weight: 500;
    margin-bottom: 8px;
  }

  :global(.ant-statistic-content) {
    font-size: 24px;
    font-weight: 600;
  }
}

// Filters
.filterCard {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-body) {
    padding: 20px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }
}

.filterHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 0;
  }
}

.filterLeft {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filterRight {
  display: flex;
  align-items: center;
  gap: 12px;
}

.viewToggle {
  :global(.ant-radio-button-wrapper) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    
    &:first-child {
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
    }
    
    &:last-child {
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
    }
  }
}

.filterRow {
  margin-top: 16px;
}

.searchInput {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  height: 36px;
  transition: all 0.2s ease;

  &:focus,
  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;
  }
}

.filterSelect {
  width: 100%;
  border-radius: 8px;

  :global(.ant-select-selector) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: 36px;
    transition: all 0.2s ease;

    &:focus,
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

// Bulk Actions
.bulkCard {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #1890ff;
  background-color: #f8f9ff;

  :global(.ant-card-body) {
    padding: 16px 20px;

    @media (max-width: 768px) {
      padding: 12px 16px;
    }
  }
}

.bulkActions {
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  span {
    color: #1890ff;
    font-weight: 500;
    font-size: 14px;

    @media (max-width: 768px) {
      font-size: 12px;
    }
  }
}

// Table
.tableCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-body) {
    padding: 0;
  }
}

.inventoryTable {
  :global(.ant-table) {
    border-radius: 12px;
    overflow: hidden;
  }

  :global(.ant-table-thead > tr > th) {
    background-color: #fafafa;
    border-bottom: 1px solid #f5f5f5;
    color: #262626;
    font-weight: 600;
    padding: 16px;
    
    &:first-child {
      border-top-left-radius: 12px;
    }
    
    &:last-child {
      border-top-right-radius: 12px;
    }

    @media (max-width: 768px) {
      padding: 12px 8px;
      font-size: 12px;
    }
  }

  :global(.ant-table-tbody > tr > td) {
    padding: 16px;
    border-bottom: 1px solid #f5f5f5;

    @media (max-width: 768px) {
      padding: 12px 8px;
    }
  }

  :global(.ant-table-tbody > tr:hover > td) {
    background-color: #f8f9ff;
  }

  :global(.ant-table-pagination) {
    padding: 16px 24px;
    border-top: 1px solid #f5f5f5;

    @media (max-width: 768px) {
      padding: 12px 16px;
    }
  }
}

// Row colors for stock status
.outOfStockRow {
  background-color: #fff2f0 !important;
  
  &:hover {
    background-color: #ffebe8 !important;
  }
}

.lowStockRow {
  background-color: #fff7e6 !important;
  
  &:hover {
    background-color: #ffefd3 !important;
  }
}

// Product Info
.productImage {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.productImageMobile {
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.productInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.productName {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.productNameMobile {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.productSku {
  color: #8c8c8c;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.productSkuMobile {
  color: #8c8c8c;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.productCategory {
  color: #1890ff;
  font-size: 12px;
}

.productCategoryMobile {
  color: #1890ff;
  font-size: 11px;
}

// Variants
.variantsList {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.variantItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.variantName {
  font-size: 12px;
  color: #262626;
}

.variantPrice {
  font-size: 11px;
  color: #52c41a;
  font-weight: 500;
}

// Stock Info
.stockInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stockCurrent {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stockUnit {
  color: #8c8c8c;
  font-size: 11px;
}

.stockMin {
  color: #8c8c8c;
  font-size: 11px;
}

.stockSufficient {
  color: #52c41a;
  font-weight: 600;
  font-size: 16px;
}

.stockLow {
  color: #fa8c16;
  font-weight: 600;
  font-size: 16px;
}

.stockOut {
  color: #ff4d4f;
  font-weight: 600;
  font-size: 16px;
}

// Warehouse Info
.warehouseInfo {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #595959;
  font-size: 13px;
}

// Date Info
.dateInfo {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

// Action Buttons
.actionButton {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9ff;
    border-color: #1890ff;
    color: #1890ff;
  }
}

.mobileActionButton {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 12px;

  &:hover {
    background-color: #f8f9ff;
    border-color: #1890ff;
    color: #1890ff;
  }
}

// Mobile Inventory Card
.mobileInventoryCard {
  width: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background-color: #ffffff;
  padding: 12px;
  margin-bottom: 8px;
}

.mobileInventoryHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.mobileProductInfo {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.mobileProductDetails {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.mobileStockStatus {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.mobileInventoryBody {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
}

.mobileStockInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #fafafa;
  border-radius: 6px;
}

.mobileStockCurrent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.mobileStockMin {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.mobileStockLabel {
  font-size: 10px;
  color: #8c8c8c;
}

.mobileWarehouseInfo {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #595959;
  font-size: 12px;
}

.mobileInventoryFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f5f5f5;
}

.mobileUpdateInfo {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #8c8c8c;
  font-size: 11px;
}

.mobileActions {
  display: flex;
  gap: 4px;
}

// Filter Drawer
.filterDrawer {
  :global(.ant-drawer-body) {
    padding: 16px;
  }
}

.filtersContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mobileFilterItem {
  display: flex;
  flex-direction: column;
  gap: 8px;

  label {
    font-weight: 500;
    color: #262626;
    font-size: 14px;
  }
}

.drawerActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;

  button {
    border-radius: 8px;
    height: 36px;
  }
}

// Item Detail Modal
.itemDetailModal {
  :global(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :global(.ant-modal-header) {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 20px 24px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }

  :global(.ant-modal-title) {
    font-size: 18px;
    font-weight: 600;
    color: #262626;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }

  :global(.ant-modal-body) {
    padding: 24px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }

  @media (max-width: 768px) {
    :global(.ant-modal) {
      margin: 0;
      max-width: 100%;
      height: 100vh;
      border-radius: 0;
    }

    :global(.ant-modal-content) {
      height: 100vh;
      border-radius: 0;
      display: flex;
      flex-direction: column;
    }

    :global(.ant-modal-body) {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
    }
  }
}

.itemDetailContainer {
  .itemDetailHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
    }

    h3 {
      margin: 0;
      color: #262626;
      font-size: 18px;
      font-weight: 600;
    }
  }
}

.detailCard {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :global(.ant-card-head) {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
    min-height: auto;
  }

  :global(.ant-card-head-title) {
    color: #262626;
    font-weight: 600;
    font-size: 14px;
  }

  :global(.ant-card-body) {
    padding: 16px;
  }

  :global(.ant-descriptions-item-label) {
    color: #8c8c8c;
    font-weight: 500;
    width: 120px;
  }

  :global(.ant-descriptions-item-content) {
    color: #262626;
  }
}

// Stock Overview
.stockOverview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stockOverviewItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.stockOverviewLabel {
  color: #8c8c8c;
  font-weight: 500;
}

.stockOverviewValue {
  color: #262626;
  font-weight: 600;
}

// SKU Code
.skuCode {
  background-color: #f5f5f5;
  color: #595959;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-family: 'Courier New', monospace;
}

// Stock Update Controls
.stockUpdateControls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stockControlButton {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9ff;
    border-color: #1890ff;
    color: #1890ff;
  }
}

.stockValue {
  font-weight: 600;
  color: #262626;
  min-width: 30px;
  text-align: center;
}

// Stock History
.historyItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.historyHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.historyDate {
  color: #262626;
  font-weight: 500;
  font-size: 13px;
}

.historyUser {
  color: #8c8c8c;
  font-size: 12px;
}

.historyContent {
  display: flex;
  align-items: center;
  gap: 8px;
}

.historyQuantity {
  font-weight: 600;
  color: #262626;
}

.historyReason {
  color: #595959;
  font-size: 12px;
}

// Update Modal
.updateModal {
  :global(.ant-modal-content) {
    border-radius: 12px;
    overflow: hidden;
  }

  :global(.ant-modal-header) {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 20px 24px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }

  :global(.ant-modal-title) {
    font-size: 18px;
    font-weight: 600;
    color: #262626;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }

  :global(.ant-modal-body) {
    padding: 24px;

    @media (max-width: 768px) {
      padding: 16px;
    }
  }

  @media (max-width: 768px) {
    :global(.ant-modal) {
      margin: 0;
      max-width: 100%;
      height: 100vh;
      border-radius: 0;
    }

    :global(.ant-modal-content) {
      height: 100vh;
      border-radius: 0;
      display: flex;
      flex-direction: column;
    }

    :global(.ant-modal-body) {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
    }
  }
}

.updateForm {
  :global(.ant-form-item-label > label) {
    color: #262626;
    font-weight: 500;
  }

  :global(.ant-form-item) {
    margin-bottom: 16px;

    @media (max-width: 768px) {
      margin-bottom: 12px;
    }
  }

  :global(.ant-input),
  :global(.ant-select-selector),
  :global(.ant-input-number) {
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    height: 36px;
    transition: all 0.2s ease;

    &:focus,
    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    @media (max-width: 768px) {
      height: 40px;
    }
  }

  :global(.ant-input-number) {
    width: 100%;
  }
}

.updateAlert {
  margin-bottom: 16px;
  border-radius: 8px;
}

.variantUpdateCard {
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);

  :global(.ant-card-body) {
    padding: 16px;
  }
}

.updateActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;

  @media (max-width: 768px) {
    margin-top: 16px;
    padding-top: 16px;
    justify-content: space-between;
  }

  button {
    border-radius: 8px;
    height: 36px;
    padding: 0 16px;
    font-weight: 500;

    @media (max-width: 768px) {
      height: 40px;
      flex: 1;
    }
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .container {
    padding: 8px;
  }

  .header {
    margin-bottom: 12px;

    &Title h1 {
      font-size: 20px;
    }
  }

  .mobileSearchContainer {
    margin-bottom: 12px;
  }

  .mobileStatsContainer {
    margin-bottom: 12px;
  }

  .mobileStatCard {
    padding: 8px;
  }

  .mobileStatValue {
    font-size: 16px;
  }

  .mobileStatLabel {
    font-size: 11px;
  }

  .bulkCard :global(.ant-card-body) {
    padding: 8px 12px;
  }

  .bulkActions {
    span {
      font-size: 11px;
    }
  }

  .mobileInventoryCard {
    padding: 8px;
  }

  .mobileInventoryHeader {
    gap: 8px;
  }

  .productImageMobile {
    width: 32px;
    height: 32px;
  }

  .mobileActionButton {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  .itemDetailModal {
    :global(.ant-modal-header) {
      padding: 12px;
    }

    :global(.ant-modal-body) {
      padding: 12px;
    }
  }

  .detailCard :global(.ant-card-body) {
    padding: 12px;
  }
}

// Landscape orientation for mobile
@media (max-width: 768px) and (orientation: landscape) {
  .itemDetailModal {
    :global(.ant-modal) {
      height: 100vh;
    }

    :global(.ant-modal-content) {
      height: 100vh;
      max-height: 100vh;
    }

    :global(.ant-modal-body) {
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
  }

  .updateModal {
    :global(.ant-modal) {
      height: 100vh;
    }

    :global(.ant-modal-content) {
      height: 100vh;
      max-height: 100vh;
    }

    :global(.ant-modal-body) {
      max-height: calc(100vh - 120px);
      overflow-y: auto;
    }
  }
}
