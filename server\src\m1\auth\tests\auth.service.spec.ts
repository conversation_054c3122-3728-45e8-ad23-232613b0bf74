import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from '../services/auth.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../user/entities/user.entity';
import { Role } from '../../user/entities/role.entity';
import { UserProfile } from '../../user/entities/user_profile.entity';
import { OtpVerification } from '../../user/entities/otp_verification.entity';
import { UserProvider } from '../../user/entities/user-provider.entity';
import { User2FASettings } from '../../user/entities/user_2fa_settings.entity';
import { AuthSession } from '../../user/entities/auth_sessions.entity';
import { UserAffiliateService } from '../../user/services/user_affiliate.service';
import { TokenService } from '../services/token.service';
import { TwofaService } from '../services/twofa.service';
import { RegisterDto } from '../dto/register.dto';
import * as bcrypt from 'bcrypt';
import { MailService } from 'src/mail/mail.service';
import { SocialLoginDto } from '../dto/social-login.dto';
import { LoginDto } from '../dto/login.dto';

describe('AuthService', () => {
  let service: AuthService;
  let userRepo: Repository<User>;
  let otpRepo: Repository<OtpVerification>;
  let roleRepo: Repository<Role>;
  let profileRepo: Repository<UserProfile>;
  let mailService: MailService;
  let tokenService: TokenService;
  let affiliateService: UserAffiliateService;

  const mockMailService = {
    sendWelcomeEmail: jest.fn().mockResolvedValue(undefined),
    sendOtpEmail: jest.fn(),
    sendOtpEmail2FA: jest.fn(),
    sendResetPasswordEmail: jest.fn(),
    send2FAActived: jest.fn(),
    sendOtpEmailDisable2FA: jest.fn(),
  } as unknown as MailService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOneBy: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(OtpVerification),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Role),
          useValue: {
            findOneBy: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserProfile),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserProvider),
          useValue: {},
        },
        {
          provide: getRepositoryToken(User2FASettings),
          useValue: {},
        },
        {
          provide: getRepositoryToken(AuthSession),
          useValue: {},
        },
        {
          provide: MailService,
          useValue: mockMailService,
        },
        {
          provide: TokenService,
          useValue: {
            generateTokens: jest
              .fn()
              .mockResolvedValue({ accessToken: 'token', refreshToken: 'refresh' }),
          },
        },
        {
          provide: UserAffiliateService,
          useValue: {
            createAffiliateChain: jest.fn(),
          },
        },
        {
          provide: TwofaService,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepo = module.get(getRepositoryToken(User));
    otpRepo = module.get(getRepositoryToken(OtpVerification));
    roleRepo = module.get(getRepositoryToken(Role));
    profileRepo = module.get(getRepositoryToken(UserProfile));
    // mailService = mockMailService as MailService;
    tokenService = module.get(TokenService);
    affiliateService = module.get(UserAffiliateService);
  });

  it('should register user if OTP is verified', async () => {
    const dto: RegisterDto = {
      email: '<EMAIL>',
      password: 'Password123',
      fullName: 'Tester',
      phone: '0123456789',
      roleId: 'r1',
      referralCode: 'TAP_REF123',
    };

    jest.spyOn(userRepo, 'findOneBy').mockResolvedValueOnce(null); // email not exist
    jest.spyOn(userRepo, 'findOneBy').mockResolvedValueOnce(null); // phone not exist
    jest.spyOn(otpRepo, 'findOne').mockResolvedValue({
      id: 'otp1',
      target: dto.email,
      otpCode: '123456',
      expiredAt: new Date(Date.now() + 60000),
      status: 'valid',
      isVerified: true,
      createdAt: new Date(),
    });
    jest.spyOn(roleRepo, 'findOneBy').mockResolvedValue({
      id: 'r1',
      role_code: 'R01',
      name: 'User',
      description: '',
      users: [],
      roleGroups: [],
    });
    jest.spyOn(userRepo, 'create').mockReturnValue({
      id: 'u1',
      uidCode: 'TAP_ABC123',
      email: dto.email,
      password: 'hashed',
      refreshToken: '',
      role: { id: 'r1' } as Role,
      referralBy: dto.referralCode ?? '',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
      roleGroupCode: '',
    } as User);
    jest.spyOn(userRepo, 'save').mockResolvedValue({
      id: 'u1',
      uidCode: 'TAP_ABC123',
      email: dto.email,
      password: 'hashed',
      refreshToken: '',
      role: { id: 'r1' } as Role,
      referralBy: dto.referralCode ?? '',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
      roleGroupCode: '',
    } as User);
    jest.spyOn(profileRepo, 'create').mockReturnValue({
      id: 'p1',
      userId: 'u1',
      fullName: dto.fullName,
      phoneNumber: dto.phone,
      avatarUrl: '',
      gender: '',
      address: '',
      provinceCode: '',
      avatarId: '',
      citizenId: '',
      dateOfBirth: new Date('1990-01-01'),
      createdAt: new Date(),
      updatedAt: new Date(),
      user: {} as User,
    } as UserProfile);
    jest.spyOn(profileRepo, 'save').mockResolvedValue({
      id: 'p1',
      userId: 'u1',
      fullName: dto.fullName,
      phoneNumber: dto.phone,
      avatarUrl: '',
      gender: '',
      address: '',
      provinceCode: '',
      avatarId: '',
      citizenId: '',
      dateOfBirth: new Date('1990-01-01'),
      createdAt: new Date(),
      updatedAt: new Date(),
      user: {} as User,
    } as UserProfile);

    const result = await service.register(dto);
    expect(result.accessToken).toBe('token');
    expect(result.refreshToken).toBe('refresh');
  });

  it('should register user if OTP is verified', async () => {
    // [this test remains unchanged]
  });

  it('should login with correct credentials and no 2FA', async () => {
    const dto: LoginDto = { email: '<EMAIL>', password: 'Password123' };
    const user = {
      id: 'u1',
      email: dto.email,
      password: await bcrypt.hash(dto.password, 10),
    } as User;

    jest.spyOn(userRepo, 'findOneBy').mockResolvedValue(user);
    jest.spyOn(bcrypt, 'compare').mockResolvedValue(true);
    jest.spyOn(userRepo, 'findOne').mockResolvedValue(null); // no 2FA

    const result = await service.login(dto);
    expect(result).toEqual({ accessToken: 'token', refreshToken: 'refresh' });
  });

  it('should login with social provider and create new user if email does not exist', async () => {
    const dto: SocialLoginDto = { provider: 'google', accessToken: 'mock-token' };

    const mockProfile = {
      provider: 'google',
      providerId: 'google-123',
      email: '<EMAIL>',
      fullName: 'Tester Google',
      picture: 'https://avatar.com/avatar.png',
    };

    jest.spyOn<any, any>(service, 'verifyWithProvider').mockResolvedValue(mockProfile);
    jest.spyOn(userRepo, 'findOne').mockResolvedValue(null);
    jest.spyOn(userRepo, 'findOneBy').mockResolvedValue(null);
    jest.spyOn(roleRepo, 'findOneBy').mockResolvedValue({ id: 'r1', role_code: 'R01' } as Role);
    jest.spyOn(userRepo, 'create').mockReturnValue({ id: 'u1', email: mockProfile.email } as User);
    jest.spyOn(userRepo, 'save').mockResolvedValue({ id: 'u1', email: mockProfile.email } as User);
    jest.spyOn(profileRepo, 'create').mockReturnValue({} as UserProfile);
    jest.spyOn(profileRepo, 'save').mockResolvedValue({} as UserProfile);

    const result = await service.loginWithProvider(dto);
    expect(result).toEqual({ accessToken: 'token', refreshToken: 'refresh' });
  });
});
