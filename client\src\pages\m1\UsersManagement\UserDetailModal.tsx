import React from 'react'
import { Modal, Form, Row, Col, Input, Select, Tag } from 'antd'
import ModalButtonGroup from '@/components/ModalButtonGroup'

const { Option } = Select

interface User {
  id: string
  fullName?: string
  full_name?: string
  name?: string
  uidCode?: string
  uid_code?: string
  email?: string
  phone?: string
  phoneNumber?: string
  avatarUrl?: string
  role?: any
  role_id?: string
  status?: 'active' | 'locked' | 'pending'
  createdAt?: string
  updatedAt?: string
}

interface Role {
  id: string
  name?: string
  role_code?: string
}

interface UserDetailModalProps {
  open: boolean
  loading: boolean
  modalType: 'view' | 'edit' | string
  userDetail?: User | null
  form: any
  roles: Role[]
  onCancel: () => void
  onOk: () => void
  getFullName: (user: User) => string
  getRoleName: (role: any) => string
  getRoleColor: (roleName: string) => string
  getStatusColor: (status: string | undefined) => string
  getStatusText: (status: string | undefined) => string
}

const UserDetailModal: React.FC<UserDetailModalProps> = ({
  open,
  loading,
  modalType,
  userDetail,
  form,
  roles,
  onCancel,
  onOk,
  getFullName,
  getRoleName,
  getRoleColor,
  getStatusColor,
  getStatusText,
}) => {
  return (
    <Modal
      title={
        modalType === 'view' && userDetail ? (
          <div>
            <span>Chi tiết người dùng</span>
            <br />
            <small style={{ color: '#8c8c8c', fontWeight: 'normal' }}>
              ID:{' '}
              <Tag color="blue" style={{ fontFamily: 'monospace' }}>
                {userDetail.id}
              </Tag>
            </small>
          </div>
        ) : modalType === 'edit' ? (
          'Chỉnh sửa người dùng'
        ) : (
          'Chi tiết người dùng'
        )
      }
      open={open}
      onCancel={onCancel}
      footer={null}
      width={600}
      className={undefined /* pass className if needed */}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>Đang tải thông tin người dùng...</div>
      ) : modalType === 'view' && userDetail ? (
        <div>
          <Row gutter={16}>
            <Col span={12}>
              <div style={{ marginBottom: 12 }}>
                <strong>Họ và tên:</strong> {getFullName(userDetail)}
              </div>
              <div style={{ marginBottom: 12 }}>
                <strong>Email:</strong> {userDetail.email || 'N/A'}
              </div>
              <div style={{ marginBottom: 12 }}>
                <strong>Số điện thoại:</strong>{' '}
                {userDetail.phone || userDetail.phoneNumber || 'N/A'}
              </div>
              <div style={{ marginBottom: 12 }}>
                <strong>Mã người dùng:</strong> {userDetail.uidCode || userDetail.uid_code || 'N/A'}
              </div>
              <div style={{ marginBottom: 12 }}>
                <strong>Trạng thái:</strong>{' '}
                <Tag color={getStatusColor(userDetail.status)}>
                  {getStatusText(userDetail.status)}
                </Tag>
              </div>
            </Col>
            <Col span={12}>
              <div style={{ marginBottom: 12 }}>
                <strong>Vai trò:</strong>{' '}
                <Tag color={getRoleColor(getRoleName(userDetail.role))}>
                  {getRoleName(userDetail.role)}
                </Tag>
              </div>
              <div style={{ marginBottom: 12 }}>
                <strong>Ngày tạo:</strong>{' '}
                {userDetail.createdAt
                  ? new Date(userDetail.createdAt).toLocaleString('vi-VN')
                  : 'N/A'}
              </div>
              <div style={{ marginBottom: 12 }}>
                <strong>Cập nhật cuối:</strong>{' '}
                {userDetail.updatedAt
                  ? new Date(userDetail.updatedAt).toLocaleString('vi-VN')
                  : 'N/A'}
              </div>
            </Col>
          </Row>
        </div>
      ) : (
        <Form form={form} layout="vertical" disabled={modalType === 'view'}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="fullName" label="Họ và tên" rules={[{ required: false }]}>
                <Input placeholder="Nhập họ và tên" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[{ required: false }, { type: 'email', message: 'Email không hợp lệ' }]}
              >
                <Input placeholder="Nhập email" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="phone" label="Số điện thoại" rules={[{ required: false }]}>
                <Input placeholder="Nhập số điện thoại" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="roleId" label="Vai trò" rules={[{ required: false }]}>
                <Select placeholder="Chọn vai trò">
                  {roles.map((role: Role) => (
                    <Option key={role.id} value={role.id}>
                      {role.name || role.role_code}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="status" label="Trạng thái" rules={[{ required: false }]}>
            <Select placeholder="Chọn trạng thái">
              <Option value="active">Hoạt động</Option>
              <Option value="pending">Đang xử lý</Option>
              <Option value="locked">Không hoạt động</Option>
            </Select>
          </Form.Item>
        </Form>
      )}

      <ModalButtonGroup
        onCancel={onCancel}
        onConfirm={modalType === 'view' ? undefined : onOk}
        cancelText="Hủy"
        confirmText={modalType === 'view' ? undefined : 'Lưu'}
        confirmLoading={loading}
        showConfirm={modalType !== 'view'}
        confirmVariant="primary"
      />
    </Modal>
  )
}

export default UserDetailModal
