import React from 'react'
import { Typo<PERSON>, Card, Row, Col, Switch, But<PERSON>, Di<PERSON>r, <PERSON><PERSON>, Modal, Form, Input, Space, Tag, message } from 'antd'
import {
  MailOutlined,
  PhoneOutlined,
  SecurityScanOutlined,
  KeyOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  EnvironmentOutlined,
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import ChangePasswordModal from './ChangePasswordModal'
import SessionsModal from './SessionsModal'
import styles from '../../Profile.module.scss'

const { Text, Title } = Typography

interface UserData {
  id: string
  email: string
  gender: string
  fullName: string
  dateOfBirth: string
  citizenId: string
  phone: string
  address: string
  points: number
  qrCode: string
}

interface SecurityTabProps {
  is2FAEnabled: boolean
  showOTPSetup: boolean
  showDisableModal: boolean
  showChangePasswordModal: boolean
  showSessionsModal: boolean
  otpEmail: string
  otpCode: string
  disableOtpCode: string
  disableOtpSent: boolean
  otpSent: boolean
  isProcessing: boolean
  userData: UserData
  styles: any
  currentLocation: {latitude: number, longitude: number} | null
  isGettingLocation: boolean
  handle2FAToggle: (checked: boolean) => void
  handleSendOTP: () => void
  handleVerifyOTP: () => void
  handleCancelOTPSetup: () => void
  handleSendDisableOTP: () => void
  handleDisable2FA: () => void
  handleCancelDisable: () => void
  handleShowChangePassword: () => void
  handleCancelChangePassword: () => void
  handleShowSessions: () => void
  handleCancelSessions: () => void
  handleOtpEmailChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleOtpCodeChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleDisableOtpCodeChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleGetCurrentLocation: () => Promise<void>
}

const SecurityTab: React.FC<SecurityTabProps> = ({
  is2FAEnabled,
  showOTPSetup,
  showDisableModal,
  showChangePasswordModal,
  showSessionsModal,
  otpEmail,
  otpCode,
  disableOtpCode,
  disableOtpSent,
  otpSent,
  isProcessing,
  userData,
  styles,
  currentLocation,
  isGettingLocation,
  handle2FAToggle,
  handleSendOTP,
  handleVerifyOTP,
  handleCancelOTPSetup,
  handleSendDisableOTP,
  handleDisable2FA,
  handleCancelDisable,
  handleShowChangePassword,
  handleCancelChangePassword,
  handleShowSessions,
  handleCancelSessions,
  handleOtpEmailChange,
  handleOtpCodeChange,
  handleDisableOtpCodeChange,
  handleGetCurrentLocation,
}) => {
  return (
    <div className={styles.securityTab}>
      <div className={styles.securityOverview}>
        <Title level={4}>Tổng quan bảo mật</Title>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={8}>
            <Card className={styles.securityCard}>
              <div className={styles.securityItem}>
                <div className={styles.securityIcon}>
                  <MailOutlined />
                </div>
                <div className={styles.securityInfo}>
                  <Text strong>Email</Text>
                  <Text type="secondary">{userData.email}</Text>
                  <Tag color="green" icon={<CheckCircleOutlined />}>
                    Đã xác thực
                  </Tag>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card className={styles.securityCard}>
              <div className={styles.securityItem}>
                <div className={styles.securityIcon}>
                  <PhoneOutlined />
                </div>
                <div className={styles.securityInfo}>
                  <Text strong>Số điện thoại</Text>
                  <Text type="secondary">
                    {userData.phone || 'Chưa cập nhật'}
                  </Text>
                  <Tag color={userData.phone ? "green" : "orange"} icon={userData.phone ? <CheckCircleOutlined /> : <InfoCircleOutlined />}>
                    {userData.phone ? 'Đã xác thực' : 'Chưa cập nhật'}
                  </Tag>
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={8}>
            <Card className={styles.securityCard}>
              <div className={styles.securityItem}>
                <div className={styles.securityIcon}>
                  <SecurityScanOutlined />
                </div>
                <div className={styles.securityInfo}>
                  <Text strong>Xác thực 2 yếu tố</Text>
                  <Text type="secondary">
                    {is2FAEnabled ? 'Đã bật' : 'Chưa bật'}
                  </Text>
                  <Tag color={is2FAEnabled ? 'green' : 'orange'} icon={is2FAEnabled ? <CheckCircleOutlined /> : <InfoCircleOutlined />}>
                    {is2FAEnabled ? 'Bảo mật cao' : 'Cần bật'}
                  </Tag>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>

      <Divider />

      <div className={styles.securityActions}>
        <Title level={4}>Hành động bảo mật</Title>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Card className={styles.actionCard}>
              <div className={styles.actionHeader}>
                <KeyOutlined className={styles.actionIcon} />
                <div>
                  <Title level={5}>Đổi mật khẩu</Title>
                  <Text type="secondary">Cập nhật mật khẩu định kỳ để bảo mật tài khoản</Text>
                </div>
              </div>
              <Button type="primary" onClick={handleShowChangePassword} className={styles.actionButton}>
                Đổi mật khẩu
              </Button>
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card className={styles.actionCard}>
              <div className={styles.actionHeader}>
                <SecurityScanOutlined className={styles.actionIcon} />
                <div>
                  <Title level={5}>Xác thực 2 yếu tố</Title>
                  <Text type="secondary">Bật 2FA để tăng cường bảo mật tài khoản</Text>
                </div>
              </div>
              <Button 
                type={is2FAEnabled ? "default" : "primary"}
                onClick={() => handle2FAToggle(!is2FAEnabled)}
                className={styles.actionButton}
              >
                {is2FAEnabled ? 'Tắt 2FA' : 'Bật 2FA'}
              </Button>
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card className={styles.actionCard}>
              <div className={styles.actionHeader}>
                <EyeOutlined className={styles.actionIcon} />
                <div>
                  <Title level={5}>Phiên đăng nhập</Title>
                  <Text type="secondary">Xem các thiết bị đang đăng nhập</Text>
                </div>
              </div>
              <Button onClick={handleShowSessions} className={styles.actionButton}>
                Xem phiên
              </Button>
            </Card>
          </Col>
          <Col xs={24} sm={12}>
            <Card className={styles.actionCard}>
              <div className={styles.actionHeader}>
                <EnvironmentOutlined className={styles.actionIcon} />
                <div>
                  <Title level={5}>Lấy vị trí hiện tại</Title>
                  <Text type="secondary">
                    {currentLocation 
                      ? `Vĩ độ: ${currentLocation.latitude.toFixed(6)}, Kinh độ: ${currentLocation.longitude.toFixed(6)}`
                      : 'Xem vị trí hiện tại của bạn'
                    }
                  </Text>
                </div>
              </div>
              <Button
                onClick={handleGetCurrentLocation}
                loading={isGettingLocation}
                className={styles.actionButton}
                type={currentLocation ? "default" : "primary"}
              >
                {currentLocation ? 'Lấy lại vị trí' : 'Lấy vị trí'}
              </Button>
            </Card>
          </Col>
        </Row>
      </div>

      {/* Modals */}
      <ChangePasswordModal
        open={showChangePasswordModal}
        onCancel={handleCancelChangePassword}
        onSuccess={handleCancelChangePassword}
        isProcessing={isProcessing}
        styles={styles}
      />
      <SessionsModal open={showSessionsModal} onCancel={handleCancelSessions} styles={styles} />

      {/* 2FA Setup Modal */}
      <Modal
        title="Thiết lập xác thực 2 yếu tố"
        open={showOTPSetup}
        onCancel={handleCancelOTPSetup}
        footer={null}
      >
        <div className={styles.otpSetup}>
          <Alert
            message="Bảo mật tài khoản"
            description="Xác thực 2 yếu tố giúp bảo vệ tài khoản của bạn khỏi truy cập trái phép."
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Form layout="vertical">
            <Form.Item label="Email nhận mã OTP">
              <Input
                value={otpEmail}
                onChange={handleOtpEmailChange}
                placeholder="Nhập email"
              />
            </Form.Item>
            {otpSent && (
              <Form.Item label="Mã OTP">
                <Input
                  value={otpCode}
                  onChange={handleOtpCodeChange}
                  placeholder="Nhập mã OTP"
                />
              </Form.Item>
            )}
            <Space>
              {!otpSent ? (
                <Button
                  type="primary"
                  onClick={handleSendOTP}
                  loading={isProcessing}
                >
                  Gửi mã OTP
                </Button>
              ) : (
                <Button
                  type="primary"
                  onClick={handleVerifyOTP}
                  loading={isProcessing}
                >
                  Xác thực
                </Button>
              )}
              <Button onClick={handleCancelOTPSetup}>Hủy</Button>
            </Space>
          </Form>
        </div>
      </Modal>

      {/* Disable 2FA Modal */}
      <Modal
        title="Tắt xác thực 2 yếu tố"
        open={showDisableModal}
        onCancel={handleCancelDisable}
        footer={null}
      >
        <div className={styles.otpSetup}>
          <Alert
            message="Cảnh báo bảo mật"
            description="Tắt xác thực 2 yếu tố sẽ làm giảm mức độ bảo mật của tài khoản."
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Form layout="vertical">
            {!disableOtpSent ? (
              <Button
                type="primary"
                danger
                onClick={handleSendDisableOTP}
                loading={isProcessing}
              >
                Gửi mã OTP xác nhận
              </Button>
            ) : (
              <>
                <Form.Item label="Mã OTP xác nhận">
                  <Input
                    value={disableOtpCode}
                    onChange={handleDisableOtpCodeChange}
                    placeholder="Nhập mã OTP"
                  />
                </Form.Item>
                <Space>
                  <Button
                    type="primary"
                    danger
                    onClick={handleDisable2FA}
                    loading={isProcessing}
                  >
                    Tắt 2FA
                  </Button>
                  <Button onClick={handleCancelDisable}>Hủy</Button>
                </Space>
              </>
            )}
          </Form>
        </div>
      </Modal>

      {/* Location Permission Guide */}
      {/* Location Troubleshoot */}
    </div>
  )
}

export default SecurityTab 