.notificationModal {
  :global(.ant-modal-header) {
    border-bottom: 1px solid #e8e8e8;
  }
  
  :global(.ant-modal-body) {
    padding: 0;
    max-height: 400px;
    overflow-y: auto;
  }
}

.modalTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  
  .badge {
    margin-left: auto;
  }
}

.notificationList {
  .notificationItem {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: #f8f9fa;
    }
    
    &.unread {
      background-color: #e6f7ff;
      
      &:hover {
        background-color: #bae7ff;
      }
    }
    
    :global(.ant-list-item-meta-title) {
      font-weight: 600;
      margin-bottom: 4px;
    }
    
    :global(.ant-list-item-meta-description) {
      color: #666;
    }
  }
}

.notificationContent {
  p {
    margin: 0 0 4px 0;
    color: #333;
  }
  
  .timestamp {
    font-size: 12px;
    color: #999;
  }
}

.iconSuccess {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #52c41a;
  color: white;
  border-radius: 50%;
  font-size: 12px;
}

.iconWarning {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #faad14;
  color: white;
  border-radius: 50%;
  font-size: 12px;
}

.iconError {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  font-size: 12px;
}

.iconInfo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
}

.emptyState {
  padding: 40px 20px;
  text-align: center;
} 