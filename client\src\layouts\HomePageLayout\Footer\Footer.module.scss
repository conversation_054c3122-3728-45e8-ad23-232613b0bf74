// ------------------------------ Container -------------------------------
/*** Footer Container ***/
.footer-container {
  background-color: var(--dark);
  color: #f5f5f5;

  .footer-container-content {
    padding-top: 4.8rem;
    padding-bottom: 4.8rem;
  }
}

/*** Link Wrapper ***/
.footer-link-title {
  font-size: 2.4rem;
  font-weight: 800;
  margin-bottom: 1.6rem;
}

.footer-link-item {
  position: relative;
  display: block;
  margin-left: 1.8rem;
  margin-bottom: 0.5rem;
  padding: 0;
  text-align: left;
  color: var(--white);
  font-size: 1.6rem;
  font-weight: normal;
  text-transform: capitalize;
  transition: 0.3s;

  &::before {
    position: absolute;
    content: '\f105';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 1rem;
    font-size: 1.2rem;
    top: 50%;
    left: -1.8rem;
    transform: translateY(-50%);
  }

  &:hover {
    letter-spacing: 0.1rem;
    box-shadow: none;
  }
}

/*** Contact ***/
.footer-contact-info {
  font-size: 1.6rem;
  margin-bottom: 0.8rem;
  line-height: 1.5;

  .footer-contact-info-icon {
    margin-right: 1.6rem;
  }
}

.footer-contact-social-wrapper {
  display: flex;
  padding-top: 0.8rem;

  .icon-social-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.2rem;
    height: 3.2rem;
    border: 0.1rem solid #fff;
    border-radius: 50%;
    margin-right: 0.6rem;

    &:hover {
      cursor: pointer;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}

/*** Gallery ***/
.footer-gallery-img {
  background-color: #f5f5f5;
  padding: 0.4rem;
  max-width: 100%;
  height: auto;
}

/*** Register ***/
.footer-register-content {
  font-size: 1.6rem;
  color: #f5f5f5;
  margin-bottom: 1.6rem;
}

.footer-register-input {
  padding: 1.6rem 2.4rem !important;
  font-size: 1.6rem;
}

.footer-register-btn-search {
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  border-radius: 0.3rem;
}

/*** Copyright ***/
.copyright {
  padding: 2.5rem 0;
  font-size: 1.5rem;
  border-top: 0.1rem solid rgba(256, 256, 256, 0.1);

  a {
    color: var(--white);
  }
}
