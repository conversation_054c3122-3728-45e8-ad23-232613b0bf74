import classNames from 'classnames/bind'

import { Link } from 'react-router-dom'

import styles from './Footer.module.scss'

const cx = classNames.bind(styles)

const FooterSystem = () => {
  // useState -------------------------------------------------------------------------------------------------

  // useEffect -------------------------------------------------------------------------------------------------

  return (
    <>
      {/* Footer Start */}
      <div className={cx('container-fluid animate__animated animate__fadeIn', 'footer-container')}>
        <div className="container">
          <div className={cx('copyright')}>
            <div>
              &copy;{' '}
              <Link className="border-bottom" to="/">
                TAP
              </Link>
              , Bản quyền thuộc về. Thiết kế bởi{' '}
              <Link className="border-bottom" to="/">
                Hợp tác xã Tân An Phát
              </Link>
            </div>
          </div>
        </div>
      </div>
      {/* Footer End  */}
    </>
  )
}

export default FooterSystem
