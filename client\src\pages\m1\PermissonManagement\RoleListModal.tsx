import React from 'react'
import { Modal, Table } from 'antd'

interface AllRole {
  id: string
  role_code: string
  name: string
  description?: string
}

interface RoleListModalProps {
  open: boolean
  roles: AllRole[]
  columns: any[]
  onCancel: () => void
}

const RoleListModal: React.FC<RoleListModalProps> = ({ open, roles, columns, onCancel }) => {
  return (
    <Modal
      title="Danh sách Roles hiện tại trong hệ thống"
      open={open}
      onCancel={onCancel}
      footer={null}
      width={800}
    >
      <Table
        columns={columns}
        dataSource={roles}
        rowKey="id"
        pagination={false}
        scroll={{ y: 400 }}
      />
    </Modal>
  )
}

export default RoleListModal
