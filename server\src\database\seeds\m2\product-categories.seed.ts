import { DataSource } from 'typeorm';
import { ProductCategory } from '../../../m2/shop/entities/product_categories.entity';

export async function seedProductCategories(dataSource: DataSource) {
  const repo = dataSource.getRepository(ProductCategory);

  // Danh sách category cha
  const parentCategories = [
    { id: '28c08779-01bb-4357-8f8e-d0300da71208', name: '<PERSON><PERSON><PERSON><PERSON> tử', slug: 'dien-tu' },
    { id: 'd49ce340-7650-49d3-87e5-61540671d2fe', name: 'Thời trang nam', slug: 'thoi-trang-nam' },
    { id: '782e55b7-4c86-4b1f-95d8-659cf70a8a65', name: 'Thờ<PERSON> trang nữ', slug: 'thoi-trang-nu' },
    {
      id: 'f8b0fedf-3228-4154-807d-34ee418f2207',
      name: '<PERSON>hờ<PERSON> trang trẻ em',
      slug: 'thoi-trang-tre-em',
    },
    { id: 'e7dc06ee-1f0c-417a-a4b0-b7693cdc538d', name: 'Đồ gia dụng', slug: 'do-gia-dung' },
    {
      id: 'f1f49908-10d8-4b79-8f76-afcb05c90a48',
      name: 'Đồ ăn vặt & bánh kẹo',
      slug: 'do-an-vat-banh-keo',
    },
    { id: 'c87a9592-f448-4507-bdd1-eb85f5f9616a', name: 'Đồ uống', slug: 'do-uong' },
    { id: '0d71eac2-3bc0-412e-adff-ed0ff2011c8a', name: 'Sách', slug: 'sach' },
    {
      id: '1cc7b455-7d3f-4c15-a598-57f2f017d727',
      name: 'Tạp chí & truyện tranh',
      slug: 'tap-chi-truyen-tranh',
    },
    { id: 'e2b8bc36-8b1b-4aee-94a5-ad3979577e9f', name: 'Hoa & quà tặng', slug: 'hoa-qua-tang' },
    { id: 'f0f2d9bd-9460-4988-b251-fbdcf5f52816', name: 'Nội thất', slug: 'noi-that' },
    {
      id: 'be003e68-3ac3-4026-b02f-3cbddfdfcfd0',
      name: 'Thực phẩm khô & đóng hộp',
      slug: 'thuc-pham-kho-dong-hop',
    },
    {
      id: '84afed53-502d-4606-a30f-6d0f8692ec8f',
      name: 'Thực phẩm tươi sống',
      slug: 'thuc-pham-tuoi-song',
    },
    { id: '709df7c4-d83b-4576-a3e4-d9caa7b82f36', name: 'Trang sức', slug: 'trang-suc' },
    { id: '673f683d-0ec2-4a46-82bd-be834dfa0a0e', name: 'Túi ví', slug: 'tui-vi' },
    {
      id: 'ecf94f19-09b4-4686-ba2a-7ca64a78f9ae',
      name: 'Vali, balo & phụ kiện du lịch',
      slug: 'vali-balo-phu-kien-du-lich',
    },
    {
      id: '78de1119-a3fa-4afe-bef6-4b00a469cc5d',
      name: 'Phụ tùng & dầu nhớt',
      slug: 'phu-tung-dau-nhot',
    },
  ];

  // Lưu cha, lấy lại từ DB để gán parent cho con
  const parentEntities: Record<string, ProductCategory> = {};
  for (const cat of parentCategories) {
    let entity = await repo.findOneBy({ slug: cat.slug });
    if (!entity) {
      entity = await repo.save(repo.create({ ...cat, id: cat.id }));
    } else if (cat.id && entity.id !== cat.id) {
      // Nếu id khác thì cập nhật lại cho đúng
      entity.id = cat.id;
      await repo.save(entity);
    }
    parentEntities[cat.slug] = entity;
  }

  // Danh sách category con
  const childCategories = [
    {
      id: '89c552f4-348c-4388-b7a3-20b2a2d660ce',
      name: 'Camera',
      slug: 'camera',
      parentSlug: 'dien-tu',
    },
    {
      id: '11145114-110e-422f-b77d-1e169002029a',
      name: 'Điện thoại',
      slug: 'dien-thoai',
      parentSlug: 'dien-tu',
    },
    {
      id: 'c2cb5982-4747-4c94-9b0e-a996b63f9059',
      name: 'Laptop',
      slug: 'laptop',
      parentSlug: 'dien-tu',
    },
    {
      id: '45bc9e4c-ee27-4299-983b-26d4a1417ec4',
      name: 'Linh kiện máy tính',
      slug: 'linh-kien-may-tinh',
      parentSlug: 'dien-tu',
    },
    { id: 'f6338543-bd75-4ec2-a634-c01b1e289b5c', name: 'Loa', slug: 'loa', parentSlug: 'dien-tu' },
    {
      id: 'c800b127-a302-4a19-8925-69191efe4278',
      name: 'Máy tính',
      slug: 'may-tinh',
      parentSlug: 'dien-tu',
    },
    {
      id: '564bb9c0-34ed-4895-bf6e-ef6c5afbe68a',
      name: 'Máy tính bảng',
      slug: 'may-tinh-bang',
      parentSlug: 'dien-tu',
    },
    {
      id: '1ef6c58e-7f5f-4451-8ce3-d23a4a2e5676',
      name: 'Phụ kiện điện thoại',
      slug: 'phu-kien-dien-thoai',
      parentSlug: 'dien-tu',
    },
    {
      id: '620f74f3-9f28-4714-b1f3-b870ddd3276c',
      name: 'Tai nghe',
      slug: 'tai-nghe',
      parentSlug: 'dien-tu',
    },
    {
      id: '30612a64-8ad0-4f4c-ad1a-2673b9725230',
      name: 'Thiết bị đeo thông minh',
      slug: 'thiet-bi-deo-thong-minh',
      parentSlug: 'dien-tu',
    },
    {
      id: '056bec49-e969-46f4-8679-5697f85b9bc6',
      name: 'Thiết bị điện tử cho xe',
      slug: 'thiet-bi-dien-tu-cho-xe',
      parentSlug: 'dien-tu',
    },
    {
      id: 'a74959c5-229d-469b-bd49-33bd6edcc996',
      name: 'Chăn ga gối',
      slug: 'chan-ga-goi',
      parentSlug: 'do-gia-dung',
    },
    {
      id: 'e66f089f-191e-40ac-98ca-5a2e7123f70b',
      name: 'Đồ điện gia dụng',
      slug: 'do-dien-gia-dung',
      parentSlug: 'do-gia-dung',
    },
    {
      id: '3631a5c5-4347-4a03-bc48-74f03546daf3',
      name: 'Đồ dùng cắm trại & dã ngoại',
      slug: 'do-dung-cam-trai-da-ngoai',
      parentSlug: 'do-gia-dung',
    },
    {
      id: 'b6c5f276-5ad5-48a9-907f-939c2addb322',
      name: 'Đồ dùng nhà bếp',
      slug: 'do-dung-nha-bep',
      parentSlug: 'do-gia-dung',
    },
    {
      id: 'a7c0ea2e-7353-48b8-8cad-895ff209c078',
      name: 'Đồ thủ công mỹ nghệ',
      slug: 'do-thu-cong-my-nghe',
      parentSlug: 'do-gia-dung',
    },
    {
      id: '3559d661-9003-4669-bdab-bf1d15cc1529',
      name: 'Dụng cụ học sinh & văn phòng phẩm',
      slug: 'dung-cu-hoc-sinh-van-phong-pham',
      parentSlug: 'do-gia-dung',
    },
    {
      id: '32aacdd7-35e3-418e-93ce-46671401dc51',
      name: 'Dụng cụ sửa chữa',
      slug: 'dung-cu-sua-chua',
      parentSlug: 'do-gia-dung',
    },
    {
      id: '62303ef1-9ee2-4fea-88e8-bb08377b7b4d',
      name: 'Dụng cụ thể thao',
      slug: 'dung-cu-the-thao',
      parentSlug: 'do-gia-dung',
    },
    {
      id: '580e6730-15a2-44ca-929d-0c4312be8cd9',
      name: 'Giày dép',
      slug: 'giay-dep',
      parentSlug: 'thoi-trang-nam',
    },
    {
      id: '8173afdb-0791-41c8-b729-5596135ff249',
      name: 'Mũ bảo hiểm & đồ bảo hộ',
      slug: 'mu-bao-hiem-do-bao-ho',
      parentSlug: 'thoi-trang-nam',
    },
    {
      id: '0786a488-8d3a-4457-9cdf-3e998c108f9d',
      name: 'Sản phẩm giặt giũ chăm sóc nhà cửa',
      slug: 'san-pham-giat-giu-cham-soc-nha-cua',
      parentSlug: 'do-gia-dung',
    },
    {
      id: '4dc91f6e-38fc-468d-817b-dfd7504add5c',
      name: 'Sữa tắm dầu gội',
      slug: 'sua-tam-dau-goi',
      parentSlug: 'do-gia-dung',
    },
    {
      id: 'd49ce340-7650-49d3-87e5-61540671d2fe',
      name: 'Thời trang nam',
      slug: 'thoi-trang-nam',
      parentSlug: 'thoi-trang-nam',
    },
    {
      id: '782e55b7-4c86-4b1f-95d8-659cf70a8a65',
      name: 'Thời trang nữ',
      slug: 'thoi-trang-nu',
      parentSlug: 'thoi-trang-nu',
    },
    {
      id: 'f8b0fedf-3228-4154-807d-34ee418f2207',
      name: 'Thời trang trẻ em',
      slug: 'thoi-trang-tre-em',
      parentSlug: 'thoi-trang-tre-em',
    },
    {
      id: '85a9067c-5a1a-40e8-9307-ab5e3783afa4',
      name: 'Trang phục & giày thể thao',
      slug: 'trang-phuc-giay-the-thao',
      parentSlug: 'thoi-trang-nam',
    },
    {
      id: 'ecf94f19-09b4-4686-ba2a-7ca64a78f9ae',
      name: 'Vali, balo & phụ kiện du lịch',
      slug: 'vali-balo-phu-kien-du-lich',
      parentSlug: 'vali-balo-phu-kien-du-lich',
    },
  ];

  for (const cat of childCategories) {
    let entity = await repo.findOneBy({ slug: cat.slug });
    if (!entity) {
      entity = await repo.save(
        repo.create({
          id: cat.id,
          name: cat.name,
          slug: cat.slug,
          parent: parentEntities[cat.parentSlug],
        }),
      );
    } else if (cat.id && entity.id !== cat.id) {
      entity.id = cat.id;
      await repo.save(entity);
    }
  }

  // Có thể thêm nhiều category khác nếu muốn
  console.log('✅ Seeded product categories!');
}
